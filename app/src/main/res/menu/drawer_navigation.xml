<?xml version="1.0" encoding="utf-8"?>
<menu xmlns:android="http://schemas.android.com/apk/res/android">
    <!--Each item id should be same as in the nav_graph fragment id-->

    <item
        android:id="@+id/profileFragment"
        android:icon="@drawable/ic_profile"
        android:title="@string/profile" />

<!--    <item-->
<!--        android:id="@+id/changeLanguage"-->
<!--        android:icon="@drawable/ic_change_language"-->
<!--        android:title="@string/change_language" />-->



    <item
        android:id="@+id/rfidFragment"
        android:icon="@drawable/ic_rfid_card"
        android:title="@string/get_your_order_rfid" />

    <item
        android:id="@+id/newsFragment"
        android:icon="@drawable/ic_news"
        android:title="@string/news" />

    <item
        android:id="@+id/buyChargerFragment"
        android:icon="@drawable/ic_buy_charger"
        android:title="@string/buy_charger" />

    <item
        android:id="@+id/gamesFragment"
        android:icon="@drawable/ic_games"
        android:title="@string/games" />

    <item
        android:id="@+id/helpFragment"
        android:icon="@drawable/ic_help"
        android:title="@string/help" />

    <item
        android:id="@+id/complainFragment"
        android:icon="@drawable/ic_complain"
        android:title="@string/complain" />
    <item
        android:id="@+id/aboutUsFragment"
        android:icon="@drawable/ic_about_us"
        android:title="@string/about_us" />



   <!-- <item
        android:id="@+id/logout"
        android:icon="@drawable/ic_logout"
        android:title="@string/logout" />-->

</menu>