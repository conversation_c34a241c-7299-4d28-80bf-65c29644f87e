<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <androidx.cardview.widget.CardView
        android:id="@+id/cvComplain"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/transparent"
        app:cardBackgroundColor="@color/white"
        app:cardCornerRadius="@dimen/_5sdp"
        app:cardElevation="@dimen/_5sdp"
        app:cardUseCompatPadding="true">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <TextView
                android:id="@+id/tvComplainName"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@color/colorPrimary"
                android:gravity="center"
                android:orientation="vertical"
                android:padding="@dimen/_10sdp"
                android:text="Complain Name"
                android:textColor="@color/white" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:orientation="horizontal">

                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:gravity="center"
                    android:orientation="vertical">

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="@dimen/_4sdp"
                        android:orientation="vertical"
                        android:padding="@dimen/_2sdp"
                        android:text="@string/transaction_id"
                        android:textColor="@color/black"
                        android:textStyle="bold" />

                    <TextView
                        android:id="@+id/tvTarsId"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="@dimen/_4sdp"
                        android:orientation="vertical"
                        android:padding="@dimen/_2sdp"
                        android:text="55"
                        android:textColor="@color/text_color" />

                </LinearLayout>

                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:gravity="end"
                    android:orientation="vertical">

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginEnd="@dimen/_4sdp"
                        android:gravity="end"
                        android:orientation="vertical"
                        android:padding="@dimen/_2sdp"
                        android:text="@string/status"
                        android:textColor="@color/black"
                        android:textStyle="bold" />

                    <TextView
                        android:id="@+id/tvStatus"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginEnd="@dimen/_4sdp"
                        android:gravity="end"
                        android:orientation="vertical"
                        android:padding="@dimen/_2sdp"
                        android:text="In Progress"
                        android:textColor="@color/text_color" />

                </LinearLayout>


            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/_5sdp"
                android:gravity="center"
                android:orientation="horizontal">

                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:gravity="center"
                    android:orientation="vertical">

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="@dimen/_5sdp"
                        android:orientation="vertical"
                        android:padding="@dimen/_2sdp"
                        android:text="@string/date"

                        android:textColor="@color/black"
                        android:textStyle="bold" />

                    <TextView
                        android:id="@+id/tvDate"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="@dimen/_5sdp"
                        android:orientation="vertical"
                        android:padding="@dimen/_2sdp"
                        android:text="15 July 2021"
                        android:textColor="@color/text_color" />

                </LinearLayout>

                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:gravity="center"
                    android:orientation="vertical">

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginEnd="@dimen/_4sdp"
                        android:gravity="end"
                        android:orientation="vertical"
                        android:padding="@dimen/_2sdp"
                        android:text="@string/time"
                        android:textColor="@color/black"
                        android:textStyle="bold" />

                    <TextView
                        android:id="@+id/tvTime"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginEnd="@dimen/_4sdp"
                        android:gravity="end"
                        android:orientation="vertical"
                        android:padding="@dimen/_2sdp"
                        android:text="10:00 AM"
                        android:textColor="@color/text_color" />

                </LinearLayout>

            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/_5sdp"
                android:gravity="center"
                android:orientation="vertical">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/_4sdp"
                    android:orientation="vertical"
                    android:padding="@dimen/_2sdp"
                    android:text="@string/detail"
                    android:textColor="@color/black"
                    android:textStyle="bold" />
            </LinearLayout>

            <TextView
                android:id="@+id/tvDec"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="#26FDC350"
                android:orientation="vertical"
                android:paddingStart="@dimen/_9sdp"
                android:paddingTop="@dimen/_5sdp"
                android:paddingEnd="@dimen/_15sdp"
                android:paddingBottom="@dimen/_15sdp"
                android:text="You can use a credit card by clicking continue to payment. Also read the."
                android:textColor="@color/text_color" />
        </LinearLayout>

    </androidx.cardview.widget.CardView>
</layout>