<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_20sdp"
        android:gravity="center"
        android:text="@string/slide3_title"
        android:textColor="@color/colorPrimary"
        android:textSize="@dimen/_15sdp"
        android:textStyle="bold" />

    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_5sdp"
        android:gravity="center"
        android:text="@string/slide3_subtitle"
        android:textColor="@color/colorPrimary" />

    <ImageView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_gravity="center"
        android:scaleType="fitCenter"
        android:src="@drawable/slider3" />

</LinearLayout>