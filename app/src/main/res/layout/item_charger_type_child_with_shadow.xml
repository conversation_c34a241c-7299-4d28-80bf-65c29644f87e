<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <androidx.cardview.widget.CardView
        android:id="@+id/cardContainer"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_margin="@dimen/_4sdp"
        app:cardCornerRadius="@dimen/_8sdp"
        app:cardElevation="@dimen/_4sdp"
        app:cardUseCompatPadding="true">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="@dimen/_4sdp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="@dimen/_4sdp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:orientation="horizontal">

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1">

                        <ImageView
                            android:id="@+id/ivDot"
                            android:layout_width="@dimen/_15sdp"
                            android:layout_height="@dimen/_15sdp"
                            android:layout_gravity="center"
                            android:layout_margin="@dimen/_2sdp"
                            android:src="@drawable/bg_outside_circle" />

                        <TextView
                            android:id="@+id/tvStatus"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_margin="@dimen/_1sdp"
                            android:padding="@dimen/_1sdp"
                            android:text="@string/app_name"
                            android:textColor="@color/black"
                            android:textSize="@dimen/_14sdp"
                            android:textStyle="bold" />

                    </LinearLayout>


                    <TextView
                        android:id="@+id/tvCode"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_margin="@dimen/_2sdp"
                        android:padding="@dimen/_2sdp"
                        android:text="@string/plug"
                        android:textSize="@dimen/_12sdp" />

                </LinearLayout>

                <View
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/_1sdp"
                    android:layout_marginTop="@dimen/_3sdp"
                    android:layout_marginBottom="@dimen/_3sdp"
                    android:background="@color/light_gray"
                    android:visibility="visible" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:orientation="horizontal">

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1">

                        <ImageView
                            android:id="@+id/ivPowerType"
                            android:layout_width="@dimen/_15sdp"
                            android:layout_height="@dimen/_15sdp"
                            android:layout_gravity="center"
                            android:layout_margin="@dimen/_2sdp"
                            android:src="@drawable/ic_power" />

                        <TextView
                            android:id="@+id/tvPowerType"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_margin="@dimen/_1sdp"
                            android:padding="@dimen/_1sdp"
                            android:text="@string/app_name"
                            android:textColor="@color/black"
                            android:textSize="@dimen/_13sdp"
                            android:textStyle="bold" />

                        <TextView
                            android:id="@+id/tvPowerRating"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_margin="@dimen/_1sdp"
                            android:padding="@dimen/_1sdp"
                            android:text="0 KWH"
                            android:textColor="@color/gray"
                            android:textSize="@dimen/_13sdp" />

                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content">

                        <TextView
                            android:id="@+id/tvConnectorPrice"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_margin="@dimen/_2sdp"
                            android:padding="@dimen/_1sdp"
                            android:text="@string/Rs"
                            android:textColor="@color/black"
                            android:textSize="@dimen/_13sdp"
                            android:textStyle="bold" />

                        <TextView
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_margin="@dimen/_2sdp"
                            android:padding="@dimen/_1sdp"
                            android:text="@string/per_kwh"
                            android:textColor="@color/gray"
                            android:textSize="@dimen/_11sdp" />

                    </LinearLayout>

                </LinearLayout>

            </LinearLayout>
        </LinearLayout>
    </androidx.cardview.widget.CardView>
</layout>
