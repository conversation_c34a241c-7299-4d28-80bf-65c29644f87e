<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    tools:context=".ui.fragment.wallet.WalletFragment">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <include
            android:id="@+id/toolbar"
            layout="@layout/main_toolbar" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical">

            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:cardBackgroundColor="@color/white"
                app:cardCornerRadius="@dimen/_10sdp"
                app:cardElevation="@dimen/_5sdp"
                app:cardUseCompatPadding="true">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_margin="@dimen/_10sdp"
                    android:orientation="vertical">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_gravity="center"
                            android:padding="@dimen/_5sdp"
                            android:text="@string/available_balance" />

                        <androidx.appcompat.widget.LinearLayoutCompat
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_gravity="center"
                            android:gravity="center"
                            android:orientation="horizontal">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_gravity="center"
                                android:padding="@dimen/_5sdp"
                                android:text="@string/Rs"
                                android:textSize="@dimen/_15sdp" />

                            <TextView
                                android:id="@+id/tvBalance"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_gravity="center"
                                android:padding="@dimen/_5sdp"
                                android:text="0"
                                android:textSize="@dimen/_15sdp" />
                        </androidx.appcompat.widget.LinearLayoutCompat>


                    </LinearLayout>

                    <LinearLayout
                        android:id="@+id/btnAddMoney"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center"
                        android:layout_margin="@dimen/_5sdp"
                        android:background="@drawable/btn_gray_round_corner"
                        android:orientation="horizontal"
                        android:padding="@dimen/_5sdp">

                        <ImageView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_gravity="center"
                            android:padding="@dimen/_1sdp"
                            android:src="@drawable/ic_round_pluse"
                            app:tint="@color/colorPrimary" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_gravity="center"
                            android:text="@string/add_money" />

                    </LinearLayout>
                </LinearLayout>

            </androidx.cardview.widget.CardView>

            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical">

                <TextView
                    android:id="@+id/tv1"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:gravity="center"
                    android:text="@string/see_activity" />

                <RelativeLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_above="@+id/tvViewMore"
                    android:layout_below="@+id/tv1">

                    <androidx.recyclerview.widget.RecyclerView
                        android:id="@+id/rvWallet"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:visibility="visible"
                        android:clipToPadding="false"
                        android:padding="@dimen/_4sdp"
                        app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
                        tools:listitem="@layout/item_wallet" />

                    <RelativeLayout
                        android:id="@+id/emptyView"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:visibility="gone">

                        <include
                            android:id="@+id/incView"
                            layout="@layout/empty_view" />
                    </RelativeLayout>
                </RelativeLayout>

                <TextView
                    android:id="@+id/tvViewMore"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_alignParentBottom="true"
                    android:layout_marginBottom="@dimen/_10sdp"
                    android:gravity="center"
                    android:padding="@dimen/_5sdp"
                    android:text="@string/view_more"
                    android:textColor="@color/colorPrimary"
                    android:textSize="@dimen/_12sdp"
                    android:textStyle="bold" />
            </RelativeLayout>

        </LinearLayout>

    </LinearLayout>
</layout>

