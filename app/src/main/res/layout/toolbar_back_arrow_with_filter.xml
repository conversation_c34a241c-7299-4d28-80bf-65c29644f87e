<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="@dimen/_60sdp"
        android:gravity="center"
        android:orientation="horizontal">

        <LinearLayout
            android:id="@+id/backButton"
            android:layout_width="@dimen/_28sdp"
            android:layout_height="@dimen/_28sdp"
            android:layout_marginStart="@dimen/_8sdp"
            android:background="@drawable/bg_back_arrow"
            android:gravity="center">

            <ImageView
                android:layout_width="@dimen/_20sdp"
                android:layout_height="@dimen/_20sdp"
                android:src="@drawable/ic_back_arrow" />
        </LinearLayout>

        <TextView
            android:id="@+id/tvTitle"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_weight="1"
            android:gravity="center"
            android:text="@string/app_name"
            android:textSize="@dimen/_16sdp"
            android:textStyle="bold"
            android:textColor="@color/black"
            android:visibility="visible" />


        <LinearLayout
            android:id="@+id/ivFilter"
            android:layout_width="@dimen/_28sdp"
            android:layout_height="@dimen/_28sdp"
            android:layout_marginStart="@dimen/_8sdp"
            android:layout_marginEnd="@dimen/_8sdp"
            android:background="@drawable/bg_back_arrow"
            android:gravity="center">

            <ImageView
                android:id="@+id/ivFilter1"
                android:layout_width="@dimen/_20sdp"
                android:layout_height="@dimen/_20sdp"
                android:src="@drawable/ic_filter" />
        </LinearLayout>

    </LinearLayout>

</layout>

