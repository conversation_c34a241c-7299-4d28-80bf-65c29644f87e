<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        tools:context=".ui.activities.ChargingStationListActivity">

        <include
            android:id="@+id/toolbar"
            layout="@layout/toolbar_back_arrow_with_filter" />

        <com.google.android.material.tabs.TabLayout
            android:id="@+id/tabBooking"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_below="@id/toolbar"
            android:layout_gravity="center"
            android:layout_marginStart="@dimen/_10sdp"
            android:layout_marginEnd="@dimen/_10sdp"
            android:background="@drawable/tab_background"
            android:elevation="2dp"
            app:tabBackground="@drawable/tab_selector"
            app:tabGravity="fill"
            app:tabIndicatorHeight="0dp"
            app:tabMode="fixed"
            app:tabRippleColor="@null"
            app:tabSelectedTextColor="@android:color/white"
            app:tabTextAppearance="@style/tab_text"
            app:tabTextColor="@android:color/black">

            <com.google.android.material.tabs.TabItem
                android:id="@+id/tabSession"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/station_list" />

            <com.google.android.material.tabs.TabItem
                android:id="@+id/tabPackage"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/favourite" />


        </com.google.android.material.tabs.TabLayout>

        <FrameLayout
            android:id="@android:id/tabcontent"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_below="@id/tabBooking"
            android:layout_margin="@dimen/_4sdp">

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/rvData"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:visibility="visible"
                app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
                tools:listitem="@layout/item_charging_station" />

        </FrameLayout>
    </RelativeLayout>
</layout>