<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/colorPrimary">

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_marginTop="@dimen/_30sdp"
            android:background="@drawable/bg_top_round">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_above="@id/btnNext"
                android:background="@drawable/bg_top_round"
                android:orientation="vertical"
                android:padding="@dimen/_15sdp"
                tools:context=".ui.activities.userDetail.UserProfileActivity">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/_2sdp"
                    android:layout_marginTop="@dimen/_10sdp"
                    android:layout_marginBottom="@dimen/_5sdp"
                    android:text="@string/user_information"
                    android:textColor="@color/colorPrimary"
                    android:textSize="@dimen/_18sdp"
                    android:textStyle="bold" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/_12sdp"
                    android:orientation="horizontal">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:orientation="vertical">

                        <TextView
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="@dimen/_3sdp"
                            android:text="@string/first_name"
                            android:textSize="@dimen/_11sdp" />

                        <EditText
                            android:id="@+id/etFirstName"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:hint="@string/first_name"
                            android:imeOptions="actionNext"
                            android:inputType="text"
                            android:textSize="@dimen/_12sdp" />

                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:orientation="vertical">

                        <TextView
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="@dimen/_3sdp"
                            android:text="@string/last_name"
                            android:textSize="@dimen/_11sdp" />

                        <EditText
                            android:id="@+id/etLastName"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:hint="@string/last_name"
                            android:imeOptions="actionNext"
                            android:inputType="text"
                            android:textSize="@dimen/_12sdp" />

                    </LinearLayout>
                </LinearLayout>

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/_12sdp"
                    android:layout_marginStart="@dimen/_2sdp"
                    android:text="@string/optional"
                    android:textColor="@color/colorPrimary"
                    android:textSize="@dimen/_11sdp" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/_12sdp"
                    android:orientation="vertical">

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="@dimen/_3sdp"
                        android:text="@string/email"
                        android:textSize="@dimen/_11sdp" />

                    <EditText
                        android:id="@+id/etEmail"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:hint="@string/email"
                        android:imeOptions="actionNext"
                        android:inputType="textEmailAddress"
                        android:textSize="@dimen/_12sdp" />

                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/_12sdp"
                    android:orientation="vertical">

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="@dimen/_3sdp"
                        android:text="@string/gender"
                        android:textSize="@dimen/_11sdp" />

                    <RadioGroup
                        android:id="@+id/radioSex"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/_5sdp"
                        android:orientation="horizontal">

                        <RadioButton
                            android:id="@+id/radioMale"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:checked="true"
                            android:text="@string/male" />

                        <RadioButton
                            android:id="@+id/radioFemale"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/female" />

                    </RadioGroup>

                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/_12sdp"
                    android:orientation="vertical">

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="@dimen/_3sdp"
                        android:text="@string/date_of_birth"
                        android:textSize="@dimen/_11sdp" />

                    <EditText
                        android:id="@+id/etDob"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:focusable="false"
                        android:hint="@string/mm_dd_yyyy"
                        android:imeOptions="actionNext"
                        android:inputType="datetime"
                        android:textSize="@dimen/_12sdp" />
                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/_12sdp"
                    android:orientation="vertical">

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="@dimen/_3sdp"
                        android:text="@string/gst_no"
                        android:textSize="@dimen/_11sdp" />

                    <EditText
                        android:id="@+id/etGstNO"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:hint="@string/gst_no"
                        android:imeOptions="actionNext"
                        android:inputType="text"
                        android:textSize="@dimen/_12sdp" />

                </LinearLayout>

<!--                <Button-->
<!--                    android:id="@+id/btnDeleteAccount"-->
<!--                    android:layout_width="match_parent"-->
<!--                    android:layout_height="wrap_content"-->
<!--                    android:layout_marginTop="@dimen/_20sdp"-->
<!--                    android:background="@drawable/btn_shape_dialog_red"-->
<!--                    android:text="@string/delete_account"-->
<!--                    android:textColor="@color/white" />-->

            </LinearLayout>


            <LinearLayout
                android:id="@+id/btnNext"
                android:layout_width="@dimen/_30sdp"
                android:layout_height="@dimen/_30sdp"
                android:layout_alignParentEnd="true"
                android:layout_alignParentBottom="true"
                android:layout_margin="@dimen/_15sdp"
                android:background="@drawable/btn_next"
                android:gravity="center">

                <ImageView
                    android:layout_width="@dimen/_20sdp"
                    android:layout_height="@dimen/_20sdp"
                    android:rotation="180"
                    android:src="@drawable/ic_back_arrow"
                    app:tint="@color/white" />
            </LinearLayout>

        </RelativeLayout>


    </RelativeLayout>

</layout>