<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    tools:context=".fragments.RFIDFragment">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical">

        <include
            android:id="@+id/toolbar"
            layout="@layout/main_toolbar" />

        <FrameLayout
            android:id="@+id/frameData"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_above="@+id/llBottom"
            android:layout_below="@+id/toolbar">

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/rvRFIDTrack"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:visibility="gone"
                app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
                tools:listitem="@layout/item_rfid_card" />

            <LinearLayout
                android:id="@+id/llNewCard"
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:layout_gravity="center"
                android:gravity="center"
                android:orientation="vertical"
                android:visibility="visible">

                <ImageView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:src="@drawable/ic_rfid_card" />

                <TextView
                    android:id="@+id/tvNewCard"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/_10sdp"
                    android:background="@drawable/dotted_square"
                    android:padding="@dimen/_10sdp"
                    android:text="@string/issue_new_card"
                    android:textColor="@color/colorPrimary"
                    android:textStyle="bold" />

                <TextView
                    android:id="@+id/tvTrackOrder"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/_10sdp"
                    android:background="@drawable/dotted_square"
                    android:padding="@dimen/_10sdp"
                    android:text="@string/track_order"
                    android:textColor="@color/colorPrimary"
                    android:textStyle="bold" />


            </LinearLayout>

        </FrameLayout>

        <LinearLayout
            android:id="@+id/llBottom"
            android:visibility="invisible"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_alignParentBottom="true"
            android:orientation="horizontal">

            <androidx.cardview.widget.CardView
                android:id="@+id/cvTrackCard"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/_5sdp"
                android:layout_marginEnd="@dimen/_5sdp"
                android:layout_marginBottom="@dimen/_10sdp"
                android:layout_weight="1"
                app:cardBackgroundColor="@color/white"
                app:cardCornerRadius="@dimen/_10sdp"
                app:cardUseCompatPadding="true">

                <androidx.appcompat.widget.LinearLayoutCompat
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:gravity="center"
                    android:orientation="horizontal"
                    android:padding="@dimen/_5sdp">

                    <ImageView
                        android:layout_width="@dimen/_30sdp"
                        android:layout_height="@dimen/_30sdp"
                        android:layout_gravity="center"
                        android:padding="@dimen/_2sdp"
                        android:src="@drawable/ic_clock2"
                        app:tint="@color/colorPrimary" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center"
                        android:padding="@dimen/_2sdp"
                        android:text="@string/track_card"
                        android:textColor="@color/black"
                        android:textSize="@dimen/_14sdp" />
                </androidx.appcompat.widget.LinearLayoutCompat>
            </androidx.cardview.widget.CardView>

            <androidx.cardview.widget.CardView
                android:id="@+id/cvNewCard"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/_5sdp"
                android:layout_marginEnd="@dimen/_5sdp"
                android:layout_marginBottom="@dimen/_10sdp"
                android:layout_weight="1"
                app:cardBackgroundColor="@color/white"
                app:cardCornerRadius="@dimen/_10sdp"
                app:cardUseCompatPadding="true">

                <androidx.appcompat.widget.LinearLayoutCompat
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:gravity="center"
                    android:orientation="horizontal"
                    android:padding="@dimen/_5sdp">

                    <ImageView
                        android:layout_width="@dimen/_30sdp"
                        android:layout_height="@dimen/_30sdp"
                        android:layout_gravity="center"
                        android:padding="@dimen/_2sdp"
                        android:src="@drawable/ic_clock2"
                        app:tint="@color/colorPrimary" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center"
                        android:padding="@dimen/_2sdp"
                        android:text="@string/new_card"
                        android:textColor="@color/black"
                        android:textSize="@dimen/_14sdp" />
                </androidx.appcompat.widget.LinearLayoutCompat>
            </androidx.cardview.widget.CardView>

        </LinearLayout>
    </RelativeLayout>

</layout>