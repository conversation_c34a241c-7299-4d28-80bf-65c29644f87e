<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@drawable/dialog_round_corner">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <FrameLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical">

                <androidx.cardview.widget.CardView
                    android:id="@+id/cvBackground"
                    android:layout_width="@dimen/_50sdp"
                    android:layout_height="@dimen/_50sdp"
                    android:layout_gravity="center"
                    android:layout_margin="@dimen/_12sdp"
                    android:gravity="center"
                    app:cardBackgroundColor="@color/bg_red_transparent"
                    app:cardCornerRadius="12dp"
                    app:cardElevation="0dp"
                    app:cardMaxElevation="0dp">

                    <ImageView
                        android:id="@+id/image"
                        android:layout_width="@dimen/_40sdp"
                        android:layout_height="@dimen/_40sdp"
                        android:layout_gravity="center"
                        android:gravity="center"
                        android:padding="@dimen/_5sdp"
                        android:src="@drawable/ic_delete" />
                </androidx.cardview.widget.CardView>

                <ImageView
                    android:id="@+id/ivDismiss"
                    android:layout_width="@dimen/_30sdp"
                    android:layout_height="@dimen/_30sdp"
                    android:layout_gravity="top|end"
                    android:layout_marginTop="@dimen/_5sdp"
                    android:layout_marginEnd="@dimen/_5sdp"
                    android:gravity="center"
                    android:padding="@dimen/_8sdp"
                    android:src="@drawable/ic_close" />

            </FrameLayout>

            <TextView
                android:id="@+id/tvTitle"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/_20sdp"
                android:layout_marginEnd="@dimen/_20sdp"
                android:gravity="center"
                android:padding="@dimen/_5sdp"
                android:text="@string/app_name"
                android:textColor="@android:color/black"
                android:textSize="@dimen/_15sdp"
                android:textStyle="bold" />

            <TextView
                android:id="@+id/tvMsg"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/_10sdp"
                android:layout_marginEnd="@dimen/_10sdp"
                android:gravity="center"
                android:padding="@dimen/_5sdp"
                android:text="@string/app_name"
                android:textColor="@android:color/black"
                android:textSize="@dimen/_12sdp" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:layout_margin="@dimen/_10sdp"
                android:gravity="center"
                android:orientation="horizontal">

                <TextView
                    android:id="@+id/tvNo"
                    android:layout_width="@dimen/_100sdp"
                    android:layout_height="@dimen/_35sdp"
                    android:layout_margin="@dimen/_3sdp"
                    android:padding="@dimen/_5sdp"
                    android:text="@string/no"
                    android:background="@drawable/btn_shape_dialog"
                    android:gravity="center"
                    android:textColor="@color/white"
                    android:textSize="@dimen/_13sdp"
                    android:visibility="visible" />

                <TextView
                    android:id="@+id/tvYes"
                    android:layout_width="@dimen/_100sdp"
                    android:layout_height="@dimen/_35sdp"
                    android:layout_margin="@dimen/_3sdp"
                    android:background="@drawable/btn_shape_dialog"
                    android:gravity="center"
                    android:textColor="@color/white"
                    android:padding="@dimen/_5sdp"
                    android:text="@string/yes"
                    android:textSize="@dimen/_13sdp" />

            </LinearLayout>

        </LinearLayout>

    </RelativeLayout>
</layout>