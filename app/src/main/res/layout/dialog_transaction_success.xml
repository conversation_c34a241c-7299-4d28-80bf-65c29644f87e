<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:layout_margin="@dimen/_10sdp"
        android:background="@drawable/dialog_round_corner"
        android:orientation="vertical">

        <FrameLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <androidx.cardview.widget.CardView
                android:id="@+id/cvBackground"
                android:layout_width="@dimen/_62sdp"
                android:layout_height="@dimen/_62sdp"
                android:layout_gravity="center"
                android:layout_margin="@dimen/_12sdp"
                android:gravity="center"
                app:cardBackgroundColor="@color/bg_primary_transparent"
                app:cardCornerRadius="12dp"
                app:cardElevation="0dp"
                app:cardMaxElevation="0dp">

                <ImageView
                    android:id="@+id/image"
                    android:layout_width="@dimen/_50sdp"
                    android:layout_height="@dimen/_50sdp"
                    android:layout_gravity="center"
                    android:gravity="center"
                    android:padding="@dimen/_5sdp"
                    android:src="@drawable/ic_succes" />
            </androidx.cardview.widget.CardView>

            <ImageView
                android:id="@+id/ivDismiss"
                android:layout_width="@dimen/_30sdp"
                android:layout_height="@dimen/_30sdp"
                android:layout_gravity="top|end"
                android:layout_marginTop="@dimen/_5sdp"
                android:layout_marginEnd="@dimen/_5sdp"
                android:gravity="center"
                android:padding="@dimen/_8sdp"
                android:src="@drawable/ic_close" />

        </FrameLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_margin="@dimen/_10sdp"
            android:gravity="center_horizontal"
            android:orientation="vertical"
            android:padding="@dimen/_10sdp">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/_5sdp"
                android:gravity="center_vertical"
                android:text="@string/thank_you"
                android:textAppearance="@style/Base.TextAppearance.AppCompat.Medium"
                android:textColor="@color/colorPrimary"
                android:textStyle="bold" />

            <TextView
                android:id="@+id/tvMsg"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginBottom="@dimen/_5sdp"
                android:gravity="center_vertical"
                android:text="@string/msg_your_transaction_was_successful"
                android:textAppearance="@style/Base.TextAppearance.AppCompat.Small" />

            <View
                android:layout_width="match_parent"
                android:layout_height="1dp"
                android:layout_margin="2dp"
                android:background="@color/colorPrimary" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="@dimen/_5sdp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:gravity="center_vertical"
                        android:text="@string/start_time"
                        android:textColor="@color/light_gray" />

                    <View
                        android:layout_width="0dp"
                        android:layout_height="0dp"
                        android:layout_weight="1" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:gravity="center_vertical"
                        android:text="@string/duration"
                        android:textColor="@color/light_gray" />

                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal">

                    <TextView
                        android:id="@+id/tvDate"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:gravity="center_vertical"
                        android:text=""
                        android:textAppearance="@style/Base.TextAppearance.AppCompat.Subhead"
                        android:textStyle="bold" />

                    <View
                        android:layout_width="0dp"
                        android:layout_height="0dp"
                        android:layout_weight="1" />

                    <TextView
                        android:id="@+id/tvTime"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:gravity="center_vertical"
                        android:textAppearance="@style/Base.TextAppearance.AppCompat.Subhead"
                        android:textStyle="bold" />

                </LinearLayout>

            </LinearLayout>


            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="@dimen/_5sdp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:gravity="center_vertical"
                        android:text="@string/charger_point"
                        android:textAllCaps="true"
                        android:textColor="@color/light_gray" />

                    <View
                        android:layout_width="0dp"
                        android:layout_height="0dp"
                        android:layout_weight="1" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:gravity="center_vertical"
                        android:text="@string/total_charges"
                        android:textColor="@color/light_gray" />

                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal">

                    <TextView
                        android:id="@+id/tvChargeStation"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:gravity="center_vertical"
                        android:layout_weight="1"
                        android:text=""
                        android:textAppearance="@style/Base.TextAppearance.AppCompat.Subhead"
                        android:textStyle="bold" />


                    <TextView
                        android:id="@+id/tvCharge"
                        android:layout_width="@dimen/_120sdp"
                        android:layout_gravity="end"
                        android:layout_height="wrap_content"
                        android:gravity="end"
                        android:text=""
                        android:textAppearance="@style/Base.TextAppearance.AppCompat.Subhead"
                        android:textStyle="bold" />

                </LinearLayout>

            </LinearLayout>


            <TextView
                android:id="@+id/btnViewWallet"
                android:layout_width="match_parent"
                android:layout_height="@dimen/_35sdp"
                android:layout_gravity="center"
                android:layout_margin="@dimen/_5sdp"
                android:background="@drawable/btn_round_shape"
                android:gravity="center"
                android:text="@string/more_info"
                android:textColor="@android:color/white"
                android:textSize="@dimen/_13sdp" />


        </LinearLayout>
    </LinearLayout>

    <!--   <ImageView
           android:layout_width="@dimen/_60sdp"
           android:layout_height="@dimen/_60sdp"
           android:layout_gravity="center|top"
           android:background="@drawable/bg_circle_white"
           android:contentDescription="@string/app_name"
           android:padding="@dimen/_10sdp"
           android:src="@drawable/ic_succesfull" />

   </FrameLayout>-->


</layout>