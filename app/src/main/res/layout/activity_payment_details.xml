<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical"
        tools:context=".ui.activities.paymentDetail.PaymentDetailsActivity">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_above="@id/btnComplain"
            android:orientation="vertical">

            <include
                android:id="@+id/toolbar"
                layout="@layout/toolbar_back_arrow" />

            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@color/transparent"
                app:cardBackgroundColor="@color/white"
                app:cardCornerRadius="@dimen/_5sdp"
                app:cardElevation="@dimen/_5sdp"
                app:cardUseCompatPadding="true">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/_10sdp"
                        android:gravity="center"
                        android:orientation="horizontal">

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="@dimen/_10sdp"
                            android:layout_weight="1"
                            android:orientation="vertical"
                            android:padding="@dimen/_2sdp"
                            android:text="@string/transaction_id"
                            android:textColor="@color/text_color" />

                        <TextView
                            android:id="@+id/tvID"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginEnd="@dimen/_10sdp"
                            android:orientation="vertical"
                            android:padding="@dimen/_2sdp"
                            android:textColor="@color/black"
                            android:text="123456789" />

                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/_10sdp"
                        android:gravity="center"
                        android:orientation="horizontal">

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="@dimen/_10sdp"
                            android:layout_weight="1"
                            android:orientation="vertical"
                            android:padding="@dimen/_2sdp"
                            android:text="@string/date_time"
                            android:textColor="@color/text_color" />

                        <TextView
                            android:textColor="@color/black"
                            android:id="@+id/tvPaymentDateTime"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginEnd="@dimen/_10sdp"
                            android:orientation="vertical"
                            android:padding="@dimen/_2sdp"
                            android:text="123456789" />

                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/_10sdp"
                        android:gravity="center"
                        android:orientation="horizontal">

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="@dimen/_10sdp"
                            android:layout_weight="1"
                            android:orientation="vertical"
                            android:padding="@dimen/_2sdp"
                            android:text="@string/amount"
                            android:textColor="@color/text_color" />

                        <TextView
                            android:textColor="@color/black"
                            android:id="@+id/tvAmount"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginEnd="@dimen/_10sdp"
                            android:orientation="vertical"
                            android:padding="@dimen/_2sdp"
                            android:text="@string/Rs" />

                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/_10sdp"
                        android:gravity="center"
                        android:orientation="horizontal">

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="@dimen/_10sdp"
                            android:layout_weight="1"
                            android:orientation="vertical"
                            android:padding="@dimen/_2sdp"
                            android:text="@string/payment_mode"
                            android:textColor="@color/text_color" />

                        <TextView
                            android:textColor="@color/black"
                            android:id="@+id/tvMode"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginEnd="@dimen/_10sdp"
                            android:orientation="vertical"
                            android:padding="@dimen/_2sdp"
                            android:text="123456789" />

                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/_10sdp"
                        android:layout_marginBottom="@dimen/_10sdp"
                        android:gravity="center"
                        android:orientation="horizontal">

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="@dimen/_10sdp"
                            android:layout_weight="1"
                            android:orientation="vertical"
                            android:padding="@dimen/_2sdp"
                            android:text="@string/payment_status"
                            android:textColor="@color/text_color" />


                        <androidx.cardview.widget.CardView
                            android:id="@+id/cvStatus"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginEnd="@dimen/_10sdp"
                            app:cardCornerRadius="@dimen/_10sdp">

                            <TextView
                                android:id="@+id/tvPaymentStatus"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:gravity="center"
                                android:orientation="vertical"
                                android:paddingStart="@dimen/_5sdp"
                                android:paddingTop="@dimen/_2sdp"
                                android:paddingEnd="@dimen/_5sdp"
                                android:paddingBottom="@dimen/_2sdp"
                                android:text="Success" />
                        </androidx.cardview.widget.CardView>

                    </LinearLayout>


                </LinearLayout>

            </androidx.cardview.widget.CardView>

        </LinearLayout>

        <Button
            android:id="@+id/btnComplain"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_alignParentBottom="true"
            android:layout_gravity="center"
            android:layout_margin="@dimen/_20sdp"
            android:background="@drawable/btn_round_shape"
            android:padding="@dimen/_10sdp"
            android:text="@string/add_complain"
            android:textAllCaps="false"
            android:textColor="@android:color/white"
            android:textSize="20sp" />
    </RelativeLayout>

</layout>