package com.nxc.evcsolutions.ui.fragment.wallet

import android.content.Context
import android.util.Log
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.RecyclerView
import com.nxc.evcsolutions.CommonUtility
import com.nxc.evcsolutions.R
import com.nxc.evcsolutions.databinding.ItemWalletBinding
import com.nxc.evcsolutions.interfaces.ItemClickListener

class WalletAdapter(private val items: ArrayList<WalletCommonData>
                    ,private val mOnItemClickListener: ItemClickListener
) :
    RecyclerView.Adapter<WalletAdapter.ViewHolder>() {
    private lateinit var mContext: Context

    init {
        // Log the initial items size
        Log.d("WalletAdapter", "Initialized with ${items.size} items")
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
        val inflater = LayoutInflater.from(parent.context)
        val binding = ItemWalletBinding.inflate(inflater, parent, false)
        mContext = parent.context
        return ViewHolder(binding)
    }

    override fun getItemCount(): Int {
        val count = items.size
        Log.d("WalletAdapter", "getItemCount: $count")
        return count
    }

    override fun onBindViewHolder(holder: ViewHolder, position: Int) {
        Log.d("WalletAdapter", "Binding item at position: $position")
        holder.bind(items[position])
    }

    inner class ViewHolder(val binding: ItemWalletBinding) : RecyclerView.ViewHolder(binding.root) {
        fun bind(item: WalletCommonData) {
        //    binding.tvName.text = item.type
            binding.tvDateTime.text =
                CommonUtility.changeDateMyFormat(item.date, "yyyy-MM-dd", "dd MMM yyyy")

            /*when (item.type) {
                "1" -> {
                    binding.ivWallet.setImageResource(R.drawable.ic_car)
                    binding.tvName.text = mContext.getString(R.string.msg_nxc_paid)
                    binding.tvReason.text=mContext.getString(R.string.paid_for_charging)
                }
                else -> {
                    binding.ivWallet.setImageResource(R.drawable.ic_rupees)
                    binding.tvName.text = mContext.getString(R.string.msg_bank_paid)
                    binding.tvReason.text=mContext.getString(R.string.msg_added_to_wallet)
                }
            }*/
            // Log the type to see what we're getting
            Log.d("WalletAdapter", "Item type: ${item.type}, class: ${item.type.javaClass.name}")

            // Handle different transaction types
            when (item.type) {
                "1", "1.0" -> {
                    binding.ivWallet.setImageResource(R.drawable.ic_car)
                    binding.tvName.text = mContext.getString(R.string.msg_nxc_paid)
                    binding.tvReason.text = mContext.getString(R.string.paid_for_charging)
                }
                "2", "2.0" -> {
                    binding.ivWallet.setImageResource(R.drawable.ic_rupees)
                    binding.tvName.text = mContext.getString(R.string.msg_bank_paid)
                    binding.tvReason.text = mContext.getString(R.string.msg_added_to_wallet)
                }
                else -> {
                    binding.ivWallet.setImageResource(R.drawable.ic_car)
                    binding.tvName.text = mContext.getString(R.string.msg_purchase_rfid)
                    binding.tvReason.text = item.discription // Use the description from the data
                }
            }

            if (item.status == "success") {
                binding.tvAmount.text = "+ " + mContext.getString(R.string.Rs) + " " + item.amount
                binding.tvAmount.setTextColor(
                    ContextCompat.getColor(
                        mContext,
                        R.color.amount_credit
                    )
                )
            } else {
                binding.tvAmount.text = "- " + mContext.getString(R.string.Rs) + " " + item.amount
                binding.tvAmount.setTextColor(ContextCompat.getColor(mContext, R.color.amount_debit))
            }

            binding.clickEvent.setOnClickListener {
                mOnItemClickListener.onItemClick(2,item)

            }
        }
    }
}
