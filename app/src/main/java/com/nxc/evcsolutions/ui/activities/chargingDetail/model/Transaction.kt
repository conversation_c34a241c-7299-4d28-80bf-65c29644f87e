package com.nxc.evcsolutions.ui.activities.chargingDetail.model

import com.google.gson.annotations.SerializedName

data class Transaction(
    @SerializedName("transaction_id") var transactionId: String? = null,
    @SerializedName("date") var date: String? = null,
    @SerializedName("time") var time: String? = null,
    @SerializedName("duration") var duration: String? = null,
    @SerializedName("charger") var charger: String? = null,
    @SerializedName("cs_name") var csName: String? = null

)