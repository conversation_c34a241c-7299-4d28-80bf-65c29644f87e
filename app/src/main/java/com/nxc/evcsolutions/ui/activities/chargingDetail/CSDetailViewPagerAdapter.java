package com.nxc.evcsolutions.ui.activities.chargingDetail;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;

import androidx.viewpager.widget.PagerAdapter;
import androidx.viewpager.widget.ViewPager;

import com.bumptech.glide.Glide;
import com.bumptech.glide.request.RequestOptions;
import com.bumptech.glide.signature.ObjectKey;
import com.nxc.evcsolutions.CommonUtility;
import com.nxc.evcsolutions.R;

import java.util.List;

public class CSDetailViewPagerAdapter extends PagerAdapter {

    private Context context;
    private LayoutInflater layoutInflater;

    List<String> csImages;
   // private Integer[] images = {R.drawable.slider1, R.drawable.slider2, R.drawable.slider3};

    public CSDetailViewPagerAdapter(Context context, List<String> csImage) {
        this.context = context;
        this.csImages = csImage;
    }

    @Override
    public int getCount() {
        return csImages.size();
    }

    @Override
    public boolean isViewFromObject(View view, Object object) {
        return view == object;
    }

    @Override
    public Object instantiateItem(ViewGroup container, final int position) {

        layoutInflater = (LayoutInflater) context.getSystemService(Context.LAYOUT_INFLATER_SERVICE);
        View view = layoutInflater.inflate(R.layout.item_cs_detail_slider, null);
        ImageView imageView = (ImageView) view.findViewById(R.id.ivSlide);
       // imageView.setImageResource(images[position]);

        Glide.with(context).load(csImages.get(position)).apply(new RequestOptions()
                .placeholder(R.drawable.nxc_logo)
                .signature(new ObjectKey(csImages.get(position)))) // here you add some value , if the next time you add the same value then it will load from cache otherwise if you put new value you will download , then save in cache
                .into(imageView);

        ViewPager vp = (ViewPager) container;
        vp.addView(view, 0);
        return view;

    }

    @Override
    public void destroyItem(ViewGroup container, int position, Object object) {

        ViewPager vp = (ViewPager) container;
        View view = (View) object;
        vp.removeView(view);

    }
}