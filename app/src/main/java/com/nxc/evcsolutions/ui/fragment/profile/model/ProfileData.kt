package com.nxc.evcsolutions.ui.fragment.profile.model


import com.google.gson.annotations.SerializedName

data class ProfileData(
    @SerializedName("area")
    val area: String,
    @SerializedName("birth_day")
    val birthDay: String = "",  // May not be present in response
    @SerializedName("city_id")
    val cityId: String,
    @SerializedName("city_name")
    val cityName: String,
    @SerializedName("country_id")
    val countryId: String,
    @SerializedName("country_name")
    val countryName: String,
    @SerializedName("e_mail")
    val eMail: String = "",  // May not be present in response
    @SerializedName("email_status")
    val emailStatus: String,
    @SerializedName("first_name")
    val firstName: String,
    @SerializedName("flat_house_no")
    val flatHouseNo: String,
    @SerializedName("flat_landmark")
    val flatLandmark: String,
    @SerializedName("gender")
    val gender: String,
    @SerializedName("last_name")
    val lastName: String,
    @SerializedName("phone")
    val phone: String,
    @SerializedName("sex")
    val sex: String,
    @SerializedName("state_id")
    val stateId: String,
    @SerializedName("state_name")
    val stateName: String,
    @SerializedName("user_id")
    val userId: String,
    @SerializedName("zipcode")
    val zipcode: String,
    @SerializedName("balance")
    val balance: String = "0"  // New field in response
)