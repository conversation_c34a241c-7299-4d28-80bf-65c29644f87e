package com.nxc.evcsolutions.ui.fragment.rfidCard

import android.content.Context
import android.text.TextUtils
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.nxc.evcsolutions.CommonUtility
import com.nxc.evcsolutions.R
import com.nxc.evcsolutions.databinding.ItemComplainBinding
import com.nxc.evcsolutions.databinding.ItemRfidBinding
import com.nxc.evcsolutions.databinding.ItemRfidCardBinding
import com.nxc.evcsolutions.interfaces.ItemClickListener
import com.nxc.evcsolutions.ui.fragment.complain.model.ComplainData
import com.nxc.evcsolutions.ui.fragment.rfidCard.model.RFIDChildCardListModel

class RFIDCardListAdapter(private val items: List<RFIDChildCardListModel>
                          ,private val mOnItemClickListener: ItemClickListener
) :
    RecyclerView.Adapter<RFIDCardListAdapter.ViewHolder>() {
    private lateinit var mContext: Context

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
        val inflater = LayoutInflater.from(parent.context)
        val binding = ItemRfidCardBinding.inflate(inflater, parent, false)
        mContext = parent.context
        return ViewHolder(binding)
    }

    override fun getItemCount(): Int = items.size

    override fun onBindViewHolder(holder: ViewHolder, position: Int) = holder.bind(items[position])

    inner class ViewHolder(val binding: ItemRfidCardBinding) : RecyclerView.ViewHolder(binding.root) {
        fun bind(item: RFIDChildCardListModel) {
           //
            if(TextUtils.isEmpty(item.name)){
                binding.tvCardName.text = mContext.getString(R.string.app_name)
            }else{
                binding.tvCardName.text =item.name
            }
            binding.tvCardNumber.text = item.idTag
            //status variable string declare
            var status = "Blocked"
            if(item.blocked == 1){
                status = "Active"
                binding.tvblockbtnstring.text = mContext.getString(R.string.block)
            } else if(item.blocked == 2) {
                status = "Blocked"
                binding.tvblockbtnstring.text = mContext.getString(R.string.unblock)
            } else if(item.blocked == 3) {
                status = "Lost/Damaged"
                //hide block button
                binding.llBlock.visibility = View.GONE
            } else {
                status = "Expired"
                binding.llBlock.visibility = View.GONE
            }
            binding.tvStatus.text = status;
            //binding.tvValid.text = CommonUtility.changeDateMyFormat()

//            binding.tvValid.text ="Valid thru  "+
//                CommonUtility.changeDateMyFormat(item.expiryDate, "yyyy-MM-dd HH:mm:ss", "dd MMM yyyy")

//            binding.llReplace.setOnClickListener {
//                mOnItemClickListener.onItemClick(1,item)
//            }
            binding.llBlock.setOnClickListener {
                mOnItemClickListener.onItemClick(2,item)
            }

        }
    }
}
