package com.nxc.evcsolutions.ui.activities.chargingDetail.model

import com.google.gson.annotations.SerializedName

data class ConsumptionCharge(
    @SerializedName("used_unit") var usedUnit: String? = null,
    @SerializedName("free_unit") var freeUnit: String? = null,
    @SerializedName("per_unit_price") var perUnitPrice: String? = null,
    @SerializedName("net_charges") var netCharges: String? = null,
    @SerializedName("tax_amount") var taxAmount: String? = null,
    @SerializedName("total") var total: String? = null
)