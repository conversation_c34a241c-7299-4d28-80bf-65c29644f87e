package com.nxc.evcsolutions.ui.fragment.rfidCard

import android.app.Activity
import android.app.Dialog
import android.content.Intent
import android.os.Bundle
import androidx.fragment.app.Fragment
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.activity.result.contract.ActivityResultContracts
import androidx.core.content.ContextCompat
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import com.nxc.evcsolutions.ConstantValue
import com.nxc.evcsolutions.R
import com.nxc.evcsolutions.databinding.FragmentRFIDBinding
import com.nxc.evcsolutions.model.ResponseModel
import com.nxc.evcsolutions.network.ApiClient
import com.nxc.evcsolutions.network.ApiInterface
import com.nxc.evcsolutions.network.RetrofitHelper
import com.nxc.evcsolutions.MainActivity
import com.nxc.evcsolutions.interfaces.ItemClickListener
import com.nxc.evcsolutions.ui.activities.AddOrderRFIDCardActivity
import com.nxc.evcsolutions.ui.activities.rfid.ReplaceRfidCardActivity
import com.nxc.evcsolutions.ui.activities.rfid.RfidTrackActivity
import com.nxc.evcsolutions.ui.activities.rfid.RfidTrackListActivity
import com.nxc.evcsolutions.ui.fragment.rfidCard.model.RFIDChildCardListModel
import com.nxc.evcsolutions.ui.fragment.rfidCard.model.RFIDChildCardModel
import com.nxc.evcsolutions.utilities.DialogHandler
import com.nxc.evcsolutions.utilities.MySharePreference
import org.json.JSONObject
import java.util.HashMap


class RFIDFragment : Fragment(), View.OnClickListener, ItemClickListener {
    private var jwtToken: String? = null
    private lateinit var binding: FragmentRFIDBinding

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        binding = FragmentRFIDBinding.inflate(layoutInflater)
        jwtToken =
            MySharePreference.getStringValue(requireContext(), ConstantValue.PREF_JWT_TOKEN)

        binding.toolbar.tvTitle.text = getString(R.string.rfid_card)


        binding.toolbar.tvTitle.visibility = View.VISIBLE
        binding.toolbar.llSearch.visibility = View.GONE
        binding.toolbar.ivNavigation.visibility = View.GONE
        binding.toolbar.backButton.visibility = View.VISIBLE
        binding.toolbar.backButton.setOnClickListener {
            (activity as MainActivity).navigationBack()
        }

        binding.tvNewCard.setOnClickListener(this)
        binding.tvTrackOrder.setOnClickListener(this)
        binding.cvTrackCard.setOnClickListener(this)
        binding.cvNewCard.setOnClickListener(this)

        getRFID()
        //viewRFIDOrder()
        // blockRFID()
        return binding.root
    }


    private fun getRFID() {
        val jwtToken =
            MySharePreference.getStringValue(requireContext(), ConstantValue.PREF_JWT_TOKEN)

        val apiService = ApiClient.client.create(ApiInterface::class.java)
        val call = apiService.getRFIDList(jwtToken!!)
        RetrofitHelper.requestRetrofit(
            requireContext(),
            call,
            object : RetrofitHelper.ApiResponseCallBackListener {
                override fun onSuccess(mResponseModel: ResponseModel) {
                    if (mResponseModel.status) {
                        val jsonObject = JSONObject(Gson().toJson(mResponseModel.responseData))

                        try {
                            val gson = Gson()
                            val itemType = object : TypeToken<RFIDChildCardModel>() {}.type
                            val mRFIDChildCardModel =
                                gson.fromJson<RFIDChildCardModel>(jsonObject.toString(), itemType)

                            val mRFIDCardListAdapter =
                                RFIDCardListAdapter(mRFIDChildCardModel.rfidData, this@RFIDFragment)
                            binding.rvRFIDTrack.adapter = mRFIDCardListAdapter

                            if (mRFIDChildCardModel.rfidData.isNotEmpty()) {
                                binding.llNewCard.visibility = View.GONE
                                binding.rvRFIDTrack.visibility = View.VISIBLE
                                binding.llBottom.visibility = View.VISIBLE
                            } else {
                                binding.llNewCard.visibility = View.VISIBLE
                                binding.rvRFIDTrack.visibility = View.GONE
                                binding.llBottom.visibility = View.GONE
                            }
                        } catch (e: Exception) {
                            binding.llNewCard.visibility = View.VISIBLE
                            binding.rvRFIDTrack.visibility = View.GONE
                            binding.llBottom.visibility = View.GONE
                        }

                    } else {
                        binding.llNewCard.visibility = View.VISIBLE
                        binding.rvRFIDTrack.visibility = View.GONE
                        binding.llBottom.visibility = View.GONE
                    }
                }

                override fun onError(mResponseModel: ResponseModel) {
                    binding.llNewCard.visibility = View.VISIBLE
                    binding.rvRFIDTrack.visibility = View.GONE
                    binding.llBottom.visibility = View.GONE
                }
            })
    }


    private fun blockRFID(item: RFIDChildCardListModel, mDialog: Dialog) {
        val apiService = ApiClient.client.create(ApiInterface::class.java)
        val postData: MutableMap<String, String> = HashMap()
        // postData["ocpp_tag_pk"] = "13"
        postData["ocpp_tag_pk"] = item.idTag
        var currentstatus = item.blocked
        currentstatus = if (currentstatus == 1) 2 else 1
        postData["blocked"] = currentstatus.toString()
        val call = apiService.blockRFID(postData, jwtToken!!)
        RetrofitHelper.requestRetrofit(
            requireContext(),
            call,
            object : RetrofitHelper.ApiResponseCallBackListener {
                override fun onSuccess(mResponseModel: ResponseModel) {
                    if (mResponseModel.status) {
                        mDialog.dismiss()
                        DialogHandler.alertDialog(
                            requireContext(),
                            getString(R.string.success),
                            mResponseModel.msg
                        )
                        //refresh list
                        getRFID()
                    } else {
                        DialogHandler.alertDialog(
                            requireContext(),
                            getString(R.string.error),
                            mResponseModel.msg
                        )
                    }
                }

                override fun onError(mResponseModel: ResponseModel) {
                    DialogHandler.alertDialog(
                        requireContext(),
                        getString(R.string.error),
                        mResponseModel.msg
                    )
                }
            })
    }

    private fun replaceRFID() {
        val apiService = ApiClient.client.create(ApiInterface::class.java)
        val postData: MutableMap<String, String> = HashMap()
        postData["replace_reason"] = "13"
        postData["replace_text"] = "13"
        postData["old_ocpp_tag"] = "13"

        val call = apiService.replaceRFID(postData, jwtToken!!)
        RetrofitHelper.requestRetrofit(
            requireContext(),
            call,
            object : RetrofitHelper.ApiResponseCallBackListener {
                override fun onSuccess(mResponseModel: ResponseModel) {
                    if (mResponseModel.status) {

                    } else {
                    }
                }

                override fun onError(mResponseModel: ResponseModel) {
                }
            })
    }



    fun toast(mResponseModel: ResponseModel) {
        DialogHandler.alertDialog(
            requireContext(),
            getString(R.string.error),
            mResponseModel.msg,
            object : DialogHandler.AlertDialogCallBackListener {
                override fun onOK(mDialog: Dialog) {
                    mDialog.dismiss()
                    //  (activity as MainActivity).navigationBack()

                }
            })
    }


    override fun onClick(v: View?) {
        //When click occurs requireContext() function is triggered
        when (v?.id) {
            //Check for the id of the view i which click event happened
            R.id.tvNewCard -> {
                resultLauncher.launch(
                    Intent(
                        requireContext(),
                        AddOrderRFIDCardActivity::class.java
                    )
                )
            }
            R.id.cvNewCard -> {
                resultLauncher.launch(
                    Intent(
                        requireContext(),
                        AddOrderRFIDCardActivity::class.java
                    )
                )
            }

            R.id.cvTrackCard -> {
                resultLauncher.launch(
                    Intent(
                        requireContext(),
                        RfidTrackListActivity::class.java
                    )
                )
            }
            R.id.tvTrackOrder -> {
                resultLauncher.launch(
                    Intent(
                        requireContext(),
                        RfidTrackListActivity::class.java
                    )
                )
            }
            /*  R.id.llReplaceCard -> {

              }
              R.id.llBlockCard -> {

                */
        }
    }

    private var resultLauncher =
        registerForActivityResult(ActivityResultContracts.StartActivityForResult()) { result ->
            if (result.resultCode == Activity.RESULT_OK) {
                // There are no request codes
                val data: Intent? = result.data
                if (data!!.getBooleanExtra("result", false)) {
                    getRFID()
                    //  getRFIDTrack()
                }
            } else {
                /* Toast.makeText(requireContext(), "back22 : " + result.resultCode, Toast.LENGTH_SHORT)
                     .show()*/
            }
        }

    override fun onItemClick(click: Int, data: Any) {
        val mRFIDChildCardListModel = data as RFIDChildCardListModel
        if (click == 1) {
            startActivity(
                Intent(
                    requireContext(),
                    ReplaceRfidCardActivity::class.java
                )
                    .putExtra("idTag",mRFIDChildCardListModel.idTag)
            )

        } else if (click == 2) {
            blockCard(mRFIDChildCardListModel)
        }

    }

    private fun blockCard(item: RFIDChildCardListModel) {
        val isBlocked = item.blocked == 1
        val title = if (isBlocked) getString(R.string.title_block_card) else "UNBLOCK CARD"
        val message = if (isBlocked) getString(R.string.msg_block_card) else "Are you sure you want to unblock this card?"
        val buttonTextResId = if (isBlocked) R.string.block else R.string.unblock

        DialogHandler.openCustomDialog(requireContext(),
            title,
            message,
            R.drawable.ic_credit_card,
            ContextCompat.getColor(requireContext(), R.color.bg_red_transparent),
            getString(buttonTextResId),
            R.drawable.btn_shape_dialog,
            ContextCompat.getColor(requireContext(), R.color.white),
            getString(R.string.no),
            R.drawable.btn_shape_dialog,
            ContextCompat.getColor(requireContext(), R.color.white),
            false,
            "",
            object : DialogHandler.DialogCallBackListener {
                override fun onDismiss(mDialog: Dialog) {
                    mDialog.dismiss()
                }

                override fun onNo(data: Any, mDialog: Dialog) {
                    mDialog.dismiss()
                }

                override fun onYes(data: Any, mDialog: Dialog) {
                    blockRFID(item, mDialog)
                }
            })
    }
}