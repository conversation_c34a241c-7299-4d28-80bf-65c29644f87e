package com.nxc.evcsolutions.ui.activities.paymentModel

import android.content.Context
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.bumptech.glide.Glide
import com.nxc.evcsolutions.databinding.ItemPaymentMethodBinding

class PaymentMethodAdapter(
    private val items: List<PaymentMethod>,
    private val listener: OnPaymentMethodClickListener
) : RecyclerView.Adapter<PaymentMethodAdapter.ViewHolder>() {

    private lateinit var context: Context
    private var selectedPosition = -1

    interface OnPaymentMethodClickListener {
        fun onPaymentMethodClick(paymentMethod: PaymentMethod, position: Int)
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
        val inflater = LayoutInflater.from(parent.context)
        val binding = ItemPaymentMethodBinding.inflate(inflater, parent, false)
        context = parent.context
        return ViewHolder(binding)
    }

    override fun getItemCount(): Int = items.size

    override fun onBindViewHolder(holder: ViewHolder, position: Int) {
        holder.bind(items[position], position)
    }

    fun setSelectedPosition(position: Int) {
        val previousPosition = selectedPosition
        selectedPosition = position
        
        if (previousPosition != -1) {
            notifyItemChanged(previousPosition)
        }
        notifyItemChanged(selectedPosition)
    }

    inner class ViewHolder(private val binding: ItemPaymentMethodBinding) : 
        RecyclerView.ViewHolder(binding.root) {
        
        fun bind(item: PaymentMethod, position: Int) {
            binding.tvPaymentName.text = item.name
            
            // Load payment method icon using Glide
            Glide.with(context)
                .load(item.icon)
                .into(binding.ivPaymentIcon)
            
            // Show selection indicator if this item is selected
            binding.ivSelected.visibility = if (position == selectedPosition) View.VISIBLE else View.GONE
            
            // Set click listener
            binding.cvPaymentMethod.setOnClickListener {
                setSelectedPosition(position)
                listener.onPaymentMethodClick(item, position)
            }
        }
    }
}
