package com.nxc.evcsolutions.ui.activities.paymentDetail

import android.content.Intent
import androidx.appcompat.app.AppCompatActivity
import android.os.Bundle
import android.util.Log
import android.view.View
import androidx.core.content.ContextCompat
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import com.nxc.evcsolutions.CommonUtility
import com.nxc.evcsolutions.ConstantValue
import com.nxc.evcsolutions.R
import com.nxc.evcsolutions.databinding.ActivityPaymentDetailsBinding
import com.nxc.evcsolutions.model.ResponseModel
import com.nxc.evcsolutions.network.ApiClient
import com.nxc.evcsolutions.network.ApiInterface
import com.nxc.evcsolutions.network.RetrofitHelper
import com.nxc.evcsolutions.ui.activities.AddComplainActivity
import com.nxc.evcsolutions.ui.commonModel.walletModel.WalletChildData
import com.nxc.evcsolutions.utilities.MySharePreference
import org.json.JSONObject
import java.util.HashMap

class PaymentDetailsActivity : AppCompatActivity(), View.OnClickListener {
    private var transactionId: String? = null
    private var jwtToken: String? = null
    private val classTag = "UserVehicleActivity"
    private var mMobile: String? = null
    private lateinit var binding: ActivityPaymentDetailsBinding
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        //  setContentView(R.layout.activity_payment_details)
        binding = ActivityPaymentDetailsBinding.inflate(layoutInflater)
        setContentView(binding.root)
        jwtToken = MySharePreference.getStringValue(this, ConstantValue.PREF_JWT_TOKEN)
        Log.d(classTag, "jwtToken :$jwtToken")
        binding.toolbar.tvTitle.text = getString(R.string.payment_detail)

        if (intent.extras != null) {
            transactionId = intent.getStringExtra("transactionId")
        }
        binding.toolbar.backButton.setOnClickListener {
            onBackPressed()
        }
        binding.btnComplain.setOnClickListener(this)
        paymentDetails()
    }

    override fun onClick(v: View?) { //When click occurs this function is triggered
        when (v?.id) { //Check for the id of the view i which click event happened
            R.id.btnComplain -> {
                val intent = Intent(this, AddComplainActivity::class.java)
                startActivity(intent)
                // overridePendingTransition(android.R.anim.slide_out_right, android.R.anim.slide_in_left);
            }
        }
    }

    private fun paymentDetails() {
        val apiService = ApiClient.client.create(ApiInterface::class.java)
        val postData: MutableMap<String, String> = HashMap()
        postData["transaction_id"] = transactionId!!
        val call = apiService.paymentDetails(postData, jwtToken!!)
        RetrofitHelper.requestRetrofit(
            this,
            call,
            object : RetrofitHelper.ApiResponseCallBackListener {
                override fun onSuccess(mResponseModel: ResponseModel) {
                    if (mResponseModel.status) {
                        val jsonObject = JSONObject(Gson().toJson(mResponseModel.responseData))
                        val gson = Gson()
                        val itemType = object : TypeToken<PaymentDetailModel>() {}.type
                        val mPaymentDetailModel =
                            gson.fromJson<PaymentDetailModel>(jsonObject.toString(), itemType)
                        binding.tvAmount.text =
                            getString(R.string.Rs) + " " + mPaymentDetailModel.balanceAmount
                        binding.tvID.text = mPaymentDetailModel.transactionId
                        binding.tvMode.text = mPaymentDetailModel.paymentType
                       // binding.tvPaymentDateTime.text = mPaymentDetailModel.startTime
                        binding.tvPaymentStatus.text = mPaymentDetailModel.status

                        binding.tvPaymentDateTime.text =
                            CommonUtility.changeDateMyFormatUTC(mPaymentDetailModel.startTime, "yyyy-MM-dd HH:mm:ss", "dd MMM yyyy hh:mm a")




                        if (mPaymentDetailModel.status == "success") {
                            binding.cvStatus.setCardBackgroundColor(
                                ContextCompat.getColor(
                                    this@PaymentDetailsActivity,
                                    R.color.light_green_50
                                )
                            )
                            binding.tvPaymentStatus.setTextColor(
                                ContextCompat.getColor(
                                    this@PaymentDetailsActivity,
                                    R.color.green
                                )
                            )
                        } else {
                            binding.cvStatus.setCardBackgroundColor(
                                ContextCompat.getColor(
                                    this@PaymentDetailsActivity,
                                    R.color.bg_red_transparent
                                )
                            )
                            binding.tvPaymentStatus.setTextColor(
                                ContextCompat.getColor(
                                    this@PaymentDetailsActivity,
                                    R.color.red
                                )
                            )
                        }
                    } else {
                        finish()
                    }
                }
                override fun onError(mResponseModel: ResponseModel) {
                    finish()
                }
            })
    }
}