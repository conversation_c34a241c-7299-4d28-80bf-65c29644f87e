package com.nxc.evcsolutions.network;

import static com.nxc.evcsolutions.ConstantValue.*;

import android.os.Handler;
import android.os.Looper;
import android.util.Log;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.lifecycle.LifecycleOwner;
import androidx.lifecycle.LiveData;
import androidx.lifecycle.Observer;

import com.nxc.evcsolutions.BuildConfig;
import com.nxc.evcsolutions.MyApp;
import com.nxc.evcsolutions.utilities.MySharePreference;

import org.json.JSONException;
import org.json.JSONObject;

import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicInteger;

import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;
import okhttp3.WebSocket;
import okhttp3.WebSocketListener;


public class SocketLiveData extends LiveData<Object> {
    private static final SocketLiveData instance = new SocketLiveData();
    private static WebSocket webSocket;
    private String TAG = "SocketLiveData";
    private static AtomicBoolean disconnected = new AtomicBoolean(true);
    private static AtomicBoolean isReconnecting = new AtomicBoolean(false);
    private static AtomicInteger reconnectAttempts = new AtomicInteger(0);
    private static final int MAX_RECONNECT_ATTEMPTS = 3;
    private static final int RECONNECT_DELAY_MS = 3000; // 3 seconds
    private static final int CONNECTION_TIMEOUT_MS = 10000; // 10 seconds
    private static final int HEARTBEAT_INTERVAL_MS = 15000; // 15 seconds
    private Handler reconnectHandler = new Handler(Looper.getMainLooper());
    private Handler timeoutHandler = new Handler(Looper.getMainLooper());
    private Handler heartbeatHandler = new Handler(Looper.getMainLooper());
    private Runnable timeoutRunnable;
    private Runnable heartbeatRunnable;

    private SocketLiveData() {
        timeoutRunnable = () -> {
            if (disconnected.get()) {
                Log.d(TAG, "Connection timeout");
                try {
                    String timeoutMessage = sanitizeErrorMessage("Connection timeout");

                    JSONObject errorMessage = new JSONObject();
                    errorMessage.put("event", EVENT_ERROR);
                    errorMessage.put("payload", timeoutMessage);
                    postValue(errorMessage.toString());

                    // Also send connection status update
                    JSONObject connectionMessage = new JSONObject();
                    connectionMessage.put("event", "connection_status");
                    connectionMessage.put("status", "failed");
                    connectionMessage.put("reason", timeoutMessage);
                    postValue(connectionMessage.toString());
                } catch (JSONException e) {
                    e.printStackTrace();
                }
                // Try to reconnect
                scheduleReconnect();
            }
        };

        // Initialize heartbeat runnable to periodically check connection
        heartbeatRunnable = () -> {
            if (!disconnected.get() && webSocket != null) {
                try {
                    // Send a ping to check if connection is still alive
                    boolean sent = webSocket.send("");
                    if (sent) {
                        // Connection is still active, schedule next heartbeat
                        heartbeatHandler.postDelayed(heartbeatRunnable, HEARTBEAT_INTERVAL_MS);
                    } else {
                        // Failed to send ping, connection might be broken
                        Log.d(TAG, "Heartbeat failed to send ping");
                        if (!disconnected.get()) {
                            // Only update status if we think we're still connected
                            disconnected.set(true);
                            notifyConnectionLost("Connection lost - heartbeat failed");
                            scheduleReconnect();
                        }
                    }
                } catch (Exception e) {
                    Log.e(TAG, "Heartbeat error: " + e.getMessage());
                    if (!disconnected.get()) {
                        disconnected.set(true);
                        notifyConnectionLost("Connection lost - heartbeat exception");
                        scheduleReconnect();
                    }
                }
            }
        };
    }

    public static SocketLiveData get() {
        return instance;
    }

    @Override
    protected synchronized void onActive() {
        super.onActive();
        connect();
    }

    @Override
    public void observe(@NonNull LifecycleOwner owner, @NonNull Observer<? super Object> observer) {
        super.observe(owner, observer);
        // DebugUtils.debug(SocketLiveData.class, "Observing");
        Log.d(TAG, "observe : " + "Observing..");
        connect();
    }

    public synchronized void connect() {
        // Reset reconnect attempts if this is a manual connection
        if (!isReconnecting.get()) {
            reconnectAttempts.set(0);
        }

        String JAVA_HOST = BuildConfig.WEBSOCKET;
        try {
            Log.d(TAG, "connect : " + "Attempting to connect.." + JAVA_HOST);
            if (disconnected.compareAndSet(true, false)) {
                Log.d(TAG, "connect : " + "Connecting.." + JAVA_HOST);

                // Set connection timeout
                timeoutHandler.removeCallbacks(timeoutRunnable);
                timeoutHandler.postDelayed(timeoutRunnable, CONNECTION_TIMEOUT_MS);

                // Configure OkHttpClient with timeout
                OkHttpClient okHttpClient = new OkHttpClient.Builder()
                        .connectTimeout(CONNECTION_TIMEOUT_MS, TimeUnit.MILLISECONDS)
                        .readTimeout(CONNECTION_TIMEOUT_MS, TimeUnit.MILLISECONDS)
                        .writeTimeout(CONNECTION_TIMEOUT_MS, TimeUnit.MILLISECONDS)
                        .build();

                Request request = new Request.Builder().url(JAVA_HOST).build();
                webSocket = okHttpClient.newWebSocket(request, webSocketListener);
            }
        } catch (Exception ex) {
            Log.d(TAG, "Exception : " + ex.getMessage());
            ex.printStackTrace();
            disconnected.set(true);

            try {
                JSONObject errorMessage = new JSONObject();
                errorMessage.put("event", EVENT_ERROR);
                errorMessage.put("payload", "Connection error: " + ex.getMessage());
                postValue(errorMessage.toString());
            } catch (JSONException e) {
                e.printStackTrace();
            }

            // Try to reconnect
            scheduleReconnect();
        }
    }

    public void sendEvent(String eventModel) {
        if (webSocket == null) {
            Log.d(TAG, "Cannot send event - WebSocket is null");
            try {
                String errorMsg = sanitizeErrorMessage("Cannot send message - not connected to server");

                JSONObject errorMessage = new JSONObject();
                errorMessage.put("event", EVENT_ERROR);
                errorMessage.put("payload", errorMsg);
                postValue(errorMessage.toString());
            } catch (JSONException e) {
                e.printStackTrace();
            }

            // Try to reconnect
            if (disconnected.get()) {
                connect();
            }
            return;
        }

        try {
            boolean sent = webSocket.send(eventModel);
            if (!sent) {
                Log.d(TAG, "Failed to send message");
                try {
                    String errorMsg = sanitizeErrorMessage("Failed to send message");

                    JSONObject errorMessage = new JSONObject();
                    errorMessage.put("event", EVENT_ERROR);
                    errorMessage.put("payload", errorMsg);
                    postValue(errorMessage.toString());
                } catch (JSONException e) {
                    e.printStackTrace();
                }

                // Message failed to send, try to reconnect
                if (disconnected.get()) {
                    connect();
                }
            }
        } catch (Exception e) {
            Log.e(TAG, "Error sending message: " + e.getMessage());
            e.printStackTrace();

            try {
                String errorMsg = sanitizeErrorMessage("Error sending message: " + e.getMessage());

                JSONObject errorMessage = new JSONObject();
                errorMessage.put("event", EVENT_ERROR);
                errorMessage.put("payload", errorMsg);
                postValue(errorMessage.toString());
            } catch (JSONException ex) {
                ex.printStackTrace();
            }

            // Try to reconnect
            if (disconnected.get()) {
                connect();
            }
        }
    }

    private final WebSocketListener webSocketListener = new WebSocketListener() {

        @Override
        public void onOpen(WebSocket webSocket, Response response) {
            super.onOpen(webSocket, response);
            disconnected.set(false);
            isReconnecting.set(false);
            reconnectAttempts.set(0);

            // Cancel the timeout since we're connected
            timeoutHandler.removeCallbacks(timeoutRunnable);

            // Start heartbeat to monitor connection
            heartbeatHandler.removeCallbacks(heartbeatRunnable);
            heartbeatHandler.postDelayed(heartbeatRunnable, HEARTBEAT_INTERVAL_MS);

            String mobile = MyApp.Companion.getGetMobile();

            Log.d(TAG, "ChargingFragment onOpen : " + "onOpen"+ mobile);
            Log.d(TAG, "ChargingFragment onOpen : " + "onOpen MyApp"+ MyApp.Companion.getGetMobile());
            Log.d(TAG, "ChargingFragment QRServer Opened Connection : " + mobile);
            if (!mobile.isEmpty()) {
                try {
                    JSONObject openMessage = new JSONObject();
                    openMessage.put(KEY_EVENT, EVENT_HANDSHAKE);
                    openMessage.put(KEY_USER_MOBILE, mobile);
                    openMessage.put(KEY_PAYLOAD, "");
                    sendEvent(openMessage.toString());
                    Log.d(TAG, "QRServer Opened Connection" + openMessage.toString());

                } catch (JSONException e) {
                    e.printStackTrace();
                }
            }

            // Notify UI that connection is established
            try {
                JSONObject connectionMessage = new JSONObject();
                connectionMessage.put("event", "connection_status");
                connectionMessage.put("status", "connected");
                postValue(connectionMessage.toString());
            } catch (JSONException e) {
                e.printStackTrace();
            }
        }

        @Override
        public void onMessage(WebSocket webSocket, String text) {
            Log.d(TAG, "onMessage RAW: " + text);

            // Check if this is a handshake message
            if (text.contains("handshake")) {
                Log.d(TAG, "HANDSHAKE MESSAGE DETECTED: " + text);
            }

            // If we're receiving messages, we're definitely connected
            // Make sure our connection status reflects this
            if (disconnected.get()) {
                Log.d(TAG, "Received message while disconnected flag was true, correcting status");
                disconnected.set(false);

                // Notify UI that connection is established
                try {
                    JSONObject connectionMessage = new JSONObject();
                    connectionMessage.put("event", "connection_status");
                    connectionMessage.put("status", "connected");
                    postValue(connectionMessage.toString());
                } catch (JSONException e) {
                    e.printStackTrace();
                }

                // Reset heartbeat
                heartbeatHandler.removeCallbacks(heartbeatRunnable);
                heartbeatHandler.postDelayed(heartbeatRunnable, HEARTBEAT_INTERVAL_MS);
            }

            Log.d(TAG, "Posting message to observers: " + text);
            postValue(text);
        }

        @Override
        public void onClosed(WebSocket webSocket, int code, String reason) {
            super.onClosed(webSocket, code, reason);
            Log.d(TAG, "onClosed : " + reason);
            disconnected.set(true);

            // Sanitize the reason message
            String sanitizedReason = sanitizeErrorMessage(reason);

            // Notify UI that connection is closed
            try {
                JSONObject connectionMessage = new JSONObject();
                connectionMessage.put("event", "connection_status");
                connectionMessage.put("status", "disconnected");
                connectionMessage.put("reason", sanitizedReason);
                postValue(connectionMessage.toString());
            } catch (JSONException e) {
                e.printStackTrace();
            }

            // Try to reconnect if this wasn't a normal closure
            if (code != 1000) {
                scheduleReconnect();
            }
        }

        @Override
        public void onFailure(WebSocket webSocket, Throwable t, @Nullable Response response) {
            super.onFailure(webSocket, t, response);
            Log.d(TAG, "onFailure : " + (t != null ? t.getMessage() : "unknown error"));
            disconnected.set(true);

            // Cancel the timeout since we've already failed
            timeoutHandler.removeCallbacks(timeoutRunnable);

            int code = response != null ? response.code() : t != null ? 400 : 0;
            @Nullable String rawMessage = response != null ? response.message() : t != null ? t.getMessage() : "Connection failed";

            // Log the raw message for debugging (only visible in logs, not to users)
            Log.d(TAG, "Raw error message: " + rawMessage);

            // Sanitize the error message to avoid showing URIs
            String sanitizedMessage = sanitizeErrorMessage(rawMessage);

            Log.d(TAG, "Sanitized message: " + sanitizedMessage);
            Log.d(TAG, "onFailure" + String.format("On Failure. Code: %s, message: %s", code, sanitizedMessage));

            try {
                JSONObject errorMessage = new JSONObject();
                errorMessage.put("event", EVENT_ERROR);
                errorMessage.put("payload", sanitizedMessage);
                errorMessage.put("code", code);
                postValue(errorMessage.toString());

                // Also send connection status update
                JSONObject connectionMessage = new JSONObject();
                connectionMessage.put("event", "connection_status");
                connectionMessage.put("status", "failed");
                connectionMessage.put("reason", sanitizedMessage);
                postValue(connectionMessage.toString());
            } catch (JSONException e) {
                e.printStackTrace();
            }

            // Try to reconnect
            scheduleReconnect();
        }
    };

    @Override
    protected void onInactive() {
        super.onInactive();
        disconnect();
        Log.d(TAG, "Inactive. Has observers observers?" + hasActiveObservers());
    }

    public boolean isDisconnected() {
        return disconnected.get();
    }

    private void disconnect() {
        if (!hasActiveObservers()) {
            if (webSocket != null) {
                webSocket.close(1000, "Done using");
            }
            // Cancel any pending reconnects, timeouts, or heartbeats
            reconnectHandler.removeCallbacksAndMessages(null);
            timeoutHandler.removeCallbacksAndMessages(null);
            heartbeatHandler.removeCallbacksAndMessages(null);
        }
    }

    /**
     * Schedule a reconnection attempt with exponential backoff
     */
    private void scheduleReconnect() {
        if (isReconnecting.compareAndSet(false, true)) {
            int attempts = reconnectAttempts.incrementAndGet();
            if (attempts <= MAX_RECONNECT_ATTEMPTS) {
                // Calculate delay with exponential backoff (3s, 6s, 9s)
                int delay = RECONNECT_DELAY_MS * attempts;
                Log.d(TAG, "Scheduling reconnect attempt " + attempts + " in " + delay + "ms");

                try {
                    JSONObject reconnectMessage = new JSONObject();
                    reconnectMessage.put("event", "connection_status");
                    reconnectMessage.put("status", "reconnecting");
                    reconnectMessage.put("attempt", attempts);
                    reconnectMessage.put("max_attempts", MAX_RECONNECT_ATTEMPTS);
                    postValue(reconnectMessage.toString());
                } catch (JSONException e) {
                    e.printStackTrace();
                }

                reconnectHandler.postDelayed(() -> {
                    if (disconnected.get()) {
                        connect();
                    } else {
                        isReconnecting.set(false);
                    }
                }, delay);
            } else {
                Log.d(TAG, "Max reconnect attempts reached");
                isReconnecting.set(false);

                try {
                    JSONObject maxAttemptsMessage = new JSONObject();
                    maxAttemptsMessage.put("event", "connection_status");
                    maxAttemptsMessage.put("status", "max_retries_reached");
                    postValue(maxAttemptsMessage.toString());

                    // Also send as an error event
                    JSONObject errorMessage = new JSONObject();
                    errorMessage.put("event", EVENT_ERROR);
                    errorMessage.put("payload", "Could not connect after " + MAX_RECONNECT_ATTEMPTS + " attempts");
                    postValue(errorMessage.toString());
                } catch (JSONException e) {
                    e.printStackTrace();
                }
            }
        }
    }

    /**
     * Manually retry connection after max retries have been reached
     */
    public void retryConnection() {
        reconnectAttempts.set(0);
        isReconnecting.set(false);
        connect();
    }

    /**
     * Helper method to notify UI of connection loss
     */
    private void notifyConnectionLost(String reason) {
        try {
            String sanitizedReason = sanitizeErrorMessage(reason);

            JSONObject connectionMessage = new JSONObject();
            connectionMessage.put("event", "connection_status");
            connectionMessage.put("status", "disconnected");
            connectionMessage.put("reason", sanitizedReason);
            postValue(connectionMessage.toString());

            Log.d(TAG, "Notified UI of connection loss: " + sanitizedReason);
        } catch (JSONException e) {
            e.printStackTrace();
        }
    }

    /**
     * Sanitize WebSocket error messages to avoid showing URIs or sensitive information
     * @param errorMessage The raw error message
     * @return A sanitized error message suitable for display to users
     */
    private String sanitizeErrorMessage(String errorMessage) {
        if (errorMessage == null) {
            return "Connection failed";
        }

        // Specific pattern matching for "failed to connect to [domain]:[port] after [time]ms"
        if (errorMessage.matches(".*[Ff]ailed to connect to .* after \\d+ms.*") ||
            errorMessage.matches(".*[Cc]onnect timed? out.*")) {
            return "Could not connect to charging server. Please check your internet connection.";
        }

        // Check for common WebSocket error patterns and replace with user-friendly messages
        if (errorMessage.contains("Failed to connect") ||
            errorMessage.contains("Connection refused") ||
            errorMessage.contains("connect timed out") ||
            errorMessage.contains("timeout") ||
            errorMessage.contains("timed out")) {
            return "Could not connect to charging server. Please check your internet connection.";
        }

        if (errorMessage.contains("closed") ||
            errorMessage.contains("Socket closed") ||
            errorMessage.contains("Connection reset") ||
            errorMessage.contains("reset")) {
            return "Connection to charging server was closed unexpectedly.";
        }

        if (errorMessage.contains("SSL") ||
            errorMessage.contains("certificate") ||
            errorMessage.contains("handshake") ||
            errorMessage.contains("trust")) {
            return "Secure connection failed. Please try again later.";
        }

        // Remove any domain:port patterns (common in connection errors)
        String sanitized = errorMessage.replaceAll("[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}(:\\d+)?", "[server]");

        // Remove any URLs or URIs from the error message
        // This regex pattern matches common URI formats
        sanitized = sanitized.replaceAll("(https?|wss?|ftp)://[^\\s/$.?#].[^\\s]*", "[server address]");

        // Remove IP addresses
        sanitized = sanitized.replaceAll("\\b(?:[0-9]{1,3}\\.){3}[0-9]{1,3}(:\\d+)?\\b", "[server address]");

        // If the message still contains technical details or is too long, provide a generic message
        if (sanitized.length() > 80 ||
            sanitized.contains("Exception") ||
            sanitized.contains("Error") ||
            sanitized.contains("failed") ||
            sanitized.contains("Failed") ||
            sanitized.contains("connect") ||
            sanitized.contains("Connection")) {
            return "Connection error. Please try again later.";
        }

        return sanitized;
    }
}
