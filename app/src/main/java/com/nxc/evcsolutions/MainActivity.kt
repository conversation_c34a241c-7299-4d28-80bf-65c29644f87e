package com.nxc.evcsolutions

import android.annotation.SuppressLint
import android.app.Dialog
import android.content.Intent
import android.os.Bundle
import android.view.Gravity
import android.view.View
import android.widget.LinearLayout
import androidx.activity.OnBackPressedCallback
import androidx.appcompat.app.AppCompatActivity
import androidx.core.view.GravityCompat
import androidx.drawerlayout.widget.DrawerLayout
import androidx.navigation.NavController
import androidx.navigation.findNavController
import androidx.navigation.ui.AppBarConfiguration
import androidx.navigation.ui.NavigationUI
import androidx.navigation.ui.setupWithNavController
import com.nxc.evcsolutions.databinding.ActivityMainBinding
import com.nxc.evcsolutions.utilities.MySharePreference

import android.widget.TextView
import androidx.core.content.ContextCompat
import com.google.gson.Gson
import com.nxc.evcsolutions.model.ResponseModel
import com.nxc.evcsolutions.network.ApiClient
import com.nxc.evcsolutions.network.ApiInterface
import com.nxc.evcsolutions.network.RetrofitHelper
import com.nxc.evcsolutions.utilities.DialogHandler
import org.json.JSONObject


/**
 * SF: https://stackoverflow.com/questions/55990820/how-to-use-navigation-drawer-and-bottom-navigation-simultaneously-navigation-a/
 *
 * Back button press from any fragment takes you to home fragment (Start fragment)
 * of nav graph
 */


class MainActivity : AppCompatActivity()/*, NavigationView.OnNavigationItemSelectedListener*/ {

    private lateinit var appBarConfiguration: AppBarConfiguration
    private lateinit var navController: NavController
    lateinit var binding: ActivityMainBinding



    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityMainBinding.inflate(layoutInflater)

        // Handle back button press
        onBackPressedDispatcher.addCallback(this, object : OnBackPressedCallback(true) {
            override fun handleOnBackPressed() {
                navigationBack()
            }
        })
        setContentView(binding.root)
        navController = findNavController(R.id.main_nav_host) //Initialising navController
        // binding.mainNavigationView.setNavigationItemSelectedListener(this);
        appBarConfiguration = AppBarConfiguration.Builder(
            R.id.chargingFragment,
            R.id.mapFragment,
            R.id.walletFragment,
            R.id.profileFragment,
            R.id.changeLanguage,
            R.id.aboutUsFragment,
            R.id.gamesFragment,
            R.id.buyChargerFragment,
            R.id.newsFragment,
            R.id.helpFragment,
            R.id.rfidFragment,
            R.id.complainFragment
            // R.id.logout
            // Navigation left
        ) //Pass the ids of fragments from nav_graph which you d'ont want to show back button in toolbar
            .setOpenableLayout(binding.mainDrawerLayout) //Pass the drawer layout id from activity xml
            .build()

        // Check if we need to navigate to a specific fragment
        handleNavigationIntent(intent)

        /* setSupportActionBar(binding.mainToolbar) //Set toolbar
        setupActionBarWithNavController(
            navController,
            appBarConfiguration
        ) */
        //Setup toolbar with back button and drawer icon according to appBarConfiguration

        visibilityNavElements(navController) //If you want to hide drawer or bottom navigation configure that in this function
        walletBalance()
    }


    fun walletBalance() {
        val jwtToken = MySharePreference.getStringValue(this, ConstantValue.PREF_JWT_TOKEN)
        val apiService = ApiClient.client.create(ApiInterface::class.java)
        val call = apiService.walletBalance(jwtToken!!)
        RetrofitHelper.requestRetrofitWithoutProgressbar(
            this,
            call,
            object : RetrofitHelper.ApiResponseCallBackListener {
                @SuppressLint("SetTextI18n")
                override fun onSuccess(mResponseModel: ResponseModel) {
                    if (mResponseModel.status) {
                        val jsonObject = JSONObject(Gson().toJson(mResponseModel.responseData))

                        val header: View = binding.mainNavigationView.getHeaderView(0)
                        val firstChar = header.findViewById<TextView>(R.id.tvFirstChar)
                        val fullName = header.findViewById<TextView>(R.id.tvFullName)
                        val tvBalance = header.findViewById<TextView>(R.id.tvBalance)
                        val llLogout = header.findViewById<LinearLayout>(R.id.llLogout)

                        llLogout.setOnClickListener {
                            logout(getString(R.string.logout),getString(R.string.logout_msg))
                        }
                        // {"status":true,"code":200,"msg":"Wallet Balance Found.","data":{"balance":"100.00","rfid_amount":"250","user_name":"Robinson Macwan"}}

                        try {
                            val strFullName = jsonObject.getString("user_name")
                            firstChar.text =
                                CommonUtility.getFirstCapitalizeWords(strFullName)
                            fullName.text = strFullName
                        } catch (e: Exception) {
                        }

                        try {
                            val rfidAmount = jsonObject.getString("rfid_amount")
                            MySharePreference.save(this@MainActivity, "rfidAmount", rfidAmount)
                        } catch (e: Exception) {
                        }
                        try {
                            val bal = jsonObject.getString(
                                "balance"
                            )
                            tvBalance.text =
                                getString(R.string.available_balance_) + " " + bal
                            MySharePreference.save(this@MainActivity, "walletBalance", bal)
                            // Also update the drawer wallet balance
                            updateDrawerWalletBalance()
                        } catch (e: Exception) {
                        }
                    }
                }

                override fun onError(mResponseModel: ResponseModel) {

                }
            })
    }

    private fun visibilityNavElements(navController: NavController) {
        //Listen for the change in fragment (navigation) and hide or show drawer or bottom navigation accordingly if required
        //Modify this according to your need
        //If you want you can implement logic to deselect(styling) the bottom navigation menu item when drawer item selected using listener

        navController.addOnDestinationChangedListener { _, destination, _ ->
            when (destination.id) {
                /*R.id.homeFragment -> {
                   // binding.llToolbar.visibility = View.VISIBLE
                }*/

                // Navigation
                R.id.profileFragment -> {
                    hideBothNavigation()
                }
                R.id.changeLanguage -> {
                    hideBothNavigation()
                }
                R.id.complainFragment -> {
                    hideBothNavigation()
                }
                R.id.aboutUsFragment -> {
                    hideBothNavigation()
                }
                R.id.helpFragment -> {
                    hideBothNavigation()
                }
                R.id.gamesFragment -> {
                    hideBothNavigation()
                }
                R.id.newsFragment -> {
                    hideBothNavigation()
                }
                R.id.rfidFragment -> {
                    hideBothNavigation()
                }

                //Bottom
                R.id.walletFragment -> {
                    showBothNavigation()
                }
                R.id.chargingFragment -> {
                    showBothNavigation()
                }

                else -> {
                    //binding.llToolbar.visibility = View.GONE
                    showBothNavigation()
                }
            }
        }

    }


    private fun hideBothNavigation() { //Hide both drawer and bottom navigation bar
        binding.mainBottomNavigationView.visibility = View.GONE
        binding.mainNavigationView.visibility = View.GONE
        binding.mainDrawerLayout.setDrawerLockMode(DrawerLayout.LOCK_MODE_LOCKED_CLOSED) //To lock navigation drawer so that it don't respond to swipe gesture
    }

    private fun hideBottomNavigation() { //Hide bottom navigation
        binding.mainBottomNavigationView.visibility = View.GONE
        binding.mainNavigationView.visibility = View.VISIBLE
        binding.mainDrawerLayout.setDrawerLockMode(DrawerLayout.LOCK_MODE_UNLOCKED) //To unlock navigation drawer
        binding.mainNavigationView.setupWithNavController(navController) //Setup Drawer navigation with navController
    }

    private fun showBothNavigation() {
        binding.mainBottomNavigationView.visibility = View.VISIBLE
        binding.mainNavigationView.visibility = View.VISIBLE
        binding.mainDrawerLayout.setDrawerLockMode(DrawerLayout.LOCK_MODE_UNLOCKED)
        setupNavControl() //To configure navController with drawer and bottom navigation
    }

    private fun setupNavControl() {
        binding.mainNavigationView.setupWithNavController(navController) //Setup Drawer navigation with navController
        binding.mainBottomNavigationView.setupWithNavController(navController) //Setup Bottom navigation with navController

        // binding.mainNavigationView.setNavigationItemSelectedListener(this);
    }


    fun exitApp() { //To exit the application call this function from fragment
        this.finishAffinity()
    }

    override fun onSupportNavigateUp(): Boolean { //Setup appBarConfiguration for back arrow
        return NavigationUI.navigateUp(navController, appBarConfiguration)
    }
    @Deprecated("Deprecated in Java")
    // Removed deprecated onBackPressed() - now using OnBackPressedCallback

    fun navigationMenu() {
        binding.mainDrawerLayout.openDrawer(Gravity.LEFT)
    }

    override fun onResume() {
        super.onResume()
        // Always update the drawer wallet balance from SharedPreferences
        updateDrawerWalletBalance()

        // Check if wallet balance needs to be updated from the server
        if (MySharePreference.getBooleanValue(this, "UPDATE_WALLET_BALANCE")) {
            // Update wallet balance from the server
            walletBalance()
            // Reset the flag
            MySharePreference.save(this, "UPDATE_WALLET_BALANCE", false)
        }
    }

    override fun onNewIntent(intent: Intent) {
        super.onNewIntent(intent)
        // Handle navigation requests when activity is already running
        handleNavigationIntent(intent)
    }

    private fun handleNavigationIntent(intent: Intent) {
        // Check if we need to navigate to a specific fragment
        if (intent.hasExtra("navigate_to")) {
            when (intent.getStringExtra("navigate_to")) {
                "wallet" -> {
                    // Navigate to wallet fragment
                    navController.navigate(R.id.walletFragment)
                }
                // Add more navigation targets as needed
            }
        }
    }

    /**
     * Update the wallet balance in the drawer navigation directly from SharedPreferences
     * This method can be called from any activity to update the drawer navigation balance
     */
    fun updateDrawerWalletBalance() {
        val walletBalance = MySharePreference.getStringValue(this, "walletBalance") ?: "0"
        val header: View = binding.mainNavigationView.getHeaderView(0)
        val tvBalance = header.findViewById<TextView>(R.id.tvBalance)
        tvBalance.text = getString(R.string.available_balance_) + " " + walletBalance
    }



    fun navigationBack() {
        when { //If drawer layout is open close that on back pressed
            binding.mainDrawerLayout.isDrawerOpen(GravityCompat.START) -> {
                binding.mainDrawerLayout.closeDrawer(GravityCompat.START)
            }
            else -> {
                // Check if we're on the home fragment
                val currentDestination = navController.currentDestination?.id
                if (currentDestination == R.id.chargingFragment) {
                    // If we're on the home fragment, exit the app
                    finishAffinity()
                } else {
                    // Otherwise, navigate back
                    navController.navigateUp()
                }
            }
        }
    }
    /* override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
         super.onActivityResult(requestCode, resultCode, data)
         val fragment: Fragment? = supportFragmentManager.findFragmentById(R.id.dualPane)
         fragment?.onActivityResult(requestCode, resultCode, data)
     }*/



    private fun logout(title: String, msg: String) {
        DialogHandler.openCustomDialog(this,
            title,
            msg,
            R.drawable.ic_logout,
            ContextCompat.getColor(this, R.color.bg_blue_transparent),

            getString(R.string.logout),
            R.drawable.btn_shape_dialog,
            ContextCompat.getColor(this, R.color.white),

            getString(R.string.no),
            R.drawable.btn_shape_dialog,
            ContextCompat.getColor(this, R.color.white),
            true,
            "",
            object : DialogHandler.DialogCallBackListener {
                override fun onDismiss(mDialog: Dialog) {
                    mDialog.dismiss()
                }

                override fun onNo(data: Any, mDialog: Dialog) {
                    mDialog.dismiss()
                }

                override fun onYes(data: Any, mDialog: Dialog) {
                    mDialog.dismiss()
                    binding.mainDrawerLayout.close()
                    MySharePreference.removeSharePref(
                        this@MainActivity
                    )
                    val intent = Intent(this@MainActivity, LoginActivity::class.java)
                    startActivity(intent)
                    finish()
                }
            })
    }
}
