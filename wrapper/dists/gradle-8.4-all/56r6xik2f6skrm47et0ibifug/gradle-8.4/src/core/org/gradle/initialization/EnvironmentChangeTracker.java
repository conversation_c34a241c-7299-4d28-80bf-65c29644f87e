/*
 * Copyright 2023 the original author or authors.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package org.gradle.initialization;

import org.gradle.internal.service.scopes.Scopes;
import org.gradle.internal.service.scopes.ServiceScope;

import javax.annotation.Nullable;

@ServiceScope(Scopes.BuildTree.class)
public interface EnvironmentChangeTracker {

    /**
     * System properties mutated in runtime
     * */
    void systemPropertyChanged(Object key, @Nullable Object value, @Nullable String consumer);

    /**
     * System properties loaded from included build/buildSrc gradle.properties
     * */
    void systemPropertyLoaded(Object key, @Nullable Object value, @Nullable Object oldValue);

    /**
     * System properties overridden by passing CLI argument
     * */
    void systemPropertyOverridden(Object key);
}
