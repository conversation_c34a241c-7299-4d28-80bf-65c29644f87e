/*
 * Copyright 2016 the original author or authors.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package org.gradle.initialization;

import java.util.Set;

/**
 * Enriches class loading with empty interfaces for certain types that have been removed,
 * but which are baked into the bytecode generated by the Groovy compiler.
 */
public interface LegacyTypesSupport {

    /**
     * Returns a set of classes that require {@link groovy.lang.GroovyObject} to be mixed in.
     */
    Set<String> getClassesToMixInGroovyObject();

    /**
     * Returns a set of types that have been removed, but which are baked into the bytecode
     * generated by the Groovy compiler
     */
    Set<String> getSyntheticClasses();

    /**
     * Generates an empty interface for the given class name.
     */
    byte[] generateSyntheticClass(String name);

    /**
     * Injects all the interfaces identified by {@link LegacyTypesSupport#getSyntheticClasses()}
     * into the given classloader.
     */
    void injectEmptyInterfacesIntoClassLoader(ClassLoader classLoader);
}
