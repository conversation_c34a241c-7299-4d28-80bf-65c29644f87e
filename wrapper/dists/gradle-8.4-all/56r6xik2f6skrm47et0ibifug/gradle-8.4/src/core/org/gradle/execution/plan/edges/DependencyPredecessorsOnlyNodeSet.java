/*
 * Copyright 2022 the original author or authors.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package org.gradle.execution.plan.edges;

import org.gradle.execution.plan.Node;
import org.gradle.execution.plan.NodeSets;

import java.util.SortedSet;
import java.util.function.Consumer;

/**
 * Only incoming dependencies, no incoming must-run-after or finalizes relationships.
 */
class DependencyPredecessorsOnlyNodeSet implements DependentNodesSet {
    private final SortedSet<Node> dependencyPredecessors = NodeSets.newSortedNodeSet();

    @Override
    public SortedSet<Node> getDependencyPredecessors() {
        return dependencyPredecessors;
    }

    @Override
    public DependentNodesSet addDependencyPredecessors(Node fromNode) {
        dependencyPredecessors.add(fromNode);
        return this;
    }

    @Override
    public DependentNodesSet addFinalizer(Node finalizer) {
        return new ComplexDependentNodesSet(this).addFinalizer(finalizer);
    }

    @Override
    public DependentNodesSet addMustPredecessor(Node fromNode) {
        return new ComplexDependentNodesSet(this).addMustPredecessor(fromNode);
    }

    @Override
    public void visitAllNodes(Consumer<Node> visitor) {
        for (Node node : dependencyPredecessors) {
            visitor.accept(node);
        }
    }
}
