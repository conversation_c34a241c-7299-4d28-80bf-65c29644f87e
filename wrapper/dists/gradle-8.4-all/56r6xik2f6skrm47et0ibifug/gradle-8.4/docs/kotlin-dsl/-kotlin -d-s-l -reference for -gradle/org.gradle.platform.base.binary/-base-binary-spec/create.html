<!DOCTYPE html>
<html>
<head>
    <meta name="viewport" content="width=device-width, initial-scale=1" charset="UTF-8">
    <title>create</title>
    <link href="../../../images/logo-icon.svg" rel="icon" type="image/svg">
    <script>var pathToRoot = "../../../";</script>
    <script>const storage = localStorage.getItem("dokka-dark-mode")
    if (storage == null) {
        const osDarkSchemePreferred = window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches
        if (osDarkSchemePreferred === true) {
            document.getElementsByTagName("html")[0].classList.add("theme-dark")
        }
    } else {
        const savedDarkMode = JSON.parse(storage)
        if(savedDarkMode === true) {
            document.getElementsByTagName("html")[0].classList.add("theme-dark")
        }
    }
    </script>
<script type="text/javascript" src="../../../scripts/sourceset_dependencies.js" async="async"></script>
<link href="../../../styles/style.css" rel="Stylesheet">
<link href="../../../styles/jetbrains-mono.css" rel="Stylesheet">
<link href="../../../styles/main.css" rel="Stylesheet">
<link href="../../../styles/prism.css" rel="Stylesheet">
<link href="../../../styles/logo-styles.css" rel="Stylesheet">
<script type="text/javascript" src="../../../scripts/clipboard.js" async="async"></script>
<script type="text/javascript" src="../../../scripts/navigation-loader.js" async="async"></script>
<script type="text/javascript" src="../../../scripts/platform-content-handler.js" async="async"></script>
<script type="text/javascript" src="../../../scripts/main.js" defer="defer"></script>
<script type="text/javascript" src="../../../scripts/prism.js" async="async"></script>
<script type="text/javascript" src="../../../scripts/symbol-parameters-wrapper_deferred.js" defer="defer"></script>
<link href="../../../images/gradle-logo.svg">
<link href="../../../styles/gradle.css" rel="Stylesheet">
</head>
<body>
<div class="navigation-wrapper" id="navigation-wrapper">
    <div id="leftToggler"><span class="icon-toggler"></span></div>
    <div class="library-name">
            <a href="../../../index.html">
                    <span>Kotlin DSL Reference for Gradle</span>
            </a>
    </div>
    <div>
8.4    </div>
    <div class="pull-right d-flex">
        <div class="filter-section" id="filter-section">
                <button class="platform-tag platform-selector jvm-like" data-active="" data-filter=":docs/java_api">API</button>
        </div>
        <button id="theme-toggle-button"><span id="theme-toggle"></span></button>
        <div id="searchBar"></div>
    </div>
</div>
<div id="container">
    <div id="leftColumn">
        <div id="sideMenu"></div>
    </div>
    <div id="main">
<div class="main-content" data-page-type="member" id="content" pageIds="Kotlin DSL Reference for Gradle::org.gradle.platform.base.binary/BaseBinarySpec/create/#java.lang.Class&lt;? extends org.gradle.platform.base.BinarySpec&gt;#java.lang.Class&lt;T&gt;#org.gradle.platform.base.internal.ComponentSpecIdentifier#org.gradle.model.internal.core.MutableModelNode#org.gradle.model.internal.core.MutableModelNode#org.gradle.internal.reflect.Instantiator#org.gradle.model.internal.core.NamedEntityInstantiator&lt;org.gradle.api.Task&gt;#org.gradle.api.internal.CollectionCallbackActionDecorator#org.gradle.api.internal.collections.DomainObjectCollectionFactory/PointingToDeclaration//-1793262594">
  <div class="breadcrumbs"><a href="../../../index.html">Kotlin DSL Reference for Gradle</a><span class="delimiter">/</span><a href="../index.html">org.gradle.platform.base.binary</a><span class="delimiter">/</span><a href="index.html">BaseBinarySpec</a><span class="delimiter">/</span><span class="current">create</span></div>
  <div class="cover ">
    <h1 class="cover"><span><span>create</span></span></h1>
  </div>
  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><div class="symbol monospace"><span class="token keyword">open </span><span class="token keyword"></span><span class="token keyword">fun </span><span class="token operator">&lt;</span><span class="token keyword"></span><a href="create.html">T</a><span class="token operator"> : </span><a href="index.html">BaseBinarySpec</a><span class="token operator">?</span><span class="token operator">&gt; </span><a href="create.html"><span class="token function">create</span></a><span class="token punctuation">(</span><span class="parameters "><span class="parameter ">publicType<span class="token operator">: </span><a href="https://docs.oracle.com/en/java/javase/11/docs/api/java.base/java/lang/Class.html">Class</a><span class="token operator">&lt;</span><span class="token keyword">out </span><a href="../../org.gradle.platform.base/-binary-spec/index.html">BinarySpec</a><span class="token operator">&gt;</span><span class="token punctuation">, </span></span><span class="parameter ">implementationType<span class="token operator">: </span><a href="https://docs.oracle.com/en/java/javase/11/docs/api/java.base/java/lang/Class.html">Class</a><span class="token operator">&lt;</span><a href="create.html">T</a><span class="token operator">&gt;</span><span class="token punctuation">, </span></span><span class="parameter ">componentId<span class="token operator">: </span><span data-unresolved-link="org.gradle.platform.base.internal/ComponentSpecIdentifier///PointingToDeclaration/">ComponentSpecIdentifier</span><span class="token punctuation">, </span></span><span class="parameter ">modelNode<span class="token operator">: </span><span data-unresolved-link="org.gradle.model.internal.core/MutableModelNode///PointingToDeclaration/">MutableModelNode</span><span class="token punctuation">, </span></span><span class="parameter "><span><span class="token annotation builtin">@</span><span data-unresolved-link="javax.annotation/Nullable///PointingToDeclaration/"><span class="token annotation builtin">Nullable</span></span> </span>componentNode<span class="token operator">: </span><span data-unresolved-link="org.gradle.model.internal.core/MutableModelNode///PointingToDeclaration/">MutableModelNode</span><span class="token punctuation">, </span></span><span class="parameter ">instantiator<span class="token operator">: </span><span data-unresolved-link="org.gradle.internal.reflect/Instantiator///PointingToDeclaration/">Instantiator</span><span class="token punctuation">, </span></span><span class="parameter ">taskInstantiator<span class="token operator">: </span><span data-unresolved-link="org.gradle.model.internal.core/NamedEntityInstantiator///PointingToDeclaration/">NamedEntityInstantiator</span><span class="token operator">&lt;</span><a href="../../org.gradle.api/-task/index.html">Task</a><span class="token operator">&gt;</span><span class="token punctuation">, </span></span><span class="parameter ">collectionCallbackActionDecorator<span class="token operator">: </span><span data-unresolved-link="org.gradle.api.internal/CollectionCallbackActionDecorator///PointingToDeclaration/">CollectionCallbackActionDecorator</span><span class="token punctuation">, </span></span><span class="parameter ">domainObjectCollectionFactory<span class="token operator">: </span><span data-unresolved-link="org.gradle.api.internal.collections/DomainObjectCollectionFactory///PointingToDeclaration/">DomainObjectCollectionFactory</span></span></span><span class="token punctuation">)</span><span class="token operator">: </span><a href="create.html">T</a><span class="clearfix"><span class="floating-right">(<a href="https://github.com/gradle/gradle/blob/e9251e572c9bd1d01e503a0dfdf43aedaeecdc3f/subprojects/platform-base/src/main/java/org/gradle/platform/base/binary/BaseBinarySpec.java#L79">source</a>)</span></span></div><p class="paragraph">Creates a <a href="index.html">BaseBinarySpec</a>.</p></div>  </div>
</div>
      <div class="footer">
        <span class="go-to-top-icon"><a href="#content" id="go-to-top-link"></a></span><span>Gradle Kotlin DSL Reference</span><span
                class="pull-right"><span>Generated by </span><a
                href="https://github.com/Kotlin/dokka"><span>dokka</span><span class="padded-icon"></span></a></span>
      </div>
    </div>
</div>
</body>
</html>
