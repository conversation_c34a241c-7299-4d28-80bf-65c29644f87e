<!DOCTYPE html>
<html>
<head>
    <meta name="viewport" content="width=device-width, initial-scale=1" charset="UTF-8">
    <title>org.gradle.api.plugins.quality</title>
    <link href="../../images/logo-icon.svg" rel="icon" type="image/svg">
    <script>var pathToRoot = "../../";</script>
    <script>const storage = localStorage.getItem("dokka-dark-mode")
    if (storage == null) {
        const osDarkSchemePreferred = window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches
        if (osDarkSchemePreferred === true) {
            document.getElementsByTagName("html")[0].classList.add("theme-dark")
        }
    } else {
        const savedDarkMode = JSON.parse(storage)
        if(savedDarkMode === true) {
            document.getElementsByTagName("html")[0].classList.add("theme-dark")
        }
    }
    </script>
<script type="text/javascript" src="../../scripts/sourceset_dependencies.js" async="async"></script>
<link href="../../styles/style.css" rel="Stylesheet">
<link href="../../styles/jetbrains-mono.css" rel="Stylesheet">
<link href="../../styles/main.css" rel="Stylesheet">
<link href="../../styles/prism.css" rel="Stylesheet">
<link href="../../styles/logo-styles.css" rel="Stylesheet">
<script type="text/javascript" src="../../scripts/clipboard.js" async="async"></script>
<script type="text/javascript" src="../../scripts/navigation-loader.js" async="async"></script>
<script type="text/javascript" src="../../scripts/platform-content-handler.js" async="async"></script>
<script type="text/javascript" src="../../scripts/main.js" defer="defer"></script>
<script type="text/javascript" src="../../scripts/prism.js" async="async"></script>
<script type="text/javascript" src="../../scripts/symbol-parameters-wrapper_deferred.js" defer="defer"></script>
<link href="../../images/gradle-logo.svg">
<link href="../../styles/gradle.css" rel="Stylesheet">
</head>
<body>
<div class="navigation-wrapper" id="navigation-wrapper">
    <div id="leftToggler"><span class="icon-toggler"></span></div>
    <div class="library-name">
            <a href="../../index.html">
                    <span>Kotlin DSL Reference for Gradle</span>
            </a>
    </div>
    <div>
8.4    </div>
    <div class="pull-right d-flex">
        <div class="filter-section" id="filter-section">
                <button class="platform-tag platform-selector jvm-like" data-active="" data-filter=":docs/java_api">API</button>
        </div>
        <button id="theme-toggle-button"><span id="theme-toggle"></span></button>
        <div id="searchBar"></div>
    </div>
</div>
<div id="container">
    <div id="leftColumn">
        <div id="sideMenu"></div>
    </div>
    <div id="main">
<div class="main-content" data-page-type="package" id="content" pageIds="Kotlin DSL Reference for Gradle::org.gradle.api.plugins.quality////PointingToDeclaration//-1793262594">
  <div class="breadcrumbs"><a href="../../index.html">Kotlin DSL Reference for Gradle</a><span class="delimiter">/</span><span class="current">org.gradle.api.plugins.quality</span></div>
  <div class="cover ">
    <h1 class="cover"><span><span>Package-level</span></span> <span><span>declarations</span></span></h1>
    <div class="platform-hinted UnderCoverText with-platform-tabs" data-platform-hinted="data-platform-hinted">
      <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><p class="paragraph">Plugins which measure and enforce code quality.</p></div>    </div>
  </div>
  <div class="tabbedcontent">
    <div class="tabs-section" tabs-section="tabs-section"><button class="section-tab" data-active="" data-togglable="TYPE">Types</button></div>
    <div class="tabs-section-body">
      <div data-togglable="TYPE">
        <h2 class="">Types</h2>
        <div class="table"><a data-name="318688683%2FClasslikes%2F-1793262594" anchor-label="AbstractCodeQualityTask" id="318688683%2FClasslikes%2F-1793262594" data-filterable-set=":docs/java_api"></a>
          <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="-abstract-code-quality-task/index.html"><span>Abstract</span><wbr></wbr><span>Code</span><wbr></wbr><span>Quality</span><wbr></wbr><span><span>Task</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="318688683%2FClasslikes%2F-1793262594"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><div class="symbol monospace"><div class="block"><div class="block"><span class="token annotation builtin">@</span><a href="../org.gradle.api/-incubating/index.html"><span class="token annotation builtin">Incubating</span></a></div><div class="block"><span class="token annotation builtin">@</span><a href="../org.gradle.work/-disable-caching-by-default/index.html"><span class="token annotation builtin">DisableCachingByDefault</span></a><span class="token punctuation">(</span><span>because<span class="token operator"> = </span><span class="breakable-word"><span class="token string">&quot;Super-class, not to be instantiated directly&quot;</span></span></span><wbr></wbr><span class="token punctuation">)</span></div></div><span class="token keyword">abstract </span><span class="token keyword">class </span><a href="-abstract-code-quality-task/index.html">AbstractCodeQualityTask</a> : <a href="../org.gradle.api.tasks/-source-task/index.html">SourceTask</a>, <a href="../org.gradle.api.tasks/-verification-task/index.html">VerificationTask</a></div><div class="brief ">Base class for code quality tasks.</div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="-215178247%2FClasslikes%2F-1793262594" anchor-label="Checkstyle" id="-215178247%2FClasslikes%2F-1793262594" data-filterable-set=":docs/java_api"></a>
          <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="-checkstyle/index.html"><span><span>Checkstyle</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-215178247%2FClasslikes%2F-1793262594"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><div class="symbol monospace"><div class="block"><div class="block"><span class="token annotation builtin">@</span><a href="../org.gradle.api.tasks/-cacheable-task/index.html"><span class="token annotation builtin">CacheableTask</span></a></div></div><span class="token keyword">abstract </span><span class="token keyword">class </span><a href="-checkstyle/index.html">Checkstyle</a> : <a href="-abstract-code-quality-task/index.html">AbstractCodeQualityTask</a>, <a href="../org.gradle.api.reporting/-reporting/index.html">Reporting</a><span class="token operator">&lt;</span><a href="../org.gradle.api.reporting/-reporting/index.html">T</a><span class="token operator">&gt; </span></div><div class="brief ">Runs Checkstyle against some source files.</div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="1544314410%2FClasslikes%2F-1793262594" anchor-label="CheckstyleExtension" id="1544314410%2FClasslikes%2F-1793262594" data-filterable-set=":docs/java_api"></a>
          <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="-checkstyle-extension/index.html"><span>Checkstyle</span><wbr></wbr><span><span>Extension</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="1544314410%2FClasslikes%2F-1793262594"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><div class="symbol monospace"><span class="token keyword">abstract </span><span class="token keyword">class </span><a href="-checkstyle-extension/index.html">CheckstyleExtension</a> : <a href="-code-quality-extension/index.html">CodeQualityExtension</a></div><div class="brief ">Configuration options for the Checkstyle plugin.</div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="-231528954%2FClasslikes%2F-1793262594" anchor-label="CheckstylePlugin" id="-231528954%2FClasslikes%2F-1793262594" data-filterable-set=":docs/java_api"></a>
          <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="-checkstyle-plugin/index.html"><span>Checkstyle</span><wbr></wbr><span><span>Plugin</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-231528954%2FClasslikes%2F-1793262594"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><div class="symbol monospace"><span class="token keyword">abstract </span><span class="token keyword">class </span><a href="-checkstyle-plugin/index.html">CheckstylePlugin</a> : <span data-unresolved-link="org.gradle.api.plugins.quality.internal/AbstractCodeQualityPlugin///PointingToDeclaration/">AbstractCodeQualityPlugin</span><span class="token operator">&lt;</span><span data-unresolved-link="org.gradle.api.plugins.quality.internal/AbstractCodeQualityPlugin///PointingToGenericParameters(0)/">T</span><span class="token operator">&gt; </span></div><div class="brief ">Checkstyle Plugin.</div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="-474067286%2FClasslikes%2F-1793262594" anchor-label="CheckstyleReports" id="-474067286%2FClasslikes%2F-1793262594" data-filterable-set=":docs/java_api"></a>
          <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="-checkstyle-reports/index.html"><span>Checkstyle</span><wbr></wbr><span><span>Reports</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-474067286%2FClasslikes%2F-1793262594"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><div class="symbol monospace"><span class="token keyword">interface </span><a href="-checkstyle-reports/index.html">CheckstyleReports</a> : <a href="../org.gradle.api.reporting/-report-container/index.html">ReportContainer</a><span class="token operator">&lt;</span><a href="../org.gradle.api.reporting/-report-container/index.html">T</a><span class="token operator">&gt; </span></div><div class="brief ">The reporting configuration for the <a href="-checkstyle/index.html">Checkstyle</a> task.</div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="-433374159%2FClasslikes%2F-1793262594" anchor-label="CodeNarc" id="-433374159%2FClasslikes%2F-1793262594" data-filterable-set=":docs/java_api"></a>
          <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="-code-narc/index.html"><span>Code</span><wbr></wbr><span><span>Narc</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-433374159%2FClasslikes%2F-1793262594"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><div class="symbol monospace"><div class="block"><div class="block"><span class="token annotation builtin">@</span><a href="../org.gradle.api.tasks/-cacheable-task/index.html"><span class="token annotation builtin">CacheableTask</span></a></div></div><span class="token keyword">abstract </span><span class="token keyword">class </span><a href="-code-narc/index.html">CodeNarc</a> : <a href="-abstract-code-quality-task/index.html">AbstractCodeQualityTask</a>, <a href="../org.gradle.api.reporting/-reporting/index.html">Reporting</a><span class="token operator">&lt;</span><a href="../org.gradle.api.reporting/-reporting/index.html">T</a><span class="token operator">&gt; </span></div><div class="brief ">Runs CodeNarc against some source files.</div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="-234257166%2FClasslikes%2F-1793262594" anchor-label="CodeNarcExtension" id="-234257166%2FClasslikes%2F-1793262594" data-filterable-set=":docs/java_api"></a>
          <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="-code-narc-extension/index.html"><span>Code</span><wbr></wbr><span>Narc</span><wbr></wbr><span><span>Extension</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-234257166%2FClasslikes%2F-1793262594"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><div class="symbol monospace"><span class="token keyword">abstract </span><span class="token keyword">class </span><a href="-code-narc-extension/index.html">CodeNarcExtension</a> : <a href="-code-quality-extension/index.html">CodeQualityExtension</a></div><div class="brief ">Configuration options for the CodeNarc plugin.</div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="1950135358%2FClasslikes%2F-1793262594" anchor-label="CodeNarcPlugin" id="1950135358%2FClasslikes%2F-1793262594" data-filterable-set=":docs/java_api"></a>
          <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="-code-narc-plugin/index.html"><span>Code</span><wbr></wbr><span>Narc</span><wbr></wbr><span><span>Plugin</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="1950135358%2FClasslikes%2F-1793262594"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><div class="symbol monospace"><span class="token keyword">abstract </span><span class="token keyword">class </span><a href="-code-narc-plugin/index.html">CodeNarcPlugin</a> : <span data-unresolved-link="org.gradle.api.plugins.quality.internal/AbstractCodeQualityPlugin///PointingToDeclaration/">AbstractCodeQualityPlugin</span><span class="token operator">&lt;</span><span data-unresolved-link="org.gradle.api.plugins.quality.internal/AbstractCodeQualityPlugin///PointingToGenericParameters(0)/">T</span><span class="token operator">&gt; </span></div><div class="brief ">CodeNarc Plugin.</div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="-1561950350%2FClasslikes%2F-1793262594" anchor-label="CodeNarcReports" id="-1561950350%2FClasslikes%2F-1793262594" data-filterable-set=":docs/java_api"></a>
          <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="-code-narc-reports/index.html"><span>Code</span><wbr></wbr><span>Narc</span><wbr></wbr><span><span>Reports</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-1561950350%2FClasslikes%2F-1793262594"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><div class="symbol monospace"><span class="token keyword">interface </span><a href="-code-narc-reports/index.html">CodeNarcReports</a> : <a href="../org.gradle.api.reporting/-report-container/index.html">ReportContainer</a><span class="token operator">&lt;</span><a href="../org.gradle.api.reporting/-report-container/index.html">T</a><span class="token operator">&gt; </span></div><div class="brief ">The reporting configuration for the <a href="-code-narc/index.html">CodeNarc</a> test.</div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="-826546475%2FClasslikes%2F-1793262594" anchor-label="CodeQualityExtension" id="-826546475%2FClasslikes%2F-1793262594" data-filterable-set=":docs/java_api"></a>
          <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="-code-quality-extension/index.html"><span>Code</span><wbr></wbr><span>Quality</span><wbr></wbr><span><span>Extension</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-826546475%2FClasslikes%2F-1793262594"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><div class="symbol monospace"><span class="token keyword">abstract </span><span class="token keyword">class </span><a href="-code-quality-extension/index.html">CodeQualityExtension</a></div><div class="brief ">Base Code Quality Extension.</div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="-1692014631%2FClasslikes%2F-1793262594" anchor-label="Pmd" id="-1692014631%2FClasslikes%2F-1793262594" data-filterable-set=":docs/java_api"></a>
          <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="-pmd/index.html"><span><span>Pmd</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-1692014631%2FClasslikes%2F-1793262594"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><div class="symbol monospace"><div class="block"><div class="block"><span class="token annotation builtin">@</span><a href="../org.gradle.api.tasks/-cacheable-task/index.html"><span class="token annotation builtin">CacheableTask</span></a></div></div><span class="token keyword">abstract </span><span class="token keyword">class </span><a href="-pmd/index.html">Pmd</a> : <a href="-abstract-code-quality-task/index.html">AbstractCodeQualityTask</a>, <a href="../org.gradle.api.reporting/-reporting/index.html">Reporting</a><span class="token operator">&lt;</span><a href="../org.gradle.api.reporting/-reporting/index.html">T</a><span class="token operator">&gt; </span></div><div class="brief ">Runs a set of static code analysis rules on Java source code files and generates a report of problems found.</div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="-1794017718%2FClasslikes%2F-1793262594" anchor-label="PmdExtension" id="-1794017718%2FClasslikes%2F-1793262594" data-filterable-set=":docs/java_api"></a>
          <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="-pmd-extension/index.html"><span>Pmd</span><wbr></wbr><span><span>Extension</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-1794017718%2FClasslikes%2F-1793262594"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><div class="symbol monospace"><span class="token keyword">abstract </span><span class="token keyword">class </span><a href="-pmd-extension/index.html">PmdExtension</a> : <a href="-code-quality-extension/index.html">CodeQualityExtension</a></div><div class="brief ">Configuration options for the PMD plugin.</div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="-2040397338%2FClasslikes%2F-1793262594" anchor-label="PmdPlugin" id="-2040397338%2FClasslikes%2F-1793262594" data-filterable-set=":docs/java_api"></a>
          <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="-pmd-plugin/index.html"><span>Pmd</span><wbr></wbr><span><span>Plugin</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-2040397338%2FClasslikes%2F-1793262594"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><div class="symbol monospace"><span class="token keyword">abstract </span><span class="token keyword">class </span><a href="-pmd-plugin/index.html">PmdPlugin</a> : <span data-unresolved-link="org.gradle.api.plugins.quality.internal/AbstractCodeQualityPlugin///PointingToDeclaration/">AbstractCodeQualityPlugin</span><span class="token operator">&lt;</span><span data-unresolved-link="org.gradle.api.plugins.quality.internal/AbstractCodeQualityPlugin///PointingToGenericParameters(0)/">T</span><span class="token operator">&gt; </span></div><div class="brief ">A plugin for the <a href="https://pmd.github.io/">PMD</a> source code analyzer.</div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="-714412342%2FClasslikes%2F-1793262594" anchor-label="PmdReports" id="-714412342%2FClasslikes%2F-1793262594" data-filterable-set=":docs/java_api"></a>
          <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="-pmd-reports/index.html"><span>Pmd</span><wbr></wbr><span><span>Reports</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-714412342%2FClasslikes%2F-1793262594"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><div class="symbol monospace"><span class="token keyword">interface </span><a href="-pmd-reports/index.html">PmdReports</a> : <a href="../org.gradle.api.reporting/-report-container/index.html">ReportContainer</a><span class="token operator">&lt;</span><a href="../org.gradle.api.reporting/-report-container/index.html">T</a><span class="token operator">&gt; </span></div><div class="brief ">The reporting configuration for the <a href="-pmd/index.html">Pmd</a> task.</div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="1316619680%2FClasslikes%2F-1793262594" anchor-label="TargetJdk" id="1316619680%2FClasslikes%2F-1793262594" data-filterable-set=":docs/java_api"></a>
          <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="-target-jdk/index.html"><span>Target</span><wbr></wbr><span><span>Jdk</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="1316619680%2FClasslikes%2F-1793262594"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><div class="symbol monospace"><span class="token keyword">enum </span><a href="-target-jdk/index.html">TargetJdk</a></div><div class="brief ">Represents the PMD targetjdk property available for PMD <5.</div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
      <div class="footer">
        <span class="go-to-top-icon"><a href="#content" id="go-to-top-link"></a></span><span>Gradle Kotlin DSL Reference</span><span
                class="pull-right"><span>Generated by </span><a
                href="https://github.com/Kotlin/dokka"><span>dokka</span><span class="padded-icon"></span></a></span>
      </div>
    </div>
</div>
</body>
</html>
