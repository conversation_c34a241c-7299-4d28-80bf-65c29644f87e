<!DOCTYPE html>
<html>
<head>
    <meta name="viewport" content="width=device-width, initial-scale=1" charset="UTF-8">
    <title>VersionConstraint</title>
    <link href="../../../images/logo-icon.svg" rel="icon" type="image/svg">
    <script>var pathToRoot = "../../../";</script>
    <script>const storage = localStorage.getItem("dokka-dark-mode")
    if (storage == null) {
        const osDarkSchemePreferred = window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches
        if (osDarkSchemePreferred === true) {
            document.getElementsByTagName("html")[0].classList.add("theme-dark")
        }
    } else {
        const savedDarkMode = JSON.parse(storage)
        if(savedDarkMode === true) {
            document.getElementsByTagName("html")[0].classList.add("theme-dark")
        }
    }
    </script>
<script type="text/javascript" src="../../../scripts/sourceset_dependencies.js" async="async"></script>
<link href="../../../styles/style.css" rel="Stylesheet">
<link href="../../../styles/jetbrains-mono.css" rel="Stylesheet">
<link href="../../../styles/main.css" rel="Stylesheet">
<link href="../../../styles/prism.css" rel="Stylesheet">
<link href="../../../styles/logo-styles.css" rel="Stylesheet">
<script type="text/javascript" src="../../../scripts/clipboard.js" async="async"></script>
<script type="text/javascript" src="../../../scripts/navigation-loader.js" async="async"></script>
<script type="text/javascript" src="../../../scripts/platform-content-handler.js" async="async"></script>
<script type="text/javascript" src="../../../scripts/main.js" defer="defer"></script>
<script type="text/javascript" src="../../../scripts/prism.js" async="async"></script>
<script type="text/javascript" src="../../../scripts/symbol-parameters-wrapper_deferred.js" defer="defer"></script>
<link href="../../../images/gradle-logo.svg">
<link href="../../../styles/gradle.css" rel="Stylesheet">
</head>
<body>
<div class="navigation-wrapper" id="navigation-wrapper">
    <div id="leftToggler"><span class="icon-toggler"></span></div>
    <div class="library-name">
            <a href="../../../index.html">
                    <span>Kotlin DSL Reference for Gradle</span>
            </a>
    </div>
    <div>
8.4    </div>
    <div class="pull-right d-flex">
        <div class="filter-section" id="filter-section">
                <button class="platform-tag platform-selector jvm-like" data-active="" data-filter=":docs/java_api">API</button>
        </div>
        <button id="theme-toggle-button"><span id="theme-toggle"></span></button>
        <div id="searchBar"></div>
    </div>
</div>
<div id="container">
    <div id="leftColumn">
        <div id="sideMenu"></div>
    </div>
    <div id="main">
<div class="main-content" data-page-type="classlike" id="content" pageIds="Kotlin DSL Reference for Gradle::org.gradle.api.artifacts/VersionConstraint///PointingToDeclaration//-1793262594">
  <div class="breadcrumbs"><a href="../../../index.html">Kotlin DSL Reference for Gradle</a><span class="delimiter">/</span><a href="../index.html">org.gradle.api.artifacts</a><span class="delimiter">/</span><span class="current">VersionConstraint</span></div>
  <div class="cover ">
    <h1 class="cover"><span>Version</span><wbr></wbr><span><span>Constraint</span></span></h1>
    <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
      <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><div class="symbol monospace"><span class="token keyword">interface </span><a href="index.html">VersionConstraint</a> : <a href="../../org.gradle.api/-describable/index.html">Describable</a><span class="clearfix"><span class="floating-right">(<a href="https://github.com/gradle/gradle/blob/e9251e572c9bd1d01e503a0dfdf43aedaeecdc3f/subprojects/core-api/src/main/java/org/gradle/api/artifacts/VersionConstraint.java#L95">source</a>)</span></span></div><p class="paragraph">Represents a constraint that is used to match module versions to a dependency. Each of <a href="get-preferred-version.html">getPreferredVersion</a>, <a href="get-required-version.html">getRequiredVersion</a> and <a href="get-strict-version.html">getStrictVersion</a> is represented by a version String, that can be compared against a module version to determine if the version matches. </p>Version syntax<p class="paragraph"> Gradle supports different ways of declaring a version String: </p><ul><li>An exact version: e.g. 1.3, 1.3.0-beta3, 1.0-20150201.131010-1</li><li>A Maven-style version range: e.g. [1.0,), [1.1, 2.0), (1.2, 1.5] <ul><li>The '[' and ']' symbols indicate an inclusive bound; '(' and ')' indicate an exclusive bound.</li><li>When the upper or lower bound is missing, the range has no upper or lower bound.</li><li>The symbol ']' can be used instead of '(' for an exclusive lower bound, and '[' instead of ')' for exclusive upper bound. e.g &quot;]1.0, 2.0[&quot;</li></ul></li><li>A prefix version range: e.g. 1.+, 1.3.+ <ul><li>Only versions exactly matching the portion before the '+' are included.</li><li>The range '+' on it's own will include any version.</li></ul></li><li>A latest-status version: e.g. latest.integration, latest.release <ul><li>Will match the highest versioned module with the specified status. See <a href="../-component-metadata/get-status.html">getStatus</a>.</li></ul></li><li>A Maven SNAPSHOT version identifier: e.g. 1.0-SNAPSHOT, 1.4.9-beta1-SNAPSHOT</li></ul>Version ordering Versions have an implicit ordering. Version ordering is used to: <ul><li>Determine if a particular version is included in a range.</li><li>Determine which version is 'newest' when performing conflict resolution.</li></ul><p class="paragraph">Versions are ordered based on the following rules:</p><ul><li>Each version is split into it's constituent &quot;parts&quot;: <ul><li>The characters [<code class="lang-kotlin">. - _ +</code>] are used to separate the different &quot;parts&quot; of a version.</li><li>Any part that contains both digits and letters is split into separate parts for each: `1a1 == 1.a.1`</li><li>Only the parts of a version are compared. The actual separator characters are not significant: `1.a.1 == 1-a+1 == 1.a-1 == 1a1`</li></ul></li><li>The equivalent parts of 2 versions are compared using the following rules: <ul><li>If both parts are numeric, the highest numeric value is <strong>higher</strong>: `1.1 &lt; 1.2`</li><li>If one part is numeric, it is considered <strong>higher</strong> than the non-numeric part: `1.a &lt; 1.1`</li><li>If both are not numeric, the parts are compared alphabetically, case-sensitive: `1.A &lt; 1.B &lt; 1.a &lt; 1.b`</li><li>An version with an extra numeric part is considered <strong>higher</strong> than a version without: `1.1 &lt; 1.1.0`</li><li>An version with an extra non-numeric part is considered <strong>lower</strong> than a version without: `1.1.a &lt; 1.1`</li></ul></li><li>Certain string values have special meaning for the purposes of ordering: <ul><li>The string &quot;dev&quot; is consider <strong>lower</strong> than any other string part: 1.0-dev &lt; 1.0-alpha &lt; 1.0-rc.</li><li>The strings &quot;rc&quot;, &quot;release&quot; and &quot;final&quot; are considered <strong>higher</strong> than any other string part (sorted in that order): 1.0-zeta &lt; 1.0-rc &lt; 1.0-release &lt; 1.0-final &lt; 1.0.</li><li>The string &quot;SNAPSHOT&quot; <strong>has no special meaning</strong>, and is sorted alphabetically like any other string part: 1.0-alpha &lt; 1.0-SNAPSHOT &lt; 1.0-zeta &lt; 1.0-rc &lt; 1.0.</li><li>Numeric snapshot versions <strong>have no special meaning</strong>, and are sorted like any other numeric part: 1.0 &lt; 1.0-20150201.121010-123 &lt; 1.1.</li></ul></li></ul><h4 class="">Inheritors</h4><div class="table"><div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api"><div class="main-subrow keyValue "><div class=""><span class="inline-flex"><div><a href="../-mutable-version-constraint/index.html">MutableVersionConstraint</a></div></span></div><div></div></div></div></div></div>    </div>
  </div>
  <div class="tabbedcontent">
    <div class="tabs-section" tabs-section="tabs-section"><button class="section-tab" data-active="" data-togglable="CONSTRUCTOR,TYPE,PROPERTY,FUNCTION">Members</button></div>
    <div class="tabs-section-body">
      <div data-togglable="FUNCTION">
        <h2 class="">Functions</h2>
        <div class="table"><a data-name="-499825410%2FFunctions%2F-1793262594" anchor-label="getBranch" id="-499825410%2FFunctions%2F-1793262594" data-filterable-set=":docs/java_api"></a>
          <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="get-branch.html"><span>get</span><wbr></wbr><span><span>Branch</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-499825410%2FFunctions%2F-1793262594"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><div class="symbol monospace"><div class="block"><div class="block"><span class="token annotation builtin">@</span><span data-unresolved-link="javax.annotation/Nullable///PointingToDeclaration/"><span class="token annotation builtin">Nullable</span></span></div></div><span class="token keyword">abstract </span><span class="token keyword"></span><span class="token keyword">fun </span><a href="get-branch.html"><span class="token function">getBranch</span></a><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token operator">: </span><a href="https://docs.oracle.com/en/java/javase/11/docs/api/java.base/java/lang/String.html">String</a></div><div class="brief ">The branch to select versions from.</div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="-1293615975%2FFunctions%2F-1793262594" anchor-label="getDisplayName" id="-1293615975%2FFunctions%2F-1793262594" data-filterable-set=":docs/java_api"></a>
          <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="../../org.gradle.api/-describable/get-display-name.html"><span>get</span><wbr></wbr><span>Display</span><wbr></wbr><span><span>Name</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-1293615975%2FFunctions%2F-1793262594"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><div class="symbol monospace"><span class="token keyword">abstract </span><span class="token keyword"></span><span class="token keyword">fun </span><a href="../../org.gradle.api/-describable/get-display-name.html"><span class="token function">getDisplayName</span></a><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token operator">: </span><a href="https://docs.oracle.com/en/java/javase/11/docs/api/java.base/java/lang/String.html">String</a></div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="1238782825%2FFunctions%2F-1793262594" anchor-label="getPreferredVersion" id="1238782825%2FFunctions%2F-1793262594" data-filterable-set=":docs/java_api"></a>
          <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="get-preferred-version.html"><span>get</span><wbr></wbr><span>Preferred</span><wbr></wbr><span><span>Version</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="1238782825%2FFunctions%2F-1793262594"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><div class="symbol monospace"><span class="token keyword">abstract </span><span class="token keyword"></span><span class="token keyword">fun </span><a href="get-preferred-version.html"><span class="token function">getPreferredVersion</span></a><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token operator">: </span><a href="https://docs.oracle.com/en/java/javase/11/docs/api/java.base/java/lang/String.html">String</a></div><div class="brief ">The preferred version of a module (which may be an exact version or a version range).</div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="-829683321%2FFunctions%2F-1793262594" anchor-label="getRejectedVersions" id="-829683321%2FFunctions%2F-1793262594" data-filterable-set=":docs/java_api"></a>
          <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="get-rejected-versions.html"><span>get</span><wbr></wbr><span>Rejected</span><wbr></wbr><span><span>Versions</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-829683321%2FFunctions%2F-1793262594"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><div class="symbol monospace"><span class="token keyword">abstract </span><span class="token keyword"></span><span class="token keyword">fun </span><a href="get-rejected-versions.html"><span class="token function">getRejectedVersions</span></a><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token operator">: </span><a href="https://docs.oracle.com/en/java/javase/11/docs/api/java.base/java/util/List.html">List</a><span class="token operator">&lt;</span><a href="https://docs.oracle.com/en/java/javase/11/docs/api/java.base/java/lang/String.html">String</a><span class="token operator">&gt;</span></div><div class="brief ">Returns the list of versions that this module rejects (which may be exact versions, or ranges, anything that fits into a version string).</div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="1464932449%2FFunctions%2F-1793262594" anchor-label="getRequiredVersion" id="1464932449%2FFunctions%2F-1793262594" data-filterable-set=":docs/java_api"></a>
          <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="get-required-version.html"><span>get</span><wbr></wbr><span>Required</span><wbr></wbr><span><span>Version</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="1464932449%2FFunctions%2F-1793262594"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><div class="symbol monospace"><span class="token keyword">abstract </span><span class="token keyword"></span><span class="token keyword">fun </span><a href="get-required-version.html"><span class="token function">getRequiredVersion</span></a><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token operator">: </span><a href="https://docs.oracle.com/en/java/javase/11/docs/api/java.base/java/lang/String.html">String</a></div><div class="brief ">The required version of a module (which may be an exact version or a version range).</div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="-1388859701%2FFunctions%2F-1793262594" anchor-label="getStrictVersion" id="-1388859701%2FFunctions%2F-1793262594" data-filterable-set=":docs/java_api"></a>
          <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="get-strict-version.html"><span>get</span><wbr></wbr><span>Strict</span><wbr></wbr><span><span>Version</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-1388859701%2FFunctions%2F-1793262594"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><div class="symbol monospace"><span class="token keyword">abstract </span><span class="token keyword"></span><span class="token keyword">fun </span><a href="get-strict-version.html"><span class="token function">getStrictVersion</span></a><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token operator">: </span><a href="https://docs.oracle.com/en/java/javase/11/docs/api/java.base/java/lang/String.html">String</a></div><div class="brief ">The strictly required version of a module (which may be an exact version or a version range).</div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
      <div class="footer">
        <span class="go-to-top-icon"><a href="#content" id="go-to-top-link"></a></span><span>Gradle Kotlin DSL Reference</span><span
                class="pull-right"><span>Generated by </span><a
                href="https://github.com/Kotlin/dokka"><span>dokka</span><span class="padded-icon"></span></a></span>
      </div>
    </div>
</div>
</body>
</html>
