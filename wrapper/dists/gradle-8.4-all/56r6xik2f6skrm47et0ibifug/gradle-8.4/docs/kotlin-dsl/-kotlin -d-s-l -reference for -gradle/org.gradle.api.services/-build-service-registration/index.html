<!DOCTYPE html>
<html>
<head>
    <meta name="viewport" content="width=device-width, initial-scale=1" charset="UTF-8">
    <title>BuildServiceRegistration</title>
    <link href="../../../images/logo-icon.svg" rel="icon" type="image/svg">
    <script>var pathToRoot = "../../../";</script>
    <script>const storage = localStorage.getItem("dokka-dark-mode")
    if (storage == null) {
        const osDarkSchemePreferred = window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches
        if (osDarkSchemePreferred === true) {
            document.getElementsByTagName("html")[0].classList.add("theme-dark")
        }
    } else {
        const savedDarkMode = JSON.parse(storage)
        if(savedDarkMode === true) {
            document.getElementsByTagName("html")[0].classList.add("theme-dark")
        }
    }
    </script>
<script type="text/javascript" src="../../../scripts/sourceset_dependencies.js" async="async"></script>
<link href="../../../styles/style.css" rel="Stylesheet">
<link href="../../../styles/jetbrains-mono.css" rel="Stylesheet">
<link href="../../../styles/main.css" rel="Stylesheet">
<link href="../../../styles/prism.css" rel="Stylesheet">
<link href="../../../styles/logo-styles.css" rel="Stylesheet">
<script type="text/javascript" src="../../../scripts/clipboard.js" async="async"></script>
<script type="text/javascript" src="../../../scripts/navigation-loader.js" async="async"></script>
<script type="text/javascript" src="../../../scripts/platform-content-handler.js" async="async"></script>
<script type="text/javascript" src="../../../scripts/main.js" defer="defer"></script>
<script type="text/javascript" src="../../../scripts/prism.js" async="async"></script>
<script type="text/javascript" src="../../../scripts/symbol-parameters-wrapper_deferred.js" defer="defer"></script>
<link href="../../../images/gradle-logo.svg">
<link href="../../../styles/gradle.css" rel="Stylesheet">
</head>
<body>
<div class="navigation-wrapper" id="navigation-wrapper">
    <div id="leftToggler"><span class="icon-toggler"></span></div>
    <div class="library-name">
            <a href="../../../index.html">
                    <span>Kotlin DSL Reference for Gradle</span>
            </a>
    </div>
    <div>
8.4    </div>
    <div class="pull-right d-flex">
        <div class="filter-section" id="filter-section">
                <button class="platform-tag platform-selector jvm-like" data-active="" data-filter=":docs/java_api">API</button>
        </div>
        <button id="theme-toggle-button"><span id="theme-toggle"></span></button>
        <div id="searchBar"></div>
    </div>
</div>
<div id="container">
    <div id="leftColumn">
        <div id="sideMenu"></div>
    </div>
    <div id="main">
<div class="main-content" data-page-type="classlike" id="content" pageIds="Kotlin DSL Reference for Gradle::org.gradle.api.services/BuildServiceRegistration///PointingToDeclaration//-**********">
  <div class="breadcrumbs"><a href="../../../index.html">Kotlin DSL Reference for Gradle</a><span class="delimiter">/</span><a href="../index.html">org.gradle.api.services</a><span class="delimiter">/</span><span class="current">BuildServiceRegistration</span></div>
  <div class="cover ">
    <h1 class="cover"><span>Build</span><wbr></wbr><span>Service</span><wbr></wbr><span><span>Registration</span></span></h1>
    <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
      <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><div class="symbol monospace"><span class="token keyword">interface </span><a href="index.html">BuildServiceRegistration</a><span class="token operator">&lt;</span><span class="token keyword"></span><a href="index.html">T</a><span class="token operator"> : </span><a href="../-build-service/index.html">BuildService</a><span class="token operator">&lt;</span><a href="index.html">P</a><span class="token operator">&gt;</span><span class="token operator">?</span><span class="token punctuation">, </span><span class="token keyword"></span><a href="index.html">P</a><span class="token operator"> : </span><a href="../-build-service-parameters/index.html">BuildServiceParameters</a><span class="token operator">?</span><span class="token operator">&gt;</span> : <a href="../../org.gradle.api/-named/index.html">Named</a><span class="clearfix"><span class="floating-right">(<a href="https://github.com/gradle/gradle/blob/e9251e572c9bd1d01e503a0dfdf43aedaeecdc3f/subprojects/core-api/src/main/java/org/gradle/api/services/BuildServiceRegistration.java#L30">source</a>)</span></span></div><p class="paragraph">Details of a build service.</p><h4 class="">Parameters</h4><div class="table"><div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api"><div class="main-subrow keyValue "><div class=""><span class="inline-flex"><div><u><span><span>&lt;T&gt;</span></span></u></div></span></div><div><div class="title"><p class="paragraph">the service type.</p></div></div></div></div><div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api"><div class="main-subrow keyValue "><div class=""><span class="inline-flex"><div><u><span><span>&lt;P&gt;</span></span></u></div></span></div><div><div class="title"><p class="paragraph">the service parameters type.</p></div></div></div></div></div></div>    </div>
  </div>
  <div class="tabbedcontent">
    <div class="tabs-section" tabs-section="tabs-section"><button class="section-tab" data-active="" data-togglable="CONSTRUCTOR,TYPE,PROPERTY,FUNCTION">Members</button></div>
    <div class="tabs-section-body">
      <div data-togglable="FUNCTION">
        <h2 class="">Functions</h2>
        <div class="table"><a data-name="2031621927%2FFunctions%2F-**********" anchor-label="getMaxParallelUsages" id="2031621927%2FFunctions%2F-**********" data-filterable-set=":docs/java_api"></a>
          <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="get-max-parallel-usages.html"><span>get</span><wbr></wbr><span>Max</span><wbr></wbr><span>Parallel</span><wbr></wbr><span><span>Usages</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="2031621927%2FFunctions%2F-**********"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><div class="symbol monospace"><span class="token keyword">abstract </span><span class="token keyword"></span><span class="token keyword">fun </span><a href="get-max-parallel-usages.html"><span class="token function">getMaxParallelUsages</span></a><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token operator">: </span><a href="../../org.gradle.api.provider/-property/index.html">Property</a><span class="token operator">&lt;</span><a href="https://docs.oracle.com/en/java/javase/11/docs/api/java.base/java/lang/Integer.html">Integer</a><span class="token operator">&gt;</span></div><div class="brief ">Specifies the maximum number of tasks that can use this service in parallel.</div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="-402315996%2FFunctions%2F-**********" anchor-label="getName" id="-402315996%2FFunctions%2F-**********" data-filterable-set=":docs/java_api"></a>
          <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="../../org.gradle.api/-named/get-name.html"><span>get</span><wbr></wbr><span><span>Name</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-402315996%2FFunctions%2F-**********"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><div class="symbol monospace"><span class="token keyword">abstract </span><span class="token keyword"></span><span class="token keyword">fun </span><a href="../../org.gradle.api/-named/get-name.html"><span class="token function">getName</span></a><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token operator">: </span><a href="https://docs.oracle.com/en/java/javase/11/docs/api/java.base/java/lang/String.html">String</a></div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="-14841908%2FFunctions%2F-**********" anchor-label="getParameters" id="-14841908%2FFunctions%2F-**********" data-filterable-set=":docs/java_api"></a>
          <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="get-parameters.html"><span>get</span><wbr></wbr><span><span>Parameters</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-14841908%2FFunctions%2F-**********"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><div class="symbol monospace"><span class="token keyword">abstract </span><span class="token keyword"></span><span class="token keyword">fun </span><a href="get-parameters.html"><span class="token function">getParameters</span></a><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token operator">: </span><a href="index.html">P</a></div><div class="brief ">Returns the parameters that will be used to instantiate the service with.</div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="-826836337%2FFunctions%2F-**********" anchor-label="getService" id="-826836337%2FFunctions%2F-**********" data-filterable-set=":docs/java_api"></a>
          <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="get-service.html"><span>get</span><wbr></wbr><span><span>Service</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-826836337%2FFunctions%2F-**********"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><div class="symbol monospace"><span class="token keyword">abstract </span><span class="token keyword"></span><span class="token keyword">fun </span><a href="get-service.html"><span class="token function">getService</span></a><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token operator">: </span><a href="../../org.gradle.api.provider/-provider/index.html">Provider</a><span class="token operator">&lt;</span><a href="index.html">T</a><span class="token operator">&gt;</span></div><div class="brief ">Returns a <a href="../../org.gradle.api.provider/-provider/index.html">Provider</a> that will create the service instance when its value is queried.</div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
      <div class="footer">
        <span class="go-to-top-icon"><a href="#content" id="go-to-top-link"></a></span><span>Gradle Kotlin DSL Reference</span><span
                class="pull-right"><span>Generated by </span><a
                href="https://github.com/Kotlin/dokka"><span>dokka</span><span class="padded-icon"></span></a></span>
      </div>
    </div>
</div>
</body>
</html>
