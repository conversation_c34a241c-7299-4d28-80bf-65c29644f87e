<!DOCTYPE html>
<html>
<head>
    <meta name="viewport" content="width=device-width, initial-scale=1" charset="UTF-8">
    <title>org.gradle.api.provider</title>
    <link href="../../images/logo-icon.svg" rel="icon" type="image/svg">
    <script>var pathToRoot = "../../";</script>
    <script>const storage = localStorage.getItem("dokka-dark-mode")
    if (storage == null) {
        const osDarkSchemePreferred = window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches
        if (osDarkSchemePreferred === true) {
            document.getElementsByTagName("html")[0].classList.add("theme-dark")
        }
    } else {
        const savedDarkMode = JSON.parse(storage)
        if(savedDarkMode === true) {
            document.getElementsByTagName("html")[0].classList.add("theme-dark")
        }
    }
    </script>
<script type="text/javascript" src="../../scripts/sourceset_dependencies.js" async="async"></script>
<link href="../../styles/style.css" rel="Stylesheet">
<link href="../../styles/jetbrains-mono.css" rel="Stylesheet">
<link href="../../styles/main.css" rel="Stylesheet">
<link href="../../styles/prism.css" rel="Stylesheet">
<link href="../../styles/logo-styles.css" rel="Stylesheet">
<script type="text/javascript" src="../../scripts/clipboard.js" async="async"></script>
<script type="text/javascript" src="../../scripts/navigation-loader.js" async="async"></script>
<script type="text/javascript" src="../../scripts/platform-content-handler.js" async="async"></script>
<script type="text/javascript" src="../../scripts/main.js" defer="defer"></script>
<script type="text/javascript" src="../../scripts/prism.js" async="async"></script>
<script type="text/javascript" src="../../scripts/symbol-parameters-wrapper_deferred.js" defer="defer"></script>
<link href="../../images/gradle-logo.svg">
<link href="../../styles/gradle.css" rel="Stylesheet">
</head>
<body>
<div class="navigation-wrapper" id="navigation-wrapper">
    <div id="leftToggler"><span class="icon-toggler"></span></div>
    <div class="library-name">
            <a href="../../index.html">
                    <span>Kotlin DSL Reference for Gradle</span>
            </a>
    </div>
    <div>
8.4    </div>
    <div class="pull-right d-flex">
        <div class="filter-section" id="filter-section">
                <button class="platform-tag platform-selector jvm-like" data-active="" data-filter=":docs/java_api">API</button>
        </div>
        <button id="theme-toggle-button"><span id="theme-toggle"></span></button>
        <div id="searchBar"></div>
    </div>
</div>
<div id="container">
    <div id="leftColumn">
        <div id="sideMenu"></div>
    </div>
    <div id="main">
<div class="main-content" data-page-type="package" id="content" pageIds="Kotlin DSL Reference for Gradle::org.gradle.api.provider////PointingToDeclaration//-**********">
  <div class="breadcrumbs"><a href="../../index.html">Kotlin DSL Reference for Gradle</a><span class="delimiter">/</span><span class="current">org.gradle.api.provider</span></div>
  <div class="cover ">
    <h1 class="cover"><span><span>Package-level</span></span> <span><span>declarations</span></span></h1>
    <div class="platform-hinted UnderCoverText with-platform-tabs" data-platform-hinted="data-platform-hinted">
      <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><p class="paragraph">The interfaces for value providers.</p></div>    </div>
  </div>
  <div class="tabbedcontent">
    <div class="tabs-section" tabs-section="tabs-section"><button class="section-tab" data-active="" data-togglable="TYPE">Types</button></div>
    <div class="tabs-section-body">
      <div data-togglable="TYPE">
        <h2 class="">Types</h2>
        <div class="table"><a data-name="-326014522%2FClasslikes%2F-**********" anchor-label="HasConfigurableValue" id="-326014522%2FClasslikes%2F-**********" data-filterable-set=":docs/java_api"></a>
          <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="-has-configurable-value/index.html"><span>Has</span><wbr></wbr><span>Configurable</span><wbr></wbr><span><span>Value</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-326014522%2FClasslikes%2F-**********"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><div class="symbol monospace"><span class="token keyword">interface </span><a href="-has-configurable-value/index.html">HasConfigurableValue</a></div><div class="brief ">Represents an object that holds a value that is configurable, meaning that the value or some source for the value, such as a <a href="-provider/index.html">Provider</a>, can be specified directly on the object.</div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="824411122%2FClasslikes%2F-**********" anchor-label="HasMultipleValues" id="824411122%2FClasslikes%2F-**********" data-filterable-set=":docs/java_api"></a>
          <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="-has-multiple-values/index.html"><span>Has</span><wbr></wbr><span>Multiple</span><wbr></wbr><span><span>Values</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="824411122%2FClasslikes%2F-**********"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><div class="symbol monospace"><div class="block"><div class="block"><span class="token annotation builtin">@</span><a href="../org.gradle.api/-supports-kotlin-assignment-overloading/index.html"><span class="token annotation builtin">SupportsKotlinAssignmentOverloading</span></a></div></div><span class="token keyword">interface </span><a href="-has-multiple-values/index.html">HasMultipleValues</a><span class="token operator">&lt;</span><span class="token keyword"></span><a href="-has-multiple-values/index.html">T</a><span class="token operator">&gt;</span> : <a href="-has-configurable-value/index.html">HasConfigurableValue</a></div><div class="brief ">Represents a property whose value can be set using multiple elements of type <a href="-has-multiple-values/index.html">T</a>, such as a collection property.</div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="1284385809%2FClasslikes%2F-**********" anchor-label="ListProperty" id="1284385809%2FClasslikes%2F-**********" data-filterable-set=":docs/java_api"></a>
          <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="-list-property/index.html"><span>List</span><wbr></wbr><span><span>Property</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="1284385809%2FClasslikes%2F-**********"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><div class="symbol monospace"><span class="token keyword">interface </span><a href="-list-property/index.html">ListProperty</a><span class="token operator">&lt;</span><span class="token keyword"></span><a href="-list-property/index.html">T</a><span class="token operator">&gt;</span> : <a href="-provider/index.html">Provider</a><span class="token operator">&lt;</span><a href="-provider/index.html">T</a><span class="token operator">&gt; </span>, <a href="-has-multiple-values/index.html">HasMultipleValues</a><span class="token operator">&lt;</span><a href="-has-multiple-values/index.html">T</a><span class="token operator">&gt; </span></div><div class="brief ">Represents a property whose type is a <a href="https://docs.oracle.com/en/java/javase/11/docs/api/java.base/java/util/List.html">List</a> of elements of type <a href="-list-property/index.html">T</a>.</div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="**********%2FClasslikes%2F-**********" anchor-label="MapProperty" id="**********%2FClasslikes%2F-**********" data-filterable-set=":docs/java_api"></a>
          <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="-map-property/index.html"><span>Map</span><wbr></wbr><span><span>Property</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="**********%2FClasslikes%2F-**********"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><div class="symbol monospace"><div class="block"><div class="block"><span class="token annotation builtin">@</span><a href="../org.gradle.api/-supports-kotlin-assignment-overloading/index.html"><span class="token annotation builtin">SupportsKotlinAssignmentOverloading</span></a></div></div><span class="token keyword">interface </span><a href="-map-property/index.html">MapProperty</a><span class="token operator">&lt;</span><span class="token keyword"></span><a href="-map-property/index.html">K</a><span class="token punctuation">, </span><span class="token keyword"></span><a href="-map-property/index.html">V</a><span class="token operator">&gt;</span> : <a href="-provider/index.html">Provider</a><span class="token operator">&lt;</span><a href="-provider/index.html">T</a><span class="token operator">&gt; </span>, <a href="-has-configurable-value/index.html">HasConfigurableValue</a></div><div class="brief ">Represents a property whose type is a <a href="https://docs.oracle.com/en/java/javase/11/docs/api/java.base/java/util/Map.html">Map</a> of keys of type <a href="-map-property/index.html">K</a> and values of type <a href="-map-property/index.html">V</a>.</div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="-692596081%2FClasslikes%2F-**********" anchor-label="Property" id="-692596081%2FClasslikes%2F-**********" data-filterable-set=":docs/java_api"></a>
          <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="-property/index.html"><span><span>Property</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-692596081%2FClasslikes%2F-**********"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><div class="symbol monospace"><div class="block"><div class="block"><span class="token annotation builtin">@</span><a href="../org.gradle.api/-supports-kotlin-assignment-overloading/index.html"><span class="token annotation builtin">SupportsKotlinAssignmentOverloading</span></a></div></div><span class="token keyword">interface </span><a href="-property/index.html">Property</a><span class="token operator">&lt;</span><span class="token keyword"></span><a href="-property/index.html">T</a><span class="token operator">&gt;</span> : <a href="-provider/index.html">Provider</a><span class="token operator">&lt;</span><a href="-provider/index.html">T</a><span class="token operator">&gt; </span>, <a href="-has-configurable-value/index.html">HasConfigurableValue</a></div><div class="brief ">A container object that represents a configurable value of a specific type.</div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="-337395213%2FClasslikes%2F-**********" anchor-label="Provider" id="-337395213%2FClasslikes%2F-**********" data-filterable-set=":docs/java_api"></a>
          <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="-provider/index.html"><span><span>Provider</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-337395213%2FClasslikes%2F-**********"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><div class="symbol monospace"><div class="block"><div class="block"><span class="token annotation builtin">@</span><a href="../org.gradle.api/-non-extensible/index.html"><span class="token annotation builtin">NonExtensible</span></a></div></div><span class="token keyword">interface </span><a href="-provider/index.html">Provider</a><span class="token operator">&lt;</span><span class="token keyword"></span><a href="-provider/index.html">T</a><span class="token operator">&gt;</span></div><div class="brief ">A container object that provides a value of a specific type.</div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="**********%2FClasslikes%2F-**********" anchor-label="ProviderConvertible" id="**********%2FClasslikes%2F-**********" data-filterable-set=":docs/java_api"></a>
          <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="-provider-convertible/index.html"><span>Provider</span><wbr></wbr><span><span>Convertible</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="**********%2FClasslikes%2F-**********"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><div class="symbol monospace"><div class="block"><div class="block"><span class="token annotation builtin">@</span><a href="../org.gradle.api/-non-extensible/index.html"><span class="token annotation builtin">NonExtensible</span></a></div></div><span class="token keyword">interface </span><a href="-provider-convertible/index.html">ProviderConvertible</a><span class="token operator">&lt;</span><span class="token keyword"></span><a href="-provider-convertible/index.html">T</a><span class="token operator">&gt;</span></div><div class="brief ">An object that can be converted to a <a href="-provider/index.html">Provider</a>.</div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="-922108283%2FClasslikes%2F-**********" anchor-label="ProviderFactory" id="-922108283%2FClasslikes%2F-**********" data-filterable-set=":docs/java_api"></a>
          <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="-provider-factory/index.html"><span>Provider</span><wbr></wbr><span><span>Factory</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-922108283%2FClasslikes%2F-**********"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><div class="symbol monospace"><div class="block"><div class="block"><span class="token annotation builtin">@</span><a href="../org.gradle.api/-non-extensible/index.html"><span class="token annotation builtin">NonExtensible</span></a></div></div><span class="token keyword">interface </span><a href="-provider-factory/index.html">ProviderFactory</a></div><div class="brief ">A factory for creating instances of <a href="-provider/index.html">Provider</a>.</div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="-**********%2FClasslikes%2F-**********" anchor-label="SetProperty" id="-**********%2FClasslikes%2F-**********" data-filterable-set=":docs/java_api"></a>
          <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="-set-property/index.html"><span>Set</span><wbr></wbr><span><span>Property</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-**********%2FClasslikes%2F-**********"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><div class="symbol monospace"><span class="token keyword">interface </span><a href="-set-property/index.html">SetProperty</a><span class="token operator">&lt;</span><span class="token keyword"></span><a href="-set-property/index.html">T</a><span class="token operator">&gt;</span> : <a href="-provider/index.html">Provider</a><span class="token operator">&lt;</span><a href="-provider/index.html">T</a><span class="token operator">&gt; </span>, <a href="-has-multiple-values/index.html">HasMultipleValues</a><span class="token operator">&lt;</span><a href="-has-multiple-values/index.html">T</a><span class="token operator">&gt; </span></div><div class="brief ">Represents a property whose type is a <a href="https://docs.oracle.com/en/java/javase/11/docs/api/java.base/java/util/Set.html">Set</a> of elements of type <a href="-set-property/index.html">T</a>.</div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="181497170%2FClasslikes%2F-**********" anchor-label="ValueSource" id="181497170%2FClasslikes%2F-**********" data-filterable-set=":docs/java_api"></a>
          <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="-value-source/index.html"><span>Value</span><wbr></wbr><span><span>Source</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="181497170%2FClasslikes%2F-**********"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><div class="symbol monospace"><span class="token keyword">interface </span><a href="-value-source/index.html">ValueSource</a><span class="token operator">&lt;</span><span class="token keyword"></span><a href="-value-source/index.html">T</a><span class="token punctuation">, </span><span class="token keyword"></span><a href="-value-source/index.html">P</a><span class="token operator"> : </span><a href="-value-source-parameters/index.html">ValueSourceParameters</a><span class="token operator">?</span><span class="token operator">&gt;</span></div><div class="brief ">Represents an external source of information used by a Gradle build.</div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="1163729224%2FClasslikes%2F-**********" anchor-label="ValueSourceParameters" id="1163729224%2FClasslikes%2F-**********" data-filterable-set=":docs/java_api"></a>
          <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="-value-source-parameters/index.html"><span>Value</span><wbr></wbr><span>Source</span><wbr></wbr><span><span>Parameters</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="1163729224%2FClasslikes%2F-**********"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><div class="symbol monospace"><span class="token keyword">interface </span><a href="-value-source-parameters/index.html">ValueSourceParameters</a></div><div class="brief ">Marker interface for parameter objects to <a href="-value-source/index.html">ValueSource</a>s.</div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
<a data-name="-1717566121%2FClasslikes%2F-**********" anchor-label="ValueSourceSpec" id="-1717566121%2FClasslikes%2F-**********" data-filterable-set=":docs/java_api"></a>
          <div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="-value-source-spec/index.html"><span>Value</span><wbr></wbr><span>Source</span><wbr></wbr><span><span>Spec</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-1717566121%2FClasslikes%2F-**********"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
                    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><div class="symbol monospace"><span class="token keyword">interface </span><a href="-value-source-spec/index.html">ValueSourceSpec</a><span class="token operator">&lt;</span><span class="token keyword"></span><a href="-value-source-spec/index.html">P</a><span class="token operator"> : </span><a href="-value-source-parameters/index.html">ValueSourceParameters</a><span class="token operator">?</span><span class="token operator">&gt;</span></div><div class="brief ">Base configuration for value source definitions.</div></div>                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
      <div class="footer">
        <span class="go-to-top-icon"><a href="#content" id="go-to-top-link"></a></span><span>Gradle Kotlin DSL Reference</span><span
                class="pull-right"><span>Generated by </span><a
                href="https://github.com/Kotlin/dokka"><span>dokka</span><span class="padded-icon"></span></a></span>
      </div>
    </div>
</div>
</body>
</html>
