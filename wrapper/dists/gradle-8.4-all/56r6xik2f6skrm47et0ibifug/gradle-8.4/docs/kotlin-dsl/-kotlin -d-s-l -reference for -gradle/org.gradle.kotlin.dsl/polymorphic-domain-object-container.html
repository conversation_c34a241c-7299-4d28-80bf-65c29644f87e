<!DOCTYPE html>
<html>
<head>
    <meta name="viewport" content="width=device-width, initial-scale=1" charset="UTF-8">
    <title>polymorphicDomainObjectContainer</title>
    <link href="../../images/logo-icon.svg" rel="icon" type="image/svg">
    <script>var pathToRoot = "../../";</script>
    <script>const storage = localStorage.getItem("dokka-dark-mode")
    if (storage == null) {
        const osDarkSchemePreferred = window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches
        if (osDarkSchemePreferred === true) {
            document.getElementsByTagName("html")[0].classList.add("theme-dark")
        }
    } else {
        const savedDarkMode = JSON.parse(storage)
        if(savedDarkMode === true) {
            document.getElementsByTagName("html")[0].classList.add("theme-dark")
        }
    }
    </script>
<script type="text/javascript" src="../../scripts/sourceset_dependencies.js" async="async"></script>
<link href="../../styles/style.css" rel="Stylesheet">
<link href="../../styles/jetbrains-mono.css" rel="Stylesheet">
<link href="../../styles/main.css" rel="Stylesheet">
<link href="../../styles/prism.css" rel="Stylesheet">
<link href="../../styles/logo-styles.css" rel="Stylesheet">
<script type="text/javascript" src="../../scripts/clipboard.js" async="async"></script>
<script type="text/javascript" src="../../scripts/navigation-loader.js" async="async"></script>
<script type="text/javascript" src="../../scripts/platform-content-handler.js" async="async"></script>
<script type="text/javascript" src="../../scripts/main.js" defer="defer"></script>
<script type="text/javascript" src="../../scripts/prism.js" async="async"></script>
<script type="text/javascript" src="../../scripts/symbol-parameters-wrapper_deferred.js" defer="defer"></script>
<link href="../../images/gradle-logo.svg">
<link href="../../styles/gradle.css" rel="Stylesheet">
</head>
<body>
<div class="navigation-wrapper" id="navigation-wrapper">
    <div id="leftToggler"><span class="icon-toggler"></span></div>
    <div class="library-name">
            <a href="../../index.html">
                    <span>Kotlin DSL Reference for Gradle</span>
            </a>
    </div>
    <div>
8.4    </div>
    <div class="pull-right d-flex">
        <div class="filter-section" id="filter-section">
                <button class="platform-tag platform-selector jvm-like" data-active="" data-filter=":docs/kotlin_dsl">DSL</button>
        </div>
        <button id="theme-toggle-button"><span id="theme-toggle"></span></button>
        <div id="searchBar"></div>
    </div>
</div>
<div id="container">
    <div id="leftColumn">
        <div id="sideMenu"></div>
    </div>
    <div id="main">
<div class="main-content" data-page-type="member" id="content" pageIds="Kotlin DSL Reference for Gradle::org.gradle.kotlin.dsl//polymorphicDomainObjectContainer/org.gradle.api.model.ObjectFactory#kotlin.reflect.KClass[TypeParam(bounds=[kotlin.Any])]/PointingToDeclaration//-1867656071">
  <div class="breadcrumbs"><a href="../../index.html">Kotlin DSL Reference for Gradle</a><span class="delimiter">/</span><a href="index.html">org.gradle.kotlin.dsl</a><span class="delimiter">/</span><span class="current">polymorphicDomainObjectContainer</span></div>
  <div class="cover ">
    <h1 class="cover"><span>polymorphic</span><wbr></wbr><span>Domain</span><wbr></wbr><span>Object</span><wbr></wbr><span><span>Container</span></span></h1>
  </div>
  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/kotlin_dsl" data-filterable-set=":docs/kotlin_dsl" data-active="" data-toggle=":docs/kotlin_dsl">DSL</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/kotlin_dsl"><div class="symbol monospace"><span class="token keyword">inline </span><span class="token keyword">fun </span><span class="token operator">&lt;</span><span class="token keyword"></span><a href="polymorphic-domain-object-container.html">T</a><span class="token operator"> : </span><a href="https://kotlinlang.org/api/latest/jvm/stdlib/kotlin/-any/index.html">Any</a><span class="token operator">&gt; </span><a href="../org.gradle.api.model/-object-factory/index.html#1812213405%2FMain%2F-1867656071">ObjectFactory</a><span class="token punctuation">.</span><a href="polymorphic-domain-object-container.html"><span class="token function">polymorphicDomainObjectContainer</span></a><span class="token punctuation">(</span><span class="parameters "><span class="parameter ">elementType<span class="token operator">: </span><a href="https://kotlinlang.org/api/latest/jvm/stdlib/kotlin.reflect/-k-class/index.html">KClass</a><span class="token operator">&lt;</span><span class="token keyword"></span><a href="polymorphic-domain-object-container.html">T</a><span class="token operator">&gt;</span></span></span><span class="token punctuation">)</span><span class="token operator">: </span><a href="../org.gradle.api/-extensible-polymorphic-domain-object-container/index.html#-1238037732%2FMain%2F-1867656071">ExtensiblePolymorphicDomainObjectContainer</a><span class="token operator">&lt;</span><span class="token keyword"></span><a href="polymorphic-domain-object-container.html">T</a><span class="token operator">&gt;</span></div><p class="paragraph">Kotlin extension function taking <a href="https://kotlinlang.org/api/latest/jvm/stdlib/kotlin.reflect/-k-class/index.html">kotlin.reflect.KClass</a> for <span data-unresolved-link="org.gradle.api.model/ObjectFactory/polymorphicDomainObjectContainer/#java.lang.Class[TypeParam(bounds=[kotlin.Any])]/PointingToDeclaration/">org.gradle.api.model.ObjectFactory.polymorphicDomainObjectContainer</span>.</p><h4 class="">See also</h4><div class="table"><div class="table-row" data-filterable-current=":docs/kotlin_dsl" data-filterable-set=":docs/kotlin_dsl"><div class="main-subrow keyValue "><div class=""><span class="inline-flex"><div><span data-unresolved-link="org.gradle.api.model/ObjectFactory/polymorphicDomainObjectContainer/#java.lang.Class[TypeParam(bounds=[kotlin.Any])]/PointingToDeclaration/"><span>Object</span><wbr></wbr><span>Factory.</span><wbr></wbr><span>polymorphic</span><wbr></wbr><span>Domain</span><wbr></wbr><span>Object</span><wbr></wbr><span><span>Container</span></span></span></div></span></div><div></div></div></div></div></div>  </div>
</div>
      <div class="footer">
        <span class="go-to-top-icon"><a href="#content" id="go-to-top-link"></a></span><span>Gradle Kotlin DSL Reference</span><span
                class="pull-right"><span>Generated by </span><a
                href="https://github.com/Kotlin/dokka"><span>dokka</span><span class="padded-icon"></span></a></span>
      </div>
    </div>
</div>
</body>
</html>
