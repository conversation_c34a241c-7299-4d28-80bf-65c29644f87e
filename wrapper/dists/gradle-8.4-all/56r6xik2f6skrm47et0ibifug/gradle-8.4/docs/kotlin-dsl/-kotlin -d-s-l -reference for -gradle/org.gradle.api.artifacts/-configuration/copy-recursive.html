<!DOCTYPE html>
<html>
<head>
    <meta name="viewport" content="width=device-width, initial-scale=1" charset="UTF-8">
    <title>copyRecursive</title>
    <link href="../../../images/logo-icon.svg" rel="icon" type="image/svg">
    <script>var pathToRoot = "../../../";</script>
    <script>const storage = localStorage.getItem("dokka-dark-mode")
    if (storage == null) {
        const osDarkSchemePreferred = window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches
        if (osDarkSchemePreferred === true) {
            document.getElementsByTagName("html")[0].classList.add("theme-dark")
        }
    } else {
        const savedDarkMode = JSON.parse(storage)
        if(savedDarkMode === true) {
            document.getElementsByTagName("html")[0].classList.add("theme-dark")
        }
    }
    </script>
<script type="text/javascript" src="../../../scripts/sourceset_dependencies.js" async="async"></script>
<link href="../../../styles/style.css" rel="Stylesheet">
<link href="../../../styles/jetbrains-mono.css" rel="Stylesheet">
<link href="../../../styles/main.css" rel="Stylesheet">
<link href="../../../styles/prism.css" rel="Stylesheet">
<link href="../../../styles/logo-styles.css" rel="Stylesheet">
<script type="text/javascript" src="../../../scripts/clipboard.js" async="async"></script>
<script type="text/javascript" src="../../../scripts/navigation-loader.js" async="async"></script>
<script type="text/javascript" src="../../../scripts/platform-content-handler.js" async="async"></script>
<script type="text/javascript" src="../../../scripts/main.js" defer="defer"></script>
<script type="text/javascript" src="../../../scripts/prism.js" async="async"></script>
<script type="text/javascript" src="../../../scripts/symbol-parameters-wrapper_deferred.js" defer="defer"></script>
<link href="../../../images/gradle-logo.svg">
<link href="../../../styles/gradle.css" rel="Stylesheet">
</head>
<body>
<div class="navigation-wrapper" id="navigation-wrapper">
    <div id="leftToggler"><span class="icon-toggler"></span></div>
    <div class="library-name">
            <a href="../../../index.html">
                    <span>Kotlin DSL Reference for Gradle</span>
            </a>
    </div>
    <div>
8.4    </div>
    <div class="pull-right d-flex">
        <div class="filter-section" id="filter-section">
                <button class="platform-tag platform-selector jvm-like" data-active="" data-filter=":docs/java_api">API</button>
        </div>
        <button id="theme-toggle-button"><span id="theme-toggle"></span></button>
        <div id="searchBar"></div>
    </div>
</div>
<div id="container">
    <div id="leftColumn">
        <div id="sideMenu"></div>
    </div>
    <div id="main">
<div class="main-content" data-page-type="member" id="content" pageIds="Kotlin DSL Reference for Gradle::org.gradle.api.artifacts/Configuration/copyRecursive/#/PointingToDeclaration//-1793262594">
  <div class="breadcrumbs"><a href="../../../index.html">Kotlin DSL Reference for Gradle</a><span class="delimiter">/</span><a href="../index.html">org.gradle.api.artifacts</a><span class="delimiter">/</span><a href="index.html">Configuration</a><span class="delimiter">/</span><span class="current">copyRecursive</span></div>
  <div class="cover ">
    <h1 class="cover"><span>copy</span><wbr></wbr><span><span>Recursive</span></span></h1>
  </div>
  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api" data-active="" data-toggle=":docs/java_api">API</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/java_api"><div class="symbol monospace"><span class="token keyword">abstract </span><span class="token keyword"></span><span class="token keyword">fun </span><a href="copy-recursive.html"><span class="token function">copyRecursive</span></a><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token operator">: </span><a href="index.html">Configuration</a><span class="clearfix"><span class="floating-right">(<a href="https://github.com/gradle/gradle/blob/e9251e572c9bd1d01e503a0dfdf43aedaeecdc3f/subprojects/core-api/src/main/java/org/gradle/api/artifacts/Configuration.java#L549">source</a>)</span></span></div><p class="paragraph">Creates a copy of this configuration that contains the dependencies directly in this configuration and those derived from superconfigurations. The new configuration will be in the UNRESOLVED state, but will retain all other attributes of this configuration except superconfigurations. <a href="get-hierarchy.html">getHierarchy</a> for the copy will not include any superconfigurations. </p><p class="paragraph"> This method is only intended for use for specific situations involving resolvable configuration, it is NOT intended as a general-purpose copying mechanism.</p><span class="kdoc-tag"><h4 class="">Return</h4><p class="paragraph">copy of this configuration</p></span><hr><div class="symbol monospace"><span class="token keyword">abstract </span><span class="token keyword"></span><span class="token keyword">fun </span><a href="copy-recursive.html"><span class="token function">copyRecursive</span></a><span class="token punctuation">(</span><span class="parameters "><span class="parameter ">dependencySpec<span class="token operator">: </span><a href="../../org.gradle.api.specs/-spec/index.html">Spec</a><span class="token operator">&lt;</span><span class="token keyword">out </span><a href="https://kotlinlang.org/api/latest/jvm/stdlib/kotlin/-any/index.html">Any</a><span class="token operator">&gt;</span></span></span><span class="token punctuation">)</span><span class="token operator">: </span><a href="index.html">Configuration</a><span class="clearfix"><span class="floating-right">(<a href="https://github.com/gradle/gradle/blob/e9251e572c9bd1d01e503a0dfdf43aedaeecdc3f/subprojects/core-api/src/main/java/org/gradle/api/artifacts/Configuration.java#L579">source</a>)</span></span></div><p class="paragraph">Creates a copy of this configuration with dependencies from superconfigurations (see <a href="copy-recursive.html">copyRecursive</a>) but filtering the dependencies using the dependencySpec. </p><p class="paragraph"> This method is only intended for use for specific situations involving resolvable configuration, it is NOT intended as a general-purpose copying mechanism.</p><span class="kdoc-tag"><h4 class="">Return</h4><p class="paragraph">copy of this configuration</p></span><h4 class="">Parameters</h4><div class="table"><div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api"><div class="main-subrow keyValue "><div class=""><span class="inline-flex"><div><u><span>dependency</span><wbr></wbr><span><span>Spec</span></span></u></div></span></div><div><div class="title"><p class="paragraph">filtering requirements</p></div></div></div></div></div><hr><div class="symbol monospace"><span class="token keyword">abstract </span><span class="token keyword"></span><span class="token keyword">fun </span><a href="copy-recursive.html"><span class="token function">copyRecursive</span></a><span class="token punctuation">(</span><span class="parameters "><span class="parameter ">dependencySpec<span class="token operator">: </span><span data-unresolved-link="groovy.lang/Closure///PointingToDeclaration/">Closure</span></span></span><span class="token punctuation">)</span><span class="token operator">: </span><a href="index.html">Configuration</a><span class="clearfix"><span class="floating-right">(<a href="https://github.com/gradle/gradle/blob/e9251e572c9bd1d01e503a0dfdf43aedaeecdc3f/subprojects/core-api/src/main/java/org/gradle/api/artifacts/Configuration.java#L607">source</a>)</span></span></div><p class="paragraph">Takes a closure which gets coerced into a <a href="../../org.gradle.api.specs/-spec/index.html">Spec</a>. Behaves otherwise in the same way as <a href="copy-recursive.html">copyRecursive</a></p><p class="paragraph"> This method is only intended for use for specific situations involving resolvable configuration, it is NOT intended as a general-purpose copying mechanism.</p><span class="kdoc-tag"><h4 class="">Return</h4><p class="paragraph">copy of this configuration</p></span><h4 class="">Parameters</h4><div class="table"><div class="table-row" data-filterable-current=":docs/java_api" data-filterable-set=":docs/java_api"><div class="main-subrow keyValue "><div class=""><span class="inline-flex"><div><u><span>dependency</span><wbr></wbr><span><span>Spec</span></span></u></div></span></div><div><div class="title"><p class="paragraph">filtering requirements</p></div></div></div></div></div></div>  </div>
</div>
      <div class="footer">
        <span class="go-to-top-icon"><a href="#content" id="go-to-top-link"></a></span><span>Gradle Kotlin DSL Reference</span><span
                class="pull-right"><span>Generated by </span><a
                href="https://github.com/Kotlin/dokka"><span>dokka</span><span class="padded-icon"></span></a></span>
      </div>
    </div>
</div>
</body>
</html>
