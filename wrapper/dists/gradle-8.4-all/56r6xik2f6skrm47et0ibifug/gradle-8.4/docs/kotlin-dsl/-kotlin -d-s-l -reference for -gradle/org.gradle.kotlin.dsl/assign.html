<!DOCTYPE html>
<html>
<head>
    <meta name="viewport" content="width=device-width, initial-scale=1" charset="UTF-8">
    <title>assign</title>
    <link href="../../images/logo-icon.svg" rel="icon" type="image/svg">
    <script>var pathToRoot = "../../";</script>
    <script>const storage = localStorage.getItem("dokka-dark-mode")
    if (storage == null) {
        const osDarkSchemePreferred = window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches
        if (osDarkSchemePreferred === true) {
            document.getElementsByTagName("html")[0].classList.add("theme-dark")
        }
    } else {
        const savedDarkMode = JSON.parse(storage)
        if(savedDarkMode === true) {
            document.getElementsByTagName("html")[0].classList.add("theme-dark")
        }
    }
    </script>
<script type="text/javascript" src="../../scripts/sourceset_dependencies.js" async="async"></script>
<link href="../../styles/style.css" rel="Stylesheet">
<link href="../../styles/jetbrains-mono.css" rel="Stylesheet">
<link href="../../styles/main.css" rel="Stylesheet">
<link href="../../styles/prism.css" rel="Stylesheet">
<link href="../../styles/logo-styles.css" rel="Stylesheet">
<script type="text/javascript" src="../../scripts/clipboard.js" async="async"></script>
<script type="text/javascript" src="../../scripts/navigation-loader.js" async="async"></script>
<script type="text/javascript" src="../../scripts/platform-content-handler.js" async="async"></script>
<script type="text/javascript" src="../../scripts/main.js" defer="defer"></script>
<script type="text/javascript" src="../../scripts/prism.js" async="async"></script>
<script type="text/javascript" src="../../scripts/symbol-parameters-wrapper_deferred.js" defer="defer"></script>
<link href="../../images/gradle-logo.svg">
<link href="../../styles/gradle.css" rel="Stylesheet">
</head>
<body>
<div class="navigation-wrapper" id="navigation-wrapper">
    <div id="leftToggler"><span class="icon-toggler"></span></div>
    <div class="library-name">
            <a href="../../index.html">
                    <span>Kotlin DSL Reference for Gradle</span>
            </a>
    </div>
    <div>
8.4    </div>
    <div class="pull-right d-flex">
        <div class="filter-section" id="filter-section">
                <button class="platform-tag platform-selector jvm-like" data-active="" data-filter=":docs/kotlin_dsl">DSL</button>
        </div>
        <button id="theme-toggle-button"><span id="theme-toggle"></span></button>
        <div id="searchBar"></div>
    </div>
</div>
<div id="container">
    <div id="leftColumn">
        <div id="sideMenu"></div>
    </div>
    <div id="main">
<div class="main-content" data-page-type="member" id="content" pageIds="Kotlin DSL Reference for Gradle::org.gradle.kotlin.dsl//assign/org.gradle.api.file.ConfigurableFileCollection#org.gradle.api.file.FileCollection/PointingToDeclaration//-**********">
  <div class="breadcrumbs"><a href="../../index.html">Kotlin DSL Reference for Gradle</a><span class="delimiter">/</span><a href="index.html">org.gradle.kotlin.dsl</a><span class="delimiter">/</span><span class="current">assign</span></div>
  <div class="cover ">
    <h1 class="cover"><span><span>assign</span></span></h1>
  </div>
  <div class="platform-hinted  with-platform-tabs" data-platform-hinted="data-platform-hinted">
    <div class="platform-bookmarks-row" data-toggle-list="data-toggle-list"><button class="platform-bookmark" data-filterable-current=":docs/kotlin_dsl" data-filterable-set=":docs/kotlin_dsl" data-active="" data-toggle=":docs/kotlin_dsl">DSL</button></div>
<div class="content sourceset-dependent-content" data-active="" data-togglable=":docs/kotlin_dsl"><div class="symbol monospace"><span class="token keyword"></span><span class="token keyword">fun </span><a href="../org.gradle.api.file/-configurable-file-collection/index.html#-717165796%2FMain%2F-**********">ConfigurableFileCollection</a><span class="token punctuation">.</span><a href="assign.html"><span class="token function">assign</span></a><span class="token punctuation">(</span><span class="parameters "><span class="parameter ">fileCollection<span class="token operator">: </span><span data-unresolved-link="org.gradle.api.file/FileCollection///PointingToDeclaration/">FileCollection</span></span></span><span class="token punctuation">)</span><span class="clearfix"><span class="floating-right">(<a href="https://github.com/gradle/gradle/blob/e9251e572c9bd1d01e503a0dfdf43aedaeecdc3f/platforms/core-configuration/kotlin-dsl/src/main/kotlin/org/gradle/kotlin/dsl/ConfigurableFileCollectionExtensions.kt#L48">source</a>)</span></span></div><p class="paragraph">Sets the ConfigurableFileCollection to contain the source paths of passed collection. This is the same as calling ConfigurableFileCollection.setFrom(fileCollection: FileCollection).</p><span class="kdoc-tag"><h4 class="">Since</h4><p class="paragraph">8.2</p></span><hr><div class="symbol monospace"><span class="token keyword"></span><span class="token keyword">fun </span><span class="token operator">&lt;</span><span class="token keyword"></span><a href="assign.html">T</a><span class="token operator">&gt; </span><a href="../org.gradle.api.provider/-property/index.html#-692596081%2FMain%2F-**********">Property</a><span class="token operator">&lt;</span><span class="token keyword"></span><a href="assign.html">T</a><span class="token operator">&gt;</span><span class="token punctuation">.</span><a href="assign.html"><span class="token function">assign</span></a><span class="token punctuation">(</span><span class="parameters "><span class="parameter ">value<span class="token operator">: </span><a href="assign.html">T</a><span class="token operator">?</span></span></span><span class="token punctuation">)</span><span class="clearfix"><span class="floating-right">(<a href="https://github.com/gradle/gradle/blob/e9251e572c9bd1d01e503a0dfdf43aedaeecdc3f/platforms/core-configuration/kotlin-dsl/src/main/kotlin/org/gradle/kotlin/dsl/PropertyExtensions.kt#L33">source</a>)</span></span></div><p class="paragraph">Assign value: T to a property with assign operator</p><span class="kdoc-tag"><h4 class="">Since</h4><p class="paragraph">8.2</p></span><hr><div class="symbol monospace"><span class="token keyword"></span><span class="token keyword">fun </span><span class="token operator">&lt;</span><span class="token keyword"></span><a href="assign.html">T</a><span class="token operator">&gt; </span><a href="../org.gradle.api.provider/-property/index.html#-692596081%2FMain%2F-**********">Property</a><span class="token operator">&lt;</span><span class="token keyword"></span><a href="assign.html">T</a><span class="token operator">&gt;</span><span class="token punctuation">.</span><a href="assign.html"><span class="token function">assign</span></a><span class="token punctuation">(</span><span class="parameters "><span class="parameter ">value<span class="token operator">: </span><span data-unresolved-link="org.gradle.api.provider/Provider///PointingToDeclaration/">Provider</span><span class="token operator">&lt;</span><span class="token keyword">out </span><a href="assign.html">T</a><span class="token operator">?</span><span class="token operator">&gt;</span></span></span><span class="token punctuation">)</span><span class="clearfix"><span class="floating-right">(<a href="https://github.com/gradle/gradle/blob/e9251e572c9bd1d01e503a0dfdf43aedaeecdc3f/platforms/core-configuration/kotlin-dsl/src/main/kotlin/org/gradle/kotlin/dsl/PropertyExtensions.kt#L43">source</a>)</span></span></div><p class="paragraph">Assign value: Provider<T> to a property with assign operator</p><span class="kdoc-tag"><h4 class="">Since</h4><p class="paragraph">8.2</p></span><hr><div class="symbol monospace"><span class="token keyword"></span><span class="token keyword">fun </span><span class="token operator">&lt;</span><span class="token keyword"></span><a href="assign.html">T</a><span class="token operator"> : </span><span data-unresolved-link="org.gradle.api.file/FileSystemLocation///PointingToDeclaration/">FileSystemLocation</span><span class="token operator">&gt; </span><a href="../org.gradle.api.file/-file-system-location-property/index.html#-672163686%2FMain%2F-**********">FileSystemLocationProperty</a><span class="token operator">&lt;</span><span class="token keyword"></span><a href="assign.html">T</a><span class="token operator">&gt;</span><span class="token punctuation">.</span><a href="assign.html"><span class="token function">assign</span></a><span class="token punctuation">(</span><span class="parameters "><span class="parameter ">file<span class="token operator">: </span><a href="https://docs.oracle.com/en/java/javase/11/docs/api/java.base/java/io/File.html">File</a><span class="token operator">?</span></span></span><span class="token punctuation">)</span><span class="clearfix"><span class="floating-right">(<a href="https://github.com/gradle/gradle/blob/e9251e572c9bd1d01e503a0dfdf43aedaeecdc3f/platforms/core-configuration/kotlin-dsl/src/main/kotlin/org/gradle/kotlin/dsl/PropertyExtensions.kt#L53">source</a>)</span></span></div><p class="paragraph">Assign file to a FileSystemLocationProperty with assign operator</p><span class="kdoc-tag"><h4 class="">Since</h4><p class="paragraph">8.2</p></span><hr><div class="symbol monospace"><span class="token keyword"></span><span class="token keyword">fun </span><span class="token operator">&lt;</span><span class="token keyword"></span><a href="assign.html">T</a><span class="token operator"> : </span><span data-unresolved-link="org.gradle.api.file/FileSystemLocation///PointingToDeclaration/">FileSystemLocation</span><span class="token operator">&gt; </span><a href="../org.gradle.api.file/-file-system-location-property/index.html#-672163686%2FMain%2F-**********">FileSystemLocationProperty</a><span class="token operator">&lt;</span><span class="token keyword"></span><a href="assign.html">T</a><span class="token operator">&gt;</span><span class="token punctuation">.</span><a href="assign.html"><span class="token function">assign</span></a><span class="token punctuation">(</span><span class="parameters "><span class="parameter ">provider<span class="token operator">: </span><span data-unresolved-link="org.gradle.api.provider/Provider///PointingToDeclaration/">Provider</span><span class="token operator">&lt;</span><span class="token keyword"></span><a href="https://docs.oracle.com/en/java/javase/11/docs/api/java.base/java/io/File.html">File</a><span class="token operator">?</span><span class="token operator">&gt;</span></span></span><span class="token punctuation">)</span><span class="clearfix"><span class="floating-right">(<a href="https://github.com/gradle/gradle/blob/e9251e572c9bd1d01e503a0dfdf43aedaeecdc3f/platforms/core-configuration/kotlin-dsl/src/main/kotlin/org/gradle/kotlin/dsl/PropertyExtensions.kt#L63">source</a>)</span></span></div><p class="paragraph">Assign file provided by a Provider to a FileSystemLocationProperty with assign operator</p><span class="kdoc-tag"><h4 class="">Since</h4><p class="paragraph">8.2</p></span><hr><div class="symbol monospace"><span class="token keyword"></span><span class="token keyword">fun </span><span class="token operator">&lt;</span><span class="token keyword"></span><a href="assign.html">T</a><span class="token operator">&gt; </span><a href="../org.gradle.api.provider/-has-multiple-values/index.html#824411122%2FMain%2F-**********">HasMultipleValues</a><span class="token operator">&lt;</span><span class="token keyword"></span><a href="assign.html">T</a><span class="token operator">&gt;</span><span class="token punctuation">.</span><a href="assign.html"><span class="token function">assign</span></a><span class="token punctuation">(</span><span class="parameters "><span class="parameter ">elements<span class="token operator">: </span><a href="https://kotlinlang.org/api/latest/jvm/stdlib/kotlin.collections/-iterable/index.html">Iterable</a><span class="token operator">&lt;</span><span class="token keyword"></span><a href="assign.html">T</a><span class="token operator">?</span><span class="token operator">&gt;</span><span class="token operator">?</span></span></span><span class="token punctuation">)</span><span class="clearfix"><span class="floating-right">(<a href="https://github.com/gradle/gradle/blob/e9251e572c9bd1d01e503a0dfdf43aedaeecdc3f/platforms/core-configuration/kotlin-dsl/src/main/kotlin/org/gradle/kotlin/dsl/PropertyExtensions.kt#L73">source</a>)</span></span></div><p class="paragraph">Sets the value of the property to the elements of the given iterable, and replaces any existing value</p><span class="kdoc-tag"><h4 class="">Since</h4><p class="paragraph">8.2</p></span><hr><div class="symbol monospace"><span class="token keyword"></span><span class="token keyword">fun </span><span class="token operator">&lt;</span><span class="token keyword"></span><a href="assign.html">T</a><span class="token operator">&gt; </span><a href="../org.gradle.api.provider/-has-multiple-values/index.html#824411122%2FMain%2F-**********">HasMultipleValues</a><span class="token operator">&lt;</span><span class="token keyword"></span><a href="assign.html">T</a><span class="token operator">&gt;</span><span class="token punctuation">.</span><a href="assign.html"><span class="token function">assign</span></a><span class="token punctuation">(</span><span class="parameters "><span class="parameter ">provider<span class="token operator">: </span><span data-unresolved-link="org.gradle.api.provider/Provider///PointingToDeclaration/">Provider</span><span class="token operator">&lt;</span><span class="token keyword">out </span><a href="https://kotlinlang.org/api/latest/jvm/stdlib/kotlin.collections/-iterable/index.html">Iterable</a><span class="token operator">&lt;</span><span class="token keyword"></span><a href="assign.html">T</a><span class="token operator">?</span><span class="token operator">&gt;</span><span class="token operator">?</span><span class="token operator">&gt;</span></span></span><span class="token punctuation">)</span><span class="clearfix"><span class="floating-right">(<a href="https://github.com/gradle/gradle/blob/e9251e572c9bd1d01e503a0dfdf43aedaeecdc3f/platforms/core-configuration/kotlin-dsl/src/main/kotlin/org/gradle/kotlin/dsl/PropertyExtensions.kt#L83">source</a>)</span></span></div><div class="symbol monospace"><span class="token keyword"></span><span class="token keyword">fun </span><span class="token operator">&lt;</span><span class="token keyword"></span><a href="assign.html">K</a><span class="token punctuation">, </span><span class="token keyword"></span><a href="assign.html">V</a><span class="token operator">&gt; </span><a href="../org.gradle.api.provider/-map-property/index.html#**********%2FMain%2F-**********">MapProperty</a><span class="token operator">&lt;</span><span class="token keyword"></span><a href="assign.html">K</a><span class="token punctuation">, </span><span class="token keyword"></span><a href="assign.html">V</a><span class="token operator">&gt;</span><span class="token punctuation">.</span><a href="assign.html"><span class="token function">assign</span></a><span class="token punctuation">(</span><span class="parameters "><span class="parameter ">provider<span class="token operator">: </span><span data-unresolved-link="org.gradle.api.provider/Provider///PointingToDeclaration/">Provider</span><span class="token operator">&lt;</span><span class="token keyword">out </span><a href="https://kotlinlang.org/api/latest/jvm/stdlib/kotlin.collections/-map/index.html">Map</a><span class="token operator">&lt;</span><span class="token keyword">out </span><a href="assign.html">K</a><span class="token operator">?</span><span class="token punctuation">, </span><span class="token keyword"></span><a href="assign.html">V</a><span class="token operator">?</span><span class="token operator">&gt;</span><span class="token operator">?</span><span class="token operator">&gt;</span></span></span><span class="token punctuation">)</span><span class="clearfix"><span class="floating-right">(<a href="https://github.com/gradle/gradle/blob/e9251e572c9bd1d01e503a0dfdf43aedaeecdc3f/platforms/core-configuration/kotlin-dsl/src/main/kotlin/org/gradle/kotlin/dsl/PropertyExtensions.kt#L103">source</a>)</span></span></div><p class="paragraph">Sets the property to have the same value of the given provider, and replaces any existing value</p><span class="kdoc-tag"><h4 class="">Since</h4><p class="paragraph">8.2</p></span><hr><div class="symbol monospace"><span class="token keyword"></span><span class="token keyword">fun </span><span class="token operator">&lt;</span><span class="token keyword"></span><a href="assign.html">K</a><span class="token punctuation">, </span><span class="token keyword"></span><a href="assign.html">V</a><span class="token operator">&gt; </span><a href="../org.gradle.api.provider/-map-property/index.html#**********%2FMain%2F-**********">MapProperty</a><span class="token operator">&lt;</span><span class="token keyword"></span><a href="assign.html">K</a><span class="token punctuation">, </span><span class="token keyword"></span><a href="assign.html">V</a><span class="token operator">&gt;</span><span class="token punctuation">.</span><a href="assign.html"><span class="token function">assign</span></a><span class="token punctuation">(</span><span class="parameters "><span class="parameter ">entries<span class="token operator">: </span><a href="https://kotlinlang.org/api/latest/jvm/stdlib/kotlin.collections/-map/index.html">Map</a><span class="token operator">&lt;</span><span class="token keyword">out </span><a href="assign.html">K</a><span class="token operator">?</span><span class="token punctuation">, </span><span class="token keyword"></span><a href="assign.html">V</a><span class="token operator">?</span><span class="token operator">&gt;</span><span class="token operator">?</span></span></span><span class="token punctuation">)</span><span class="clearfix"><span class="floating-right">(<a href="https://github.com/gradle/gradle/blob/e9251e572c9bd1d01e503a0dfdf43aedaeecdc3f/platforms/core-configuration/kotlin-dsl/src/main/kotlin/org/gradle/kotlin/dsl/PropertyExtensions.kt#L93">source</a>)</span></span></div><p class="paragraph">Sets the value of this property to the entries of the given Map, and replaces any existing value</p><span class="kdoc-tag"><h4 class="">Since</h4><p class="paragraph">8.2</p></span></div>  </div>
</div>
      <div class="footer">
        <span class="go-to-top-icon"><a href="#content" id="go-to-top-link"></a></span><span>Gradle Kotlin DSL Reference</span><span
                class="pull-right"><span>Generated by </span><a
                href="https://github.com/Kotlin/dokka"><span>dokka</span><span class="padded-icon"></span></a></span>
      </div>
    </div>
</div>
</body>
</html>
