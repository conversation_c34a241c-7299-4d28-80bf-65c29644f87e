<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01//EN" "http://www.w3.org/TR/html4/strict.dtd">
<html><head><META http-equiv="Content-Type" content="text/html; charset=UTF-8"><title>GenerateMavenPom - Gradle DSL Version 8.4</title><link xmlns:xslthl="http://xslthl.sf.net" rel="stylesheet" href="https://fonts.googleapis.com/css?family=Lato:400,400i,700"><link xmlns:xslthl="http://xslthl.sf.net" crossorigin="crossorigin" href="//assets.gradle.com" rel="preconnect"><meta xmlns:xslthl="http://xslthl.sf.net" content="width=device-width, initial-scale=1" name="viewport"><link xmlns:xslthl="http://xslthl.sf.net" type="text/css" rel="stylesheet" href="base.css"><meta content="DocBook XSL Stylesheets V1.75.2" name="generator"><link rel="home" href="index.html" title="Gradle DSL Version 8.4"><link rel="up" href="index.html" title="Gradle DSL Version 8.4"></head><body><div class="layout"><header xmlns:xslthl="http://xslthl.sf.net" itemtype="https://schema.org/WPHeader" itemscope="itemscope" class="site-layout__header site-header"><nav itemtype="https://schema.org/SiteNavigationElement" itemscope="itemscope" class="site-header__navigation"><div class="site-header__navigation-header"><a title="Gradle Docs" href="https://docs.gradle.org" class="logo" target="_top"><svg xmlns="http://www.w3.org/2000/svg" version="1.1" viewBox="0 0 278 86" height="43px" width="139px"><defs><style>.cls-1 {
                                    fill: #02303a;
                                    }</style></defs><title>Gradle</title><path d="M155,56.32V70.27a18.32,18.32,0,0,1-5.59,2.83,21.82,21.82,0,0,1-6.36.89,21.08,21.08,0,0,1-7.64-1.31A17.12,17.12,0,0,1,129.59,69a16.14,16.14,0,0,1-3.73-5.58,18.78,18.78,0,0,1-1.31-7.08,19.58,19.58,0,0,1,1.26-7.14A15.68,15.68,0,0,1,135,40a20.39,20.39,0,0,1,7.45-1.29,22,22,0,0,1,3.92.33,20.43,20.43,0,0,1,3.39.92,15.16,15.16,0,0,1,2.85,1.42A17.3,17.3,0,0,1,155,43.25l-1.84,2.91a1.72,1.72,0,0,1-1.12.84,2,2,0,0,1-1.5-.34L149,45.75a10.49,10.49,0,0,0-1.75-.79,14.33,14.33,0,0,0-2.17-.54,15.29,15.29,0,0,0-2.78-.22,11.91,11.91,0,0,0-4.61.86,9.66,9.66,0,0,0-3.52,2.46,10.9,10.9,0,0,0-2.24,3.84,14.88,14.88,0,0,0-.79,5,15.23,15.23,0,0,0,.85,5.28,11.06,11.06,0,0,0,2.38,3.94A10.15,10.15,0,0,0,138.05,68a14.28,14.28,0,0,0,8.25.44,17.1,17.1,0,0,0,2.94-1.09V61.14h-4.35a1.3,1.3,0,0,1-1-.35,1.15,1.15,0,0,1-.35-.85V56.32Zm10.47-2.93a10.53,10.53,0,0,1,2.72-3.45,5.77,5.77,0,0,1,3.72-1.25,4.5,4.5,0,0,1,2.72.74l-.38,4.41a1.18,1.18,0,0,1-.34.61,1,1,0,0,1-.61.18,6.76,6.76,0,0,1-1.06-.12,8.22,8.22,0,0,0-1.38-.12,5,5,0,0,0-1.74.28,4.37,4.37,0,0,0-1.37.83,5.55,5.55,0,0,0-1.07,1.3,12.26,12.26,0,0,0-.87,1.74V73.61H160V49.14h3.45a1.94,1.94,0,0,1,1.27.32,1.9,1.9,0,0,1,.48,1.16Zm11.36-.84A14.49,14.49,0,0,1,187,48.69a9.92,9.92,0,0,1,3.84.7,8.06,8.06,0,0,1,2.86,2,8.38,8.38,0,0,1,1.78,3,11.64,11.64,0,0,1,.61,3.82V73.61h-2.68a2.64,2.64,0,0,1-1.28-.25,1.72,1.72,0,0,1-.72-1l-.52-1.77a20.25,20.25,0,0,1-1.82,1.47,10.86,10.86,0,0,1-1.83,1.06,10.36,10.36,0,0,1-2,.66,12,12,0,0,1-2.4.22,9.64,9.64,0,0,1-2.86-.41,6.28,6.28,0,0,1-2.27-1.26,5.6,5.6,0,0,1-1.48-2.07,7.38,7.38,0,0,1-.52-2.89,5.7,5.7,0,0,1,.31-1.85,5.3,5.3,0,0,1,1-1.75,8.25,8.25,0,0,1,1.83-1.57,11.17,11.17,0,0,1,2.75-1.29,23.28,23.28,0,0,1,3.81-.9,36.77,36.77,0,0,1,5-.41V58.16a5.35,5.35,0,0,0-1.05-3.64,3.83,3.83,0,0,0-3-1.18,7.3,7.3,0,0,0-2.38.33,9.39,9.39,0,0,0-1.65.75l-1.3.75a2.52,2.52,0,0,1-1.3.34,1.7,1.7,0,0,1-1.05-.32,2.61,2.61,0,0,1-.69-.76Zm13.5,10.61a31.66,31.66,0,0,0-4.3.45,11,11,0,0,0-2.79.82,3.57,3.57,0,0,0-1.5,1.17,2.89,2.89,0,0,0,.47,3.67,3.93,3.93,0,0,0,2.39.67,7,7,0,0,0,3.14-.66,9.52,9.52,0,0,0,2.59-2Zm32.53-25V73.61h-3.6a1.39,1.39,0,0,1-1.48-1.07l-.5-2.36a12.4,12.4,0,0,1-3.4,2.74,9.17,9.17,0,0,1-4.47,1,7.95,7.95,0,0,1-6.55-3.26A11.61,11.61,0,0,1,201,66.79a19.71,19.71,0,0,1-.66-5.34,16.77,16.77,0,0,1,.74-5.06,12.21,12.21,0,0,1,2.13-4,9.88,9.88,0,0,1,3.31-2.69,9.64,9.64,0,0,1,4.34-1,8.63,8.63,0,0,1,3.51.64,9,9,0,0,1,2.6,1.74V38.17ZM217,55.39a5.94,5.94,0,0,0-2.18-1.72,6.54,6.54,0,0,0-2.54-.5,5.68,5.68,0,0,0-2.41.5A4.87,4.87,0,0,0,208,55.19a7.19,7.19,0,0,0-1.17,2.57,14.83,14.83,0,0,0-.4,3.69,16.34,16.34,0,0,0,.34,3.63,7.14,7.14,0,0,0,1,2.44,3.79,3.79,0,0,0,1.58,1.36,5,5,0,0,0,2.07.41,6,6,0,0,0,3.13-.76A9.19,9.19,0,0,0,217,66.36Zm17.67-17.22V73.61h-5.89V38.17ZM245.1,62.11a11.37,11.37,0,0,0,.67,3.26,6.54,6.54,0,0,0,1.38,2.27,5.39,5.39,0,0,0,2,1.33,7.26,7.26,0,0,0,2.61.44,8.21,8.21,0,0,0,2.47-.33,11.51,11.51,0,0,0,1.81-.74c.52-.27,1-.52,1.36-.74a2.31,2.31,0,0,1,1.13-.33,1.21,1.21,0,0,1,1.1.55L261.36,70a9.45,9.45,0,0,1-2.19,1.92,12.18,12.18,0,0,1-2.54,1.24,14,14,0,0,1-2.7.66,18.78,18.78,0,0,1-2.65.19,12.93,12.93,0,0,1-4.75-.85,10.65,10.65,0,0,1-3.82-2.5,11.8,11.8,0,0,1-2.55-4.1,15.9,15.9,0,0,1-.93-5.67,13.55,13.55,0,0,1,.81-4.71,11.34,11.34,0,0,1,2.33-3.84,11,11,0,0,1,3.69-2.59,12.31,12.31,0,0,1,4.93-1,11.86,11.86,0,0,1,4.27.74,9.25,9.25,0,0,1,3.36,2.16,9.84,9.84,0,0,1,2.21,3.48,13,13,0,0,1,.8,4.71,3.82,3.82,0,0,1-.29,1.8,1.19,1.19,0,0,1-1.1.46Zm11.23-3.55A7.28,7.28,0,0,0,256,56.4a5.16,5.16,0,0,0-1-1.77,4.44,4.44,0,0,0-1.63-1.21,5.68,5.68,0,0,0-2.3-.44,5.46,5.46,0,0,0-4,1.45,7.13,7.13,0,0,0-1.87,4.13ZM112.26,14a13.72,13.72,0,0,0-19.08-.32,1.27,1.27,0,0,0-.41.93,1.31,1.31,0,0,0,.38.95l1.73,1.73a1.31,1.31,0,0,0,1.71.12,7.78,7.78,0,0,1,4.71-1.57,7.87,7.87,0,0,1,5.57,13.43C96,40.2,81.41,9.66,48.4,25.37a4.48,4.48,0,0,0-2,6.29l5.66,9.79a4.49,4.49,0,0,0,6.07,1.67l.14-.08-.11.08,2.51-1.41a57.72,57.72,0,0,0,7.91-5.89,1.37,1.37,0,0,1,1.8-.06h0a1.29,1.29,0,0,1,0,2A59.79,59.79,0,0,1,62.11,44l-.09.05-2.51,1.4a7,7,0,0,1-3.47.91,7.19,7.19,0,0,1-6.23-3.57l-5.36-9.24C34.17,40.81,27.93,54.8,31.28,72.5a1.31,1.31,0,0,0,1.29,1.06h6.09A1.3,1.3,0,0,0,40,72.42a8.94,8.94,0,0,1,17.73,0A1.3,1.3,0,0,0,59,73.56h5.94a1.31,1.31,0,0,0,1.3-1.14,8.93,8.93,0,0,1,17.72,0,1.3,1.3,0,0,0,1.29,1.14h5.87a1.3,1.3,0,0,0,1.3-1.28c.14-8.28,2.37-17.79,8.74-22.55C123.15,33.25,117.36,19.12,112.26,14ZM89.79,38.92l-4.2-2.11h0a2.64,2.64,0,1,1,4.2,2.12Z" class="cls-1"/></svg></a><div class="site-header__doc-type sr-only">DSL Reference</div><div class="site-header-version">8.4</div><button class="site-header__navigation-button hamburger" aria-label="Navigation Menu" type="button"><span class="hamburger__bar"></span><span class="hamburger__bar"></span><span class="hamburger__bar"></span></button></div><div class="site-header__navigation-collapsible site-header__navigation-collapsible--collapse"><ul class="site-header__navigation-items"><li tabindex="0" class="site-header__navigation-item site-header__navigation-submenu-section"><span class="site-header__navigation-link">
                                Community
                            </span><div class="site-header__navigation-submenu"><div itemprop="name" class="site-header__navigation-submenu-item"><a itemprop="url" href="https://gradle.org/" class="site-header__navigation-submenu-item-link" target="_top"><span class="site-header__navigation-submenu-item-link-text">Community Home</span></a></div><div itemprop="name" class="site-header__navigation-submenu-item"><a itemprop="url" href="https://discuss.gradle.org/" class="site-header__navigation-submenu-item-link" target="_top"><span class="site-header__navigation-submenu-item-link-text">Community Forums</span></a></div><div itemprop="name" class="site-header__navigation-submenu-item"><a itemprop="url" href="https://plugins.gradle.org" class="site-header__navigation-submenu-item-link" target="_top"><span class="site-header__navigation-submenu-item-link-text">Community Plugins</span></a></div></div></li><li itemprop="name" class="site-header__navigation-item"><a itemprop="url" href="https://gradle.org/training/" class="site-header__navigation-link" target="_top">Training</a></li><li tabindex="0" class="site-header__navigation-item site-header__navigation-submenu-section"><span class="site-header__navigation-link">
                                News
                            </span><div class="site-header__navigation-submenu"><div itemprop="name" class="site-header__navigation-submenu-item"><a itemprop="url" href="https://newsletter.gradle.org" class="site-header__navigation-submenu-item-link"><span class="site-header__navigation-submenu-item-link-text">Newsletter</span></a></div><div itemprop="name" class="site-header__navigation-submenu-item"><a itemprop="url" href="https://blog.gradle.org" class="site-header__navigation-submenu-item-link"><span class="site-header__navigation-submenu-item-link-text">Blog</span></a></div><div class="site-header__navigation-submenu-item"><a href="https://twitter.com/gradle" class="site-header__navigation-submenu-item-link"><span class="site-header__navigation-submenu-item-link-text">Twitter</span></a></div></div></li><li itemprop="name" class="site-header__navigation-item"><a itemprop="url" href="https://gradle.com/enterprise" class="site-header__navigation-link" target="_top">Enterprise</a></li><li class="site-header__navigation-item"><a href="https://github.com/gradle/gradle" title="Gradle on GitHub" class="site-header__navigation-link"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" height="20" width="20"><title>github</title><path fill-rule="evenodd" fill="#02303A" d="M10 0C4.477 0 0 4.477 0 10c0 4.418 2.865 8.166 6.839 9.489.5.092.682-.217.682-.482 0-.237-.008-.866-.013-1.7-2.782.603-3.369-1.342-3.369-1.342-.454-1.155-1.11-1.462-1.11-1.462-.908-.62.069-.608.069-.608 1.003.07 1.531 1.03 1.531 1.03.892 1.529 2.341 1.087 2.91.831.092-.646.35-1.086.636-1.336-2.22-.253-4.555-1.11-4.555-4.943 0-1.091.39-1.984 1.029-2.683-.103-.253-.446-1.27.098-2.647 0 0 .84-.268 2.75 1.026A9.578 9.578 0 0 1 10 4.836c.85.004 1.705.114 2.504.337 1.909-1.294 2.747-1.026 2.747-1.026.546 1.377.203 2.394.1 2.647.64.699 1.028 1.592 1.028 2.683 0 3.842-2.339 4.687-4.566 4.935.359.309.678.919.678 1.852 0 1.336-.012 2.415-.012 2.743 0 .267.18.579.688.481C17.137 18.163 20 14.418 20 10c0-5.523-4.478-10-10-10"/></svg></a></li></ul></div></nav></header><main class="main-content"><nav class="docs-navigation"><div class="search-container"><input placeholder="Search Docs" class="search-input" id="search-input" name="q" type="search"></div><ul><li><a class="reference-links" href="../userguide/userguide.html">User Manual Home</a></li><li><a class="reference-links" href="index.html">DSL Reference Home</a></li><li><a class="reference-links" href="../release-notes.html">Release Notes</a></li><ul class="sections"><li><a xmlns:xslthl="http://xslthl.sf.net" href="org.gradle.api.publish.maven.tasks.GenerateMavenPom.html#N2891F" title="Properties">Properties</a></li><li><a xmlns:xslthl="http://xslthl.sf.net" href="org.gradle.api.publish.maven.tasks.GenerateMavenPom.html#N28A4E" title="Methods">Methods</a></li></ul><li><h3>Build script blocks</h3></li><li><a class="link" href="org.gradle.api.Project.html#org.gradle.api.Project:allprojects(groovy.lang.Closure)"><code class="literal">allprojects { }</code></a></li><li><a class="link" href="org.gradle.api.Project.html#org.gradle.api.Project:artifacts(groovy.lang.Closure)"><code class="literal">artifacts { }</code></a></li><li><a class="link" href="org.gradle.api.Project.html#org.gradle.api.Project:buildscript(groovy.lang.Closure)"><code class="literal">buildscript { }</code></a></li><li><a class="link" href="org.gradle.api.Project.html#org.gradle.api.Project:configurations(groovy.lang.Closure)"><code class="literal">configurations { }</code></a></li><li><a class="link" href="org.gradle.api.Project.html#org.gradle.api.Project:dependencies(groovy.lang.Closure)"><code class="literal">dependencies { }</code></a></li><li><a class="link" href="org.gradle.api.Project.html#org.gradle.api.Project:repositories(groovy.lang.Closure)"><code class="literal">repositories { }</code></a></li><li><a class="link" href="org.gradle.api.Project.html#org.gradle.api.Project:sourceSets(groovy.lang.Closure)"><code class="literal">sourceSets { }</code></a></li><li><a class="link" href="org.gradle.api.Project.html#org.gradle.api.Project:subprojects(groovy.lang.Closure)"><code class="literal">subprojects { }</code></a></li><li><a class="link" href="org.gradle.api.Project.html#org.gradle.api.Project:publishing(groovy.lang.Closure)"><code class="literal">publishing { }</code></a></li><li><h3>Core types</h3></li><li><a class="link" href="org.gradle.api.Project.html"><code class="literal">Project</code></a></li><li><a class="link" href="org.gradle.api.Task.html"><code class="literal">Task</code></a></li><li><a class="link" href="org.gradle.api.invocation.Gradle.html"><code class="literal">Gradle</code></a></li><li><a class="link" href="org.gradle.api.initialization.Settings.html"><code class="literal">Settings</code></a></li><li><a class="link" href="org.gradle.api.initialization.IncludedBuild.html"><code class="literal">IncludedBuild</code></a></li><li><a class="link" href="org.gradle.api.file.ProjectLayout.html"><code class="literal">ProjectLayout</code></a></li><li><a class="link" href="org.gradle.api.Script.html"><code class="literal">Script</code></a></li><li><a class="link" href="org.gradle.api.tasks.SourceSet.html"><code class="literal">SourceSet</code></a></li><li><a class="link" href="org.gradle.api.tasks.SourceSetOutput.html"><code class="literal">SourceSetOutput</code></a></li><li><a class="link" href="org.gradle.api.file.SourceDirectorySet.html"><code class="literal">SourceDirectorySet</code></a></li><li><a class="link" href="org.gradle.api.artifacts.Configuration.html"><code class="literal">Configuration</code></a></li><li><a class="link" href="org.gradle.api.artifacts.ConsumableConfiguration.html"><code class="literal">ConsumableConfiguration</code></a></li><li><a class="link" href="org.gradle.api.artifacts.ResolvableConfiguration.html"><code class="literal">ResolvableConfiguration</code></a></li><li><a class="link" href="org.gradle.api.artifacts.DependencyScopeConfiguration.html"><code class="literal">DependencyScopeConfiguration</code></a></li><li><a class="link" href="org.gradle.api.artifacts.ResolutionStrategy.html"><code class="literal">ResolutionStrategy</code></a></li><li><a class="link" href="org.gradle.api.artifacts.query.ArtifactResolutionQuery.html"><code class="literal">ArtifactResolutionQuery</code></a></li><li><a class="link" href="org.gradle.api.artifacts.ComponentSelection.html"><code class="literal">ComponentSelection</code></a></li><li><a class="link" href="org.gradle.api.artifacts.ComponentSelectionRules.html"><code class="literal">ComponentSelectionRules</code></a></li><li><a class="link" href="org.gradle.api.artifacts.dsl.DependencyAdder.html"><code class="literal">DependencyAdder</code></a></li><li><a class="link" href="org.gradle.api.plugins.ExtensionAware.html"><code class="literal">ExtensionAware</code></a></li><li><a class="link" href="org.gradle.api.plugins.ExtraPropertiesExtension.html"><code class="literal">ExtraPropertiesExtension</code></a></li><li><a class="link" href="org.gradle.plugin.use.PluginDependenciesSpec.html"><code class="literal">PluginDependenciesSpec</code></a></li><li><a class="link" href="org.gradle.plugin.use.PluginDependencySpec.html"><code class="literal">PluginDependencySpec</code></a></li><li><a class="link" href="org.gradle.plugin.management.PluginManagementSpec.html"><code class="literal">PluginManagementSpec</code></a></li><li><a class="link" href="org.gradle.api.provider.ProviderFactory.html"><code class="literal">ProviderFactory</code></a></li><li><a class="link" href="org.gradle.api.resources.ResourceHandler.html"><code class="literal">ResourceHandler</code></a></li><li><a class="link" href="org.gradle.api.resources.TextResourceFactory.html"><code class="literal">TextResourceFactory</code></a></li><li><a class="link" href="org.gradle.work.InputChanges.html"><code class="literal">InputChanges</code></a></li><li><a class="link" href="org.gradle.api.distribution.Distribution.html"><code class="literal">Distribution</code></a></li><li><h3>Publishing types</h3></li><li><a class="link" href="org.gradle.api.publish.PublishingExtension.html"><code class="literal">PublishingExtension</code></a></li><li><a class="link" href="org.gradle.api.publish.ivy.IvyPublication.html"><code class="literal">IvyPublication</code></a></li><li><a class="link" href="org.gradle.api.publish.ivy.IvyArtifact.html"><code class="literal">IvyArtifact</code></a></li><li><a class="link" href="org.gradle.api.publish.ivy.IvyArtifactSet.html"><code class="literal">IvyArtifactSet</code></a></li><li><a class="link" href="org.gradle.api.publish.ivy.IvyModuleDescriptorSpec.html"><code class="literal">IvyModuleDescriptorSpec</code></a></li><li><a class="link" href="org.gradle.api.publish.ivy.IvyModuleDescriptorAuthor.html"><code class="literal">IvyModuleDescriptorAuthor</code></a></li><li><a class="link" href="org.gradle.api.publish.ivy.IvyModuleDescriptorLicense.html"><code class="literal">IvyModuleDescriptorLicense</code></a></li><li><a class="link" href="org.gradle.api.publish.ivy.IvyModuleDescriptorDescription.html"><code class="literal">IvyModuleDescriptorDescription</code></a></li><li><a class="link" href="org.gradle.api.publish.maven.MavenPublication.html"><code class="literal">MavenPublication</code></a></li><li><a class="link" href="org.gradle.api.publish.maven.MavenArtifact.html"><code class="literal">MavenArtifact</code></a></li><li><a class="link" href="org.gradle.api.publish.maven.MavenArtifactSet.html"><code class="literal">MavenArtifactSet</code></a></li><li><a class="link" href="org.gradle.api.publish.maven.MavenPom.html"><code class="literal">MavenPom</code></a></li><li><a class="link" href="org.gradle.api.publish.maven.MavenPomCiManagement.html"><code class="literal">MavenPomCiManagement</code></a></li><li><a class="link" href="org.gradle.api.publish.maven.MavenPomContributor.html"><code class="literal">MavenPomContributor</code></a></li><li><a class="link" href="org.gradle.api.publish.maven.MavenPomContributorSpec.html"><code class="literal">MavenPomContributorSpec</code></a></li><li><a class="link" href="org.gradle.api.publish.maven.MavenPomDeveloper.html"><code class="literal">MavenPomDeveloper</code></a></li><li><a class="link" href="org.gradle.api.publish.maven.MavenPomDeveloperSpec.html"><code class="literal">MavenPomDeveloperSpec</code></a></li><li><a class="link" href="org.gradle.api.publish.maven.MavenPomDistributionManagement.html"><code class="literal">MavenPomDistributionManagement</code></a></li><li><a class="link" href="org.gradle.api.publish.maven.MavenPomIssueManagement.html"><code class="literal">MavenPomIssueManagement</code></a></li><li><a class="link" href="org.gradle.api.publish.maven.MavenPomLicense.html"><code class="literal">MavenPomLicense</code></a></li><li><a class="link" href="org.gradle.api.publish.maven.MavenPomLicenseSpec.html"><code class="literal">MavenPomLicenseSpec</code></a></li><li><a class="link" href="org.gradle.api.publish.maven.MavenPomMailingList.html"><code class="literal">MavenPomMailingList</code></a></li><li><a class="link" href="org.gradle.api.publish.maven.MavenPomMailingListSpec.html"><code class="literal">MavenPomMailingListSpec</code></a></li><li><a class="link" href="org.gradle.api.publish.maven.MavenPomOrganization.html"><code class="literal">MavenPomOrganization</code></a></li><li><a class="link" href="org.gradle.api.publish.maven.MavenPomRelocation.html"><code class="literal">MavenPomRelocation</code></a></li><li><a class="link" href="org.gradle.api.publish.maven.MavenPomScm.html"><code class="literal">MavenPomScm</code></a></li><li><h3>Container types</h3></li><li><a class="link" href="org.gradle.api.tasks.TaskContainer.html"><code class="literal">TaskContainer</code></a></li><li><a class="link" href="org.gradle.api.artifacts.ConfigurationContainer.html"><code class="literal">ConfigurationContainer</code></a></li><li><a class="link" href="org.gradle.api.artifacts.dsl.RepositoryHandler.html"><code class="literal">RepositoryHandler</code></a></li><li><a class="link" href="org.gradle.api.artifacts.dsl.DependencyHandler.html"><code class="literal">DependencyHandler</code></a></li><li><a class="link" href="org.gradle.api.artifacts.dsl.ComponentMetadataHandler.html"><code class="literal">ComponentMetadataHandler</code></a></li><li><a class="link" href="org.gradle.api.artifacts.dsl.ArtifactHandler.html"><code class="literal">ArtifactHandler</code></a></li><li><h3>Build Cache types</h3></li><li><a class="link" href="org.gradle.caching.configuration.BuildCacheConfiguration.html"><code class="literal">BuildCacheConfiguration</code></a></li><li><a class="link" href="org.gradle.caching.local.DirectoryBuildCache.html"><code class="literal">DirectoryBuildCache</code></a></li><li><a class="link" href="org.gradle.caching.http.HttpBuildCache.html"><code class="literal">HttpBuildCache</code></a></li><li><h3>Input Normalization types</h3></li><li><a class="link" href="org.gradle.normalization.InputNormalizationHandler.html"><code class="literal">InputNormalizationHandler</code></a></li><li><a class="link" href="org.gradle.normalization.InputNormalization.html"><code class="literal">InputNormalization</code></a></li><li><a class="link" href="org.gradle.normalization.RuntimeClasspathNormalization.html"><code class="literal">RuntimeClasspathNormalization</code></a></li><li><h3>Help Task types</h3></li><li><a class="link" href="org.gradle.api.tasks.diagnostics.TaskReportTask.html"><code class="literal">TaskReportTask</code></a></li><li><a class="link" href="org.gradle.api.tasks.diagnostics.ProjectReportTask.html"><code class="literal">ProjectReportTask</code></a></li><li><a class="link" href="org.gradle.api.tasks.diagnostics.DependencyReportTask.html"><code class="literal">DependencyReportTask</code></a></li><li><a class="link" href="org.gradle.api.tasks.diagnostics.DependencyInsightReportTask.html"><code class="literal">DependencyInsightReportTask</code></a></li><li><a class="link" href="org.gradle.api.tasks.diagnostics.PropertyReportTask.html"><code class="literal">PropertyReportTask</code></a></li><li><a class="link" href="org.gradle.api.reporting.components.ComponentReport.html"><code class="literal">ComponentReport</code></a></li><li><a class="link" href="org.gradle.api.reporting.dependents.DependentComponentsReport.html"><code class="literal">DependentComponentsReport</code></a></li><li><a class="link" href="org.gradle.api.reporting.model.ModelReport.html"><code class="literal">ModelReport</code></a></li><li><a class="link" href="org.gradle.api.tasks.diagnostics.OutgoingVariantsReportTask.html"><code class="literal">OutgoingVariantsReportTask</code></a></li><li><a class="link" href="org.gradle.api.tasks.diagnostics.ResolvableConfigurationsReportTask.html"><code class="literal">ResolvableConfigurationsReportTask</code></a></li><li><h3>Task types</h3></li><li><a class="link" href="org.gradle.api.plugins.antlr.AntlrTask.html"><code class="literal">AntlrTask</code></a></li><li><a class="link" href="org.gradle.api.tasks.diagnostics.BuildEnvironmentReportTask.html"><code class="literal">BuildEnvironmentReportTask</code></a></li><li><a class="link" href="org.gradle.api.plugins.quality.Checkstyle.html"><code class="literal">Checkstyle</code></a></li><li><a class="link" href="org.gradle.api.plugins.quality.CodeNarc.html"><code class="literal">CodeNarc</code></a></li><li><a class="link" href="org.gradle.api.tasks.Copy.html"><code class="literal">Copy</code></a></li><li><a class="link" href="org.gradle.jvm.application.tasks.CreateStartScripts.html"><code class="literal">CreateStartScripts</code></a></li><li><a class="link" href="org.gradle.api.tasks.Delete.html"><code class="literal">Delete</code></a></li><li><a class="link" href="org.gradle.plugins.ear.Ear.html"><code class="literal">Ear</code></a></li><li><a class="link" href="org.gradle.api.tasks.Exec.html"><code class="literal">Exec</code></a></li><li><a class="link" href="org.gradle.api.publish.ivy.tasks.GenerateIvyDescriptor.html"><code class="literal">GenerateIvyDescriptor</code></a></li><li><a class="link" href="org.gradle.api.publish.maven.tasks.GenerateMavenPom.html"><code class="literal">GenerateMavenPom</code></a></li><li><a class="link" href="org.gradle.api.reporting.GenerateBuildDashboard.html"><code class="literal">GenerateBuildDashboard</code></a></li><li><a class="link" href="org.gradle.api.tasks.GradleBuild.html"><code class="literal">GradleBuild</code></a></li><li><a class="link" href="org.gradle.api.tasks.compile.GroovyCompile.html"><code class="literal">GroovyCompile</code></a></li><li><a class="link" href="org.gradle.api.tasks.javadoc.Groovydoc.html"><code class="literal">Groovydoc</code></a></li><li><a class="link" href="org.gradle.api.reporting.dependencies.HtmlDependencyReportTask.html"><code class="literal">HtmlDependencyReportTask</code></a></li><li><a class="link" href="org.gradle.testing.jacoco.tasks.JacocoReport.html"><code class="literal">JacocoReport</code></a></li><li><a class="link" href="org.gradle.testing.jacoco.tasks.JacocoCoverageVerification.html"><code class="literal">JacocoCoverageVerification</code></a></li><li><a class="link" href="org.gradle.api.tasks.bundling.Jar.html"><code class="literal">Jar</code></a></li><li><a class="link" href="org.gradle.api.tasks.compile.JavaCompile.html"><code class="literal">JavaCompile</code></a></li><li><a class="link" href="org.gradle.api.tasks.javadoc.Javadoc.html"><code class="literal">Javadoc</code></a></li><li><a class="link" href="org.gradle.api.tasks.JavaExec.html"><code class="literal">JavaExec</code></a></li><li><a class="link" href="org.gradle.api.plugins.quality.Pmd.html"><code class="literal">Pmd</code></a></li><li><a class="link" href="org.gradle.language.jvm.tasks.ProcessResources.html"><code class="literal">ProcessResources</code></a></li><li><a class="link" href="org.gradle.api.publish.ivy.tasks.PublishToIvyRepository.html"><code class="literal">PublishToIvyRepository</code></a></li><li><a class="link" href="org.gradle.api.publish.maven.tasks.PublishToMavenRepository.html"><code class="literal">PublishToMavenRepository</code></a></li><li><a class="link" href="org.gradle.api.tasks.scala.ScalaCompile.html"><code class="literal">ScalaCompile</code></a></li><li><a class="link" href="org.gradle.api.tasks.scala.ScalaDoc.html"><code class="literal">ScalaDoc</code></a></li><li><a class="link" href="org.gradle.buildinit.tasks.InitBuild.html"><code class="literal">InitBuild</code></a></li><li><a class="link" href="org.gradle.plugins.signing.Sign.html"><code class="literal">Sign</code></a></li><li><a class="link" href="org.gradle.api.tasks.Sync.html"><code class="literal">Sync</code></a></li><li><a class="link" href="org.gradle.api.tasks.bundling.Tar.html"><code class="literal">Tar</code></a></li><li><a class="link" href="org.gradle.api.tasks.testing.AbstractTestTask.html"><code class="literal">AbstractTestTask</code></a></li><li><a class="link" href="org.gradle.api.tasks.testing.Test.html"><code class="literal">Test</code></a></li><li><a class="link" href="org.gradle.api.tasks.testing.TestReport.html"><code class="literal">TestReport</code></a></li><li><a class="link" href="org.gradle.api.tasks.bundling.War.html"><code class="literal">War</code></a></li><li><a class="link" href="org.gradle.api.tasks.wrapper.Wrapper.html"><code class="literal">Wrapper</code></a></li><li><a class="link" href="org.gradle.api.tasks.WriteProperties.html"><code class="literal">WriteProperties</code></a></li><li><a class="link" href="org.gradle.api.tasks.bundling.Zip.html"><code class="literal">Zip</code></a></li><li><h3>Test types</h3></li><li><a class="link" href="org.gradle.testing.base.TestingExtension.html"><code class="literal">TestingExtension</code></a></li><li><a class="link" href="org.gradle.testing.base.TestSuite.html"><code class="literal">TestSuite</code></a></li><li><a class="link" href="org.gradle.api.plugins.jvm.JvmTestSuite.html"><code class="literal">JvmTestSuite</code></a></li><li><a class="link" href="org.gradle.testing.base.TestSuiteTarget.html"><code class="literal">TestSuiteTarget</code></a></li><li><a class="link" href="org.gradle.api.plugins.jvm.JvmTestSuiteTarget.html"><code class="literal">JvmTestSuiteTarget</code></a></li><li><a class="link" href="org.gradle.api.tasks.testing.Test.html"><code class="literal">Test</code></a></li><li><a class="link" href="org.gradle.api.artifacts.dsl.Dependencies.html"><code class="literal">Dependencies</code></a></li><li><a class="link" href="org.gradle.api.artifacts.dsl.GradleDependencies.html"><code class="literal">GradleDependencies</code></a></li><li><a class="link" href="org.gradle.api.plugins.jvm.TestFixturesDependencyModifiers.html"><code class="literal">TestFixturesDependencyModifiers</code></a></li><li><a class="link" href="org.gradle.api.plugins.jvm.PlatformDependencyModifiers.html"><code class="literal">PlatformDependencyModifiers</code></a></li><li><a class="link" href="org.gradle.api.plugins.jvm.JvmComponentDependencies.html"><code class="literal">JvmComponentDependencies</code></a></li><li><h3>Reporting types</h3></li><li><a class="link" href="org.gradle.api.reporting.CustomizableHtmlReport.html"><code class="literal">CustomizableHtmlReport</code></a></li><li><a class="link" href="org.gradle.api.reporting.SingleFileReport.html"><code class="literal">SingleFileReport</code></a></li><li><a class="link" href="org.gradle.api.reporting.DirectoryReport.html"><code class="literal">DirectoryReport</code></a></li><li><a class="link" href="org.gradle.api.reporting.Report.html"><code class="literal">Report</code></a></li><li><a class="link" href="org.gradle.api.reporting.Reporting.html"><code class="literal">Reporting</code></a></li><li><a class="link" href="org.gradle.api.reporting.ReportContainer.html"><code class="literal">ReportContainer</code></a></li><li><a class="link" href="org.gradle.api.reporting.ReportingExtension.html"><code class="literal">ReportingExtension</code></a></li><li><a class="link" href="org.gradle.api.tasks.testing.AggregateTestReport.html"><code class="literal">AggregateTestReport</code></a></li><li><a class="link" href="org.gradle.testing.jacoco.plugins.JacocoCoverageReport.html"><code class="literal">JacocoCoverageReport</code></a></li><li><h3>Eclipse/IDEA model types</h3></li><li><a class="link" href="org.gradle.plugins.ide.eclipse.model.EclipseModel.html"><code class="literal">EclipseModel</code></a></li><li><a class="link" href="org.gradle.plugins.ide.eclipse.model.EclipseProject.html"><code class="literal">EclipseProject</code></a></li><li><a class="link" href="org.gradle.plugins.ide.eclipse.model.EclipseClasspath.html"><code class="literal">EclipseClasspath</code></a></li><li><a class="link" href="org.gradle.plugins.ide.eclipse.model.EclipseJdt.html"><code class="literal">EclipseJdt</code></a></li><li><a class="link" href="org.gradle.plugins.ide.eclipse.model.EclipseWtp.html"><code class="literal">EclipseWtp</code></a></li><li><a class="link" href="org.gradle.plugins.ide.eclipse.model.EclipseWtpComponent.html"><code class="literal">EclipseWtpComponent</code></a></li><li><a class="link" href="org.gradle.plugins.ide.eclipse.model.EclipseWtpFacet.html"><code class="literal">EclipseWtpFacet</code></a></li><li><a class="link" href="org.gradle.plugins.ide.idea.model.IdeaModel.html"><code class="literal">IdeaModel</code></a></li><li><a class="link" href="org.gradle.plugins.ide.idea.model.IdeaProject.html"><code class="literal">IdeaProject</code></a></li><li><a class="link" href="org.gradle.plugins.ide.idea.model.IdeaModule.html"><code class="literal">IdeaModule</code></a></li><li><a class="link" href="org.gradle.plugins.ide.idea.model.IdeaWorkspace.html"><code class="literal">IdeaWorkspace</code></a></li><li><a class="link" href="org.gradle.plugins.ide.api.XmlFileContentMerger.html"><code class="literal">XmlFileContentMerger</code></a></li><li><a class="link" href="org.gradle.plugins.ide.api.FileContentMerger.html"><code class="literal">FileContentMerger</code></a></li><li><h3>Eclipse/IDEA task types</h3></li><li><a class="link" href="org.gradle.plugins.ide.eclipse.GenerateEclipseProject.html"><code class="literal">GenerateEclipseProject</code></a></li><li><a class="link" href="org.gradle.plugins.ide.eclipse.GenerateEclipseClasspath.html"><code class="literal">GenerateEclipseClasspath</code></a></li><li><a class="link" href="org.gradle.plugins.ide.eclipse.GenerateEclipseJdt.html"><code class="literal">GenerateEclipseJdt</code></a></li><li><a class="link" href="org.gradle.plugins.ide.eclipse.GenerateEclipseWtpComponent.html"><code class="literal">GenerateEclipseWtpComponent</code></a></li><li><a class="link" href="org.gradle.plugins.ide.eclipse.GenerateEclipseWtpFacet.html"><code class="literal">GenerateEclipseWtpFacet</code></a></li><li><a class="link" href="org.gradle.plugins.ide.idea.GenerateIdeaModule.html"><code class="literal">GenerateIdeaModule</code></a></li><li><a class="link" href="org.gradle.plugins.ide.idea.GenerateIdeaProject.html"><code class="literal">GenerateIdeaProject</code></a></li><li><a class="link" href="org.gradle.plugins.ide.idea.GenerateIdeaWorkspace.html"><code class="literal">GenerateIdeaWorkspace</code></a></li><li><h3>Xcode task types</h3></li><li><a class="link" href="org.gradle.ide.xcode.tasks.GenerateSchemeFileTask.html"><code class="literal">GenerateSchemeFileTask</code></a></li><li><a class="link" href="org.gradle.ide.xcode.tasks.GenerateWorkspaceSettingsFileTask.html"><code class="literal">GenerateWorkspaceSettingsFileTask</code></a></li><li><a class="link" href="org.gradle.ide.xcode.tasks.GenerateXcodeProjectFileTask.html"><code class="literal">GenerateXcodeProjectFileTask</code></a></li><li><a class="link" href="org.gradle.ide.xcode.tasks.GenerateXcodeWorkspaceFileTask.html"><code class="literal">GenerateXcodeWorkspaceFileTask</code></a></li><li><h3>Visual Studio task types</h3></li><li><a class="link" href="org.gradle.ide.visualstudio.tasks.GenerateSolutionFileTask.html"><code class="literal">GenerateSolutionFileTask</code></a></li><li><a class="link" href="org.gradle.ide.visualstudio.tasks.GenerateProjectFileTask.html"><code class="literal">GenerateProjectFileTask</code></a></li><li><a class="link" href="org.gradle.ide.visualstudio.tasks.GenerateFiltersFileTask.html"><code class="literal">GenerateFiltersFileTask</code></a></li><li><h3>Artifact transform types</h3></li><li><a class="link" href="org.gradle.api.artifacts.transform.TransformAction.html"><code class="literal">TransformAction</code></a></li><li><a class="link" href="org.gradle.api.artifacts.transform.TransformOutputs.html"><code class="literal">TransformOutputs</code></a></li><li><a class="link" href="org.gradle.api.artifacts.transform.TransformSpec.html"><code class="literal">TransformSpec</code></a></li><li><h3>Native tool chain types</h3></li><li><a class="link" href="org.gradle.nativeplatform.toolchain.Gcc.html"><code class="literal">Gcc</code></a></li><li><a class="link" href="org.gradle.nativeplatform.toolchain.Clang.html"><code class="literal">Clang</code></a></li><li><a class="link" href="org.gradle.nativeplatform.toolchain.VisualCpp.html"><code class="literal">VisualCpp</code></a></li><li><a class="link" href="org.gradle.nativeplatform.toolchain.Swiftc.html"><code class="literal">Swiftc</code></a></li><li><h3>C++ component types</h3></li><li><a class="link" href="org.gradle.language.cpp.CppApplication.html"><code class="literal">CppApplication</code></a></li><li><a class="link" href="org.gradle.language.cpp.CppLibrary.html"><code class="literal">CppLibrary</code></a></li><li><a class="link" href="org.gradle.nativeplatform.test.cpp.CppTestSuite.html"><code class="literal">CppTestSuite</code></a></li><li><h3>Swift component types</h3></li><li><a class="link" href="org.gradle.language.swift.SwiftApplication.html"><code class="literal">SwiftApplication</code></a></li><li><a class="link" href="org.gradle.language.swift.SwiftLibrary.html"><code class="literal">SwiftLibrary</code></a></li><li><a class="link" href="org.gradle.nativeplatform.test.xctest.SwiftXCTestSuite.html"><code class="literal">SwiftXCTestSuite</code></a></li><li><h3>Native component task types</h3></li><li><a class="link" href="org.gradle.language.cpp.tasks.CppCompile.html"><code class="literal">CppCompile</code></a></li><li><a class="link" href="org.gradle.language.swift.tasks.SwiftCompile.html"><code class="literal">SwiftCompile</code></a></li><li><a class="link" href="org.gradle.nativeplatform.tasks.LinkExecutable.html"><code class="literal">LinkExecutable</code></a></li><li><a class="link" href="org.gradle.nativeplatform.tasks.LinkSharedLibrary.html"><code class="literal">LinkSharedLibrary</code></a></li><li><a class="link" href="org.gradle.nativeplatform.tasks.CreateStaticLibrary.html"><code class="literal">CreateStaticLibrary</code></a></li><li><a class="link" href="org.gradle.nativeplatform.tasks.LinkMachOBundle.html"><code class="literal">LinkMachOBundle</code></a></li><li><a class="link" href="org.gradle.nativeplatform.tasks.InstallExecutable.html"><code class="literal">InstallExecutable</code></a></li><li><a class="link" href="org.gradle.nativeplatform.test.xctest.tasks.InstallXCTestBundle.html"><code class="literal">InstallXCTestBundle</code></a></li><li><a class="link" href="org.gradle.nativeplatform.test.tasks.RunTestExecutable.html"><code class="literal">RunTestExecutable</code></a></li><li><a class="link" href="org.gradle.nativeplatform.test.xctest.tasks.XCTest.html"><code class="literal">XCTest</code></a></li></ul></nav><div class="content"><div id="content"><div class="chapter"><div class="titlepage"><div><div><h1 xmlns:xslthl="http://xslthl.sf.net"><a name="org.gradle.api.publish.maven.tasks.GenerateMavenPom"></a>GenerateMavenPom</h1></div></div></div><div class="toc"><p><b>Table of Contents</b></p><dl><dt><span class="section"><a href="org.gradle.api.publish.maven.tasks.GenerateMavenPom.html#N2891F">Properties</a></span></dt><dt><span class="section"><a href="org.gradle.api.publish.maven.tasks.GenerateMavenPom.html#N28A4E">Methods</a></span></dt><dt><span class="section"><a href="org.gradle.api.publish.maven.tasks.GenerateMavenPom.html#N28B36">Script blocks</a></span></dt><dt><span class="section"><a href="org.gradle.api.publish.maven.tasks.GenerateMavenPom.html#N28B3B">Property details</a></span></dt><dt><span class="section"><a href="org.gradle.api.publish.maven.tasks.GenerateMavenPom.html#N28D02">Method details</a></span></dt></dl></div><div class="segmentedlist"><table><tr><th>API Documentation:</th><td><a class="ulink" href="../javadoc/org/gradle/api/publish/maven/tasks/GenerateMavenPom.html" target="_top"><code class="classname">GenerateMavenPom</code></a></td></tr></table></div><p>Generates a Maven module descriptor (POM) file.</p><div class="section"><div class="titlepage"><div><div><h3 class="title"><a xmlns:xslthl="http://xslthl.sf.net" name="N2891F" class="section-anchor" href="#N2891F"></a>Properties</h3></div></div></div><div xmlns:xslthl="http://xslthl.sf.net" class="table"><div class="table-contents"><table id="N28922"><thead><tr><td>Property</td><td>Description</td></tr></thead><tr><td><a class="link" href="org.gradle.api.publish.maven.tasks.GenerateMavenPom.html#org.gradle.api.publish.maven.tasks.GenerateMavenPom:actions"><code class="literal">actions</code></a></td><td><p>The sequence of <a class="ulink" href="../javadoc/org/gradle/api/Action.html" target="_top"><code class="classname">Action</code></a> objects which will be executed by this task, in the order of
execution.</p></td></tr><tr><td><a class="link" href="org.gradle.api.publish.maven.tasks.GenerateMavenPom.html#org.gradle.api.publish.maven.tasks.GenerateMavenPom:ant"><code class="literal">ant</code></a></td><td><p>The <code class="literal">AntBuilder</code> for this task.  You can use this in your build file to execute ant
tasks.</p></td></tr><tr><td><a class="link" href="org.gradle.api.publish.maven.tasks.GenerateMavenPom.html#org.gradle.api.publish.maven.tasks.GenerateMavenPom:convention"><code class="literal">convention</code></a></td><td><div class="caution" title="Caution">Deprecated</div><p>The <a class="ulink" href="../javadoc/org/gradle/api/plugins/Convention.html" target="_top"><code class="classname">Convention</code></a> object for this task. A <a class="ulink" href="../javadoc/org/gradle/api/Plugin.html" target="_top"><code class="classname">Plugin</code></a> can use the convention object to
contribute properties and methods to this task.</p></td></tr><tr><td><a class="link" href="org.gradle.api.publish.maven.tasks.GenerateMavenPom.html#org.gradle.api.publish.maven.tasks.GenerateMavenPom:dependsOn"><code class="literal">dependsOn</code></a></td><td><p>The dependencies of this task.</p></td></tr><tr><td><a class="link" href="org.gradle.api.publish.maven.tasks.GenerateMavenPom.html#org.gradle.api.publish.maven.tasks.GenerateMavenPom:description"><code class="literal">description</code></a></td><td><p>The description of this task.</p></td></tr><tr><td><a class="link" href="org.gradle.api.publish.maven.tasks.GenerateMavenPom.html#org.gradle.api.publish.maven.tasks.GenerateMavenPom:destination"><code class="literal">destination</code></a></td><td><p>The file the POM will be written to.</p></td></tr><tr><td><a class="link" href="org.gradle.api.publish.maven.tasks.GenerateMavenPom.html#org.gradle.api.publish.maven.tasks.GenerateMavenPom:destroyables"><code class="literal">destroyables</code></a></td><td><p>The destroyables of this task.</p></td></tr><tr><td><a class="link" href="org.gradle.api.publish.maven.tasks.GenerateMavenPom.html#org.gradle.api.publish.maven.tasks.GenerateMavenPom:didWork"><code class="literal">didWork</code></a></td><td><p>Checks if the task actually did any work.  Even if a Task executes, it may determine that it has nothing to
do.  For example, a compilation task may determine that source files have not changed since the last time a the
task was run.</p></td></tr><tr><td><a class="link" href="org.gradle.api.publish.maven.tasks.GenerateMavenPom.html#org.gradle.api.publish.maven.tasks.GenerateMavenPom:enabled"><code class="literal">enabled</code></a></td><td><p>Returns if this task is enabled or not.</p></td></tr><tr><td><a class="link" href="org.gradle.api.publish.maven.tasks.GenerateMavenPom.html#org.gradle.api.publish.maven.tasks.GenerateMavenPom:extensions"><code class="literal">extensions</code></a></td><td><p>The container of extensions.</p></td></tr><tr><td><a class="link" href="org.gradle.api.publish.maven.tasks.GenerateMavenPom.html#org.gradle.api.publish.maven.tasks.GenerateMavenPom:finalizedBy"><code class="literal">finalizedBy</code></a></td><td><p>Returns tasks that finalize this task.</p></td></tr><tr><td><a class="link" href="org.gradle.api.publish.maven.tasks.GenerateMavenPom.html#org.gradle.api.publish.maven.tasks.GenerateMavenPom:group"><code class="literal">group</code></a></td><td><p>The task group which this task belongs to. The task group is used in reports and user interfaces to
group related tasks together when presenting a list of tasks to the user.</p></td></tr><tr><td><a class="link" href="org.gradle.api.publish.maven.tasks.GenerateMavenPom.html#org.gradle.api.publish.maven.tasks.GenerateMavenPom:inputs"><code class="literal">inputs</code></a></td><td><p>The inputs of this task.</p></td></tr><tr><td><a class="link" href="org.gradle.api.publish.maven.tasks.GenerateMavenPom.html#org.gradle.api.publish.maven.tasks.GenerateMavenPom:localState"><code class="literal">localState</code></a></td><td><p>The local state of this task.</p></td></tr><tr><td><a class="link" href="org.gradle.api.publish.maven.tasks.GenerateMavenPom.html#org.gradle.api.publish.maven.tasks.GenerateMavenPom:logger"><code class="literal">logger</code></a></td><td><p>The logger for this task. You can use this in your build file to write log messages.</p></td></tr><tr><td><a class="link" href="org.gradle.api.publish.maven.tasks.GenerateMavenPom.html#org.gradle.api.publish.maven.tasks.GenerateMavenPom:logging"><code class="literal">logging</code></a></td><td><p>The <a class="ulink" href="../javadoc/org/gradle/api/logging/LoggingManager.html" target="_top"><code class="classname">LoggingManager</code></a> which can be used to receive logging and to control the
standard output/error capture for this task. By default, System.out is redirected to the Gradle logging system at
the QUIET log level, and System.err is redirected at the ERROR log level.</p></td></tr><tr><td><a class="link" href="org.gradle.api.publish.maven.tasks.GenerateMavenPom.html#org.gradle.api.publish.maven.tasks.GenerateMavenPom:mustRunAfter"><code class="literal">mustRunAfter</code></a></td><td><p>Returns tasks that this task must run after.</p></td></tr><tr><td><a class="link" href="org.gradle.api.publish.maven.tasks.GenerateMavenPom.html#org.gradle.api.publish.maven.tasks.GenerateMavenPom:name"><code class="literal">name</code></a></td><td><p>The name of this task. The name uniquely identifies the task within its <a class="ulink" href="../dsl/org.gradle.api.Project.html" target="_top"><code class="classname">Project</code></a>.</p></td></tr><tr><td><a class="link" href="org.gradle.api.publish.maven.tasks.GenerateMavenPom.html#org.gradle.api.publish.maven.tasks.GenerateMavenPom:outputs"><code class="literal">outputs</code></a></td><td><p>The outputs of this task.</p></td></tr><tr><td><a class="link" href="org.gradle.api.publish.maven.tasks.GenerateMavenPom.html#org.gradle.api.publish.maven.tasks.GenerateMavenPom:path"><code class="literal">path</code></a></td><td><p>The path of the task, which is a fully qualified name for the task. The path of a task is the path of
its <a class="ulink" href="../dsl/org.gradle.api.Project.html" target="_top"><code class="classname">Project</code></a> plus the name of the task, separated by <code class="literal">:</code>.</p></td></tr><tr><td><a class="link" href="org.gradle.api.publish.maven.tasks.GenerateMavenPom.html#org.gradle.api.publish.maven.tasks.GenerateMavenPom:pom"><code class="literal">pom</code></a></td><td><p>The Maven POM.</p></td></tr><tr><td><a class="link" href="org.gradle.api.publish.maven.tasks.GenerateMavenPom.html#org.gradle.api.publish.maven.tasks.GenerateMavenPom:project"><code class="literal">project</code></a></td><td><p>The <a class="ulink" href="../dsl/org.gradle.api.Project.html" target="_top"><code class="classname">Project</code></a> which this task belongs to.</p></td></tr><tr><td><a class="link" href="org.gradle.api.publish.maven.tasks.GenerateMavenPom.html#org.gradle.api.publish.maven.tasks.GenerateMavenPom:shouldRunAfter"><code class="literal">shouldRunAfter</code></a></td><td><p>Returns tasks that this task should run after.</p></td></tr><tr><td><a class="link" href="org.gradle.api.publish.maven.tasks.GenerateMavenPom.html#org.gradle.api.publish.maven.tasks.GenerateMavenPom:state"><code class="literal">state</code></a></td><td><p>The execution state of this task. This provides information about the execution of this task, such as
whether it has executed, been skipped, has failed, etc.</p></td></tr><tr><td><a class="link" href="org.gradle.api.publish.maven.tasks.GenerateMavenPom.html#org.gradle.api.publish.maven.tasks.GenerateMavenPom:taskDependencies"><code class="literal">taskDependencies</code></a></td><td><p>Returns a <a class="ulink" href="../javadoc/org/gradle/api/tasks/TaskDependency.html" target="_top"><code class="classname">TaskDependency</code></a> which contains all the tasks that this task depends on.</p></td></tr><tr><td><a class="link" href="org.gradle.api.publish.maven.tasks.GenerateMavenPom.html#org.gradle.api.publish.maven.tasks.GenerateMavenPom:temporaryDir"><code class="literal">temporaryDir</code></a></td><td><p>Returns a directory which this task can use to write temporary files to. Each task instance is provided with a
separate temporary directory. There are no guarantees that the contents of this directory will be kept beyond the
execution of the task.</p></td></tr><tr><td><a class="link" href="org.gradle.api.publish.maven.tasks.GenerateMavenPom.html#org.gradle.api.publish.maven.tasks.GenerateMavenPom:timeout"><code class="literal">timeout</code></a></td><td><p>The timeout of this task.</p></td></tr></table></div></div></div><div class="section"><div class="titlepage"><div><div><h3 class="title"><a xmlns:xslthl="http://xslthl.sf.net" name="N28A4E" class="section-anchor" href="#N28A4E"></a>Methods</h3></div></div></div><div xmlns:xslthl="http://xslthl.sf.net" class="table"><div class="table-contents"><table id="N28A51"><thead><tr><td>Method</td><td>Description</td></tr></thead><tr><td><code class="literal"><a class="link" href="org.gradle.api.publish.maven.tasks.GenerateMavenPom.html#org.gradle.api.publish.maven.tasks.GenerateMavenPom:dependsOn(java.lang.Object[])">dependsOn</a>(paths)</code></td><td><p>Adds the given dependencies to this task. See <a class="link" href="org.gradle.api.Task.html#org.gradle.api.Task.dependencies">here</a> for a description of the types
of objects which can be used as task dependencies.</p></td></tr><tr><td><code class="literal"><a class="link" href="org.gradle.api.publish.maven.tasks.GenerateMavenPom.html#org.gradle.api.publish.maven.tasks.GenerateMavenPom:doFirst(groovy.lang.Closure)">doFirst</a>(action)</code></td><td><p>Adds the given closure to the beginning of this task's action list. The closure is passed this task as a
parameter when executed.</p></td></tr><tr><td><code class="literal"><a class="link" href="org.gradle.api.publish.maven.tasks.GenerateMavenPom.html#org.gradle.api.publish.maven.tasks.GenerateMavenPom:doFirst(java.lang.String, org.gradle.api.Action)">doFirst</a>(actionName, action)</code></td><td><p>Adds the given <a class="ulink" href="../javadoc/org/gradle/api/Action.html" target="_top"><code class="classname">Action</code></a> to the beginning of this task's action list.</p></td></tr><tr><td><code class="literal"><a class="link" href="org.gradle.api.publish.maven.tasks.GenerateMavenPom.html#org.gradle.api.publish.maven.tasks.GenerateMavenPom:doFirst(org.gradle.api.Action)">doFirst</a>(action)</code></td><td><p>Adds the given <a class="ulink" href="../javadoc/org/gradle/api/Action.html" target="_top"><code class="classname">Action</code></a> to the beginning of this task's action list.</p></td></tr><tr><td><code class="literal"><a class="link" href="org.gradle.api.publish.maven.tasks.GenerateMavenPom.html#org.gradle.api.publish.maven.tasks.GenerateMavenPom:doLast(groovy.lang.Closure)">doLast</a>(action)</code></td><td><p>Adds the given closure to the end of this task's action list.  The closure is passed this task as a parameter
when executed.</p></td></tr><tr><td><code class="literal"><a class="link" href="org.gradle.api.publish.maven.tasks.GenerateMavenPom.html#org.gradle.api.publish.maven.tasks.GenerateMavenPom:doLast(java.lang.String, org.gradle.api.Action)">doLast</a>(actionName, action)</code></td><td><p>Adds the given <a class="ulink" href="../javadoc/org/gradle/api/Action.html" target="_top"><code class="classname">Action</code></a> to the end of this task's action list.</p></td></tr><tr><td><code class="literal"><a class="link" href="org.gradle.api.publish.maven.tasks.GenerateMavenPom.html#org.gradle.api.publish.maven.tasks.GenerateMavenPom:doLast(org.gradle.api.Action)">doLast</a>(action)</code></td><td><p>Adds the given <a class="ulink" href="../javadoc/org/gradle/api/Action.html" target="_top"><code class="classname">Action</code></a> to the end of this task's action list.</p></td></tr><tr><td><code class="literal"><a class="link" href="org.gradle.api.publish.maven.tasks.GenerateMavenPom.html#org.gradle.api.publish.maven.tasks.GenerateMavenPom:doNotTrackState(java.lang.String)">doNotTrackState</a>(reasonNotToTrackState)</code></td><td><p>Do not track the state of the task.

</p></td></tr><tr><td><code class="literal"><a class="link" href="org.gradle.api.publish.maven.tasks.GenerateMavenPom.html#org.gradle.api.publish.maven.tasks.GenerateMavenPom:finalizedBy(java.lang.Object[])">finalizedBy</a>(paths)</code></td><td><p>Adds the given finalizer tasks for this task.</p></td></tr><tr><td><code class="literal"><a class="link" href="org.gradle.api.publish.maven.tasks.GenerateMavenPom.html#org.gradle.api.publish.maven.tasks.GenerateMavenPom:hasProperty(java.lang.String)">hasProperty</a>(propertyName)</code></td><td><p>Determines if this task has the given property. See <a class="link" href="org.gradle.api.Task.html#org.gradle.api.Task.properties">here</a> for details of the
properties which are available for a task.</p></td></tr><tr><td><code class="literal"><a class="link" href="org.gradle.api.publish.maven.tasks.GenerateMavenPom.html#org.gradle.api.publish.maven.tasks.GenerateMavenPom:mustRunAfter(java.lang.Object[])">mustRunAfter</a>(paths)</code></td><td><p>Specifies that this task must run after all of the supplied tasks.</p></td></tr><tr><td><code class="literal"><a class="link" href="org.gradle.api.publish.maven.tasks.GenerateMavenPom.html#org.gradle.api.publish.maven.tasks.GenerateMavenPom:onlyIf(groovy.lang.Closure)">onlyIf</a>(onlyIfClosure)</code></td><td><p>Execute the task only if the given closure returns true.  The closure will be evaluated at task execution
time, not during configuration.  The closure will be passed a single parameter, this task. If the closure returns
false, the task will be skipped.</p></td></tr><tr><td><code class="literal"><a class="link" href="org.gradle.api.publish.maven.tasks.GenerateMavenPom.html#org.gradle.api.publish.maven.tasks.GenerateMavenPom:onlyIf(java.lang.String, org.gradle.api.specs.Spec)">onlyIf</a>(onlyIfReason, onlyIfSpec)</code></td><td><div class="caution" title="Caution">Incubating</div><p>Execute the task only if the given spec is satisfied. The spec will be evaluated at task execution time, not
during configuration. If the Spec is not satisfied, the task will be skipped.</p></td></tr><tr><td><code class="literal"><a class="link" href="org.gradle.api.publish.maven.tasks.GenerateMavenPom.html#org.gradle.api.publish.maven.tasks.GenerateMavenPom:onlyIf(org.gradle.api.specs.Spec)">onlyIf</a>(onlyIfSpec)</code></td><td><p>Execute the task only if the given spec is satisfied. The spec will be evaluated at task execution time, not
during configuration. If the Spec is not satisfied, the task will be skipped.</p></td></tr><tr><td><code class="literal"><a class="link" href="org.gradle.api.publish.maven.tasks.GenerateMavenPom.html#org.gradle.api.publish.maven.tasks.GenerateMavenPom:property(java.lang.String)">property</a>(propertyName)</code></td><td><p>Returns the value of the given property of this task.  This method locates a property as follows:</p></td></tr><tr><td><code class="literal"><a class="link" href="org.gradle.api.publish.maven.tasks.GenerateMavenPom.html#org.gradle.api.publish.maven.tasks.GenerateMavenPom:setProperty(java.lang.String, java.lang.Object)">setProperty</a>(name, value)</code></td><td><p>Sets a property of this task.  This method searches for a property with the given name in the following
locations, and sets the property on the first location where it finds the property.</p></td></tr><tr><td><code class="literal"><a class="link" href="org.gradle.api.publish.maven.tasks.GenerateMavenPom.html#org.gradle.api.publish.maven.tasks.GenerateMavenPom:shouldRunAfter(java.lang.Object[])">shouldRunAfter</a>(paths)</code></td><td><p>Specifies that this task should run after all of the supplied tasks.</p></td></tr><tr><td><code class="literal"><a class="link" href="org.gradle.api.publish.maven.tasks.GenerateMavenPom.html#org.gradle.api.publish.maven.tasks.GenerateMavenPom:usesService(org.gradle.api.provider.Provider)">usesService</a>(service)</code></td><td><p>Registers a <a class="ulink" href="../javadoc/org/gradle/api/services/BuildService.html" target="_top"><code class="classname">BuildService</code></a> that is used by this task so
<a class="ulink" href="../javadoc/org/gradle/api/services/BuildServiceRegistration.html#getMaxParallelUsages--" target="_top"><code class="classname">BuildServiceRegistration.getMaxParallelUsages()</code></a> can be honored.</p></td></tr></table></div></div></div><div class="section"><div class="titlepage"><div><div><h3 class="title"><a xmlns:xslthl="http://xslthl.sf.net" name="N28B36" class="section-anchor" href="#N28B36"></a>Script blocks</h3></div></div></div><p>No script blocks</p></div><div class="section"><div class="titlepage"><div><div><h3 class="title"><a xmlns:xslthl="http://xslthl.sf.net" name="N28B3B" class="section-anchor" href="#N28B3B"></a>Property details</h3></div></div></div><div class="section"><div class="titlepage"><div><div><h4 class="signature"><a xmlns:xslthl="http://xslthl.sf.net" name="org.gradle.api.publish.maven.tasks.GenerateMavenPom:actions" class="section-anchor" href="#org.gradle.api.publish.maven.tasks.GenerateMavenPom:actions"></a><code class="classname"><a class="ulink" href="https://docs.oracle.com/javase/8/docs/api/java/util/List.html" target="_top"><code class="classname">List</code></a>&lt;<a class="ulink" href="../javadoc/org/gradle/api/Action.html" target="_top"><code class="classname">Action</code></a>&lt;? super <a class="ulink" href="../dsl/org.gradle.api.Task.html" target="_top"><code class="classname">Task</code></a>&gt;&gt;</code> <code class="literal">actions</code></h4></div></div></div><p>The sequence of <a class="ulink" href="../javadoc/org/gradle/api/Action.html" target="_top"><code class="classname">Action</code></a> objects which will be executed by this task, in the order of
execution.</p></div><div class="section"><div class="titlepage"><div><div><h4 class="signature"><a xmlns:xslthl="http://xslthl.sf.net" name="org.gradle.api.publish.maven.tasks.GenerateMavenPom:ant" class="section-anchor" href="#org.gradle.api.publish.maven.tasks.GenerateMavenPom:ant"></a><a class="ulink" href="../javadoc/org/gradle/api/AntBuilder.html" target="_top"><code class="classname">AntBuilder</code></a> <code class="literal">ant</code> (read-only)</h4></div></div></div><p>The <code class="literal">AntBuilder</code> for this task.  You can use this in your build file to execute ant
tasks.</p></div><div class="section"><div class="titlepage"><div><div><h4 class="signature"><a xmlns:xslthl="http://xslthl.sf.net" name="org.gradle.api.publish.maven.tasks.GenerateMavenPom:convention" class="section-anchor" href="#org.gradle.api.publish.maven.tasks.GenerateMavenPom:convention"></a><a class="ulink" href="../javadoc/org/gradle/api/plugins/Convention.html" target="_top"><code class="classname">Convention</code></a> <code class="literal">convention</code> (read-only)</h4></div></div></div><div class="caution" title="Caution"><p>Note: This property is <a class="ulink" href="../userguide/feature_lifecycle.html" target="_top">deprecated</a> and will be removed in the next major version of Gradle.</p></div><p>The <a class="ulink" href="../javadoc/org/gradle/api/plugins/Convention.html" target="_top"><code class="classname">Convention</code></a> object for this task. A <a class="ulink" href="../javadoc/org/gradle/api/Plugin.html" target="_top"><code class="classname">Plugin</code></a> can use the convention object to
contribute properties and methods to this task.</p></div><div class="section"><div class="titlepage"><div><div><h4 class="signature"><a xmlns:xslthl="http://xslthl.sf.net" name="org.gradle.api.publish.maven.tasks.GenerateMavenPom:dependsOn" class="section-anchor" href="#org.gradle.api.publish.maven.tasks.GenerateMavenPom:dependsOn"></a><code class="classname"><a class="ulink" href="https://docs.oracle.com/javase/8/docs/api/java/util/Set.html" target="_top"><code class="classname">Set</code></a>&lt;<a class="ulink" href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html" target="_top"><code class="classname">Object</code></a>&gt;</code> <code class="literal">dependsOn</code></h4></div></div></div><p>The dependencies of this task.</p></div><div class="section"><div class="titlepage"><div><div><h4 class="signature"><a xmlns:xslthl="http://xslthl.sf.net" name="org.gradle.api.publish.maven.tasks.GenerateMavenPom:description" class="section-anchor" href="#org.gradle.api.publish.maven.tasks.GenerateMavenPom:description"></a><a class="ulink" href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html" target="_top"><code class="classname">String</code></a> <code class="literal">description</code></h4></div></div></div><p>The description of this task.</p></div><div class="section"><div class="titlepage"><div><div><h4 class="signature"><a xmlns:xslthl="http://xslthl.sf.net" name="org.gradle.api.publish.maven.tasks.GenerateMavenPom:destination" class="section-anchor" href="#org.gradle.api.publish.maven.tasks.GenerateMavenPom:destination"></a><a class="ulink" href="https://docs.oracle.com/javase/8/docs/api/java/io/File.html" target="_top"><code class="classname">File</code></a> <code class="literal">destination</code></h4></div></div></div><p>The file the POM will be written to.</p></div><div class="section"><div class="titlepage"><div><div><h4 class="signature"><a xmlns:xslthl="http://xslthl.sf.net" name="org.gradle.api.publish.maven.tasks.GenerateMavenPom:destroyables" class="section-anchor" href="#org.gradle.api.publish.maven.tasks.GenerateMavenPom:destroyables"></a><a class="ulink" href="../javadoc/org/gradle/api/tasks/TaskDestroyables.html" target="_top"><code class="classname">TaskDestroyables</code></a> <code class="literal">destroyables</code> (read-only)</h4></div></div></div><p>The destroyables of this task.</p></div><div class="section"><div class="titlepage"><div><div><h4 class="signature"><a xmlns:xslthl="http://xslthl.sf.net" name="org.gradle.api.publish.maven.tasks.GenerateMavenPom:didWork" class="section-anchor" href="#org.gradle.api.publish.maven.tasks.GenerateMavenPom:didWork"></a><code class="classname">boolean</code> <code class="literal">didWork</code></h4></div></div></div><p>Checks if the task actually did any work.  Even if a Task executes, it may determine that it has nothing to
do.  For example, a compilation task may determine that source files have not changed since the last time a the
task was run.</p></div><div class="section"><div class="titlepage"><div><div><h4 class="signature"><a xmlns:xslthl="http://xslthl.sf.net" name="org.gradle.api.publish.maven.tasks.GenerateMavenPom:enabled" class="section-anchor" href="#org.gradle.api.publish.maven.tasks.GenerateMavenPom:enabled"></a><code class="classname">boolean</code> <code class="literal">enabled</code></h4></div></div></div><p>Returns if this task is enabled or not.</p></div><div class="section"><div class="titlepage"><div><div><h4 class="signature"><a xmlns:xslthl="http://xslthl.sf.net" name="org.gradle.api.publish.maven.tasks.GenerateMavenPom:extensions" class="section-anchor" href="#org.gradle.api.publish.maven.tasks.GenerateMavenPom:extensions"></a><a class="ulink" href="../javadoc/org/gradle/api/plugins/ExtensionContainer.html" target="_top"><code class="classname">ExtensionContainer</code></a> <code class="literal">extensions</code> (read-only)</h4></div></div></div><p>The container of extensions.</p></div><div class="section"><div class="titlepage"><div><div><h4 class="signature"><a xmlns:xslthl="http://xslthl.sf.net" name="org.gradle.api.publish.maven.tasks.GenerateMavenPom:finalizedBy" class="section-anchor" href="#org.gradle.api.publish.maven.tasks.GenerateMavenPom:finalizedBy"></a><a class="ulink" href="../javadoc/org/gradle/api/tasks/TaskDependency.html" target="_top"><code class="classname">TaskDependency</code></a> <code class="literal">finalizedBy</code></h4></div></div></div><p>Returns tasks that finalize this task.</p></div><div class="section"><div class="titlepage"><div><div><h4 class="signature"><a xmlns:xslthl="http://xslthl.sf.net" name="org.gradle.api.publish.maven.tasks.GenerateMavenPom:group" class="section-anchor" href="#org.gradle.api.publish.maven.tasks.GenerateMavenPom:group"></a><a class="ulink" href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html" target="_top"><code class="classname">String</code></a> <code class="literal">group</code></h4></div></div></div><p>The task group which this task belongs to. The task group is used in reports and user interfaces to
group related tasks together when presenting a list of tasks to the user.</p></div><div class="section"><div class="titlepage"><div><div><h4 class="signature"><a xmlns:xslthl="http://xslthl.sf.net" name="org.gradle.api.publish.maven.tasks.GenerateMavenPom:inputs" class="section-anchor" href="#org.gradle.api.publish.maven.tasks.GenerateMavenPom:inputs"></a><a class="ulink" href="../javadoc/org/gradle/api/tasks/TaskInputs.html" target="_top"><code class="classname">TaskInputs</code></a> <code class="literal">inputs</code> (read-only)</h4></div></div></div><p>The inputs of this task.</p></div><div class="section"><div class="titlepage"><div><div><h4 class="signature"><a xmlns:xslthl="http://xslthl.sf.net" name="org.gradle.api.publish.maven.tasks.GenerateMavenPom:localState" class="section-anchor" href="#org.gradle.api.publish.maven.tasks.GenerateMavenPom:localState"></a><a class="ulink" href="../javadoc/org/gradle/api/tasks/TaskLocalState.html" target="_top"><code class="classname">TaskLocalState</code></a> <code class="literal">localState</code> (read-only)</h4></div></div></div><p>The local state of this task.</p></div><div class="section"><div class="titlepage"><div><div><h4 class="signature"><a xmlns:xslthl="http://xslthl.sf.net" name="org.gradle.api.publish.maven.tasks.GenerateMavenPom:logger" class="section-anchor" href="#org.gradle.api.publish.maven.tasks.GenerateMavenPom:logger"></a><a class="ulink" href="../javadoc/org/gradle/api/logging/Logger.html" target="_top"><code class="classname">Logger</code></a> <code class="literal">logger</code> (read-only)</h4></div></div></div><p>The logger for this task. You can use this in your build file to write log messages.</p></div><div class="section"><div class="titlepage"><div><div><h4 class="signature"><a xmlns:xslthl="http://xslthl.sf.net" name="org.gradle.api.publish.maven.tasks.GenerateMavenPom:logging" class="section-anchor" href="#org.gradle.api.publish.maven.tasks.GenerateMavenPom:logging"></a><a class="ulink" href="../javadoc/org/gradle/api/logging/LoggingManager.html" target="_top"><code class="classname">LoggingManager</code></a> <code class="literal">logging</code> (read-only)</h4></div></div></div><p>The <a class="ulink" href="../javadoc/org/gradle/api/logging/LoggingManager.html" target="_top"><code class="classname">LoggingManager</code></a> which can be used to receive logging and to control the
standard output/error capture for this task. By default, System.out is redirected to the Gradle logging system at
the QUIET log level, and System.err is redirected at the ERROR log level.</p></div><div class="section"><div class="titlepage"><div><div><h4 class="signature"><a xmlns:xslthl="http://xslthl.sf.net" name="org.gradle.api.publish.maven.tasks.GenerateMavenPom:mustRunAfter" class="section-anchor" href="#org.gradle.api.publish.maven.tasks.GenerateMavenPom:mustRunAfter"></a><a class="ulink" href="../javadoc/org/gradle/api/tasks/TaskDependency.html" target="_top"><code class="classname">TaskDependency</code></a> <code class="literal">mustRunAfter</code></h4></div></div></div><p>Returns tasks that this task must run after.</p></div><div class="section"><div class="titlepage"><div><div><h4 class="signature"><a xmlns:xslthl="http://xslthl.sf.net" name="org.gradle.api.publish.maven.tasks.GenerateMavenPom:name" class="section-anchor" href="#org.gradle.api.publish.maven.tasks.GenerateMavenPom:name"></a><a class="ulink" href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html" target="_top"><code class="classname">String</code></a> <code class="literal">name</code> (read-only)</h4></div></div></div><p>The name of this task. The name uniquely identifies the task within its <a class="ulink" href="../dsl/org.gradle.api.Project.html" target="_top"><code class="classname">Project</code></a>.</p></div><div class="section"><div class="titlepage"><div><div><h4 class="signature"><a xmlns:xslthl="http://xslthl.sf.net" name="org.gradle.api.publish.maven.tasks.GenerateMavenPom:outputs" class="section-anchor" href="#org.gradle.api.publish.maven.tasks.GenerateMavenPom:outputs"></a><a class="ulink" href="../javadoc/org/gradle/api/tasks/TaskOutputs.html" target="_top"><code class="classname">TaskOutputs</code></a> <code class="literal">outputs</code> (read-only)</h4></div></div></div><p>The outputs of this task.</p></div><div class="section"><div class="titlepage"><div><div><h4 class="signature"><a xmlns:xslthl="http://xslthl.sf.net" name="org.gradle.api.publish.maven.tasks.GenerateMavenPom:path" class="section-anchor" href="#org.gradle.api.publish.maven.tasks.GenerateMavenPom:path"></a><a class="ulink" href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html" target="_top"><code class="classname">String</code></a> <code class="literal">path</code> (read-only)</h4></div></div></div><p>The path of the task, which is a fully qualified name for the task. The path of a task is the path of
its <a class="ulink" href="../dsl/org.gradle.api.Project.html" target="_top"><code class="classname">Project</code></a> plus the name of the task, separated by <code class="literal">:</code>.</p></div><div class="section"><div class="titlepage"><div><div><h4 class="signature"><a xmlns:xslthl="http://xslthl.sf.net" name="org.gradle.api.publish.maven.tasks.GenerateMavenPom:pom" class="section-anchor" href="#org.gradle.api.publish.maven.tasks.GenerateMavenPom:pom"></a><a class="ulink" href="../dsl/org.gradle.api.publish.maven.MavenPom.html" target="_top"><code class="classname">MavenPom</code></a> <code class="literal">pom</code></h4></div></div></div><p>The Maven POM.</p></div><div class="section"><div class="titlepage"><div><div><h4 class="signature"><a xmlns:xslthl="http://xslthl.sf.net" name="org.gradle.api.publish.maven.tasks.GenerateMavenPom:project" class="section-anchor" href="#org.gradle.api.publish.maven.tasks.GenerateMavenPom:project"></a><a class="ulink" href="../dsl/org.gradle.api.Project.html" target="_top"><code class="classname">Project</code></a> <code class="literal">project</code> (read-only)</h4></div></div></div><p>The <a class="ulink" href="../dsl/org.gradle.api.Project.html" target="_top"><code class="classname">Project</code></a> which this task belongs to.</p><p>Calling this method from a task action is not supported when configuration caching is enabled.</p></div><div class="section"><div class="titlepage"><div><div><h4 class="signature"><a xmlns:xslthl="http://xslthl.sf.net" name="org.gradle.api.publish.maven.tasks.GenerateMavenPom:shouldRunAfter" class="section-anchor" href="#org.gradle.api.publish.maven.tasks.GenerateMavenPom:shouldRunAfter"></a><a class="ulink" href="../javadoc/org/gradle/api/tasks/TaskDependency.html" target="_top"><code class="classname">TaskDependency</code></a> <code class="literal">shouldRunAfter</code></h4></div></div></div><p>Returns tasks that this task should run after.</p></div><div class="section"><div class="titlepage"><div><div><h4 class="signature"><a xmlns:xslthl="http://xslthl.sf.net" name="org.gradle.api.publish.maven.tasks.GenerateMavenPom:state" class="section-anchor" href="#org.gradle.api.publish.maven.tasks.GenerateMavenPom:state"></a><a class="ulink" href="../javadoc/org/gradle/api/tasks/TaskState.html" target="_top"><code class="classname">TaskState</code></a> <code class="literal">state</code> (read-only)</h4></div></div></div><p>The execution state of this task. This provides information about the execution of this task, such as
whether it has executed, been skipped, has failed, etc.</p></div><div class="section"><div class="titlepage"><div><div><h4 class="signature"><a xmlns:xslthl="http://xslthl.sf.net" name="org.gradle.api.publish.maven.tasks.GenerateMavenPom:taskDependencies" class="section-anchor" href="#org.gradle.api.publish.maven.tasks.GenerateMavenPom:taskDependencies"></a><a class="ulink" href="../javadoc/org/gradle/api/tasks/TaskDependency.html" target="_top"><code class="classname">TaskDependency</code></a> <code class="literal">taskDependencies</code> (read-only)</h4></div></div></div><p>Returns a <a class="ulink" href="../javadoc/org/gradle/api/tasks/TaskDependency.html" target="_top"><code class="classname">TaskDependency</code></a> which contains all the tasks that this task depends on.</p><p>Calling this method from a task action is not supported when configuration caching is enabled.</p></div><div class="section"><div class="titlepage"><div><div><h4 class="signature"><a xmlns:xslthl="http://xslthl.sf.net" name="org.gradle.api.publish.maven.tasks.GenerateMavenPom:temporaryDir" class="section-anchor" href="#org.gradle.api.publish.maven.tasks.GenerateMavenPom:temporaryDir"></a><a class="ulink" href="https://docs.oracle.com/javase/8/docs/api/java/io/File.html" target="_top"><code class="classname">File</code></a> <code class="literal">temporaryDir</code> (read-only)</h4></div></div></div><p>Returns a directory which this task can use to write temporary files to. Each task instance is provided with a
separate temporary directory. There are no guarantees that the contents of this directory will be kept beyond the
execution of the task.</p></div><div class="section"><div class="titlepage"><div><div><h4 class="signature"><a xmlns:xslthl="http://xslthl.sf.net" name="org.gradle.api.publish.maven.tasks.GenerateMavenPom:timeout" class="section-anchor" href="#org.gradle.api.publish.maven.tasks.GenerateMavenPom:timeout"></a><code class="classname"><a class="ulink" href="../javadoc/org/gradle/api/provider/Property.html" target="_top"><code class="classname">Property</code></a>&lt;<a class="ulink" href="https://docs.oracle.com/javase/8/docs/api/java/time/Duration.html" target="_top"><code class="classname">Duration</code></a>&gt;</code> <code class="literal">timeout</code></h4></div></div></div><p>The timeout of this task.</p><pre class="programlisting">
task myTask {
    timeout = Duration.ofMinutes(<span xmlns:xslthl="http://xslthl.sf.net" class="hl-number">10</span>)
}
</pre><p>
The Thread executing this task will be interrupted if the task takes longer than the specified amount of time to run.
In order for a task to work properly with this feature, it needs to react to interrupts and must clean up any resources it opened.
</p><p>By default, tasks never time out.</p></div></div><div class="section"><div class="titlepage"><div><div><h3 class="title"><a xmlns:xslthl="http://xslthl.sf.net" name="N28D02" class="section-anchor" href="#N28D02"></a>Method details</h3></div></div></div><div class="section"><div class="titlepage"><div><div><h4 class="signature"><a xmlns:xslthl="http://xslthl.sf.net" name="org.gradle.api.publish.maven.tasks.GenerateMavenPom:dependsOn(java.lang.Object[])" class="section-anchor" href="#org.gradle.api.publish.maven.tasks.GenerateMavenPom:dependsOn(java.lang.Object[])"></a><a class="ulink" href="../dsl/org.gradle.api.Task.html" target="_top"><code class="classname">Task</code></a> <code class="literal">dependsOn</code>(<code class="classname"><a class="ulink" href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html" target="_top"><code class="classname">Object</code></a>...</code> paths)</h4></div></div></div><p>Adds the given dependencies to this task. See <a class="link" href="org.gradle.api.Task.html#org.gradle.api.Task.dependencies">here</a> for a description of the types
of objects which can be used as task dependencies.</p></div><div class="section"><div class="titlepage"><div><div><h4 class="signature"><a xmlns:xslthl="http://xslthl.sf.net" name="org.gradle.api.publish.maven.tasks.GenerateMavenPom:doFirst(groovy.lang.Closure)" class="section-anchor" href="#org.gradle.api.publish.maven.tasks.GenerateMavenPom:doFirst(groovy.lang.Closure)"></a><a class="ulink" href="../dsl/org.gradle.api.Task.html" target="_top"><code class="classname">Task</code></a> <code class="literal">doFirst</code>(<a class="ulink" href="https://docs.groovy-lang.org/3.0.17/html/gapi/groovy/lang/Closure.html" target="_top"><code class="classname">Closure</code></a> action)</h4></div></div></div><p>Adds the given closure to the beginning of this task's action list. The closure is passed this task as a
parameter when executed.</p></div><div class="section"><div class="titlepage"><div><div><h4 class="signature"><a xmlns:xslthl="http://xslthl.sf.net" name="org.gradle.api.publish.maven.tasks.GenerateMavenPom:doFirst(java.lang.String, org.gradle.api.Action)" class="section-anchor" href="#org.gradle.api.publish.maven.tasks.GenerateMavenPom:doFirst(java.lang.String, org.gradle.api.Action)"></a><a class="ulink" href="../dsl/org.gradle.api.Task.html" target="_top"><code class="classname">Task</code></a> <code class="literal">doFirst</code>(<a class="ulink" href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html" target="_top"><code class="classname">String</code></a> actionName, <code class="classname"><a class="ulink" href="../javadoc/org/gradle/api/Action.html" target="_top"><code class="classname">Action</code></a>&lt;? super <a class="ulink" href="../dsl/org.gradle.api.Task.html" target="_top"><code class="classname">Task</code></a>&gt;</code> action)</h4></div></div></div><p>Adds the given <a class="ulink" href="../javadoc/org/gradle/api/Action.html" target="_top"><code class="classname">Action</code></a> to the beginning of this task's action list.</p></div><div class="section"><div class="titlepage"><div><div><h4 class="signature"><a xmlns:xslthl="http://xslthl.sf.net" name="org.gradle.api.publish.maven.tasks.GenerateMavenPom:doFirst(org.gradle.api.Action)" class="section-anchor" href="#org.gradle.api.publish.maven.tasks.GenerateMavenPom:doFirst(org.gradle.api.Action)"></a><a class="ulink" href="../dsl/org.gradle.api.Task.html" target="_top"><code class="classname">Task</code></a> <code class="literal">doFirst</code>(<code class="classname"><a class="ulink" href="../javadoc/org/gradle/api/Action.html" target="_top"><code class="classname">Action</code></a>&lt;? super <a class="ulink" href="../dsl/org.gradle.api.Task.html" target="_top"><code class="classname">Task</code></a>&gt;</code> action)</h4></div></div></div><p>Adds the given <a class="ulink" href="../javadoc/org/gradle/api/Action.html" target="_top"><code class="classname">Action</code></a> to the beginning of this task's action list.</p></div><div class="section"><div class="titlepage"><div><div><h4 class="signature"><a xmlns:xslthl="http://xslthl.sf.net" name="org.gradle.api.publish.maven.tasks.GenerateMavenPom:doLast(groovy.lang.Closure)" class="section-anchor" href="#org.gradle.api.publish.maven.tasks.GenerateMavenPom:doLast(groovy.lang.Closure)"></a><a class="ulink" href="../dsl/org.gradle.api.Task.html" target="_top"><code class="classname">Task</code></a> <code class="literal">doLast</code>(<a class="ulink" href="https://docs.groovy-lang.org/3.0.17/html/gapi/groovy/lang/Closure.html" target="_top"><code class="classname">Closure</code></a> action)</h4></div></div></div><p>Adds the given closure to the end of this task's action list.  The closure is passed this task as a parameter
when executed.</p></div><div class="section"><div class="titlepage"><div><div><h4 class="signature"><a xmlns:xslthl="http://xslthl.sf.net" name="org.gradle.api.publish.maven.tasks.GenerateMavenPom:doLast(java.lang.String, org.gradle.api.Action)" class="section-anchor" href="#org.gradle.api.publish.maven.tasks.GenerateMavenPom:doLast(java.lang.String, org.gradle.api.Action)"></a><a class="ulink" href="../dsl/org.gradle.api.Task.html" target="_top"><code class="classname">Task</code></a> <code class="literal">doLast</code>(<a class="ulink" href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html" target="_top"><code class="classname">String</code></a> actionName, <code class="classname"><a class="ulink" href="../javadoc/org/gradle/api/Action.html" target="_top"><code class="classname">Action</code></a>&lt;? super <a class="ulink" href="../dsl/org.gradle.api.Task.html" target="_top"><code class="classname">Task</code></a>&gt;</code> action)</h4></div></div></div><p>Adds the given <a class="ulink" href="../javadoc/org/gradle/api/Action.html" target="_top"><code class="classname">Action</code></a> to the end of this task's action list.</p></div><div class="section"><div class="titlepage"><div><div><h4 class="signature"><a xmlns:xslthl="http://xslthl.sf.net" name="org.gradle.api.publish.maven.tasks.GenerateMavenPom:doLast(org.gradle.api.Action)" class="section-anchor" href="#org.gradle.api.publish.maven.tasks.GenerateMavenPom:doLast(org.gradle.api.Action)"></a><a class="ulink" href="../dsl/org.gradle.api.Task.html" target="_top"><code class="classname">Task</code></a> <code class="literal">doLast</code>(<code class="classname"><a class="ulink" href="../javadoc/org/gradle/api/Action.html" target="_top"><code class="classname">Action</code></a>&lt;? super <a class="ulink" href="../dsl/org.gradle.api.Task.html" target="_top"><code class="classname">Task</code></a>&gt;</code> action)</h4></div></div></div><p>Adds the given <a class="ulink" href="../javadoc/org/gradle/api/Action.html" target="_top"><code class="classname">Action</code></a> to the end of this task's action list.</p></div><div class="section"><div class="titlepage"><div><div><h4 class="signature"><a xmlns:xslthl="http://xslthl.sf.net" name="org.gradle.api.publish.maven.tasks.GenerateMavenPom:doNotTrackState(java.lang.String)" class="section-anchor" href="#org.gradle.api.publish.maven.tasks.GenerateMavenPom:doNotTrackState(java.lang.String)"></a><code class="classname">void</code> <code class="literal">doNotTrackState</code>(<a class="ulink" href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html" target="_top"><code class="classname">String</code></a> reasonNotToTrackState)</h4></div></div></div><p>Do not track the state of the task.

</p><p>Instructs Gradle to treat the task as untracked.</p></div><div class="section"><div class="titlepage"><div><div><h4 class="signature"><a xmlns:xslthl="http://xslthl.sf.net" name="org.gradle.api.publish.maven.tasks.GenerateMavenPom:finalizedBy(java.lang.Object[])" class="section-anchor" href="#org.gradle.api.publish.maven.tasks.GenerateMavenPom:finalizedBy(java.lang.Object[])"></a><a class="ulink" href="../dsl/org.gradle.api.Task.html" target="_top"><code class="classname">Task</code></a> <code class="literal">finalizedBy</code>(<code class="classname"><a class="ulink" href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html" target="_top"><code class="classname">Object</code></a>...</code> paths)</h4></div></div></div><p>Adds the given finalizer tasks for this task.</p><pre class="programlisting">
task taskY {
    finalizedBy <span xmlns:xslthl="http://xslthl.sf.net" class="hl-string">"taskX"</span>
}
</pre><p>See <a class="link" href="org.gradle.api.Task.html#org.gradle.api.Task.dependencies">here</a> for a description of the types of objects which can be used to specify
a finalizer task.</p></div><div class="section"><div class="titlepage"><div><div><h4 class="signature"><a xmlns:xslthl="http://xslthl.sf.net" name="org.gradle.api.publish.maven.tasks.GenerateMavenPom:hasProperty(java.lang.String)" class="section-anchor" href="#org.gradle.api.publish.maven.tasks.GenerateMavenPom:hasProperty(java.lang.String)"></a><code class="classname">boolean</code> <code class="literal">hasProperty</code>(<a class="ulink" href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html" target="_top"><code class="classname">String</code></a> propertyName)</h4></div></div></div><p>Determines if this task has the given property. See <a class="link" href="org.gradle.api.Task.html#org.gradle.api.Task.properties">here</a> for details of the
properties which are available for a task.</p></div><div class="section"><div class="titlepage"><div><div><h4 class="signature"><a xmlns:xslthl="http://xslthl.sf.net" name="org.gradle.api.publish.maven.tasks.GenerateMavenPom:mustRunAfter(java.lang.Object[])" class="section-anchor" href="#org.gradle.api.publish.maven.tasks.GenerateMavenPom:mustRunAfter(java.lang.Object[])"></a><a class="ulink" href="../dsl/org.gradle.api.Task.html" target="_top"><code class="classname">Task</code></a> <code class="literal">mustRunAfter</code>(<code class="classname"><a class="ulink" href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html" target="_top"><code class="classname">Object</code></a>...</code> paths)</h4></div></div></div><p>Specifies that this task must run after all of the supplied tasks.</p><pre class="programlisting">
task taskY {
    mustRunAfter <span xmlns:xslthl="http://xslthl.sf.net" class="hl-string">"taskX"</span>
}
</pre><p>For each supplied task, this action adds a task 'ordering', and does not specify a 'dependency' between the tasks.
As such, it is still possible to execute 'taskY' without first executing the 'taskX' in the example.</p><p>See <a class="link" href="org.gradle.api.Task.html#org.gradle.api.Task.dependencies">here</a> for a description of the types of objects which can be used to specify
an ordering relationship.</p></div><div class="section"><div class="titlepage"><div><div><h4 class="signature"><a xmlns:xslthl="http://xslthl.sf.net" name="org.gradle.api.publish.maven.tasks.GenerateMavenPom:onlyIf(groovy.lang.Closure)" class="section-anchor" href="#org.gradle.api.publish.maven.tasks.GenerateMavenPom:onlyIf(groovy.lang.Closure)"></a><code class="classname">void</code> <code class="literal">onlyIf</code>(<a class="ulink" href="https://docs.groovy-lang.org/3.0.17/html/gapi/groovy/lang/Closure.html" target="_top"><code class="classname">Closure</code></a> onlyIfClosure)</h4></div></div></div><p>Execute the task only if the given closure returns true.  The closure will be evaluated at task execution
time, not during configuration.  The closure will be passed a single parameter, this task. If the closure returns
false, the task will be skipped.</p><p>You may add multiple such predicates. The task is skipped if any of the predicates return false.</p><p>Typical usage:<code class="literal">myTask.onlyIf { isProductionEnvironment() }</code></p></div><div class="section"><div class="titlepage"><div><div><h4 class="signature"><a xmlns:xslthl="http://xslthl.sf.net" name="org.gradle.api.publish.maven.tasks.GenerateMavenPom:onlyIf(java.lang.String, org.gradle.api.specs.Spec)" class="section-anchor" href="#org.gradle.api.publish.maven.tasks.GenerateMavenPom:onlyIf(java.lang.String, org.gradle.api.specs.Spec)"></a><code class="classname">void</code> <code class="literal">onlyIf</code>(<a class="ulink" href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html" target="_top"><code class="classname">String</code></a> onlyIfReason, <code class="classname"><a class="ulink" href="../javadoc/org/gradle/api/specs/Spec.html" target="_top"><code class="classname">Spec</code></a>&lt;? super <a class="ulink" href="../dsl/org.gradle.api.Task.html" target="_top"><code class="classname">Task</code></a>&gt;</code> onlyIfSpec)</h4></div></div></div><div class="caution" title="Caution"><p>Note: This method is <a class="ulink" href="../userguide/feature_lifecycle.html" target="_top">incubating</a> and may change in a future version of Gradle.</p></div><p>Execute the task only if the given spec is satisfied. The spec will be evaluated at task execution time, not
during configuration. If the Spec is not satisfied, the task will be skipped.</p><p>You may add multiple such predicates. The task is skipped if any of the predicates return false.</p><p>Typical usage (from Java):</p><pre class="programlisting">myTask.onlyIf(<span xmlns:xslthl="http://xslthl.sf.net" class="hl-string">"run only in production environment"</span>, <span xmlns:xslthl="http://xslthl.sf.net" class="hl-keyword">new</span> Spec&lt;Task&gt;() {
   <span xmlns:xslthl="http://xslthl.sf.net" class="hl-keyword">boolean</span> isSatisfiedBy(Task task) {
      <span xmlns:xslthl="http://xslthl.sf.net" class="hl-keyword">return</span> isProductionEnvironment();
   }
});
</pre></div><div class="section"><div class="titlepage"><div><div><h4 class="signature"><a xmlns:xslthl="http://xslthl.sf.net" name="org.gradle.api.publish.maven.tasks.GenerateMavenPom:onlyIf(org.gradle.api.specs.Spec)" class="section-anchor" href="#org.gradle.api.publish.maven.tasks.GenerateMavenPom:onlyIf(org.gradle.api.specs.Spec)"></a><code class="classname">void</code> <code class="literal">onlyIf</code>(<code class="classname"><a class="ulink" href="../javadoc/org/gradle/api/specs/Spec.html" target="_top"><code class="classname">Spec</code></a>&lt;? super <a class="ulink" href="../dsl/org.gradle.api.Task.html" target="_top"><code class="classname">Task</code></a>&gt;</code> onlyIfSpec)</h4></div></div></div><p>Execute the task only if the given spec is satisfied. The spec will be evaluated at task execution time, not
during configuration. If the Spec is not satisfied, the task will be skipped.</p><p>You may add multiple such predicates. The task is skipped if any of the predicates return false.</p><p>Typical usage (from Java):</p><pre class="programlisting">myTask.onlyIf(<span xmlns:xslthl="http://xslthl.sf.net" class="hl-keyword">new</span> Spec&lt;Task&gt;() {
   <span xmlns:xslthl="http://xslthl.sf.net" class="hl-keyword">boolean</span> isSatisfiedBy(Task task) {
      <span xmlns:xslthl="http://xslthl.sf.net" class="hl-keyword">return</span> isProductionEnvironment();
   }
});
</pre></div><div class="section"><div class="titlepage"><div><div><h4 class="signature"><a xmlns:xslthl="http://xslthl.sf.net" name="org.gradle.api.publish.maven.tasks.GenerateMavenPom:property(java.lang.String)" class="section-anchor" href="#org.gradle.api.publish.maven.tasks.GenerateMavenPom:property(java.lang.String)"></a><a class="ulink" href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html" target="_top"><code class="classname">Object</code></a> <code class="literal">property</code>(<a class="ulink" href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html" target="_top"><code class="classname">String</code></a> propertyName)</h4></div></div></div><p>Returns the value of the given property of this task.  This method locates a property as follows:</p><div class="orderedlist"><ol class="orderedlist" type="1"><li class="listitem">If this task object has a property with the given name, return the value of the property.</li><li class="listitem">If this task has an extension with the given name, return the extension. </li><li class="listitem">If this task's convention object has a property with the given name, return the value of the property.</li><li class="listitem">If this task has an extra property with the given name, return the value of the property.</li><li class="listitem">If not found, throw <a class="ulink" href="https://docs.groovy-lang.org/3.0.17/html/gapi/groovy/lang/MissingPropertyException.html" target="_top"><code class="classname">MissingPropertyException</code></a></li></ol></div></div><div class="section"><div class="titlepage"><div><div><h4 class="signature"><a xmlns:xslthl="http://xslthl.sf.net" name="org.gradle.api.publish.maven.tasks.GenerateMavenPom:setProperty(java.lang.String, java.lang.Object)" class="section-anchor" href="#org.gradle.api.publish.maven.tasks.GenerateMavenPom:setProperty(java.lang.String, java.lang.Object)"></a><code class="classname">void</code> <code class="literal">setProperty</code>(<a class="ulink" href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html" target="_top"><code class="classname">String</code></a> name, <a class="ulink" href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html" target="_top"><code class="classname">Object</code></a> value)</h4></div></div></div><p>Sets a property of this task.  This method searches for a property with the given name in the following
locations, and sets the property on the first location where it finds the property.</p><div class="orderedlist"><ol class="orderedlist" type="1"><li class="listitem">The task object itself.  For example, the <code class="literal">enabled</code> project property.</li><li class="listitem">The task's convention object.</li><li class="listitem">The task's extra properties.</li></ol></div><p>

If the property is not found, a <a class="ulink" href="https://docs.groovy-lang.org/3.0.17/html/gapi/groovy/lang/MissingPropertyException.html" target="_top"><code class="classname">MissingPropertyException</code></a> is thrown.</p></div><div class="section"><div class="titlepage"><div><div><h4 class="signature"><a xmlns:xslthl="http://xslthl.sf.net" name="org.gradle.api.publish.maven.tasks.GenerateMavenPom:shouldRunAfter(java.lang.Object[])" class="section-anchor" href="#org.gradle.api.publish.maven.tasks.GenerateMavenPom:shouldRunAfter(java.lang.Object[])"></a><a class="ulink" href="../javadoc/org/gradle/api/tasks/TaskDependency.html" target="_top"><code class="classname">TaskDependency</code></a> <code class="literal">shouldRunAfter</code>(<code class="classname"><a class="ulink" href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html" target="_top"><code class="classname">Object</code></a>...</code> paths)</h4></div></div></div><p>Specifies that this task should run after all of the supplied tasks.</p><pre class="programlisting">
task taskY {
    shouldRunAfter <span xmlns:xslthl="http://xslthl.sf.net" class="hl-string">"taskX"</span>
}
</pre><p>For each supplied task, this action adds a task 'ordering', and does not specify a 'dependency' between the tasks.
As such, it is still possible to execute 'taskY' without first executing the 'taskX' in the example.</p><p>See <a class="link" href="org.gradle.api.Task.html#org.gradle.api.Task.dependencies">here</a> for a description of the types of objects which can be used to specify
an ordering relationship.</p></div><div class="section"><div class="titlepage"><div><div><h4 class="signature"><a xmlns:xslthl="http://xslthl.sf.net" name="org.gradle.api.publish.maven.tasks.GenerateMavenPom:usesService(org.gradle.api.provider.Provider)" class="section-anchor" href="#org.gradle.api.publish.maven.tasks.GenerateMavenPom:usesService(org.gradle.api.provider.Provider)"></a><code class="classname">void</code> <code class="literal">usesService</code>(<code class="classname"><a class="ulink" href="../javadoc/org/gradle/api/provider/Provider.html" target="_top"><code class="classname">Provider</code></a>&lt;? extends <a class="ulink" href="../javadoc/org/gradle/api/services/BuildService.html" target="_top"><code class="classname">BuildService</code></a>&lt;?&gt;&gt;</code> service)</h4></div></div></div><p>Registers a <a class="ulink" href="../javadoc/org/gradle/api/services/BuildService.html" target="_top"><code class="classname">BuildService</code></a> that is used by this task so
<a class="ulink" href="../javadoc/org/gradle/api/services/BuildServiceRegistration.html#getMaxParallelUsages--" target="_top"><code class="classname">BuildServiceRegistration.getMaxParallelUsages()</code></a> can be honored.</p></div></div></div></div><footer xmlns:xslthl="http://xslthl.sf.net" itemtype="https://schema.org/WPFooter" itemscope="itemscope" class="site-layout__footer site-footer"><nav itemtype="https://schema.org/SiteNavigationElement" class="site-footer__navigation"><section class="site-footer__links"><div class="site-footer__link-group"><header><strong>Docs</strong></header><ul class="site-footer__links-list"><li itemprop="name"><a itemprop="url" href="/userguide/userguide.html">User Manual</a></li><li itemprop="name"><a itemprop="url" href="/dsl/">DSL Reference</a></li><li itemprop="name"><a itemprop="url" href="/release-notes.html">Release Notes</a></li><li itemprop="name"><a itemprop="url" href="/javadoc/">Javadoc</a></li></ul></div><div class="site-footer__link-group"><header><strong>News</strong></header><ul class="site-footer__links-list"><li itemprop="name"><a itemprop="url" href="https://blog.gradle.org/">Blog</a></li><li itemprop="name"><a itemprop="url" href="https://newsletter.gradle.org/">Newsletter</a></li><li itemprop="name"><a itemprop="url" href="https://twitter.com/gradle">Twitter</a></li></ul></div><div class="site-footer__link-group"><header><strong>Products</strong></header><ul class="site-footer__links-list"><li itemprop="name"><a itemprop="url" href="https://gradle.com/build-scans">Build Scan&trade;</a></li><li itemprop="name"><a itemprop="url" href="https://gradle.com/build-cache">Build Cache</a></li><li itemprop="name"><a itemprop="url" href="https://gradle.com/enterprise/resources">Enterprise Docs</a></li></ul></div><div class="site-footer__link-group"><header><strong>Get Help</strong></header><ul class="site-footer__links-list"><li itemprop="name"><a itemprop="url" href="https://discuss.gradle.org/c/help-discuss">Forums</a></li><li itemprop="name"><a itemprop="url" href="https://github.com/gradle/">GitHub</a></li><li itemprop="name"><a itemprop="url" href="https://gradle.org/training/">Training</a></li><li itemprop="name"><a itemprop="url" href="https://gradle.org/services/">Services</a></li></ul></div></section><section id="newsletter-form-container" class="site-footer__subscribe-newsletter"><header class="newsletter-form__header"><h5>Stay <code>UP-TO-DATE</code> on new features and news</h5></header><p class="disclaimer">By entering your email, you agree to our <a href="https://gradle.org/terms/">Terms</a> and <a href="https://gradle.org/privacy/">Privacy Policy</a>, including receipt of emails. You can unsubscribe at any time.</p><div class="newsletter-form__container"><form method="post" action="https://go.gradle.com/l/68052/2018-09-07/bk6wml" class="newsletter-form" id="newsletter-form"><input required="" maxlength="255" pattern="[^@\s]+@[^@\s]+\.[^@\s]+" placeholder="<EMAIL>" type="email" name="email" class="email" id="email"><button type="submit" class="submit" id="submit">Subscribe</button></form></div></section></nav></footer><aside class="secondary-navigation"></aside></div></main><div class="site-footer-secondary"><div class="site-footer-secondary__contents"><div class="site-footer__copy">&copy; <a href="https://gradle.com">Gradle Inc. </a><time>2021</time>
                                All rights reserved.
                            </div><div class="site-footer__logo"><a href="/"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 90 66.06"><defs><style>.cls-1 {
                                            fill: #02303a;
                                            }</style></defs><title>gradle</title><path d="M85.11,4.18a14.27,14.27,0,0,0-19.83-.34,1.38,1.38,0,0,0,0,2L67,7.6a1.36,1.36,0,0,0,1.78.12A8.18,8.18,0,0,1,79.5,20.06C68.17,31.38,53.05-.36,18.73,16a4.65,4.65,0,0,0-2,6.54l5.89,10.17a4.64,4.64,0,0,0,6.3,1.73l.14-.08-.11.08L31.53,33a60.29,60.29,0,0,0,8.22-6.13,1.44,1.44,0,0,1,1.87-.06h0a1.34,1.34,0,0,1,.06,2A61.61,61.61,0,0,1,33,35.34l-.09,0-2.61,1.46a7.34,7.34,0,0,1-3.61.94,7.45,7.45,0,0,1-6.47-3.71l-5.57-9.61C4,32-2.54,46.56,1,65a1.36,1.36,0,0,0,1.33,1.11H8.61A1.36,1.36,0,0,0,10,64.87a9.29,9.29,0,0,1,18.42,0,1.35,1.35,0,0,0,1.34,1.19H35.9a1.36,1.36,0,0,0,1.34-1.19,9.29,9.29,0,0,1,18.42,0A1.36,1.36,0,0,0,57,66.06H63.1a1.36,1.36,0,0,0,1.36-1.34c.14-8.6,2.46-18.48,9.07-23.43C96.43,24.16,90.41,9.48,85.11,4.18ZM61.76,30.05l-4.37-2.19h0a2.74,2.74,0,1,1,4.37,2.2Z" class="cls-1"/></svg></a></div><div class="site-footer-secondary__links"><a href="https://gradle.com/careers">Careers</a> |
                                <a href="https://gradle.org/privacy">Privacy</a> |
                                <a href="https://gradle.org/terms">Terms of Service</a> |
                                <a href="https://gradle.org/contact/">Contact</a></div></div></div></div></body></html>