<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc -->
<title>Zip (Gradle API 8.4)</title>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<link rel="stylesheet" type="text/css" href="../../../../../javadoc.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../jquery/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
<script type="text/javascript" src="../../../../../jquery/jszip/dist/jszip.min.js"></script>
<script type="text/javascript" src="../../../../../jquery/jszip-utils/dist/jszip-utils.min.js"></script>
<!--[if IE]>
<script type="text/javascript" src="../../../../../jquery/jszip-utils/dist/jszip-utils-ie.min.js"></script>
<![endif]-->
<script type="text/javascript" src="../../../../../jquery/jquery-3.6.0.min.js"></script>
<script type="text/javascript" src="../../../../../jquery/jquery-ui.min.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="Zip (Gradle API 8.4)";
        }
    }
    catch(err) {
    }
//-->
var data = {"i0":10,"i1":10,"i2":10,"i3":10,"i4":10,"i5":10,"i6":10,"i7":10};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
var pathtoroot = "../../../../../";
var useModuleDirectories = false;
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="fixedNav">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../../allclasses.html">All&nbsp;Classes</a></li>
</ul>
<ul class="navListSearch">
<li><label for="search">SEARCH:</label>
<input type="text" id="search" value="search" disabled="disabled">
<input type="reset" id="reset" value="reset" disabled="disabled">
</li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li><a href="#nested.class.summary">Nested</a>&nbsp;|&nbsp;</li>
<li><a href="#field.summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#field.detail">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
</div>
<div class="navPadding">&nbsp;</div>
<script type="text/javascript"><!--
$('.navPadding').css('padding-top', $('.fixedNav').css("height"));
//-->
</script>
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle"><span class="packageLabelInType">Package</span>&nbsp;<a href="package-summary.html">org.gradle.api.tasks.bundling</a></div>
<h2 title="Class Zip" class="title">Class Zip</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>org.gradle.api.internal.AbstractTask</li>
<li>
<ul class="inheritance">
<li><a href="../../DefaultTask.html" title="class in org.gradle.api">org.gradle.api.DefaultTask</a></li>
<li>
<ul class="inheritance">
<li>org.gradle.api.internal.ConventionTask</li>
<li>
<ul class="inheritance">
<li><a href="../AbstractCopyTask.html" title="class in org.gradle.api.tasks">org.gradle.api.tasks.AbstractCopyTask</a></li>
<li>
<ul class="inheritance">
<li><a href="AbstractArchiveTask.html" title="class in org.gradle.api.tasks.bundling">org.gradle.api.tasks.bundling.AbstractArchiveTask</a></li>
<li>
<ul class="inheritance">
<li>org.gradle.api.tasks.bundling.Zip</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Implemented Interfaces:</dt>
<dd><code>java.lang.Comparable&lt;<a href="../../Task.html" title="interface in org.gradle.api">Task</a>&gt;</code>, <code><a href="../../file/ContentFilterable.html" title="interface in org.gradle.api.file">ContentFilterable</a></code>, <code><a href="../../file/CopyProcessingSpec.html" title="interface in org.gradle.api.file">CopyProcessingSpec</a></code>, <code><a href="../../file/CopySourceSpec.html" title="interface in org.gradle.api.file">CopySourceSpec</a></code>, <code><a href="../../file/CopySpec.html" title="interface in org.gradle.api.file">CopySpec</a></code>, <code>org.gradle.api.internal.DynamicObjectAware</code>, <code>org.gradle.api.internal.file.copy.CopySpecSource</code>, <code>org.gradle.api.internal.IConventionAware</code>, <code>org.gradle.api.internal.TaskInternal</code>, <code><a href="../../plugins/ExtensionAware.html" title="interface in org.gradle.api.plugins">ExtensionAware</a></code>, <code><a href="../../Task.html" title="interface in org.gradle.api">Task</a></code>, <code><a href="../util/PatternFilterable.html" title="interface in org.gradle.api.tasks.util">PatternFilterable</a></code>, <code><a href="../../../util/Configurable.html" title="interface in org.gradle.util">Configurable</a>&lt;<a href="../../Task.html" title="interface in org.gradle.api">Task</a>&gt;</code></dd>
</dl>
<dl>
<dt>Direct Known Subclasses:</dt>
<dd><code><a href="../../../jvm/tasks/Jar.html" title="class in org.gradle.jvm.tasks">Jar</a></code></dd>
</dl>
<hr>
<pre><a href="../../../work/DisableCachingByDefault.html" title="annotation in org.gradle.work">@DisableCachingByDefault</a>(<a href="../../../work/DisableCachingByDefault.html#because--">because</a>="Not worth caching")
public abstract class <span class="typeNameLabel">Zip</span>
extends <a href="AbstractArchiveTask.html" title="class in org.gradle.api.tasks.bundling">AbstractArchiveTask</a></pre>
<div class="block">Assembles a ZIP archive.

 The default is to compress the contents of the zip.</div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== NESTED CLASS SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="nested.class.summary">
<!--   -->
</a>
<h3>Nested Class Summary</h3>
<ul class="blockList">
<li class="blockList"><a name="nested.classes.inherited.from.class.org.gradle.api.Task">
<!--   -->
</a>
<h3>Nested classes/interfaces inherited from interface&nbsp;org.gradle.api.<a href="../../Task.html" title="interface in org.gradle.api">Task</a></h3>
<code><a href="../../Task.Namer.html" title="class in org.gradle.api">Task.Namer</a></code></li>
</ul>
</li>
</ul>
<!-- =========== FIELD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.summary">
<!--   -->
</a>
<h3>Field Summary</h3>
<table class="memberSummary" summary="Field Summary table, listing fields, and an explanation">
<caption><span>Fields</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colSecond" scope="col">Field</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#ZIP_EXTENSION">ZIP_EXTENSION</a></span></code></th>
<td class="colLast">&nbsp;</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="fields.inherited.from.class.org.gradle.api.Task">
<!--   -->
</a>
<h3>Fields inherited from interface&nbsp;org.gradle.api.<a href="../../Task.html" title="interface in org.gradle.api">Task</a></h3>
<code><a href="../../Task.html#TASK_ACTION">TASK_ACTION</a>, <a href="../../Task.html#TASK_CONSTRUCTOR_ARGS">TASK_CONSTRUCTOR_ARGS</a>, <a href="../../Task.html#TASK_DEPENDS_ON">TASK_DEPENDS_ON</a>, <a href="../../Task.html#TASK_DESCRIPTION">TASK_DESCRIPTION</a>, <a href="../../Task.html#TASK_GROUP">TASK_GROUP</a>, <a href="../../Task.html#TASK_NAME">TASK_NAME</a>, <a href="../../Task.html#TASK_OVERWRITE">TASK_OVERWRITE</a>, <a href="../../Task.html#TASK_TYPE">TASK_TYPE</a></code></li>
</ul>
</li>
</ul>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="memberSummary" summary="Constructor Summary table, listing constructors, and an explanation">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Constructor</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tr class="altColor">
<th class="colConstructorName" scope="row"><code><span class="memberNameLink"><a href="#Zip--">Zip</a></span>()</code></th>
<td class="colLast">&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colSecond" scope="col">Method</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>protected org.gradle.api.internal.file.copy.CopyAction</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#createCopyAction--">createCopyAction</a></span>()</code></th>
<td class="colLast">&nbsp;</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>protected org.gradle.api.internal.file.copy.ZipCompressor</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getCompressor--">getCompressor</a></span>()</code></th>
<td class="colLast">&nbsp;</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code><a href="ZipEntryCompression.html" title="enum in org.gradle.api.tasks.bundling">ZipEntryCompression</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getEntryCompression--">getEntryCompression</a></span>()</code></th>
<td class="colLast">
<div class="block">Returns the compression level of the entries of the archive.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>java.lang.String</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getMetadataCharset--">getMetadataCharset</a></span>()</code></th>
<td class="colLast">
<div class="block">The character set used to encode ZIP metadata like file names.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#isZip64--">isZip64</a></span>()</code></th>
<td class="colLast">
<div class="block">Whether the zip can contain more than 65535 files and/or support files greater than 4GB in size.</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setEntryCompression-org.gradle.api.tasks.bundling.ZipEntryCompression-">setEntryCompression</a></span>&#8203;(<a href="ZipEntryCompression.html" title="enum in org.gradle.api.tasks.bundling">ZipEntryCompression</a>&nbsp;entryCompression)</code></th>
<td class="colLast">
<div class="block">Sets the compression level of the entries of the archive.</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setMetadataCharset-java.lang.String-">setMetadataCharset</a></span>&#8203;(java.lang.String&nbsp;metadataCharset)</code></th>
<td class="colLast">
<div class="block">The character set used to encode ZIP metadata like file names.</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setZip64-boolean-">setZip64</a></span>&#8203;(boolean&nbsp;allowZip64)</code></th>
<td class="colLast">
<div class="block">Enables building zips with more than 65535 files or bigger than 4GB.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.org.gradle.api.tasks.bundling.AbstractArchiveTask">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;org.gradle.api.tasks.bundling.<a href="AbstractArchiveTask.html" title="class in org.gradle.api.tasks.bundling">AbstractArchiveTask</a></h3>
<code><a href="AbstractArchiveTask.html#createCopyActionExecuter--">createCopyActionExecuter</a>, <a href="AbstractArchiveTask.html#getArchiveAppendix--">getArchiveAppendix</a>, <a href="AbstractArchiveTask.html#getArchiveBaseName--">getArchiveBaseName</a>, <a href="AbstractArchiveTask.html#getArchiveClassifier--">getArchiveClassifier</a>, <a href="AbstractArchiveTask.html#getArchiveExtension--">getArchiveExtension</a>, <a href="AbstractArchiveTask.html#getArchiveFile--">getArchiveFile</a>, <a href="AbstractArchiveTask.html#getArchiveFileName--">getArchiveFileName</a>, <a href="AbstractArchiveTask.html#getArchivePath--">getArchivePath</a>, <a href="AbstractArchiveTask.html#getArchiveVersion--">getArchiveVersion</a>, <a href="AbstractArchiveTask.html#getDestinationDirectory--">getDestinationDirectory</a>, <a href="AbstractArchiveTask.html#into-java.lang.Object-">into</a>, <a href="AbstractArchiveTask.html#into-java.lang.Object-groovy.lang.Closure-">into</a>, <a href="AbstractArchiveTask.html#into-java.lang.Object-org.gradle.api.Action-">into</a>, <a href="AbstractArchiveTask.html#isPreserveFileTimestamps--">isPreserveFileTimestamps</a>, <a href="AbstractArchiveTask.html#isReproducibleFileOrder--">isReproducibleFileOrder</a>, <a href="AbstractArchiveTask.html#setPreserveFileTimestamps-boolean-">setPreserveFileTimestamps</a>, <a href="AbstractArchiveTask.html#setReproducibleFileOrder-boolean-">setReproducibleFileOrder</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.org.gradle.api.tasks.AbstractCopyTask">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;org.gradle.api.tasks.<a href="../AbstractCopyTask.html" title="class in org.gradle.api.tasks">AbstractCopyTask</a></h3>
<code><a href="../AbstractCopyTask.html#copy--">copy</a>, <a href="../AbstractCopyTask.html#createRootSpec--">createRootSpec</a>, <a href="../AbstractCopyTask.html#dirPermissions-org.gradle.api.Action-">dirPermissions</a>, <a href="../AbstractCopyTask.html#eachFile-groovy.lang.Closure-">eachFile</a>, <a href="../AbstractCopyTask.html#eachFile-org.gradle.api.Action-">eachFile</a>, <a href="../AbstractCopyTask.html#exclude-groovy.lang.Closure-">exclude</a>, <a href="../AbstractCopyTask.html#exclude-java.lang.Iterable-">exclude</a>, <a href="../AbstractCopyTask.html#exclude-java.lang.String...-">exclude</a>, <a href="../AbstractCopyTask.html#exclude-org.gradle.api.specs.Spec-">exclude</a>, <a href="../AbstractCopyTask.html#expand-java.util.Map-">expand</a>, <a href="../AbstractCopyTask.html#expand-java.util.Map-org.gradle.api.Action-">expand</a>, <a href="../AbstractCopyTask.html#filePermissions-org.gradle.api.Action-">filePermissions</a>, <a href="../AbstractCopyTask.html#filesMatching-java.lang.Iterable-org.gradle.api.Action-">filesMatching</a>, <a href="../AbstractCopyTask.html#filesMatching-java.lang.String-org.gradle.api.Action-">filesMatching</a>, <a href="../AbstractCopyTask.html#filesNotMatching-java.lang.Iterable-org.gradle.api.Action-">filesNotMatching</a>, <a href="../AbstractCopyTask.html#filesNotMatching-java.lang.String-org.gradle.api.Action-">filesNotMatching</a>, <a href="../AbstractCopyTask.html#filter-groovy.lang.Closure-">filter</a>, <a href="../AbstractCopyTask.html#filter-java.lang.Class-">filter</a>, <a href="../AbstractCopyTask.html#filter-java.util.Map-java.lang.Class-">filter</a>, <a href="../AbstractCopyTask.html#filter-org.gradle.api.Transformer-">filter</a>, <a href="../AbstractCopyTask.html#from-java.lang.Object...-">from</a>, <a href="../AbstractCopyTask.html#from-java.lang.Object-groovy.lang.Closure-">from</a>, <a href="../AbstractCopyTask.html#from-java.lang.Object-org.gradle.api.Action-">from</a>, <a href="../AbstractCopyTask.html#getDirectoryFileTreeFactory--">getDirectoryFileTreeFactory</a>, <a href="../AbstractCopyTask.html#getDirMode--">getDirMode</a>, <a href="../AbstractCopyTask.html#getDirPermissions--">getDirPermissions</a>, <a href="../AbstractCopyTask.html#getDocumentationRegistry--">getDocumentationRegistry</a>, <a href="../AbstractCopyTask.html#getDuplicatesStrategy--">getDuplicatesStrategy</a>, <a href="../AbstractCopyTask.html#getExcludes--">getExcludes</a>, <a href="../AbstractCopyTask.html#getFileLookup--">getFileLookup</a>, <a href="../AbstractCopyTask.html#getFileMode--">getFileMode</a>, <a href="../AbstractCopyTask.html#getFilePermissions--">getFilePermissions</a>, <a href="../AbstractCopyTask.html#getFileResolver--">getFileResolver</a>, <a href="../AbstractCopyTask.html#getFileSystem--">getFileSystem</a>, <a href="../AbstractCopyTask.html#getFilteringCharset--">getFilteringCharset</a>, <a href="../AbstractCopyTask.html#getIncludeEmptyDirs--">getIncludeEmptyDirs</a>, <a href="../AbstractCopyTask.html#getIncludes--">getIncludes</a>, <a href="../AbstractCopyTask.html#getInstantiator--">getInstantiator</a>, <a href="../AbstractCopyTask.html#getMainSpec--">getMainSpec</a>, <a href="../AbstractCopyTask.html#getObjectFactory--">getObjectFactory</a>, <a href="../AbstractCopyTask.html#getRootSpec--">getRootSpec</a>, <a href="../AbstractCopyTask.html#getSource--">getSource</a>, <a href="../AbstractCopyTask.html#include-groovy.lang.Closure-">include</a>, <a href="../AbstractCopyTask.html#include-java.lang.Iterable-">include</a>, <a href="../AbstractCopyTask.html#include-java.lang.String...-">include</a>, <a href="../AbstractCopyTask.html#include-org.gradle.api.specs.Spec-">include</a>, <a href="../AbstractCopyTask.html#isCaseSensitive--">isCaseSensitive</a>, <a href="../AbstractCopyTask.html#rename-groovy.lang.Closure-">rename</a>, <a href="../AbstractCopyTask.html#rename-java.lang.String-java.lang.String-">rename</a>, <a href="../AbstractCopyTask.html#rename-java.util.regex.Pattern-java.lang.String-">rename</a>, <a href="../AbstractCopyTask.html#rename-org.gradle.api.Transformer-">rename</a>, <a href="../AbstractCopyTask.html#setCaseSensitive-boolean-">setCaseSensitive</a>, <a href="../AbstractCopyTask.html#setDirMode-java.lang.Integer-">setDirMode</a>, <a href="../AbstractCopyTask.html#setDuplicatesStrategy-org.gradle.api.file.DuplicatesStrategy-">setDuplicatesStrategy</a>, <a href="../AbstractCopyTask.html#setExcludes-java.lang.Iterable-">setExcludes</a>, <a href="../AbstractCopyTask.html#setFileMode-java.lang.Integer-">setFileMode</a>, <a href="../AbstractCopyTask.html#setFilteringCharset-java.lang.String-">setFilteringCharset</a>, <a href="../AbstractCopyTask.html#setIncludeEmptyDirs-boolean-">setIncludeEmptyDirs</a>, <a href="../AbstractCopyTask.html#setIncludes-java.lang.Iterable-">setIncludes</a>, <a href="../AbstractCopyTask.html#with-org.gradle.api.file.CopySpec...-">with</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.org.gradle.api.internal.ConventionTask">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;org.gradle.api.internal.ConventionTask</h3>
<code>conventionMapping, conventionMapping, getConventionMapping</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.org.gradle.api.DefaultTask">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;org.gradle.api.<a href="../../DefaultTask.html" title="class in org.gradle.api">DefaultTask</a></h3>
<code><a href="../../DefaultTask.html#compareTo-org.gradle.api.Task-">compareTo</a>, <a href="../../DefaultTask.html#configure-groovy.lang.Closure-">configure</a>, <a href="../../DefaultTask.html#dependsOn-java.lang.Object...-">dependsOn</a>, <a href="../../DefaultTask.html#doFirst-groovy.lang.Closure-">doFirst</a>, <a href="../../DefaultTask.html#doFirst-java.lang.String-org.gradle.api.Action-">doFirst</a>, <a href="../../DefaultTask.html#doFirst-org.gradle.api.Action-">doFirst</a>, <a href="../../DefaultTask.html#doLast-groovy.lang.Closure-">doLast</a>, <a href="../../DefaultTask.html#doLast-java.lang.String-org.gradle.api.Action-">doLast</a>, <a href="../../DefaultTask.html#doLast-org.gradle.api.Action-">doLast</a>, <a href="../../DefaultTask.html#finalizedBy-java.lang.Object...-">finalizedBy</a>, <a href="../../DefaultTask.html#getActions--">getActions</a>, <a href="../../DefaultTask.html#getAnt--">getAnt</a>, <a href="../../DefaultTask.html#getDependsOn--">getDependsOn</a>, <a href="../../DefaultTask.html#getDescription--">getDescription</a>, <a href="../../DefaultTask.html#getDestroyables--">getDestroyables</a>, <a href="../../DefaultTask.html#getDidWork--">getDidWork</a>, <a href="../../DefaultTask.html#getEnabled--">getEnabled</a>, <a href="../../DefaultTask.html#getExtensions--">getExtensions</a>, <a href="../../DefaultTask.html#getFinalizedBy--">getFinalizedBy</a>, <a href="../../DefaultTask.html#getGroup--">getGroup</a>, <a href="../../DefaultTask.html#getInputs--">getInputs</a>, <a href="../../DefaultTask.html#getLocalState--">getLocalState</a>, <a href="../../DefaultTask.html#getLogger--">getLogger</a>, <a href="../../DefaultTask.html#getLogging--">getLogging</a>, <a href="../../DefaultTask.html#getMustRunAfter--">getMustRunAfter</a>, <a href="../../DefaultTask.html#getName--">getName</a>, <a href="../../DefaultTask.html#getOutputs--">getOutputs</a>, <a href="../../DefaultTask.html#getPath--">getPath</a>, <a href="../../DefaultTask.html#getProject--">getProject</a>, <a href="../../DefaultTask.html#getShouldRunAfter--">getShouldRunAfter</a>, <a href="../../DefaultTask.html#getState--">getState</a>, <a href="../../DefaultTask.html#getTaskDependencies--">getTaskDependencies</a>, <a href="../../DefaultTask.html#getTemporaryDir--">getTemporaryDir</a>, <a href="../../DefaultTask.html#getTimeout--">getTimeout</a>, <a href="../../DefaultTask.html#hasProperty-java.lang.String-">hasProperty</a>, <a href="../../DefaultTask.html#mustRunAfter-java.lang.Object...-">mustRunAfter</a>, <a href="../../DefaultTask.html#onlyIf-groovy.lang.Closure-">onlyIf</a>, <a href="../../DefaultTask.html#onlyIf-java.lang.String-org.gradle.api.specs.Spec-">onlyIf</a>, <a href="../../DefaultTask.html#onlyIf-org.gradle.api.specs.Spec-">onlyIf</a>, <a href="../../DefaultTask.html#property-java.lang.String-">property</a>, <a href="../../DefaultTask.html#setActions-java.util.List-">setActions</a>, <a href="../../DefaultTask.html#setDependsOn-java.lang.Iterable-">setDependsOn</a>, <a href="../../DefaultTask.html#setDescription-java.lang.String-">setDescription</a>, <a href="../../DefaultTask.html#setDidWork-boolean-">setDidWork</a>, <a href="../../DefaultTask.html#setEnabled-boolean-">setEnabled</a>, <a href="../../DefaultTask.html#setFinalizedBy-java.lang.Iterable-">setFinalizedBy</a>, <a href="../../DefaultTask.html#setGroup-java.lang.String-">setGroup</a>, <a href="../../DefaultTask.html#setMustRunAfter-java.lang.Iterable-">setMustRunAfter</a>, <a href="../../DefaultTask.html#setOnlyIf-groovy.lang.Closure-">setOnlyIf</a>, <a href="../../DefaultTask.html#setOnlyIf-java.lang.String-org.gradle.api.specs.Spec-">setOnlyIf</a>, <a href="../../DefaultTask.html#setOnlyIf-org.gradle.api.specs.Spec-">setOnlyIf</a>, <a href="../../DefaultTask.html#setProperty-java.lang.String-java.lang.Object-">setProperty</a>, <a href="../../DefaultTask.html#setShouldRunAfter-java.lang.Iterable-">setShouldRunAfter</a>, <a href="../../DefaultTask.html#shouldRunAfter-java.lang.Object...-">shouldRunAfter</a>, <a href="../../DefaultTask.html#usesService-org.gradle.api.provider.Provider-">usesService</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.org.gradle.api.internal.AbstractTask">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;org.gradle.api.internal.AbstractTask</h3>
<code>acceptServiceReferences, appendParallelSafeAction, doNotTrackState, getAsDynamicObject, getConvention, getIdentityPath, getImpliesSubProjects, getLifecycleDependencies, getOnlyIf, getReasonNotToTrackState, getReasonTaskIsIncompatibleWithConfigurationCache, getRequiredServices, getServices, getSharedResources, getStandardOutputCapture, getTaskActions, getTaskIdentity, getTemporaryDirFactory, hasTaskActions, injectIntoNewInstance, isCompatibleWithConfigurationCache, isEnabled, isHasCustomActions, notCompatibleWithConfigurationCache, prependParallelSafeAction, setImpliesSubProjects</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>clone, equals, finalize, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.org.gradle.api.Task">
<!--   -->
</a>
<h3>Methods inherited from interface&nbsp;org.gradle.api.<a href="../../Task.html" title="interface in org.gradle.api">Task</a></h3>
<code><a href="../../Task.html#doNotTrackState-java.lang.String-">doNotTrackState</a>, <a href="../../Task.html#getConvention--">getConvention</a>, <a href="../../Task.html#notCompatibleWithConfigurationCache-java.lang.String-">notCompatibleWithConfigurationCache</a></code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ FIELD DETAIL =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.detail">
<!--   -->
</a>
<h3>Field Detail</h3>
<a name="ZIP_EXTENSION">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>ZIP_EXTENSION</h4>
<pre>public static final&nbsp;java.lang.String ZIP_EXTENSION</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../../../constant-values.html#org.gradle.api.tasks.bundling.Zip.ZIP_EXTENSION">Constant Field Values</a></dd>
</dl>
</li>
</ul>
</li>
</ul>
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a name="Zip--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>Zip</h4>
<pre>public&nbsp;Zip()</pre>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="getCompressor--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCompressor</h4>
<pre class="methodSignature"><a href="../Internal.html" title="annotation in org.gradle.api.tasks">@Internal</a>
protected&nbsp;org.gradle.api.internal.file.copy.ZipCompressor&nbsp;getCompressor()</pre>
</li>
</ul>
<a name="createCopyAction--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createCopyAction</h4>
<pre class="methodSignature">protected&nbsp;org.gradle.api.internal.file.copy.CopyAction&nbsp;createCopyAction()</pre>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../AbstractCopyTask.html#createCopyAction--">createCopyAction</a></code>&nbsp;in class&nbsp;<code><a href="../AbstractCopyTask.html" title="class in org.gradle.api.tasks">AbstractCopyTask</a></code></dd>
</dl>
</li>
</ul>
<a name="getEntryCompression--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getEntryCompression</h4>
<pre class="methodSignature"><a href="../Input.html" title="annotation in org.gradle.api.tasks">@Input</a>
public&nbsp;<a href="ZipEntryCompression.html" title="enum in org.gradle.api.tasks.bundling">ZipEntryCompression</a>&nbsp;getEntryCompression()</pre>
<div class="block">Returns the compression level of the entries of the archive. If set to <a href="ZipEntryCompression.html#DEFLATED"><code>ZipEntryCompression.DEFLATED</code></a> (the default), each entry is
 compressed using the DEFLATE algorithm. If set to <a href="ZipEntryCompression.html#STORED"><code>ZipEntryCompression.STORED</code></a> the entries of the archive are left uncompressed.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the compression level of the archive contents.</dd>
</dl>
</li>
</ul>
<a name="setEntryCompression-org.gradle.api.tasks.bundling.ZipEntryCompression-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setEntryCompression</h4>
<pre class="methodSignature">public&nbsp;void&nbsp;setEntryCompression&#8203;(<a href="ZipEntryCompression.html" title="enum in org.gradle.api.tasks.bundling">ZipEntryCompression</a>&nbsp;entryCompression)</pre>
<div class="block">Sets the compression level of the entries of the archive. If set to <a href="ZipEntryCompression.html#DEFLATED"><code>ZipEntryCompression.DEFLATED</code></a> (the default), each entry is
 compressed using the DEFLATE algorithm. If set to <a href="ZipEntryCompression.html#STORED"><code>ZipEntryCompression.STORED</code></a> the entries of the archive are left uncompressed.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>entryCompression</code> - <code>STORED</code> or <code>DEFLATED</code></dd>
</dl>
</li>
</ul>
<a name="setZip64-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setZip64</h4>
<pre class="methodSignature">public&nbsp;void&nbsp;setZip64&#8203;(boolean&nbsp;allowZip64)</pre>
<div class="block">Enables building zips with more than 65535 files or bigger than 4GB.</div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="#isZip64--"><code>isZip64()</code></a></dd>
</dl>
</li>
</ul>
<a name="isZip64--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isZip64</h4>
<pre class="methodSignature"><a href="../Input.html" title="annotation in org.gradle.api.tasks">@Input</a>
public&nbsp;boolean&nbsp;isZip64()</pre>
<div class="block">Whether the zip can contain more than 65535 files and/or support files greater than 4GB in size.
 <p>
 The standard zip format has hard limits on file size and count.
 The <a href="http://en.wikipedia.org/wiki/Zip_(file_format)#ZIP64">Zip64 format extension</a>
 practically removes these limits and is therefore required for building large zips.
 <p>
 However, not all Zip readers support the Zip64 extensions.
 Notably, the <code>ZipInputStream</code> JDK class does not support Zip64 for versions earlier than Java 7.
 This means you should not enable this property if you are building JARs to be used with Java 6 and earlier runtimes.</div>
</li>
</ul>
<a name="getMetadataCharset--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getMetadataCharset</h4>
<pre class="methodSignature">@Nullable
<a href="../Optional.html" title="annotation in org.gradle.api.tasks">@Optional</a>
<a href="../Input.html" title="annotation in org.gradle.api.tasks">@Input</a>
public&nbsp;java.lang.String&nbsp;getMetadataCharset()</pre>
<div class="block">The character set used to encode ZIP metadata like file names.
 Defaults to the platform's default character set.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>null if using the platform's default character set for ZIP metadata</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>2.14</dd>
</dl>
</li>
</ul>
<a name="setMetadataCharset-java.lang.String-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>setMetadataCharset</h4>
<pre class="methodSignature">public&nbsp;void&nbsp;setMetadataCharset&#8203;(java.lang.String&nbsp;metadataCharset)</pre>
<div class="block">The character set used to encode ZIP metadata like file names.
 Defaults to the platform's default character set.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>metadataCharset</code> - the character set used to encode ZIP metadata like file names</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>2.14</dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../../allclasses.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li><a href="#nested.class.summary">Nested</a>&nbsp;|&nbsp;</li>
<li><a href="#field.summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#field.detail">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
