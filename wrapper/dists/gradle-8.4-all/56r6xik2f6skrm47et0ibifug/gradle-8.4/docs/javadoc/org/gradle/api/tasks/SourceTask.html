<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc -->
<title>SourceTask (Gradle API 8.4)</title>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<link rel="stylesheet" type="text/css" href="../../../../javadoc.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../jquery/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
<script type="text/javascript" src="../../../../jquery/jszip/dist/jszip.min.js"></script>
<script type="text/javascript" src="../../../../jquery/jszip-utils/dist/jszip-utils.min.js"></script>
<!--[if IE]>
<script type="text/javascript" src="../../../../jquery/jszip-utils/dist/jszip-utils-ie.min.js"></script>
<![endif]-->
<script type="text/javascript" src="../../../../jquery/jquery-3.6.0.min.js"></script>
<script type="text/javascript" src="../../../../jquery/jquery-ui.min.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="SourceTask (Gradle API 8.4)";
        }
    }
    catch(err) {
    }
//-->
var data = {"i0":10,"i1":10,"i2":10,"i3":10,"i4":10,"i5":10,"i6":10,"i7":10,"i8":10,"i9":10,"i10":10,"i11":10,"i12":10,"i13":10,"i14":10,"i15":10,"i16":10,"i17":10};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
var pathtoroot = "../../../../";
var useModuleDirectories = false;
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="fixedNav">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses.html">All&nbsp;Classes</a></li>
</ul>
<ul class="navListSearch">
<li><label for="search">SEARCH:</label>
<input type="text" id="search" value="search" disabled="disabled">
<input type="reset" id="reset" value="reset" disabled="disabled">
</li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li><a href="#nested.class.summary">Nested</a>&nbsp;|&nbsp;</li>
<li><a href="#field.summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
</div>
<div class="navPadding">&nbsp;</div>
<script type="text/javascript"><!--
$('.navPadding').css('padding-top', $('.fixedNav').css("height"));
//-->
</script>
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle"><span class="packageLabelInType">Package</span>&nbsp;<a href="package-summary.html">org.gradle.api.tasks</a></div>
<h2 title="Class SourceTask" class="title">Class SourceTask</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>org.gradle.api.internal.AbstractTask</li>
<li>
<ul class="inheritance">
<li><a href="../DefaultTask.html" title="class in org.gradle.api">org.gradle.api.DefaultTask</a></li>
<li>
<ul class="inheritance">
<li>org.gradle.api.internal.ConventionTask</li>
<li>
<ul class="inheritance">
<li>org.gradle.api.tasks.SourceTask</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Implemented Interfaces:</dt>
<dd><code>java.lang.Comparable&lt;<a href="../Task.html" title="interface in org.gradle.api">Task</a>&gt;</code>, <code>org.gradle.api.internal.DynamicObjectAware</code>, <code>org.gradle.api.internal.IConventionAware</code>, <code>org.gradle.api.internal.TaskInternal</code>, <code><a href="../plugins/ExtensionAware.html" title="interface in org.gradle.api.plugins">ExtensionAware</a></code>, <code><a href="../Task.html" title="interface in org.gradle.api">Task</a></code>, <code><a href="util/PatternFilterable.html" title="interface in org.gradle.api.tasks.util">PatternFilterable</a></code>, <code><a href="../../util/Configurable.html" title="interface in org.gradle.util">Configurable</a>&lt;<a href="../Task.html" title="interface in org.gradle.api">Task</a>&gt;</code></dd>
</dl>
<dl>
<dt>Direct Known Subclasses:</dt>
<dd><code><a href="../plugins/quality/AbstractCodeQualityTask.html" title="class in org.gradle.api.plugins.quality">AbstractCodeQualityTask</a></code>, <code><a href="compile/AbstractCompile.html" title="class in org.gradle.api.tasks.compile">AbstractCompile</a></code>, <code><a href="../plugins/antlr/AntlrTask.html" title="class in org.gradle.api.plugins.antlr">AntlrTask</a></code>, <code><a href="javadoc/Groovydoc.html" title="class in org.gradle.api.tasks.javadoc">Groovydoc</a></code>, <code><a href="javadoc/Javadoc.html" title="class in org.gradle.api.tasks.javadoc">Javadoc</a></code>, <code><a href="scala/ScalaDoc.html" title="class in org.gradle.api.tasks.scala">ScalaDoc</a></code></dd>
</dl>
<hr>
<pre><a href="../NonNullApi.html" title="annotation in org.gradle.api">@NonNullApi</a>
<a href="../../work/DisableCachingByDefault.html" title="annotation in org.gradle.work">@DisableCachingByDefault</a>(<a href="../../work/DisableCachingByDefault.html#because--">because</a>="Super-class, not to be instantiated directly")
public abstract class <span class="typeNameLabel">SourceTask</span>
extends org.gradle.api.internal.ConventionTask
implements <a href="util/PatternFilterable.html" title="interface in org.gradle.api.tasks.util">PatternFilterable</a></pre>
<div class="block">A <code>SourceTask</code> performs some operation on source files.</div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== NESTED CLASS SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="nested.class.summary">
<!--   -->
</a>
<h3>Nested Class Summary</h3>
<ul class="blockList">
<li class="blockList"><a name="nested.classes.inherited.from.class.org.gradle.api.Task">
<!--   -->
</a>
<h3>Nested classes/interfaces inherited from interface&nbsp;org.gradle.api.<a href="../Task.html" title="interface in org.gradle.api">Task</a></h3>
<code><a href="../Task.Namer.html" title="class in org.gradle.api">Task.Namer</a></code></li>
</ul>
</li>
</ul>
<!-- =========== FIELD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.summary">
<!--   -->
</a>
<h3>Field Summary</h3>
<ul class="blockList">
<li class="blockList"><a name="fields.inherited.from.class.org.gradle.api.Task">
<!--   -->
</a>
<h3>Fields inherited from interface&nbsp;org.gradle.api.<a href="../Task.html" title="interface in org.gradle.api">Task</a></h3>
<code><a href="../Task.html#TASK_ACTION">TASK_ACTION</a>, <a href="../Task.html#TASK_CONSTRUCTOR_ARGS">TASK_CONSTRUCTOR_ARGS</a>, <a href="../Task.html#TASK_DEPENDS_ON">TASK_DEPENDS_ON</a>, <a href="../Task.html#TASK_DESCRIPTION">TASK_DESCRIPTION</a>, <a href="../Task.html#TASK_GROUP">TASK_GROUP</a>, <a href="../Task.html#TASK_NAME">TASK_NAME</a>, <a href="../Task.html#TASK_OVERWRITE">TASK_OVERWRITE</a>, <a href="../Task.html#TASK_TYPE">TASK_TYPE</a></code></li>
</ul>
</li>
</ul>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="memberSummary" summary="Constructor Summary table, listing constructors, and an explanation">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Constructor</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tr class="altColor">
<th class="colConstructorName" scope="row"><code><span class="memberNameLink"><a href="#SourceTask--">SourceTask</a></span>()</code></th>
<td class="colLast">&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colSecond" scope="col">Method</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code><a href="SourceTask.html" title="class in org.gradle.api.tasks">SourceTask</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#exclude-groovy.lang.Closure-">exclude</a></span>&#8203;(<a href="https://docs.groovy-lang.org/docs/groovy-3.0.17/html/gapi/groovy/lang/Closure.html?is-external=true" title="class or interface in groovy.lang" class="externalLink">Closure</a>&nbsp;excludeSpec)</code></th>
<td class="colLast">
<div class="block">Adds an exclude spec.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code><a href="SourceTask.html" title="class in org.gradle.api.tasks">SourceTask</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#exclude-java.lang.Iterable-">exclude</a></span>&#8203;(java.lang.Iterable&lt;java.lang.String&gt;&nbsp;excludes)</code></th>
<td class="colLast">
<div class="block">Adds an ANT style exclude pattern.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code><a href="SourceTask.html" title="class in org.gradle.api.tasks">SourceTask</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#exclude-java.lang.String...-">exclude</a></span>&#8203;(java.lang.String...&nbsp;excludes)</code></th>
<td class="colLast">
<div class="block">Adds an ANT style exclude pattern.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code><a href="SourceTask.html" title="class in org.gradle.api.tasks">SourceTask</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#exclude-org.gradle.api.specs.Spec-">exclude</a></span>&#8203;(<a href="../specs/Spec.html" title="interface in org.gradle.api.specs">Spec</a>&lt;<a href="../file/FileTreeElement.html" title="interface in org.gradle.api.file">FileTreeElement</a>&gt;&nbsp;excludeSpec)</code></th>
<td class="colLast">
<div class="block">Adds an exclude spec.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>java.util.Set&lt;java.lang.String&gt;</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getExcludes--">getExcludes</a></span>()</code></th>
<td class="colLast">
<div class="block">Returns the set of exclude patterns.</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>java.util.Set&lt;java.lang.String&gt;</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getIncludes--">getIncludes</a></span>()</code></th>
<td class="colLast">
<div class="block">Returns the set of include patterns.</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>protected <a href="util/PatternFilterable.html" title="interface in org.gradle.api.tasks.util">PatternFilterable</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getPatternSet--">getPatternSet</a></span>()</code></th>
<td class="colLast">&nbsp;</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>protected org.gradle.internal.Factory&lt;<a href="util/PatternSet.html" title="class in org.gradle.api.tasks.util">PatternSet</a>&gt;</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getPatternSetFactory--">getPatternSetFactory</a></span>()</code></th>
<td class="colLast">&nbsp;</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code><a href="../file/FileTree.html" title="interface in org.gradle.api.file">FileTree</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getSource--">getSource</a></span>()</code></th>
<td class="colLast">
<div class="block">Returns the source for this task, after the include and exclude patterns have been applied.</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code><a href="SourceTask.html" title="class in org.gradle.api.tasks">SourceTask</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#include-groovy.lang.Closure-">include</a></span>&#8203;(<a href="https://docs.groovy-lang.org/docs/groovy-3.0.17/html/gapi/groovy/lang/Closure.html?is-external=true" title="class or interface in groovy.lang" class="externalLink">Closure</a>&nbsp;includeSpec)</code></th>
<td class="colLast">
<div class="block">Adds an include spec.</div>
</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code><a href="SourceTask.html" title="class in org.gradle.api.tasks">SourceTask</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#include-java.lang.Iterable-">include</a></span>&#8203;(java.lang.Iterable&lt;java.lang.String&gt;&nbsp;includes)</code></th>
<td class="colLast">
<div class="block">Adds an ANT style include pattern.</div>
</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code><a href="SourceTask.html" title="class in org.gradle.api.tasks">SourceTask</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#include-java.lang.String...-">include</a></span>&#8203;(java.lang.String...&nbsp;includes)</code></th>
<td class="colLast">
<div class="block">Adds an ANT style include pattern.</div>
</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code><a href="SourceTask.html" title="class in org.gradle.api.tasks">SourceTask</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#include-org.gradle.api.specs.Spec-">include</a></span>&#8203;(<a href="../specs/Spec.html" title="interface in org.gradle.api.specs">Spec</a>&lt;<a href="../file/FileTreeElement.html" title="interface in org.gradle.api.file">FileTreeElement</a>&gt;&nbsp;includeSpec)</code></th>
<td class="colLast">
<div class="block">Adds an include spec.</div>
</td>
</tr>
<tr id="i13" class="rowColor">
<td class="colFirst"><code><a href="SourceTask.html" title="class in org.gradle.api.tasks">SourceTask</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setExcludes-java.lang.Iterable-">setExcludes</a></span>&#8203;(java.lang.Iterable&lt;java.lang.String&gt;&nbsp;excludes)</code></th>
<td class="colLast">
<div class="block">Set the allowable exclude patterns.</div>
</td>
</tr>
<tr id="i14" class="altColor">
<td class="colFirst"><code><a href="SourceTask.html" title="class in org.gradle.api.tasks">SourceTask</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setIncludes-java.lang.Iterable-">setIncludes</a></span>&#8203;(java.lang.Iterable&lt;java.lang.String&gt;&nbsp;includes)</code></th>
<td class="colLast">
<div class="block">Set the allowable include patterns.</div>
</td>
</tr>
<tr id="i15" class="rowColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setSource-java.lang.Object-">setSource</a></span>&#8203;(java.lang.Object&nbsp;source)</code></th>
<td class="colLast">
<div class="block">Sets the source for this task.</div>
</td>
</tr>
<tr id="i16" class="altColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setSource-org.gradle.api.file.FileTree-">setSource</a></span>&#8203;(<a href="../file/FileTree.html" title="interface in org.gradle.api.file">FileTree</a>&nbsp;source)</code></th>
<td class="colLast">
<div class="block">Sets the source for this task.</div>
</td>
</tr>
<tr id="i17" class="rowColor">
<td class="colFirst"><code><a href="SourceTask.html" title="class in org.gradle.api.tasks">SourceTask</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#source-java.lang.Object...-">source</a></span>&#8203;(java.lang.Object...&nbsp;sources)</code></th>
<td class="colLast">
<div class="block">Adds some source to this task.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.org.gradle.api.internal.ConventionTask">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;org.gradle.api.internal.ConventionTask</h3>
<code>conventionMapping, conventionMapping, getConventionMapping</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.org.gradle.api.DefaultTask">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;org.gradle.api.<a href="../DefaultTask.html" title="class in org.gradle.api">DefaultTask</a></h3>
<code><a href="../DefaultTask.html#compareTo-org.gradle.api.Task-">compareTo</a>, <a href="../DefaultTask.html#configure-groovy.lang.Closure-">configure</a>, <a href="../DefaultTask.html#dependsOn-java.lang.Object...-">dependsOn</a>, <a href="../DefaultTask.html#doFirst-groovy.lang.Closure-">doFirst</a>, <a href="../DefaultTask.html#doFirst-java.lang.String-org.gradle.api.Action-">doFirst</a>, <a href="../DefaultTask.html#doFirst-org.gradle.api.Action-">doFirst</a>, <a href="../DefaultTask.html#doLast-groovy.lang.Closure-">doLast</a>, <a href="../DefaultTask.html#doLast-java.lang.String-org.gradle.api.Action-">doLast</a>, <a href="../DefaultTask.html#doLast-org.gradle.api.Action-">doLast</a>, <a href="../DefaultTask.html#finalizedBy-java.lang.Object...-">finalizedBy</a>, <a href="../DefaultTask.html#getActions--">getActions</a>, <a href="../DefaultTask.html#getAnt--">getAnt</a>, <a href="../DefaultTask.html#getDependsOn--">getDependsOn</a>, <a href="../DefaultTask.html#getDescription--">getDescription</a>, <a href="../DefaultTask.html#getDestroyables--">getDestroyables</a>, <a href="../DefaultTask.html#getDidWork--">getDidWork</a>, <a href="../DefaultTask.html#getEnabled--">getEnabled</a>, <a href="../DefaultTask.html#getExtensions--">getExtensions</a>, <a href="../DefaultTask.html#getFinalizedBy--">getFinalizedBy</a>, <a href="../DefaultTask.html#getGroup--">getGroup</a>, <a href="../DefaultTask.html#getInputs--">getInputs</a>, <a href="../DefaultTask.html#getLocalState--">getLocalState</a>, <a href="../DefaultTask.html#getLogger--">getLogger</a>, <a href="../DefaultTask.html#getLogging--">getLogging</a>, <a href="../DefaultTask.html#getMustRunAfter--">getMustRunAfter</a>, <a href="../DefaultTask.html#getName--">getName</a>, <a href="../DefaultTask.html#getOutputs--">getOutputs</a>, <a href="../DefaultTask.html#getPath--">getPath</a>, <a href="../DefaultTask.html#getProject--">getProject</a>, <a href="../DefaultTask.html#getShouldRunAfter--">getShouldRunAfter</a>, <a href="../DefaultTask.html#getState--">getState</a>, <a href="../DefaultTask.html#getTaskDependencies--">getTaskDependencies</a>, <a href="../DefaultTask.html#getTemporaryDir--">getTemporaryDir</a>, <a href="../DefaultTask.html#getTimeout--">getTimeout</a>, <a href="../DefaultTask.html#hasProperty-java.lang.String-">hasProperty</a>, <a href="../DefaultTask.html#mustRunAfter-java.lang.Object...-">mustRunAfter</a>, <a href="../DefaultTask.html#onlyIf-groovy.lang.Closure-">onlyIf</a>, <a href="../DefaultTask.html#onlyIf-java.lang.String-org.gradle.api.specs.Spec-">onlyIf</a>, <a href="../DefaultTask.html#onlyIf-org.gradle.api.specs.Spec-">onlyIf</a>, <a href="../DefaultTask.html#property-java.lang.String-">property</a>, <a href="../DefaultTask.html#setActions-java.util.List-">setActions</a>, <a href="../DefaultTask.html#setDependsOn-java.lang.Iterable-">setDependsOn</a>, <a href="../DefaultTask.html#setDescription-java.lang.String-">setDescription</a>, <a href="../DefaultTask.html#setDidWork-boolean-">setDidWork</a>, <a href="../DefaultTask.html#setEnabled-boolean-">setEnabled</a>, <a href="../DefaultTask.html#setFinalizedBy-java.lang.Iterable-">setFinalizedBy</a>, <a href="../DefaultTask.html#setGroup-java.lang.String-">setGroup</a>, <a href="../DefaultTask.html#setMustRunAfter-java.lang.Iterable-">setMustRunAfter</a>, <a href="../DefaultTask.html#setOnlyIf-groovy.lang.Closure-">setOnlyIf</a>, <a href="../DefaultTask.html#setOnlyIf-java.lang.String-org.gradle.api.specs.Spec-">setOnlyIf</a>, <a href="../DefaultTask.html#setOnlyIf-org.gradle.api.specs.Spec-">setOnlyIf</a>, <a href="../DefaultTask.html#setProperty-java.lang.String-java.lang.Object-">setProperty</a>, <a href="../DefaultTask.html#setShouldRunAfter-java.lang.Iterable-">setShouldRunAfter</a>, <a href="../DefaultTask.html#shouldRunAfter-java.lang.Object...-">shouldRunAfter</a>, <a href="../DefaultTask.html#usesService-org.gradle.api.provider.Provider-">usesService</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.org.gradle.api.internal.AbstractTask">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;org.gradle.api.internal.AbstractTask</h3>
<code>acceptServiceReferences, appendParallelSafeAction, doNotTrackState, getAsDynamicObject, getConvention, getIdentityPath, getImpliesSubProjects, getLifecycleDependencies, getOnlyIf, getReasonNotToTrackState, getReasonTaskIsIncompatibleWithConfigurationCache, getRequiredServices, getServices, getSharedResources, getStandardOutputCapture, getTaskActions, getTaskIdentity, getTemporaryDirFactory, hasTaskActions, injectIntoNewInstance, isCompatibleWithConfigurationCache, isEnabled, isHasCustomActions, notCompatibleWithConfigurationCache, prependParallelSafeAction, setImpliesSubProjects</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>clone, equals, finalize, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.org.gradle.api.Task">
<!--   -->
</a>
<h3>Methods inherited from interface&nbsp;org.gradle.api.<a href="../Task.html" title="interface in org.gradle.api">Task</a></h3>
<code><a href="../Task.html#doNotTrackState-java.lang.String-">doNotTrackState</a>, <a href="../Task.html#getConvention--">getConvention</a>, <a href="../Task.html#notCompatibleWithConfigurationCache-java.lang.String-">notCompatibleWithConfigurationCache</a></code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a name="SourceTask--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>SourceTask</h4>
<pre>public&nbsp;SourceTask()</pre>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="getPatternSetFactory--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getPatternSetFactory</h4>
<pre class="methodSignature">@Inject
protected&nbsp;org.gradle.internal.Factory&lt;<a href="util/PatternSet.html" title="class in org.gradle.api.tasks.util">PatternSet</a>&gt;&nbsp;getPatternSetFactory()</pre>
</li>
</ul>
<a name="getPatternSet--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getPatternSet</h4>
<pre class="methodSignature"><a href="Internal.html" title="annotation in org.gradle.api.tasks">@Internal</a>
protected&nbsp;<a href="util/PatternFilterable.html" title="interface in org.gradle.api.tasks.util">PatternFilterable</a>&nbsp;getPatternSet()</pre>
</li>
</ul>
<a name="getSource--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getSource</h4>
<pre class="methodSignature"><a href="InputFiles.html" title="annotation in org.gradle.api.tasks">@InputFiles</a>
<a href="SkipWhenEmpty.html" title="annotation in org.gradle.api.tasks">@SkipWhenEmpty</a>
<a href="IgnoreEmptyDirectories.html" title="annotation in org.gradle.api.tasks">@IgnoreEmptyDirectories</a>
<a href="PathSensitive.html" title="annotation in org.gradle.api.tasks">@PathSensitive</a>(<a href="PathSensitivity.html#ABSOLUTE">ABSOLUTE</a>)
public&nbsp;<a href="../file/FileTree.html" title="interface in org.gradle.api.file">FileTree</a>&nbsp;getSource()</pre>
<div class="block">Returns the source for this task, after the include and exclude patterns have been applied. Ignores source files which do not exist.

 <p>
 The <a href="PathSensitivity.html" title="enum in org.gradle.api.tasks"><code>PathSensitivity</code></a> for the sources is configured to be <a href="PathSensitivity.html#ABSOLUTE"><code>PathSensitivity.ABSOLUTE</code></a>.
 If your sources are less strict, please change it accordingly by overriding this method in your subclass.
 </p></div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The source.</dd>
</dl>
</li>
</ul>
<a name="setSource-org.gradle.api.file.FileTree-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setSource</h4>
<pre class="methodSignature">public&nbsp;void&nbsp;setSource&#8203;(<a href="../file/FileTree.html" title="interface in org.gradle.api.file">FileTree</a>&nbsp;source)</pre>
<div class="block">Sets the source for this task.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>source</code> - The source.</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>4.0</dd>
</dl>
</li>
</ul>
<a name="setSource-java.lang.Object-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setSource</h4>
<pre class="methodSignature">public&nbsp;void&nbsp;setSource&#8203;(java.lang.Object&nbsp;source)</pre>
<div class="block">Sets the source for this task. The given source object is evaluated as per <a href="../Project.html#files-java.lang.Object...-"><code>Project.files(Object...)</code></a>.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>source</code> - The source.</dd>
</dl>
</li>
</ul>
<a name="source-java.lang.Object...-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>source</h4>
<pre class="methodSignature">public&nbsp;<a href="SourceTask.html" title="class in org.gradle.api.tasks">SourceTask</a>&nbsp;source&#8203;(java.lang.Object...&nbsp;sources)</pre>
<div class="block">Adds some source to this task. The given source objects will be evaluated as per <a href="../Project.html#files-java.lang.Object...-"><code>Project.files(Object...)</code></a>.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>sources</code> - The source to add</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>this</dd>
</dl>
</li>
</ul>
<a name="include-java.lang.String...-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>include</h4>
<pre class="methodSignature">public&nbsp;<a href="SourceTask.html" title="class in org.gradle.api.tasks">SourceTask</a>&nbsp;include&#8203;(java.lang.String...&nbsp;includes)</pre>
<div class="block">Adds an ANT style include pattern. This method may be called multiple times to append new patterns and multiple
 patterns may be specified in a single call.

 If includes are not provided, then all files in this container will be included. If includes are provided, then a
 file must match at least one of the include patterns to be processed.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="util/PatternFilterable.html#include-java.lang.String...-">include</a></code>&nbsp;in interface&nbsp;<code><a href="util/PatternFilterable.html" title="interface in org.gradle.api.tasks.util">PatternFilterable</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>includes</code> - a vararg list of include patterns</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>this</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="util/PatternFilterable.html" title="interface in org.gradle.api.tasks.util"><code>Pattern Format</code></a></dd>
</dl>
</li>
</ul>
<a name="include-java.lang.Iterable-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>include</h4>
<pre class="methodSignature">public&nbsp;<a href="SourceTask.html" title="class in org.gradle.api.tasks">SourceTask</a>&nbsp;include&#8203;(java.lang.Iterable&lt;java.lang.String&gt;&nbsp;includes)</pre>
<div class="block">Adds an ANT style include pattern. This method may be called multiple times to append new patterns and multiple
 patterns may be specified in a single call.

 If includes are not provided, then all files in this container will be included. If includes are provided, then a
 file must match at least one of the include patterns to be processed.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="util/PatternFilterable.html#include-java.lang.Iterable-">include</a></code>&nbsp;in interface&nbsp;<code><a href="util/PatternFilterable.html" title="interface in org.gradle.api.tasks.util">PatternFilterable</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>includes</code> - a Iterable providing more include patterns</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>this</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="util/PatternFilterable.html" title="interface in org.gradle.api.tasks.util"><code>Pattern Format</code></a></dd>
</dl>
</li>
</ul>
<a name="include-org.gradle.api.specs.Spec-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>include</h4>
<pre class="methodSignature">public&nbsp;<a href="SourceTask.html" title="class in org.gradle.api.tasks">SourceTask</a>&nbsp;include&#8203;(<a href="../specs/Spec.html" title="interface in org.gradle.api.specs">Spec</a>&lt;<a href="../file/FileTreeElement.html" title="interface in org.gradle.api.file">FileTreeElement</a>&gt;&nbsp;includeSpec)</pre>
<div class="block">Adds an include spec. This method may be called multiple times to append new specs.

 If includes are not provided, then all files in this container will be included. If includes are provided, then a
 file must match at least one of the include patterns or specs to be included.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="util/PatternFilterable.html#include-org.gradle.api.specs.Spec-">include</a></code>&nbsp;in interface&nbsp;<code><a href="util/PatternFilterable.html" title="interface in org.gradle.api.tasks.util">PatternFilterable</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>includeSpec</code> - the spec to add</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>this</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="util/PatternFilterable.html" title="interface in org.gradle.api.tasks.util"><code>Pattern Format</code></a></dd>
</dl>
</li>
</ul>
<a name="include-groovy.lang.Closure-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>include</h4>
<pre class="methodSignature">public&nbsp;<a href="SourceTask.html" title="class in org.gradle.api.tasks">SourceTask</a>&nbsp;include&#8203;(<a href="https://docs.groovy-lang.org/docs/groovy-3.0.17/html/gapi/groovy/lang/Closure.html?is-external=true" title="class or interface in groovy.lang" class="externalLink">Closure</a>&nbsp;includeSpec)</pre>
<div class="block">Adds an include spec. This method may be called multiple times to append new specs. The given closure is passed a
 <a href="../file/FileTreeElement.html" title="interface in org.gradle.api.file"><code>FileTreeElement</code></a> as its parameter.

 If includes are not provided, then all files in this container will be included. If includes are provided, then a
 file must match at least one of the include patterns or specs to be included.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="util/PatternFilterable.html#include-groovy.lang.Closure-">include</a></code>&nbsp;in interface&nbsp;<code><a href="util/PatternFilterable.html" title="interface in org.gradle.api.tasks.util">PatternFilterable</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>includeSpec</code> - the spec to add</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>this</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="util/PatternFilterable.html" title="interface in org.gradle.api.tasks.util"><code>Pattern Format</code></a></dd>
</dl>
</li>
</ul>
<a name="exclude-java.lang.String...-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>exclude</h4>
<pre class="methodSignature">public&nbsp;<a href="SourceTask.html" title="class in org.gradle.api.tasks">SourceTask</a>&nbsp;exclude&#8203;(java.lang.String...&nbsp;excludes)</pre>
<div class="block">Adds an ANT style exclude pattern. This method may be called multiple times to append new patterns and multiple
 patterns may be specified in a single call.

 If excludes are not provided, then no files will be excluded. If excludes are provided, then files must not match
 any exclude pattern to be processed.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="util/PatternFilterable.html#exclude-java.lang.String...-">exclude</a></code>&nbsp;in interface&nbsp;<code><a href="util/PatternFilterable.html" title="interface in org.gradle.api.tasks.util">PatternFilterable</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>excludes</code> - a vararg list of exclude patterns</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>this</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="util/PatternFilterable.html" title="interface in org.gradle.api.tasks.util"><code>Pattern Format</code></a></dd>
</dl>
</li>
</ul>
<a name="exclude-java.lang.Iterable-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>exclude</h4>
<pre class="methodSignature">public&nbsp;<a href="SourceTask.html" title="class in org.gradle.api.tasks">SourceTask</a>&nbsp;exclude&#8203;(java.lang.Iterable&lt;java.lang.String&gt;&nbsp;excludes)</pre>
<div class="block">Adds an ANT style exclude pattern. This method may be called multiple times to append new patterns and multiple
 patterns may be specified in a single call.

 If excludes are not provided, then no files will be excluded. If excludes are provided, then files must not match
 any exclude pattern to be processed.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="util/PatternFilterable.html#exclude-java.lang.Iterable-">exclude</a></code>&nbsp;in interface&nbsp;<code><a href="util/PatternFilterable.html" title="interface in org.gradle.api.tasks.util">PatternFilterable</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>excludes</code> - a Iterable providing new exclude patterns</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>this</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="util/PatternFilterable.html" title="interface in org.gradle.api.tasks.util"><code>Pattern Format</code></a></dd>
</dl>
</li>
</ul>
<a name="exclude-org.gradle.api.specs.Spec-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>exclude</h4>
<pre class="methodSignature">public&nbsp;<a href="SourceTask.html" title="class in org.gradle.api.tasks">SourceTask</a>&nbsp;exclude&#8203;(<a href="../specs/Spec.html" title="interface in org.gradle.api.specs">Spec</a>&lt;<a href="../file/FileTreeElement.html" title="interface in org.gradle.api.file">FileTreeElement</a>&gt;&nbsp;excludeSpec)</pre>
<div class="block">Adds an exclude spec. This method may be called multiple times to append new specs.

 If excludes are not provided, then no files will be excluded. If excludes are provided, then files must not match
 any exclude pattern to be processed.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="util/PatternFilterable.html#exclude-org.gradle.api.specs.Spec-">exclude</a></code>&nbsp;in interface&nbsp;<code><a href="util/PatternFilterable.html" title="interface in org.gradle.api.tasks.util">PatternFilterable</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>excludeSpec</code> - the spec to add</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>this</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="util/PatternFilterable.html" title="interface in org.gradle.api.tasks.util"><code>Pattern Format</code></a></dd>
</dl>
</li>
</ul>
<a name="exclude-groovy.lang.Closure-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>exclude</h4>
<pre class="methodSignature">public&nbsp;<a href="SourceTask.html" title="class in org.gradle.api.tasks">SourceTask</a>&nbsp;exclude&#8203;(<a href="https://docs.groovy-lang.org/docs/groovy-3.0.17/html/gapi/groovy/lang/Closure.html?is-external=true" title="class or interface in groovy.lang" class="externalLink">Closure</a>&nbsp;excludeSpec)</pre>
<div class="block">Adds an exclude spec. This method may be called multiple times to append new specs.The given closure is passed a
 <a href="../file/FileTreeElement.html" title="interface in org.gradle.api.file"><code>FileTreeElement</code></a> as its parameter. The closure should return true or false. Example:

 <pre class='autoTested'>
 copySpec {
   from 'source'
   into 'destination'
   //an example of excluding files from certain configuration:
   exclude { it.file in configurations.someConf.files }
 }
 </pre>

 If excludes are not provided, then no files will be excluded. If excludes are provided, then files must not match
 any exclude pattern to be processed.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="util/PatternFilterable.html#exclude-groovy.lang.Closure-">exclude</a></code>&nbsp;in interface&nbsp;<code><a href="util/PatternFilterable.html" title="interface in org.gradle.api.tasks.util">PatternFilterable</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>excludeSpec</code> - the spec to add</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>this</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../file/FileTreeElement.html" title="interface in org.gradle.api.file"><code>FileTreeElement</code></a></dd>
</dl>
</li>
</ul>
<a name="getIncludes--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getIncludes</h4>
<pre class="methodSignature"><a href="Internal.html" title="annotation in org.gradle.api.tasks">@Internal</a>
public&nbsp;java.util.Set&lt;java.lang.String&gt;&nbsp;getIncludes()</pre>
<div class="block">Returns the set of include patterns.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="util/PatternFilterable.html#getIncludes--">getIncludes</a></code>&nbsp;in interface&nbsp;<code><a href="util/PatternFilterable.html" title="interface in org.gradle.api.tasks.util">PatternFilterable</a></code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The include patterns. Returns an empty set when there are no include patterns.</dd>
</dl>
</li>
</ul>
<a name="setIncludes-java.lang.Iterable-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setIncludes</h4>
<pre class="methodSignature">public&nbsp;<a href="SourceTask.html" title="class in org.gradle.api.tasks">SourceTask</a>&nbsp;setIncludes&#8203;(java.lang.Iterable&lt;java.lang.String&gt;&nbsp;includes)</pre>
<div class="block">Set the allowable include patterns.  Note that unlike <a href="util/PatternFilterable.html#include-java.lang.Iterable-"><code>PatternFilterable.include(Iterable)</code></a> this replaces any previously
 defined includes.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="util/PatternFilterable.html#setIncludes-java.lang.Iterable-">setIncludes</a></code>&nbsp;in interface&nbsp;<code><a href="util/PatternFilterable.html" title="interface in org.gradle.api.tasks.util">PatternFilterable</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>includes</code> - an Iterable providing new include patterns</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>this</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="util/PatternFilterable.html" title="interface in org.gradle.api.tasks.util"><code>Pattern Format</code></a></dd>
</dl>
</li>
</ul>
<a name="getExcludes--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getExcludes</h4>
<pre class="methodSignature"><a href="Internal.html" title="annotation in org.gradle.api.tasks">@Internal</a>
public&nbsp;java.util.Set&lt;java.lang.String&gt;&nbsp;getExcludes()</pre>
<div class="block">Returns the set of exclude patterns.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="util/PatternFilterable.html#getExcludes--">getExcludes</a></code>&nbsp;in interface&nbsp;<code><a href="util/PatternFilterable.html" title="interface in org.gradle.api.tasks.util">PatternFilterable</a></code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The exclude patterns. Returns an empty set when there are no exclude patterns.</dd>
</dl>
</li>
</ul>
<a name="setExcludes-java.lang.Iterable-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>setExcludes</h4>
<pre class="methodSignature">public&nbsp;<a href="SourceTask.html" title="class in org.gradle.api.tasks">SourceTask</a>&nbsp;setExcludes&#8203;(java.lang.Iterable&lt;java.lang.String&gt;&nbsp;excludes)</pre>
<div class="block">Set the allowable exclude patterns.  Note that unlike <a href="util/PatternFilterable.html#exclude-java.lang.Iterable-"><code>PatternFilterable.exclude(Iterable)</code></a> this replaces any previously
 defined excludes.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="util/PatternFilterable.html#setExcludes-java.lang.Iterable-">setExcludes</a></code>&nbsp;in interface&nbsp;<code><a href="util/PatternFilterable.html" title="interface in org.gradle.api.tasks.util">PatternFilterable</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>excludes</code> - an Iterable providing new exclude patterns</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>this</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="util/PatternFilterable.html" title="interface in org.gradle.api.tasks.util"><code>Pattern Format</code></a></dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li><a href="#nested.class.summary">Nested</a>&nbsp;|&nbsp;</li>
<li><a href="#field.summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
