<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc -->
<title>ScalaCompileOptions (Gradle API 8.4)</title>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<link rel="stylesheet" type="text/css" href="../../../../../javadoc.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../jquery/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
<script type="text/javascript" src="../../../../../jquery/jszip/dist/jszip.min.js"></script>
<script type="text/javascript" src="../../../../../jquery/jszip-utils/dist/jszip-utils.min.js"></script>
<!--[if IE]>
<script type="text/javascript" src="../../../../../jquery/jszip-utils/dist/jszip-utils-ie.min.js"></script>
<![endif]-->
<script type="text/javascript" src="../../../../../jquery/jquery-3.6.0.min.js"></script>
<script type="text/javascript" src="../../../../../jquery/jquery-ui.min.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="ScalaCompileOptions (Gradle API 8.4)";
        }
    }
    catch(err) {
    }
//-->
var pathtoroot = "../../../../../";
var useModuleDirectories = false;
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="fixedNav">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../../allclasses.html">All&nbsp;Classes</a></li>
</ul>
<ul class="navListSearch">
<li><label for="search">SEARCH:</label>
<input type="text" id="search" value="search" disabled="disabled">
<input type="reset" id="reset" value="reset" disabled="disabled">
</li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li>Method</li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
</div>
<div class="navPadding">&nbsp;</div>
<script type="text/javascript"><!--
$('.navPadding').css('padding-top', $('.fixedNav').css("height"));
//-->
</script>
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle"><span class="packageLabelInType">Package</span>&nbsp;<a href="package-summary.html">org.gradle.api.tasks.scala</a></div>
<h2 title="Class ScalaCompileOptions" class="title">Class ScalaCompileOptions</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li><a href="../compile/AbstractOptions.html" title="class in org.gradle.api.tasks.compile">org.gradle.api.tasks.compile.AbstractOptions</a></li>
<li>
<ul class="inheritance">
<li><a href="../../../language/scala/tasks/BaseScalaCompileOptions.html" title="class in org.gradle.language.scala.tasks">org.gradle.language.scala.tasks.BaseScalaCompileOptions</a></li>
<li>
<ul class="inheritance">
<li>org.gradle.api.tasks.scala.ScalaCompileOptions</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Implemented Interfaces:</dt>
<dd><code>java.io.Serializable</code></dd>
</dl>
<hr>
<pre>public abstract class <span class="typeNameLabel">ScalaCompileOptions</span>
extends <a href="../../../language/scala/tasks/BaseScalaCompileOptions.html" title="class in org.gradle.language.scala.tasks">BaseScalaCompileOptions</a></pre>
<div class="block">Options for Scala Compilation.</div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../../../serialized-form.html#org.gradle.api.tasks.scala.ScalaCompileOptions">Serialized Form</a></dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="memberSummary" summary="Constructor Summary table, listing constructors, and an explanation">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Constructor</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tr class="altColor">
<th class="colConstructorName" scope="row"><code><span class="memberNameLink"><a href="#ScalaCompileOptions--">ScalaCompileOptions</a></span>()</code></th>
<td class="colLast">&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.org.gradle.language.scala.tasks.BaseScalaCompileOptions">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;org.gradle.language.scala.tasks.<a href="../../../language/scala/tasks/BaseScalaCompileOptions.html" title="class in org.gradle.language.scala.tasks">BaseScalaCompileOptions</a></h3>
<code><a href="../../../language/scala/tasks/BaseScalaCompileOptions.html#getAdditionalParameters--">getAdditionalParameters</a>, <a href="../../../language/scala/tasks/BaseScalaCompileOptions.html#getDebugLevel--">getDebugLevel</a>, <a href="../../../language/scala/tasks/BaseScalaCompileOptions.html#getEncoding--">getEncoding</a>, <a href="../../../language/scala/tasks/BaseScalaCompileOptions.html#getForkOptions--">getForkOptions</a>, <a href="../../../language/scala/tasks/BaseScalaCompileOptions.html#getIncrementalOptions--">getIncrementalOptions</a>, <a href="../../../language/scala/tasks/BaseScalaCompileOptions.html#getKeepAliveMode--">getKeepAliveMode</a>, <a href="../../../language/scala/tasks/BaseScalaCompileOptions.html#getLoggingLevel--">getLoggingLevel</a>, <a href="../../../language/scala/tasks/BaseScalaCompileOptions.html#getLoggingPhases--">getLoggingPhases</a>, <a href="../../../language/scala/tasks/BaseScalaCompileOptions.html#getObjectFactory--">getObjectFactory</a>, <a href="../../../language/scala/tasks/BaseScalaCompileOptions.html#isDeprecation--">isDeprecation</a>, <a href="../../../language/scala/tasks/BaseScalaCompileOptions.html#isFailOnError--">isFailOnError</a>, <a href="../../../language/scala/tasks/BaseScalaCompileOptions.html#isForce--">isForce</a>, <a href="../../../language/scala/tasks/BaseScalaCompileOptions.html#isListFiles--">isListFiles</a>, <a href="../../../language/scala/tasks/BaseScalaCompileOptions.html#isOptimize--">isOptimize</a>, <a href="../../../language/scala/tasks/BaseScalaCompileOptions.html#isUnchecked--">isUnchecked</a>, <a href="../../../language/scala/tasks/BaseScalaCompileOptions.html#setAdditionalParameters-java.util.List-">setAdditionalParameters</a>, <a href="../../../language/scala/tasks/BaseScalaCompileOptions.html#setDebugLevel-java.lang.String-">setDebugLevel</a>, <a href="../../../language/scala/tasks/BaseScalaCompileOptions.html#setDeprecation-boolean-">setDeprecation</a>, <a href="../../../language/scala/tasks/BaseScalaCompileOptions.html#setEncoding-java.lang.String-">setEncoding</a>, <a href="../../../language/scala/tasks/BaseScalaCompileOptions.html#setFailOnError-boolean-">setFailOnError</a>, <a href="../../../language/scala/tasks/BaseScalaCompileOptions.html#setForce-boolean-">setForce</a>, <a href="../../../language/scala/tasks/BaseScalaCompileOptions.html#setForkOptions-org.gradle.api.tasks.scala.ScalaForkOptions-">setForkOptions</a>, <a href="../../../language/scala/tasks/BaseScalaCompileOptions.html#setIncrementalOptions-org.gradle.api.tasks.scala.IncrementalCompileOptions-">setIncrementalOptions</a>, <a href="../../../language/scala/tasks/BaseScalaCompileOptions.html#setListFiles-boolean-">setListFiles</a>, <a href="../../../language/scala/tasks/BaseScalaCompileOptions.html#setLoggingLevel-java.lang.String-">setLoggingLevel</a>, <a href="../../../language/scala/tasks/BaseScalaCompileOptions.html#setLoggingPhases-java.util.List-">setLoggingPhases</a>, <a href="../../../language/scala/tasks/BaseScalaCompileOptions.html#setOptimize-boolean-">setOptimize</a>, <a href="../../../language/scala/tasks/BaseScalaCompileOptions.html#setUnchecked-boolean-">setUnchecked</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.org.gradle.api.tasks.compile.AbstractOptions">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;org.gradle.api.tasks.compile.<a href="../compile/AbstractOptions.html" title="class in org.gradle.api.tasks.compile">AbstractOptions</a></h3>
<code><a href="../compile/AbstractOptions.html#define-java.util.Map-">define</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>clone, equals, finalize, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a name="ScalaCompileOptions--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>ScalaCompileOptions</h4>
<pre>public&nbsp;ScalaCompileOptions()</pre>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../../allclasses.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li>Method</li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
