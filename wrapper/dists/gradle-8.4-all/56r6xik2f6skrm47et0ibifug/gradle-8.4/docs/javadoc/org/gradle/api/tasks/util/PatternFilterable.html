<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc -->
<title>PatternFilterable (Gradle API 8.4)</title>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<link rel="stylesheet" type="text/css" href="../../../../../javadoc.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../jquery/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
<script type="text/javascript" src="../../../../../jquery/jszip/dist/jszip.min.js"></script>
<script type="text/javascript" src="../../../../../jquery/jszip-utils/dist/jszip-utils.min.js"></script>
<!--[if IE]>
<script type="text/javascript" src="../../../../../jquery/jszip-utils/dist/jszip-utils-ie.min.js"></script>
<![endif]-->
<script type="text/javascript" src="../../../../../jquery/jquery-3.6.0.min.js"></script>
<script type="text/javascript" src="../../../../../jquery/jquery-ui.min.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="PatternFilterable (Gradle API 8.4)";
        }
    }
    catch(err) {
    }
//-->
var data = {"i0":6,"i1":6,"i2":6,"i3":6,"i4":6,"i5":6,"i6":6,"i7":6,"i8":6,"i9":6,"i10":6,"i11":6};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],4:["t3","Abstract Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
var pathtoroot = "../../../../../";
var useModuleDirectories = false;
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="fixedNav">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../../allclasses.html">All&nbsp;Classes</a></li>
</ul>
<ul class="navListSearch">
<li><label for="search">SEARCH:</label>
<input type="text" id="search" value="search" disabled="disabled">
<input type="reset" id="reset" value="reset" disabled="disabled">
</li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
</div>
<div class="navPadding">&nbsp;</div>
<script type="text/javascript"><!--
$('.navPadding').css('padding-top', $('.fixedNav').css("height"));
//-->
</script>
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle"><span class="packageLabelInType">Package</span>&nbsp;<a href="package-summary.html">org.gradle.api.tasks.util</a></div>
<h2 title="Interface PatternFilterable" class="title">Interface PatternFilterable</h2>
</div>
<div class="contentContainer">
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Known Subinterfaces:</dt>
<dd><code><a href="../../plugins/antlr/AntlrSourceDirectorySet.html" title="interface in org.gradle.api.plugins.antlr">AntlrSourceDirectorySet</a></code>, <code><a href="../../file/ConfigurableFileTree.html" title="interface in org.gradle.api.file">ConfigurableFileTree</a></code>, <code><a href="../../file/CopySpec.html" title="interface in org.gradle.api.file">CopySpec</a></code>, <code><a href="../GroovySourceDirectorySet.html" title="interface in org.gradle.api.tasks">GroovySourceDirectorySet</a></code>, <code><a href="../ScalaSourceDirectorySet.html" title="interface in org.gradle.api.tasks">ScalaSourceDirectorySet</a></code>, <code><a href="../../file/SourceDirectorySet.html" title="interface in org.gradle.api.file">SourceDirectorySet</a></code>, <code><a href="../../file/SyncSpec.html" title="interface in org.gradle.api.file">SyncSpec</a></code></dd>
</dl>
<dl>
<dt>All Known Implementing Classes:</dt>
<dd><code><a href="../bundling/AbstractArchiveTask.html" title="class in org.gradle.api.tasks.bundling">AbstractArchiveTask</a></code>, <code><a href="../../plugins/quality/AbstractCodeQualityTask.html" title="class in org.gradle.api.plugins.quality">AbstractCodeQualityTask</a></code>, <code><a href="../compile/AbstractCompile.html" title="class in org.gradle.api.tasks.compile">AbstractCompile</a></code>, <code><a href="../AbstractCopyTask.html" title="class in org.gradle.api.tasks">AbstractCopyTask</a></code>, <code><a href="../../../language/scala/tasks/AbstractScalaCompile.html" title="class in org.gradle.language.scala.tasks">AbstractScalaCompile</a></code>, <code><a href="../../plugins/antlr/AntlrTask.html" title="class in org.gradle.api.plugins.antlr">AntlrTask</a></code>, <code><a href="../../plugins/quality/Checkstyle.html" title="class in org.gradle.api.plugins.quality">Checkstyle</a></code>, <code><a href="../../plugins/quality/CodeNarc.html" title="class in org.gradle.api.plugins.quality">CodeNarc</a></code>, <code><a href="../Copy.html" title="class in org.gradle.api.tasks">Copy</a></code>, <code><a href="../../../plugins/ear/Ear.html" title="class in org.gradle.plugins.ear">Ear</a></code>, <code><a href="../compile/GroovyCompile.html" title="class in org.gradle.api.tasks.compile">GroovyCompile</a></code>, <code><a href="../javadoc/Groovydoc.html" title="class in org.gradle.api.tasks.javadoc">Groovydoc</a></code>, <code><a href="../bundling/Jar.html" title="class in org.gradle.api.tasks.bundling">Jar</a></code>, <code><a href="../../../jvm/tasks/Jar.html" title="class in org.gradle.jvm.tasks">Jar</a></code>, <code><a href="../compile/JavaCompile.html" title="class in org.gradle.api.tasks.compile">JavaCompile</a></code>, <code><a href="../javadoc/Javadoc.html" title="class in org.gradle.api.tasks.javadoc">Javadoc</a></code>, <code><a href="PatternSet.html" title="class in org.gradle.api.tasks.util">PatternSet</a></code>, <code><a href="../../plugins/quality/Pmd.html" title="class in org.gradle.api.plugins.quality">Pmd</a></code>, <code><a href="../../../language/jvm/tasks/ProcessResources.html" title="class in org.gradle.language.jvm.tasks">ProcessResources</a></code>, <code><a href="../scala/ScalaCompile.html" title="class in org.gradle.api.tasks.scala">ScalaCompile</a></code>, <code><a href="../scala/ScalaDoc.html" title="class in org.gradle.api.tasks.scala">ScalaDoc</a></code>, <code><a href="../SourceTask.html" title="class in org.gradle.api.tasks">SourceTask</a></code>, <code><a href="../Sync.html" title="class in org.gradle.api.tasks">Sync</a></code>, <code><a href="../bundling/Tar.html" title="class in org.gradle.api.tasks.bundling">Tar</a></code>, <code><a href="../testing/Test.html" title="class in org.gradle.api.tasks.testing">Test</a></code>, <code><a href="../bundling/War.html" title="class in org.gradle.api.tasks.bundling">War</a></code>, <code><a href="../bundling/Zip.html" title="class in org.gradle.api.tasks.bundling">Zip</a></code></dd>
</dl>
<hr>
<pre>public interface <span class="typeNameLabel">PatternFilterable</span></pre>
<div class="block"><p>A <code>PatternFilterable</code> represents some file container which Ant-style include and exclude patterns or specs
 can be applied to.</p>

 <p>Patterns may include:</p>

 <ul>

 <li>'*' to match any number of characters

 <li>'?' to match any single character

 <li>'**' to match any number of directories or files

 </ul>

 <p>Either '/' or '\' may be used in a pattern to separate directories. Patterns ending with '/' or '\' will have '**'
 automatically appended.</p>

 <p>Examples:</p>
 <pre>
 all files ending with 'jsp' (including subdirectories)
    **&#47;*.jsp

 all files beginning with 'template_' in the level1/level2 directory
    level1/level2/template_*

 all files (including subdirectories) beneath src/main/webapp
   src/main/webapp/

 all files beneath any .svn directory (including subdirectories) under src/main/java
   src/main/java/**&#47;.svn/**
 </pre>

 <p>You may also use a closure or <a href="../../specs/Spec.html" title="interface in org.gradle.api.specs"><code>Spec</code></a> to specify which files to include or exclude. The closure or <a href="../../specs/Spec.html" title="interface in org.gradle.api.specs"><code>Spec</code></a>
 is passed a <a href="../../file/FileTreeElement.html" title="interface in org.gradle.api.file"><code>FileTreeElement</code></a>, and must return a boolean value.</p>

 <p>If no include patterns or specs are specified, then all files in this container will be included. If any include
 patterns or specs are specified, then a file is included if it matches any of the patterns or specs.</p>

 <p>If no exclude patterns or spec are specified, then no files will be excluded. If any exclude patterns or specs are
 specified, then a file is include only if it matches none of the patterns or specs.</p></div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t3" class="tableTab"><span><a href="javascript:show(4);">Abstract Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colSecond" scope="col">Method</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code><a href="PatternFilterable.html" title="interface in org.gradle.api.tasks.util">PatternFilterable</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#exclude-groovy.lang.Closure-">exclude</a></span>&#8203;(<a href="https://docs.groovy-lang.org/docs/groovy-3.0.17/html/gapi/groovy/lang/Closure.html?is-external=true" title="class or interface in groovy.lang" class="externalLink">Closure</a>&nbsp;excludeSpec)</code></th>
<td class="colLast">
<div class="block">Adds an exclude spec.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code><a href="PatternFilterable.html" title="interface in org.gradle.api.tasks.util">PatternFilterable</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#exclude-java.lang.Iterable-">exclude</a></span>&#8203;(java.lang.Iterable&lt;java.lang.String&gt;&nbsp;excludes)</code></th>
<td class="colLast">
<div class="block">Adds an ANT style exclude pattern.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code><a href="PatternFilterable.html" title="interface in org.gradle.api.tasks.util">PatternFilterable</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#exclude-java.lang.String...-">exclude</a></span>&#8203;(java.lang.String...&nbsp;excludes)</code></th>
<td class="colLast">
<div class="block">Adds an ANT style exclude pattern.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code><a href="PatternFilterable.html" title="interface in org.gradle.api.tasks.util">PatternFilterable</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#exclude-org.gradle.api.specs.Spec-">exclude</a></span>&#8203;(<a href="../../specs/Spec.html" title="interface in org.gradle.api.specs">Spec</a>&lt;<a href="../../file/FileTreeElement.html" title="interface in org.gradle.api.file">FileTreeElement</a>&gt;&nbsp;excludeSpec)</code></th>
<td class="colLast">
<div class="block">Adds an exclude spec.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>java.util.Set&lt;java.lang.String&gt;</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getExcludes--">getExcludes</a></span>()</code></th>
<td class="colLast">
<div class="block">Returns the set of exclude patterns.</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>java.util.Set&lt;java.lang.String&gt;</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getIncludes--">getIncludes</a></span>()</code></th>
<td class="colLast">
<div class="block">Returns the set of include patterns.</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code><a href="PatternFilterable.html" title="interface in org.gradle.api.tasks.util">PatternFilterable</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#include-groovy.lang.Closure-">include</a></span>&#8203;(<a href="https://docs.groovy-lang.org/docs/groovy-3.0.17/html/gapi/groovy/lang/Closure.html?is-external=true" title="class or interface in groovy.lang" class="externalLink">Closure</a>&nbsp;includeSpec)</code></th>
<td class="colLast">
<div class="block">Adds an include spec.</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code><a href="PatternFilterable.html" title="interface in org.gradle.api.tasks.util">PatternFilterable</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#include-java.lang.Iterable-">include</a></span>&#8203;(java.lang.Iterable&lt;java.lang.String&gt;&nbsp;includes)</code></th>
<td class="colLast">
<div class="block">Adds an ANT style include pattern.</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code><a href="PatternFilterable.html" title="interface in org.gradle.api.tasks.util">PatternFilterable</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#include-java.lang.String...-">include</a></span>&#8203;(java.lang.String...&nbsp;includes)</code></th>
<td class="colLast">
<div class="block">Adds an ANT style include pattern.</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code><a href="PatternFilterable.html" title="interface in org.gradle.api.tasks.util">PatternFilterable</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#include-org.gradle.api.specs.Spec-">include</a></span>&#8203;(<a href="../../specs/Spec.html" title="interface in org.gradle.api.specs">Spec</a>&lt;<a href="../../file/FileTreeElement.html" title="interface in org.gradle.api.file">FileTreeElement</a>&gt;&nbsp;includeSpec)</code></th>
<td class="colLast">
<div class="block">Adds an include spec.</div>
</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code><a href="PatternFilterable.html" title="interface in org.gradle.api.tasks.util">PatternFilterable</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setExcludes-java.lang.Iterable-">setExcludes</a></span>&#8203;(java.lang.Iterable&lt;java.lang.String&gt;&nbsp;excludes)</code></th>
<td class="colLast">
<div class="block">Set the allowable exclude patterns.</div>
</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code><a href="PatternFilterable.html" title="interface in org.gradle.api.tasks.util">PatternFilterable</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setIncludes-java.lang.Iterable-">setIncludes</a></span>&#8203;(java.lang.Iterable&lt;java.lang.String&gt;&nbsp;includes)</code></th>
<td class="colLast">
<div class="block">Set the allowable include patterns.</div>
</td>
</tr>
</table>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="getIncludes--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getIncludes</h4>
<pre class="methodSignature">java.util.Set&lt;java.lang.String&gt;&nbsp;getIncludes()</pre>
<div class="block">Returns the set of include patterns.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The include patterns. Returns an empty set when there are no include patterns.</dd>
</dl>
</li>
</ul>
<a name="getExcludes--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getExcludes</h4>
<pre class="methodSignature">java.util.Set&lt;java.lang.String&gt;&nbsp;getExcludes()</pre>
<div class="block">Returns the set of exclude patterns.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The exclude patterns. Returns an empty set when there are no exclude patterns.</dd>
</dl>
</li>
</ul>
<a name="setIncludes-java.lang.Iterable-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setIncludes</h4>
<pre class="methodSignature"><a href="PatternFilterable.html" title="interface in org.gradle.api.tasks.util">PatternFilterable</a>&nbsp;setIncludes&#8203;(java.lang.Iterable&lt;java.lang.String&gt;&nbsp;includes)</pre>
<div class="block">Set the allowable include patterns.  Note that unlike <a href="#include-java.lang.Iterable-"><code>include(Iterable)</code></a> this replaces any previously
 defined includes.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>includes</code> - an Iterable providing new include patterns</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>this</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="PatternFilterable.html" title="interface in org.gradle.api.tasks.util"><code>Pattern Format</code></a></dd>
</dl>
</li>
</ul>
<a name="setExcludes-java.lang.Iterable-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setExcludes</h4>
<pre class="methodSignature"><a href="PatternFilterable.html" title="interface in org.gradle.api.tasks.util">PatternFilterable</a>&nbsp;setExcludes&#8203;(java.lang.Iterable&lt;java.lang.String&gt;&nbsp;excludes)</pre>
<div class="block">Set the allowable exclude patterns.  Note that unlike <a href="#exclude-java.lang.Iterable-"><code>exclude(Iterable)</code></a> this replaces any previously
 defined excludes.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>excludes</code> - an Iterable providing new exclude patterns</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>this</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="PatternFilterable.html" title="interface in org.gradle.api.tasks.util"><code>Pattern Format</code></a></dd>
</dl>
</li>
</ul>
<a name="include-java.lang.String...-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>include</h4>
<pre class="methodSignature"><a href="PatternFilterable.html" title="interface in org.gradle.api.tasks.util">PatternFilterable</a>&nbsp;include&#8203;(java.lang.String...&nbsp;includes)</pre>
<div class="block">Adds an ANT style include pattern. This method may be called multiple times to append new patterns and multiple
 patterns may be specified in a single call.

 If includes are not provided, then all files in this container will be included. If includes are provided, then a
 file must match at least one of the include patterns to be processed.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>includes</code> - a vararg list of include patterns</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>this</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="PatternFilterable.html" title="interface in org.gradle.api.tasks.util"><code>Pattern Format</code></a></dd>
</dl>
</li>
</ul>
<a name="include-java.lang.Iterable-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>include</h4>
<pre class="methodSignature"><a href="PatternFilterable.html" title="interface in org.gradle.api.tasks.util">PatternFilterable</a>&nbsp;include&#8203;(java.lang.Iterable&lt;java.lang.String&gt;&nbsp;includes)</pre>
<div class="block">Adds an ANT style include pattern. This method may be called multiple times to append new patterns and multiple
 patterns may be specified in a single call.

 If includes are not provided, then all files in this container will be included. If includes are provided, then a
 file must match at least one of the include patterns to be processed.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>includes</code> - a Iterable providing more include patterns</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>this</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="PatternFilterable.html" title="interface in org.gradle.api.tasks.util"><code>Pattern Format</code></a></dd>
</dl>
</li>
</ul>
<a name="include-org.gradle.api.specs.Spec-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>include</h4>
<pre class="methodSignature"><a href="PatternFilterable.html" title="interface in org.gradle.api.tasks.util">PatternFilterable</a>&nbsp;include&#8203;(<a href="../../specs/Spec.html" title="interface in org.gradle.api.specs">Spec</a>&lt;<a href="../../file/FileTreeElement.html" title="interface in org.gradle.api.file">FileTreeElement</a>&gt;&nbsp;includeSpec)</pre>
<div class="block">Adds an include spec. This method may be called multiple times to append new specs.

 If includes are not provided, then all files in this container will be included. If includes are provided, then a
 file must match at least one of the include patterns or specs to be included.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>includeSpec</code> - the spec to add</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>this</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="PatternFilterable.html" title="interface in org.gradle.api.tasks.util"><code>Pattern Format</code></a></dd>
</dl>
</li>
</ul>
<a name="include-groovy.lang.Closure-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>include</h4>
<pre class="methodSignature"><a href="PatternFilterable.html" title="interface in org.gradle.api.tasks.util">PatternFilterable</a>&nbsp;include&#8203;(<a href="https://docs.groovy-lang.org/docs/groovy-3.0.17/html/gapi/groovy/lang/Closure.html?is-external=true" title="class or interface in groovy.lang" class="externalLink">Closure</a>&nbsp;includeSpec)</pre>
<div class="block">Adds an include spec. This method may be called multiple times to append new specs. The given closure is passed a
 <a href="../../file/FileTreeElement.html" title="interface in org.gradle.api.file"><code>FileTreeElement</code></a> as its parameter.

 If includes are not provided, then all files in this container will be included. If includes are provided, then a
 file must match at least one of the include patterns or specs to be included.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>includeSpec</code> - the spec to add</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>this</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="PatternFilterable.html" title="interface in org.gradle.api.tasks.util"><code>Pattern Format</code></a></dd>
</dl>
</li>
</ul>
<a name="exclude-java.lang.String...-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>exclude</h4>
<pre class="methodSignature"><a href="PatternFilterable.html" title="interface in org.gradle.api.tasks.util">PatternFilterable</a>&nbsp;exclude&#8203;(java.lang.String...&nbsp;excludes)</pre>
<div class="block">Adds an ANT style exclude pattern. This method may be called multiple times to append new patterns and multiple
 patterns may be specified in a single call.

 If excludes are not provided, then no files will be excluded. If excludes are provided, then files must not match
 any exclude pattern to be processed.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>excludes</code> - a vararg list of exclude patterns</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>this</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="PatternFilterable.html" title="interface in org.gradle.api.tasks.util"><code>Pattern Format</code></a></dd>
</dl>
</li>
</ul>
<a name="exclude-java.lang.Iterable-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>exclude</h4>
<pre class="methodSignature"><a href="PatternFilterable.html" title="interface in org.gradle.api.tasks.util">PatternFilterable</a>&nbsp;exclude&#8203;(java.lang.Iterable&lt;java.lang.String&gt;&nbsp;excludes)</pre>
<div class="block">Adds an ANT style exclude pattern. This method may be called multiple times to append new patterns and multiple
 patterns may be specified in a single call.

 If excludes are not provided, then no files will be excluded. If excludes are provided, then files must not match
 any exclude pattern to be processed.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>excludes</code> - a Iterable providing new exclude patterns</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>this</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="PatternFilterable.html" title="interface in org.gradle.api.tasks.util"><code>Pattern Format</code></a></dd>
</dl>
</li>
</ul>
<a name="exclude-org.gradle.api.specs.Spec-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>exclude</h4>
<pre class="methodSignature"><a href="PatternFilterable.html" title="interface in org.gradle.api.tasks.util">PatternFilterable</a>&nbsp;exclude&#8203;(<a href="../../specs/Spec.html" title="interface in org.gradle.api.specs">Spec</a>&lt;<a href="../../file/FileTreeElement.html" title="interface in org.gradle.api.file">FileTreeElement</a>&gt;&nbsp;excludeSpec)</pre>
<div class="block">Adds an exclude spec. This method may be called multiple times to append new specs.

 If excludes are not provided, then no files will be excluded. If excludes are provided, then files must not match
 any exclude pattern to be processed.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>excludeSpec</code> - the spec to add</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>this</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="PatternFilterable.html" title="interface in org.gradle.api.tasks.util"><code>Pattern Format</code></a></dd>
</dl>
</li>
</ul>
<a name="exclude-groovy.lang.Closure-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>exclude</h4>
<pre class="methodSignature"><a href="PatternFilterable.html" title="interface in org.gradle.api.tasks.util">PatternFilterable</a>&nbsp;exclude&#8203;(<a href="https://docs.groovy-lang.org/docs/groovy-3.0.17/html/gapi/groovy/lang/Closure.html?is-external=true" title="class or interface in groovy.lang" class="externalLink">Closure</a>&nbsp;excludeSpec)</pre>
<div class="block">Adds an exclude spec. This method may be called multiple times to append new specs.The given closure is passed a
 <a href="../../file/FileTreeElement.html" title="interface in org.gradle.api.file"><code>FileTreeElement</code></a> as its parameter. The closure should return true or false. Example:

 <pre class='autoTested'>
 copySpec {
   from 'source'
   into 'destination'
   //an example of excluding files from certain configuration:
   exclude { it.file in configurations.someConf.files }
 }
 </pre>

 If excludes are not provided, then no files will be excluded. If excludes are provided, then files must not match
 any exclude pattern to be processed.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>excludeSpec</code> - the spec to add</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>this</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../file/FileTreeElement.html" title="interface in org.gradle.api.file"><code>FileTreeElement</code></a></dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../../allclasses.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
