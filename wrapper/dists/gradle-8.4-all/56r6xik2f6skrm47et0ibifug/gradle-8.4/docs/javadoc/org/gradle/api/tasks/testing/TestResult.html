<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc -->
<title>TestResult (Gradle API 8.4)</title>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<link rel="stylesheet" type="text/css" href="../../../../../javadoc.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../jquery/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
<script type="text/javascript" src="../../../../../jquery/jszip/dist/jszip.min.js"></script>
<script type="text/javascript" src="../../../../../jquery/jszip-utils/dist/jszip-utils.min.js"></script>
<!--[if IE]>
<script type="text/javascript" src="../../../../../jquery/jszip-utils/dist/jszip-utils-ie.min.js"></script>
<![endif]-->
<script type="text/javascript" src="../../../../../jquery/jquery-3.6.0.min.js"></script>
<script type="text/javascript" src="../../../../../jquery/jquery-ui.min.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="TestResult (Gradle API 8.4)";
        }
    }
    catch(err) {
    }
//-->
var data = {"i0":6,"i1":6,"i2":6,"i3":6,"i4":6,"i5":6,"i6":6,"i7":6,"i8":6,"i9":6};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],4:["t3","Abstract Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
var pathtoroot = "../../../../../";
var useModuleDirectories = false;
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="fixedNav">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../../allclasses.html">All&nbsp;Classes</a></li>
</ul>
<ul class="navListSearch">
<li><label for="search">SEARCH:</label>
<input type="text" id="search" value="search" disabled="disabled">
<input type="reset" id="reset" value="reset" disabled="disabled">
</li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li><a href="#nested.class.summary">Nested</a>&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
</div>
<div class="navPadding">&nbsp;</div>
<script type="text/javascript"><!--
$('.navPadding').css('padding-top', $('.fixedNav').css("height"));
//-->
</script>
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle"><span class="packageLabelInType">Package</span>&nbsp;<a href="package-summary.html">org.gradle.api.tasks.testing</a></div>
<h2 title="Interface TestResult" class="title">Interface TestResult</h2>
</div>
<div class="contentContainer">
<div class="description">
<ul class="blockList">
<li class="blockList">
<hr>
<pre>public interface <span class="typeNameLabel">TestResult</span></pre>
<div class="block">Describes a test result.</div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== NESTED CLASS SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="nested.class.summary">
<!--   -->
</a>
<h3>Nested Class Summary</h3>
<table class="memberSummary" summary="Nested Class Summary table, listing nested classes, and an explanation">
<caption><span>Nested Classes</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colSecond" scope="col">Interface</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static class&nbsp;</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="TestResult.ResultType.html" title="enum in org.gradle.api.tasks.testing">TestResult.ResultType</a></span></code></th>
<td class="colLast">
<div class="block">The final status of a test.</div>
</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t3" class="tableTab"><span><a href="javascript:show(4);">Abstract Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colSecond" scope="col">Method</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>long</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getEndTime--">getEndTime</a></span>()</code></th>
<td class="colLast">
<div class="block">Returns the time when this test completed execution.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>java.lang.Throwable</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getException--">getException</a></span>()</code></th>
<td class="colLast">
<div class="block">If the test failed with an exception, this will be the exception.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>java.util.List&lt;java.lang.Throwable&gt;</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getExceptions--">getExceptions</a></span>()</code></th>
<td class="colLast">
<div class="block">If the test failed with any exceptions, this will contain the exceptions.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>long</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getFailedTestCount--">getFailedTestCount</a></span>()</code></th>
<td class="colLast">
<div class="block">Returns the number of failed atomic tests executed for this test.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>java.util.List&lt;<a href="TestFailure.html" title="class in org.gradle.api.tasks.testing">TestFailure</a>&gt;</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getFailures--">getFailures</a></span>()</code></th>
<td class="colLast">
<div class="block">If the test failed with any exceptions, this will contain the exceptions and a description of the failure types.</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code><a href="TestResult.ResultType.html" title="enum in org.gradle.api.tasks.testing">TestResult.ResultType</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getResultType--">getResultType</a></span>()</code></th>
<td class="colLast">
<div class="block">Returns the type of result.</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>long</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getSkippedTestCount--">getSkippedTestCount</a></span>()</code></th>
<td class="colLast">
<div class="block">Returns the number of skipped atomic tests executed for this test.</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>long</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getStartTime--">getStartTime</a></span>()</code></th>
<td class="colLast">
<div class="block">Returns the time when this test started execution.</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>long</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getSuccessfulTestCount--">getSuccessfulTestCount</a></span>()</code></th>
<td class="colLast">
<div class="block">Returns the number of successful atomic tests executed for this test.</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>long</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getTestCount--">getTestCount</a></span>()</code></th>
<td class="colLast">
<div class="block">Returns the total number of atomic tests executed for this test.</div>
</td>
</tr>
</table>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="getResultType--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getResultType</h4>
<pre class="methodSignature"><a href="TestResult.ResultType.html" title="enum in org.gradle.api.tasks.testing">TestResult.ResultType</a>&nbsp;getResultType()</pre>
<div class="block">Returns the type of result.  Generally one wants it to be SUCCESS!</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The result type.</dd>
</dl>
</li>
</ul>
<a name="getException--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getException</h4>
<pre class="methodSignature">@Nullable
java.lang.Throwable&nbsp;getException()</pre>
<div class="block">If the test failed with an exception, this will be the exception.  Some test frameworks do not fail without an
 exception (JUnit), so in those cases this method will never return null.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The exception, if any, logged for this test.  If none, a null is returned.</dd>
</dl>
</li>
</ul>
<a name="getFailures--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getFailures</h4>
<pre class="methodSignature"><a href="../../Incubating.html" title="annotation in org.gradle.api">@Incubating</a>
java.util.List&lt;<a href="TestFailure.html" title="class in org.gradle.api.tasks.testing">TestFailure</a>&gt;&nbsp;getFailures()</pre>
<div class="block">If the test failed with any exceptions, this will contain the exceptions and a description of the failure types.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The failures, if any, logged for this test. If none, an empty list is returned.</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>7.6</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="#getExceptions--"><code>getExceptions()</code></a></dd>
</dl>
</li>
</ul>
<a name="getExceptions--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getExceptions</h4>
<pre class="methodSignature">java.util.List&lt;java.lang.Throwable&gt;&nbsp;getExceptions()</pre>
<div class="block">If the test failed with any exceptions, this will contain the exceptions.  Some test frameworks do not fail
 without an exception (JUnit), so in those cases this method will never return an empty list.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The exceptions, if any, logged for this test. If none, an empty list is returned.</dd>
</dl>
</li>
</ul>
<a name="getStartTime--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getStartTime</h4>
<pre class="methodSignature">long&nbsp;getStartTime()</pre>
<div class="block">Returns the time when this test started execution.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The start time, in milliseconds since the epoch.</dd>
</dl>
</li>
</ul>
<a name="getEndTime--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getEndTime</h4>
<pre class="methodSignature">long&nbsp;getEndTime()</pre>
<div class="block">Returns the time when this test completed execution.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The end t ime, in milliseconds since the epoch.</dd>
</dl>
</li>
</ul>
<a name="getTestCount--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getTestCount</h4>
<pre class="methodSignature">long&nbsp;getTestCount()</pre>
<div class="block">Returns the total number of atomic tests executed for this test. This will return 1 if this test is itself an
 atomic test.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The number of tests, possibly 0</dd>
</dl>
</li>
</ul>
<a name="getSuccessfulTestCount--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getSuccessfulTestCount</h4>
<pre class="methodSignature">long&nbsp;getSuccessfulTestCount()</pre>
<div class="block">Returns the number of successful atomic tests executed for this test.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The number of tests, possibly 0</dd>
</dl>
</li>
</ul>
<a name="getFailedTestCount--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getFailedTestCount</h4>
<pre class="methodSignature">long&nbsp;getFailedTestCount()</pre>
<div class="block">Returns the number of failed atomic tests executed for this test.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The number of tests, possibly 0</dd>
</dl>
</li>
</ul>
<a name="getSkippedTestCount--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>getSkippedTestCount</h4>
<pre class="methodSignature">long&nbsp;getSkippedTestCount()</pre>
<div class="block">Returns the number of skipped atomic tests executed for this test.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The number of tests, possibly 0</dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../../allclasses.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li><a href="#nested.class.summary">Nested</a>&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
