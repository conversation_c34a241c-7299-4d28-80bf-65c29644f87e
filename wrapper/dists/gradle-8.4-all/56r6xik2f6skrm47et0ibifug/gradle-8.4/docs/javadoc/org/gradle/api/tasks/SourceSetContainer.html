<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc -->
<title>SourceSetContainer (Gradle API 8.4)</title>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<link rel="stylesheet" type="text/css" href="../../../../javadoc.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../jquery/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
<script type="text/javascript" src="../../../../jquery/jszip/dist/jszip.min.js"></script>
<script type="text/javascript" src="../../../../jquery/jszip-utils/dist/jszip-utils.min.js"></script>
<!--[if IE]>
<script type="text/javascript" src="../../../../jquery/jszip-utils/dist/jszip-utils-ie.min.js"></script>
<![endif]-->
<script type="text/javascript" src="../../../../jquery/jquery-3.6.0.min.js"></script>
<script type="text/javascript" src="../../../../jquery/jquery-ui.min.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="SourceSetContainer (Gradle API 8.4)";
        }
    }
    catch(err) {
    }
//-->
var pathtoroot = "../../../../";
var useModuleDirectories = false;
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="fixedNav">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses.html">All&nbsp;Classes</a></li>
</ul>
<ul class="navListSearch">
<li><label for="search">SEARCH:</label>
<input type="text" id="search" value="search" disabled="disabled">
<input type="reset" id="reset" value="reset" disabled="disabled">
</li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li>Method</li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
</div>
<div class="navPadding">&nbsp;</div>
<script type="text/javascript"><!--
$('.navPadding').css('padding-top', $('.fixedNav').css("height"));
//-->
</script>
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle"><span class="packageLabelInType">Package</span>&nbsp;<a href="package-summary.html">org.gradle.api.tasks</a></div>
<h2 title="Interface SourceSetContainer" class="title">Interface SourceSetContainer</h2>
</div>
<div class="contentContainer">
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Superinterfaces:</dt>
<dd><code>java.util.Collection&lt;<a href="SourceSet.html" title="interface in org.gradle.api.tasks">SourceSet</a>&gt;</code>, <code><a href="../../util/Configurable.html" title="interface in org.gradle.util">Configurable</a>&lt;<a href="../NamedDomainObjectContainer.html" title="interface in org.gradle.api">NamedDomainObjectContainer</a>&lt;<a href="SourceSet.html" title="interface in org.gradle.api.tasks">SourceSet</a>&gt;&gt;</code>, <code><a href="../DomainObjectCollection.html" title="interface in org.gradle.api">DomainObjectCollection</a>&lt;<a href="SourceSet.html" title="interface in org.gradle.api.tasks">SourceSet</a>&gt;</code>, <code><a href="../DomainObjectSet.html" title="interface in org.gradle.api">DomainObjectSet</a>&lt;<a href="SourceSet.html" title="interface in org.gradle.api.tasks">SourceSet</a>&gt;</code>, <code>java.lang.Iterable&lt;<a href="SourceSet.html" title="interface in org.gradle.api.tasks">SourceSet</a>&gt;</code>, <code><a href="../NamedDomainObjectCollection.html" title="interface in org.gradle.api">NamedDomainObjectCollection</a>&lt;<a href="SourceSet.html" title="interface in org.gradle.api.tasks">SourceSet</a>&gt;</code>, <code><a href="../NamedDomainObjectContainer.html" title="interface in org.gradle.api">NamedDomainObjectContainer</a>&lt;<a href="SourceSet.html" title="interface in org.gradle.api.tasks">SourceSet</a>&gt;</code>, <code><a href="../NamedDomainObjectSet.html" title="interface in org.gradle.api">NamedDomainObjectSet</a>&lt;<a href="SourceSet.html" title="interface in org.gradle.api.tasks">SourceSet</a>&gt;</code>, <code>java.util.Set&lt;<a href="SourceSet.html" title="interface in org.gradle.api.tasks">SourceSet</a>&gt;</code></dd>
</dl>
<hr>
<pre>public interface <span class="typeNameLabel">SourceSetContainer</span>
extends <a href="../NamedDomainObjectContainer.html" title="interface in org.gradle.api">NamedDomainObjectContainer</a>&lt;<a href="SourceSet.html" title="interface in org.gradle.api.tasks">SourceSet</a>&gt;</pre>
<div class="block">A <code>SourceSetContainer</code> manages a set of <a href="SourceSet.html" title="interface in org.gradle.api.tasks"><code>SourceSet</code></a> objects.</div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.util.Collection">
<!--   -->
</a>
<h3>Methods inherited from interface&nbsp;java.util.Collection</h3>
<code>parallelStream, removeIf, stream, toArray</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.org.gradle.api.DomainObjectCollection">
<!--   -->
</a>
<h3>Methods inherited from interface&nbsp;org.gradle.api.<a href="../DomainObjectCollection.html" title="interface in org.gradle.api">DomainObjectCollection</a></h3>
<code><a href="../DomainObjectCollection.html#addAllLater-org.gradle.api.provider.Provider-">addAllLater</a>, <a href="../DomainObjectCollection.html#addLater-org.gradle.api.provider.Provider-">addLater</a>, <a href="../DomainObjectCollection.html#all-groovy.lang.Closure-">all</a>, <a href="../DomainObjectCollection.html#all-org.gradle.api.Action-">all</a>, <a href="../DomainObjectCollection.html#configureEach-org.gradle.api.Action-">configureEach</a>, <a href="../DomainObjectCollection.html#whenObjectAdded-groovy.lang.Closure-">whenObjectAdded</a>, <a href="../DomainObjectCollection.html#whenObjectAdded-org.gradle.api.Action-">whenObjectAdded</a>, <a href="../DomainObjectCollection.html#whenObjectRemoved-groovy.lang.Closure-">whenObjectRemoved</a>, <a href="../DomainObjectCollection.html#whenObjectRemoved-org.gradle.api.Action-">whenObjectRemoved</a>, <a href="../DomainObjectCollection.html#withType-java.lang.Class-groovy.lang.Closure-">withType</a>, <a href="../DomainObjectCollection.html#withType-java.lang.Class-org.gradle.api.Action-">withType</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Iterable">
<!--   -->
</a>
<h3>Methods inherited from interface&nbsp;java.lang.Iterable</h3>
<code>forEach</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.org.gradle.api.NamedDomainObjectCollection">
<!--   -->
</a>
<h3>Methods inherited from interface&nbsp;org.gradle.api.<a href="../NamedDomainObjectCollection.html" title="interface in org.gradle.api">NamedDomainObjectCollection</a></h3>
<code><a href="../NamedDomainObjectCollection.html#add-T-">add</a>, <a href="../NamedDomainObjectCollection.html#addAll-java.util.Collection-">addAll</a>, <a href="../NamedDomainObjectCollection.html#addRule-java.lang.String-groovy.lang.Closure-">addRule</a>, <a href="../NamedDomainObjectCollection.html#addRule-java.lang.String-org.gradle.api.Action-">addRule</a>, <a href="../NamedDomainObjectCollection.html#addRule-org.gradle.api.Rule-">addRule</a>, <a href="../NamedDomainObjectCollection.html#findByName-java.lang.String-">findByName</a>, <a href="../NamedDomainObjectCollection.html#getAsMap--">getAsMap</a>, <a href="../NamedDomainObjectCollection.html#getAt-java.lang.String-">getAt</a>, <a href="../NamedDomainObjectCollection.html#getByName-java.lang.String-">getByName</a>, <a href="../NamedDomainObjectCollection.html#getByName-java.lang.String-groovy.lang.Closure-">getByName</a>, <a href="../NamedDomainObjectCollection.html#getByName-java.lang.String-org.gradle.api.Action-">getByName</a>, <a href="../NamedDomainObjectCollection.html#getCollectionSchema--">getCollectionSchema</a>, <a href="../NamedDomainObjectCollection.html#getNamer--">getNamer</a>, <a href="../NamedDomainObjectCollection.html#getNames--">getNames</a>, <a href="../NamedDomainObjectCollection.html#getRules--">getRules</a>, <a href="../NamedDomainObjectCollection.html#named-java.lang.String-">named</a>, <a href="../NamedDomainObjectCollection.html#named-java.lang.String-java.lang.Class-">named</a>, <a href="../NamedDomainObjectCollection.html#named-java.lang.String-java.lang.Class-org.gradle.api.Action-">named</a>, <a href="../NamedDomainObjectCollection.html#named-java.lang.String-org.gradle.api.Action-">named</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.org.gradle.api.NamedDomainObjectContainer">
<!--   -->
</a>
<h3>Methods inherited from interface&nbsp;org.gradle.api.<a href="../NamedDomainObjectContainer.html" title="interface in org.gradle.api">NamedDomainObjectContainer</a></h3>
<code><a href="../NamedDomainObjectContainer.html#configure-groovy.lang.Closure-">configure</a>, <a href="../NamedDomainObjectContainer.html#create-java.lang.String-">create</a>, <a href="../NamedDomainObjectContainer.html#create-java.lang.String-groovy.lang.Closure-">create</a>, <a href="../NamedDomainObjectContainer.html#create-java.lang.String-org.gradle.api.Action-">create</a>, <a href="../NamedDomainObjectContainer.html#maybeCreate-java.lang.String-">maybeCreate</a>, <a href="../NamedDomainObjectContainer.html#register-java.lang.String-">register</a>, <a href="../NamedDomainObjectContainer.html#register-java.lang.String-org.gradle.api.Action-">register</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.org.gradle.api.NamedDomainObjectSet">
<!--   -->
</a>
<h3>Methods inherited from interface&nbsp;org.gradle.api.<a href="../NamedDomainObjectSet.html" title="interface in org.gradle.api">NamedDomainObjectSet</a></h3>
<code><a href="../NamedDomainObjectSet.html#findAll-groovy.lang.Closure-">findAll</a>, <a href="../NamedDomainObjectSet.html#matching-groovy.lang.Closure-">matching</a>, <a href="../NamedDomainObjectSet.html#matching-org.gradle.api.specs.Spec-">matching</a>, <a href="../NamedDomainObjectSet.html#withType-java.lang.Class-">withType</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.util.Set">
<!--   -->
</a>
<h3>Methods inherited from interface&nbsp;java.util.Set</h3>
<code>add, addAll, clear, contains, containsAll, equals, hashCode, isEmpty, iterator, remove, removeAll, retainAll, size, spliterator, toArray, toArray</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li>Method</li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
