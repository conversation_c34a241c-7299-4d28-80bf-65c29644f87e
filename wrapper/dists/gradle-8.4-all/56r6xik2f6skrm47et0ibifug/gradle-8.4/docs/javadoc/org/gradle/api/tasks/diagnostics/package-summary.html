<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc -->
<title>org.gradle.api.tasks.diagnostics (Gradle API 8.4)</title>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<link rel="stylesheet" type="text/css" href="../../../../../javadoc.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../jquery/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
<script type="text/javascript" src="../../../../../jquery/jszip/dist/jszip.min.js"></script>
<script type="text/javascript" src="../../../../../jquery/jszip-utils/dist/jszip-utils.min.js"></script>
<!--[if IE]>
<script type="text/javascript" src="../../../../../jquery/jszip-utils/dist/jszip-utils-ie.min.js"></script>
<![endif]-->
<script type="text/javascript" src="../../../../../jquery/jquery-3.6.0.min.js"></script>
<script type="text/javascript" src="../../../../../jquery/jquery-ui.min.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="org.gradle.api.tasks.diagnostics (Gradle API 8.4)";
        }
    }
    catch(err) {
    }
//-->
var pathtoroot = "../../../../../";
var useModuleDirectories = false;
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="fixedNav">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../index.html">Overview</a></li>
<li class="navBarCell1Rev">Package</li>
<li>Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../../allclasses.html">All&nbsp;Classes</a></li>
</ul>
<ul class="navListSearch">
<li><label for="search">SEARCH:</label>
<input type="text" id="search" value="search" disabled="disabled">
<input type="reset" id="reset" value="reset" disabled="disabled">
</li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
</div>
<div class="navPadding">&nbsp;</div>
<script type="text/javascript"><!--
$('.navPadding').css('padding-top', $('.fixedNav').css("height"));
//-->
</script>
<div class="header">
<p><a href="../../NonNullApi.html" title="annotation in org.gradle.api">@NonNullApi</a>
</p>
<h1 title="Package" class="title">Package&nbsp;org.gradle.api.tasks.diagnostics</h1>
</div>
<div class="contentContainer"><a name="package.description">
<!--   -->
</a>
<div class="block">The diagnostic <a href="../../Task.html" title="interface in org.gradle.api"><code>Task</code></a> implementations.</div>
<ul class="blockList">
<li class="blockList">
<table class="typeSummary" summary="Class Summary table, listing classes, and an explanation">
<caption><span>Class Summary</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Class</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tbody>
<tr class="altColor">
<th class="colFirst" scope="row"><a href="AbstractConfigurationReportTask.html" title="class in org.gradle.api.tasks.diagnostics">AbstractConfigurationReportTask</a></th>
<td class="colLast">
<div class="block">Base class for reporting tasks which display information about attributes and related data associated to a variant/configuration.</div>
</td>
</tr>
<tr class="rowColor">
<th class="colFirst" scope="row"><a href="AbstractDependencyReportTask.html" title="class in org.gradle.api.tasks.diagnostics">AbstractDependencyReportTask</a></th>
<td class="colLast">
<div class="block">Displays the dependency tree for a configuration.</div>
</td>
</tr>
<tr class="altColor">
<th class="colFirst" scope="row"><a href="AbstractDependencyReportTask.DependencyReportModel.html" title="class in org.gradle.api.tasks.diagnostics">AbstractDependencyReportTask.DependencyReportModel</a></th>
<td class="colLast">
<div class="block">Report model.</div>
</td>
</tr>
<tr class="rowColor">
<th class="colFirst" scope="row"><a href="AbstractProjectBasedReportTask.html" title="class in org.gradle.api.tasks.diagnostics">AbstractProjectBasedReportTask</a>&lt;T&gt;</th>
<td class="colLast">
<div class="block">The base class for all Project based project report tasks.</div>
</td>
</tr>
<tr class="altColor">
<th class="colFirst" scope="row"><a href="AbstractReportTask.html" title="class in org.gradle.api.tasks.diagnostics">AbstractReportTask</a></th>
<td class="colLast">Deprecated.
<div class="deprecationComment">Use <a href="AbstractProjectBasedReportTask.html" title="class in org.gradle.api.tasks.diagnostics"><code>AbstractProjectBasedReportTask</code></a> instead.</div>
</td>
</tr>
<tr class="rowColor">
<th class="colFirst" scope="row"><a href="BuildEnvironmentReportTask.html" title="class in org.gradle.api.tasks.diagnostics">BuildEnvironmentReportTask</a></th>
<td class="colLast">
<div class="block">Provides information about the build environment for the project that the task is associated with.</div>
</td>
</tr>
<tr class="altColor">
<th class="colFirst" scope="row"><a href="ConventionReportTask.html" title="class in org.gradle.api.tasks.diagnostics">ConventionReportTask</a></th>
<td class="colLast">
<div class="block">The base class for all project based report tasks with custom task actions.</div>
</td>
</tr>
<tr class="rowColor">
<th class="colFirst" scope="row"><a href="DependencyInsightReportTask.html" title="class in org.gradle.api.tasks.diagnostics">DependencyInsightReportTask</a></th>
<td class="colLast">
<div class="block">Generates a report that attempts to answer questions like:
 
 Why is this dependency in the dependency graph?
 Exactly which dependencies are pulling this dependency into the graph?
 What is the actual version (i.e.</div>
</td>
</tr>
<tr class="altColor">
<th class="colFirst" scope="row"><a href="DependencyReportTask.html" title="class in org.gradle.api.tasks.diagnostics">DependencyReportTask</a></th>
<td class="colLast">
<div class="block">Displays the dependency tree for a project.</div>
</td>
</tr>
<tr class="rowColor">
<th class="colFirst" scope="row"><a href="OutgoingVariantsReportTask.html" title="class in org.gradle.api.tasks.diagnostics">OutgoingVariantsReportTask</a></th>
<td class="colLast">
<div class="block">A task which reports the outgoing variants of a project on the command line.</div>
</td>
</tr>
<tr class="altColor">
<th class="colFirst" scope="row"><a href="ProjectBasedReportTask.html" title="class in org.gradle.api.tasks.diagnostics">ProjectBasedReportTask</a></th>
<td class="colLast">Deprecated.
<div class="deprecationComment">Use <a href="AbstractProjectBasedReportTask.html" title="class in org.gradle.api.tasks.diagnostics"><code>AbstractProjectBasedReportTask</code></a> instead.</div>
</td>
</tr>
<tr class="rowColor">
<th class="colFirst" scope="row"><a href="ProjectReportTask.html" title="class in org.gradle.api.tasks.diagnostics">ProjectReportTask</a></th>
<td class="colLast">
<div class="block">Displays a list of projects in the build.</div>
</td>
</tr>
<tr class="altColor">
<th class="colFirst" scope="row"><a href="ProjectReportTask.ProjectReportModel.html" title="class in org.gradle.api.tasks.diagnostics">ProjectReportTask.ProjectReportModel</a></th>
<td class="colLast">
<div class="block">Report model.</div>
</td>
</tr>
<tr class="rowColor">
<th class="colFirst" scope="row"><a href="PropertyReportTask.html" title="class in org.gradle.api.tasks.diagnostics">PropertyReportTask</a></th>
<td class="colLast">
<div class="block">Displays the properties of a project.</div>
</td>
</tr>
<tr class="altColor">
<th class="colFirst" scope="row"><a href="PropertyReportTask.PropertyReportModel.html" title="class in org.gradle.api.tasks.diagnostics">PropertyReportTask.PropertyReportModel</a></th>
<td class="colLast">
<div class="block">Model for the report.</div>
</td>
</tr>
<tr class="rowColor">
<th class="colFirst" scope="row"><a href="ResolvableConfigurationsReportTask.html" title="class in org.gradle.api.tasks.diagnostics">ResolvableConfigurationsReportTask</a></th>
<td class="colLast">
<div class="block">A task which reports the configurations of a project which can be resolved on the command line.</div>
</td>
</tr>
<tr class="altColor">
<th class="colFirst" scope="row"><a href="TaskReportTask.html" title="class in org.gradle.api.tasks.diagnostics">TaskReportTask</a></th>
<td class="colLast">
<div class="block">Displays a list of tasks in the project.</div>
</td>
</tr>
</tbody>
</table>
</li>
</ul>
</div>
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../index.html">Overview</a></li>
<li class="navBarCell1Rev">Package</li>
<li>Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../../allclasses.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
