<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc -->
<title>ExtensionAware (Gradle API 8.4)</title>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<link rel="stylesheet" type="text/css" href="../../../../javadoc.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../jquery/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
<script type="text/javascript" src="../../../../jquery/jszip/dist/jszip.min.js"></script>
<script type="text/javascript" src="../../../../jquery/jszip-utils/dist/jszip-utils.min.js"></script>
<!--[if IE]>
<script type="text/javascript" src="../../../../jquery/jszip-utils/dist/jszip-utils-ie.min.js"></script>
<![endif]-->
<script type="text/javascript" src="../../../../jquery/jquery-3.6.0.min.js"></script>
<script type="text/javascript" src="../../../../jquery/jquery-ui.min.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="ExtensionAware (Gradle API 8.4)";
        }
    }
    catch(err) {
    }
//-->
var data = {"i0":6};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],4:["t3","Abstract Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
var pathtoroot = "../../../../";
var useModuleDirectories = false;
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="fixedNav">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses.html">All&nbsp;Classes</a></li>
</ul>
<ul class="navListSearch">
<li><label for="search">SEARCH:</label>
<input type="text" id="search" value="search" disabled="disabled">
<input type="reset" id="reset" value="reset" disabled="disabled">
</li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
</div>
<div class="navPadding">&nbsp;</div>
<script type="text/javascript"><!--
$('.navPadding').css('padding-top', $('.fixedNav').css("height"));
//-->
</script>
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle"><span class="packageLabelInType">Package</span>&nbsp;<a href="package-summary.html">org.gradle.api.plugins</a></div>
<h2 title="Interface ExtensionAware" class="title">Interface ExtensionAware</h2>
</div>
<div class="contentContainer">
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Known Subinterfaces:</dt>
<dd><code><a href="../artifacts/dsl/DependencyHandler.html" title="interface in org.gradle.api.artifacts.dsl">DependencyHandler</a></code>, <code><a href="../invocation/Gradle.html" title="interface in org.gradle.api.invocation">Gradle</a></code>, <code><a href="../../nativeplatform/tasks/ObjectFilesToBinary.html" title="interface in org.gradle.nativeplatform.tasks">ObjectFilesToBinary</a></code>, <code><a href="../Project.html" title="interface in org.gradle.api">Project</a></code>, <code><a href="../initialization/Settings.html" title="interface in org.gradle.api.initialization">Settings</a></code>, <code><a href="../tasks/SourceSet.html" title="interface in org.gradle.api.tasks">SourceSet</a></code>, <code><a href="../Task.html" title="interface in org.gradle.api">Task</a></code>, <code><a href="../toolchain/management/ToolchainManagement.html" title="interface in org.gradle.api.toolchain.management">ToolchainManagement</a></code></dd>
</dl>
<dl>
<dt>All Known Implementing Classes:</dt>
<dd><code><a href="../tasks/bundling/AbstractArchiveTask.html" title="class in org.gradle.api.tasks.bundling">AbstractArchiveTask</a></code>, <code><a href="quality/AbstractCodeQualityTask.html" title="class in org.gradle.api.plugins.quality">AbstractCodeQualityTask</a></code>, <code><a href="../tasks/compile/AbstractCompile.html" title="class in org.gradle.api.tasks.compile">AbstractCompile</a></code>, <code><a href="../tasks/diagnostics/AbstractConfigurationReportTask.html" title="class in org.gradle.api.tasks.diagnostics">AbstractConfigurationReportTask</a></code>, <code><a href="../tasks/AbstractCopyTask.html" title="class in org.gradle.api.tasks">AbstractCopyTask</a></code>, <code><a href="../tasks/diagnostics/AbstractDependencyReportTask.html" title="class in org.gradle.api.tasks.diagnostics">AbstractDependencyReportTask</a></code>, <code><a href="../tasks/AbstractExecTask.html" title="class in org.gradle.api.tasks">AbstractExecTask</a></code>, <code><a href="../../nativeplatform/tasks/AbstractLinkTask.html" title="class in org.gradle.nativeplatform.tasks">AbstractLinkTask</a></code>, <code><a href="../../language/nativeplatform/tasks/AbstractNativeCompileTask.html" title="class in org.gradle.language.nativeplatform.tasks">AbstractNativeCompileTask</a></code>, <code><a href="../../language/nativeplatform/tasks/AbstractNativePCHCompileTask.html" title="class in org.gradle.language.nativeplatform.tasks">AbstractNativePCHCompileTask</a></code>, <code><a href="../../language/nativeplatform/tasks/AbstractNativeSourceCompileTask.html" title="class in org.gradle.language.nativeplatform.tasks">AbstractNativeSourceCompileTask</a></code>, <code><a href="../tasks/diagnostics/AbstractProjectBasedReportTask.html" title="class in org.gradle.api.tasks.diagnostics">AbstractProjectBasedReportTask</a></code>, <code><a href="../publish/maven/tasks/AbstractPublishToMaven.html" title="class in org.gradle.api.publish.maven.tasks">AbstractPublishToMaven</a></code>, <code><a href="../tasks/diagnostics/AbstractReportTask.html" title="class in org.gradle.api.tasks.diagnostics">AbstractReportTask</a></code>, <code><a href="../../language/scala/tasks/AbstractScalaCompile.html" title="class in org.gradle.language.scala.tasks">AbstractScalaCompile</a></code>, <code>org.gradle.api.internal.AbstractTask</code>, <code><a href="../tasks/testing/AbstractTestTask.html" title="class in org.gradle.api.tasks.testing">AbstractTestTask</a></code>, <code><a href="antlr/AntlrTask.html" title="class in org.gradle.api.plugins.antlr">AntlrTask</a></code>, <code><a href="../tasks/ant/AntTarget.html" title="class in org.gradle.api.tasks.ant">AntTarget</a></code>, <code><a href="../../language/assembler/tasks/Assemble.html" title="class in org.gradle.language.assembler.tasks">Assemble</a></code>, <code><a href="../tasks/diagnostics/BuildEnvironmentReportTask.html" title="class in org.gradle.api.tasks.diagnostics">BuildEnvironmentReportTask</a></code>, <code><a href="../../language/c/tasks/CCompile.html" title="class in org.gradle.language.c.tasks">CCompile</a></code>, <code><a href="quality/Checkstyle.html" title="class in org.gradle.api.plugins.quality">Checkstyle</a></code>, <code><a href="quality/CodeNarc.html" title="class in org.gradle.api.plugins.quality">CodeNarc</a></code>, <code><a href="../reporting/components/ComponentReport.html" title="class in org.gradle.api.reporting.components">ComponentReport</a></code>, <code><a href="../tasks/diagnostics/ConventionReportTask.html" title="class in org.gradle.api.tasks.diagnostics">ConventionReportTask</a></code>, <code>org.gradle.api.internal.ConventionTask</code>, <code><a href="../tasks/Copy.html" title="class in org.gradle.api.tasks">Copy</a></code>, <code><a href="../../language/cpp/tasks/CppCompile.html" title="class in org.gradle.language.cpp.tasks">CppCompile</a></code>, <code><a href="../../language/cpp/tasks/CppPreCompiledHeaderCompile.html" title="class in org.gradle.language.cpp.tasks">CppPreCompiledHeaderCompile</a></code>, <code><a href="../../language/c/tasks/CPreCompiledHeaderCompile.html" title="class in org.gradle.language.c.tasks">CPreCompiledHeaderCompile</a></code>, <code><a href="../tasks/application/CreateStartScripts.html" title="class in org.gradle.api.tasks.application">CreateStartScripts</a></code>, <code><a href="../../jvm/application/tasks/CreateStartScripts.html" title="class in org.gradle.jvm.application.tasks">CreateStartScripts</a></code>, <code><a href="../../nativeplatform/tasks/CreateStaticLibrary.html" title="class in org.gradle.nativeplatform.tasks">CreateStaticLibrary</a></code>, <code><a href="../DefaultTask.html" title="class in org.gradle.api">DefaultTask</a></code>, <code><a href="../tasks/Delete.html" title="class in org.gradle.api.tasks">Delete</a></code>, <code><a href="../tasks/diagnostics/DependencyInsightReportTask.html" title="class in org.gradle.api.tasks.diagnostics">DependencyInsightReportTask</a></code>, <code><a href="../tasks/diagnostics/DependencyReportTask.html" title="class in org.gradle.api.tasks.diagnostics">DependencyReportTask</a></code>, <code><a href="../reporting/dependents/DependentComponentsReport.html" title="class in org.gradle.api.reporting.dependents">DependentComponentsReport</a></code>, <code><a href="../../plugins/ear/Ear.html" title="class in org.gradle.plugins.ear">Ear</a></code>, <code><a href="../tasks/Exec.html" title="class in org.gradle.api.tasks">Exec</a></code>, <code><a href="../../nativeplatform/tasks/ExtractSymbols.html" title="class in org.gradle.nativeplatform.tasks">ExtractSymbols</a></code>, <code><a href="../reporting/GenerateBuildDashboard.html" title="class in org.gradle.api.reporting">GenerateBuildDashboard</a></code>, <code><a href="../../nativeplatform/test/cunit/tasks/GenerateCUnitLauncher.html" title="class in org.gradle.nativeplatform.test.cunit.tasks">GenerateCUnitLauncher</a></code>, <code><a href="../../plugins/ide/eclipse/GenerateEclipseClasspath.html" title="class in org.gradle.plugins.ide.eclipse">GenerateEclipseClasspath</a></code>, <code><a href="../../plugins/ide/eclipse/GenerateEclipseJdt.html" title="class in org.gradle.plugins.ide.eclipse">GenerateEclipseJdt</a></code>, <code><a href="../../plugins/ide/eclipse/GenerateEclipseProject.html" title="class in org.gradle.plugins.ide.eclipse">GenerateEclipseProject</a></code>, <code><a href="../../plugins/ide/eclipse/GenerateEclipseWtpComponent.html" title="class in org.gradle.plugins.ide.eclipse">GenerateEclipseWtpComponent</a></code>, <code><a href="../../plugins/ide/eclipse/GenerateEclipseWtpFacet.html" title="class in org.gradle.plugins.ide.eclipse">GenerateEclipseWtpFacet</a></code>, <code><a href="../../ide/visualstudio/tasks/GenerateFiltersFileTask.html" title="class in org.gradle.ide.visualstudio.tasks">GenerateFiltersFileTask</a></code>, <code><a href="../../plugins/ide/idea/GenerateIdeaModule.html" title="class in org.gradle.plugins.ide.idea">GenerateIdeaModule</a></code>, <code><a href="../../plugins/ide/idea/GenerateIdeaProject.html" title="class in org.gradle.plugins.ide.idea">GenerateIdeaProject</a></code>, <code><a href="../../plugins/ide/idea/GenerateIdeaWorkspace.html" title="class in org.gradle.plugins.ide.idea">GenerateIdeaWorkspace</a></code>, <code><a href="../publish/ivy/tasks/GenerateIvyDescriptor.html" title="class in org.gradle.api.publish.ivy.tasks">GenerateIvyDescriptor</a></code>, <code><a href="../publish/maven/tasks/GenerateMavenPom.html" title="class in org.gradle.api.publish.maven.tasks">GenerateMavenPom</a></code>, <code><a href="../publish/tasks/GenerateModuleMetadata.html" title="class in org.gradle.api.publish.tasks">GenerateModuleMetadata</a></code>, <code><a href="../../plugin/devel/tasks/GeneratePluginDescriptors.html" title="class in org.gradle.plugin.devel.tasks">GeneratePluginDescriptors</a></code>, <code><a href="../../ide/visualstudio/tasks/GenerateProjectFileTask.html" title="class in org.gradle.ide.visualstudio.tasks">GenerateProjectFileTask</a></code>, <code><a href="../../ide/xcode/tasks/GenerateSchemeFileTask.html" title="class in org.gradle.ide.xcode.tasks">GenerateSchemeFileTask</a></code>, <code><a href="../../ide/visualstudio/tasks/GenerateSolutionFileTask.html" title="class in org.gradle.ide.visualstudio.tasks">GenerateSolutionFileTask</a></code>, <code><a href="../../swiftpm/tasks/GenerateSwiftPackageManagerManifest.html" title="class in org.gradle.swiftpm.tasks">GenerateSwiftPackageManagerManifest</a></code>, <code><a href="../../ide/xcode/tasks/GenerateWorkspaceSettingsFileTask.html" title="class in org.gradle.ide.xcode.tasks">GenerateWorkspaceSettingsFileTask</a></code>, <code><a href="../../ide/xcode/tasks/GenerateXcodeProjectFileTask.html" title="class in org.gradle.ide.xcode.tasks">GenerateXcodeProjectFileTask</a></code>, <code><a href="../../ide/xcode/tasks/GenerateXcodeWorkspaceFileTask.html" title="class in org.gradle.ide.xcode.tasks">GenerateXcodeWorkspaceFileTask</a></code>, <code><a href="../../plugins/ide/api/GeneratorTask.html" title="class in org.gradle.plugins.ide.api">GeneratorTask</a></code>, <code><a href="../tasks/GradleBuild.html" title="class in org.gradle.api.tasks">GradleBuild</a></code>, <code><a href="../tasks/compile/GroovyCompile.html" title="class in org.gradle.api.tasks.compile">GroovyCompile</a></code>, <code><a href="../tasks/javadoc/Groovydoc.html" title="class in org.gradle.api.tasks.javadoc">Groovydoc</a></code>, <code><a href="../reporting/dependencies/HtmlDependencyReportTask.html" title="class in org.gradle.api.reporting.dependencies">HtmlDependencyReportTask</a></code>, <code><a href="../../buildinit/tasks/InitBuild.html" title="class in org.gradle.buildinit.tasks">InitBuild</a></code>, <code><a href="../../nativeplatform/tasks/InstallExecutable.html" title="class in org.gradle.nativeplatform.tasks">InstallExecutable</a></code>, <code><a href="../../nativeplatform/test/xctest/tasks/InstallXCTestBundle.html" title="class in org.gradle.nativeplatform.test.xctest.tasks">InstallXCTestBundle</a></code>, <code><a href="../../testing/jacoco/tasks/JacocoBase.html" title="class in org.gradle.testing.jacoco.tasks">JacocoBase</a></code>, <code><a href="../../testing/jacoco/tasks/JacocoCoverageVerification.html" title="class in org.gradle.testing.jacoco.tasks">JacocoCoverageVerification</a></code>, <code><a href="../../testing/jacoco/tasks/JacocoReport.html" title="class in org.gradle.testing.jacoco.tasks">JacocoReport</a></code>, <code><a href="../../testing/jacoco/tasks/JacocoReportBase.html" title="class in org.gradle.testing.jacoco.tasks">JacocoReportBase</a></code>, <code><a href="../tasks/bundling/Jar.html" title="class in org.gradle.api.tasks.bundling">Jar</a></code>, <code><a href="../../jvm/tasks/Jar.html" title="class in org.gradle.jvm.tasks">Jar</a></code>, <code><a href="../tasks/compile/JavaCompile.html" title="class in org.gradle.api.tasks.compile">JavaCompile</a></code>, <code><a href="../tasks/javadoc/Javadoc.html" title="class in org.gradle.api.tasks.javadoc">Javadoc</a></code>, <code><a href="../tasks/JavaExec.html" title="class in org.gradle.api.tasks">JavaExec</a></code>, <code><a href="../../nativeplatform/tasks/LinkExecutable.html" title="class in org.gradle.nativeplatform.tasks">LinkExecutable</a></code>, <code><a href="../../nativeplatform/tasks/LinkMachOBundle.html" title="class in org.gradle.nativeplatform.tasks">LinkMachOBundle</a></code>, <code><a href="../../nativeplatform/tasks/LinkSharedLibrary.html" title="class in org.gradle.nativeplatform.tasks">LinkSharedLibrary</a></code>, <code><a href="../reporting/model/ModelReport.html" title="class in org.gradle.api.reporting.model">ModelReport</a></code>, <code><a href="../../language/objectivec/tasks/ObjectiveCCompile.html" title="class in org.gradle.language.objectivec.tasks">ObjectiveCCompile</a></code>, <code><a href="../../language/objectivecpp/tasks/ObjectiveCppCompile.html" title="class in org.gradle.language.objectivecpp.tasks">ObjectiveCppCompile</a></code>, <code><a href="../../language/objectivecpp/tasks/ObjectiveCppPreCompiledHeaderCompile.html" title="class in org.gradle.language.objectivecpp.tasks">ObjectiveCppPreCompiledHeaderCompile</a></code>, <code><a href="../../language/objectivec/tasks/ObjectiveCPreCompiledHeaderCompile.html" title="class in org.gradle.language.objectivec.tasks">ObjectiveCPreCompiledHeaderCompile</a></code>, <code><a href="../tasks/diagnostics/OutgoingVariantsReportTask.html" title="class in org.gradle.api.tasks.diagnostics">OutgoingVariantsReportTask</a></code>, <code><a href="../../plugin/devel/tasks/PluginUnderTestMetadata.html" title="class in org.gradle.plugin.devel.tasks">PluginUnderTestMetadata</a></code>, <code><a href="quality/Pmd.html" title="class in org.gradle.api.plugins.quality">Pmd</a></code>, <code><a href="../../nativeplatform/tasks/PrefixHeaderFileGenerateTask.html" title="class in org.gradle.nativeplatform.tasks">PrefixHeaderFileGenerateTask</a></code>, <code><a href="../../language/jvm/tasks/ProcessResources.html" title="class in org.gradle.language.jvm.tasks">ProcessResources</a></code>, <code><a href="../tasks/diagnostics/ProjectBasedReportTask.html" title="class in org.gradle.api.tasks.diagnostics">ProjectBasedReportTask</a></code>, <code><a href="../tasks/diagnostics/ProjectReportTask.html" title="class in org.gradle.api.tasks.diagnostics">ProjectReportTask</a></code>, <code><a href="../../plugins/ide/api/PropertiesGeneratorTask.html" title="class in org.gradle.plugins.ide.api">PropertiesGeneratorTask</a></code>, <code><a href="../../plugins/ide/api/PropertyListGeneratorTask.html" title="class in org.gradle.plugins.ide.api">PropertyListGeneratorTask</a></code>, <code><a href="../tasks/diagnostics/PropertyReportTask.html" title="class in org.gradle.api.tasks.diagnostics">PropertyReportTask</a></code>, <code><a href="../publish/ivy/tasks/PublishToIvyRepository.html" title="class in org.gradle.api.publish.ivy.tasks">PublishToIvyRepository</a></code>, <code><a href="../publish/maven/tasks/PublishToMavenLocal.html" title="class in org.gradle.api.publish.maven.tasks">PublishToMavenLocal</a></code>, <code><a href="../publish/maven/tasks/PublishToMavenRepository.html" title="class in org.gradle.api.publish.maven.tasks">PublishToMavenRepository</a></code>, <code><a href="../tasks/diagnostics/ResolvableConfigurationsReportTask.html" title="class in org.gradle.api.tasks.diagnostics">ResolvableConfigurationsReportTask</a></code>, <code><a href="../../nativeplatform/test/tasks/RunTestExecutable.html" title="class in org.gradle.nativeplatform.test.tasks">RunTestExecutable</a></code>, <code><a href="../tasks/scala/ScalaCompile.html" title="class in org.gradle.api.tasks.scala">ScalaCompile</a></code>, <code><a href="../tasks/scala/ScalaDoc.html" title="class in org.gradle.api.tasks.scala">ScalaDoc</a></code>, <code><a href="../../plugins/signing/Sign.html" title="class in org.gradle.plugins.signing">Sign</a></code>, <code><a href="../tasks/SourceTask.html" title="class in org.gradle.api.tasks">SourceTask</a></code>, <code><a href="../../nativeplatform/tasks/StripSymbols.html" title="class in org.gradle.nativeplatform.tasks">StripSymbols</a></code>, <code><a href="../../language/swift/tasks/SwiftCompile.html" title="class in org.gradle.language.swift.tasks">SwiftCompile</a></code>, <code><a href="../tasks/Sync.html" title="class in org.gradle.api.tasks">Sync</a></code>, <code><a href="../tasks/bundling/Tar.html" title="class in org.gradle.api.tasks.bundling">Tar</a></code>, <code><a href="../tasks/diagnostics/TaskReportTask.html" title="class in org.gradle.api.tasks.diagnostics">TaskReportTask</a></code>, <code><a href="../tasks/testing/Test.html" title="class in org.gradle.api.tasks.testing">Test</a></code>, <code><a href="../tasks/testing/TestReport.html" title="class in org.gradle.api.tasks.testing">TestReport</a></code>, <code><a href="../../language/nativeplatform/tasks/UnexportMainSymbol.html" title="class in org.gradle.language.nativeplatform.tasks">UnexportMainSymbol</a></code>, <code><a href="../tasks/Upload.html" title="class in org.gradle.api.tasks">Upload</a></code>, <code><a href="../../plugin/devel/tasks/ValidatePlugins.html" title="class in org.gradle.plugin.devel.tasks">ValidatePlugins</a></code>, <code><a href="../tasks/bundling/War.html" title="class in org.gradle.api.tasks.bundling">War</a></code>, <code><a href="../../language/rc/tasks/WindowsResourceCompile.html" title="class in org.gradle.language.rc.tasks">WindowsResourceCompile</a></code>, <code><a href="../tasks/wrapper/Wrapper.html" title="class in org.gradle.api.tasks.wrapper">Wrapper</a></code>, <code><a href="../tasks/WriteProperties.html" title="class in org.gradle.api.tasks">WriteProperties</a></code>, <code><a href="../../nativeplatform/test/xctest/tasks/XCTest.html" title="class in org.gradle.nativeplatform.test.xctest.tasks">XCTest</a></code>, <code><a href="../../plugins/ide/api/XmlGeneratorTask.html" title="class in org.gradle.plugins.ide.api">XmlGeneratorTask</a></code>, <code><a href="../tasks/bundling/Zip.html" title="class in org.gradle.api.tasks.bundling">Zip</a></code></dd>
</dl>
<hr>
<pre>public interface <span class="typeNameLabel">ExtensionAware</span></pre>
<div class="block">Objects that can be extended at runtime with other objects.

 <pre class='autoTested'>
 // Extensions are just plain objects, there is no interface/type
 class MyExtension {
   String foo

   MyExtension(String foo) {
     this.foo = foo
   }
 }

 // Add new extensions via the extension container
 project.extensions.create('custom', MyExtension, "bar")
 //                       («name»,   «type»,       «constructor args», …)

 // extensions appear as properties on the target object by the given name
 assert project.custom instanceof MyExtension
 assert project.custom.foo == "bar"

 // also via a namespace method
 project.custom {
   assert foo == "bar"
   foo = "other"
 }
 assert project.custom.foo == "other"

 // Extensions added with the extension container's create method are themselves extensible
 assert project.custom instanceof ExtensionAware
 project.custom.extensions.create("nested", MyExtension, "baz")
 assert project.custom.nested.foo == "baz"

 // All extension aware objects have a special “ext” extension of type ExtraPropertiesExtension
 assert project.hasProperty("myProperty") == false
 project.ext.myProperty = "myValue"

 // Properties added to the “ext” extension are promoted to the owning object
 assert project.myProperty == "myValue"
 </pre>

 Many Gradle objects are extension aware. This includes; projects, tasks, configurations, dependencies etc.
 <p>
 For more on adding &amp; creating extensions, see <a href="ExtensionContainer.html" title="interface in org.gradle.api.plugins"><code>ExtensionContainer</code></a>.
 <p>
 For more on extra properties, see <a href="ExtraPropertiesExtension.html" title="interface in org.gradle.api.plugins"><code>ExtraPropertiesExtension</code></a>.
 <p>
 An <code>ExtensionAware</code> object has several 'scopes' that Gradle searches for properties. These scopes are:</p>

 <ul>
 <li>The object itself. This scope includes any property getters and setters declared by the
 implementation class. The properties of this scope are readable or writable depending on the presence
 of the corresponding getter or setter method.</li>

 <li>Groovy Meta-programming methods implemented by the object's class, like <code>propertyMissing()</code>. Care must be taken by plugin authors to
 ensure <code>propertyMissing()</code> is implemented such that if a property is not found a MissingPropertyException(String, Class) exception is thrown.
 If <code>propertyMissing()</code> always returns a value for any property, <em>Gradle will not search the rest of the scopes below.</em></li>

 <li>The <em>extra</em> properties of the object.  Each object maintains a map of extra properties, which
 can contain any arbitrary name -&gt; value pair.  Once defined, the properties of this scope are readable and writable.</li>

 <li>The <em>extensions</em> added to the object by plugins. Each extension is available as a read-only property with the same name as the extension.</li>
 </ul></div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t3" class="tableTab"><span><a href="javascript:show(4);">Abstract Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colSecond" scope="col">Method</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code><a href="ExtensionContainer.html" title="interface in org.gradle.api.plugins">ExtensionContainer</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getExtensions--">getExtensions</a></span>()</code></th>
<td class="colLast">
<div class="block">The container of extensions.</div>
</td>
</tr>
</table>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="getExtensions--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>getExtensions</h4>
<pre class="methodSignature"><a href="../tasks/Internal.html" title="annotation in org.gradle.api.tasks">@Internal</a>
<a href="ExtensionContainer.html" title="interface in org.gradle.api.plugins">ExtensionContainer</a>&nbsp;getExtensions()</pre>
<div class="block">The container of extensions.</div>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
