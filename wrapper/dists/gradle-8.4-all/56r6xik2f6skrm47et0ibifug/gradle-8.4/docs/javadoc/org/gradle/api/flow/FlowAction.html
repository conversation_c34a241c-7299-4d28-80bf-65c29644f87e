<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc -->
<title>FlowAction (Gradle API 8.4)</title>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<link rel="stylesheet" type="text/css" href="../../../../javadoc.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../jquery/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
<script type="text/javascript" src="../../../../jquery/jszip/dist/jszip.min.js"></script>
<script type="text/javascript" src="../../../../jquery/jszip-utils/dist/jszip-utils.min.js"></script>
<!--[if IE]>
<script type="text/javascript" src="../../../../jquery/jszip-utils/dist/jszip-utils-ie.min.js"></script>
<![endif]-->
<script type="text/javascript" src="../../../../jquery/jquery-3.6.0.min.js"></script>
<script type="text/javascript" src="../../../../jquery/jquery-ui.min.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="FlowAction (Gradle API 8.4)";
        }
    }
    catch(err) {
    }
//-->
var data = {"i0":6};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],4:["t3","Abstract Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
var pathtoroot = "../../../../";
var useModuleDirectories = false;
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="fixedNav">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses.html">All&nbsp;Classes</a></li>
</ul>
<ul class="navListSearch">
<li><label for="search">SEARCH:</label>
<input type="text" id="search" value="search" disabled="disabled">
<input type="reset" id="reset" value="reset" disabled="disabled">
</li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
</div>
<div class="navPadding">&nbsp;</div>
<script type="text/javascript"><!--
$('.navPadding').css('padding-top', $('.fixedNav').css("height"));
//-->
</script>
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle"><span class="packageLabelInType">Package</span>&nbsp;<a href="package-summary.html">org.gradle.api.flow</a></div>
<h2 title="Interface FlowAction" class="title">Interface FlowAction&lt;P extends <a href="FlowParameters.html" title="interface in org.gradle.api.flow">FlowParameters</a>&gt;</h2>
</div>
<div class="contentContainer">
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt><span class="paramLabel">Type Parameters:</span></dt>
<dd><code>P</code> - Parameter type for the dataflow action. Should be <a href="FlowParameters.None.html" title="class in org.gradle.api.flow"><code>FlowParameters.None</code></a> if the action does not have parameters.</dd>
</dl>
<hr>
<pre><a href="../Incubating.html" title="annotation in org.gradle.api">@Incubating</a>
public interface <span class="typeNameLabel">FlowAction&lt;P extends <a href="FlowParameters.html" title="interface in org.gradle.api.flow">FlowParameters</a>&gt;</span></pre>
<div class="block">A dataflow action.

 <p>
 A parameterized and isolated piece of work that becomes eligible for execution within a
 <a href="FlowScope.html" title="interface in org.gradle.api.flow"><code>dataflow scope</code></a> as soon as all of its input <a href="FlowParameters.html" title="interface in org.gradle.api.flow"><code>parameters</code></a>
 become available.
 </p>
 <p>
 Implementations can benefit from constructor injection of services
 using the <code>@Inject</code> annotation.
 Currently, only a small subset of services is supported:
 </p>
 <ul>
     <li><a href="../file/ArchiveOperations.html" title="interface in org.gradle.api.file"><code>ArchiveOperations</code></a> provides means to operate on archives such as ZIP or TAR files.</li>
     <li><a href="../../process/ExecOperations.html" title="interface in org.gradle.process"><code>ExecOperations</code></a> provides means to execute external processes.</li>
     <li><a href="../file/FileSystemOperations.html" title="interface in org.gradle.api.file"><code>FileSystemOperations</code></a> provides means to operate on the file system.</li>
 </ul>
 <pre class='autoTested'>
 /**
  * Plays a given {@link Parameters#getMediaFile() media file} using {@code ffplay}.
  */
 class FFPlay implements FlowAction&lt;FFPlay.Parameters&gt; {

     interface Parameters extends FlowParameters {
         @Input
         Property&lt;File&gt; getMediaFile();
     }

     private final ExecOperations execOperations;

     @Inject
     FFPlay(ExecOperations execOperations) {
         this.execOperations = execOperations;
     }

     @Override
     public void execute(Parameters parameters) throws Exception {
         execOperations.exec(spec -&gt; {
             spec.commandLine(
                 "ffplay", "-nodisp", "-autoexit", "-hide_banner", "-loglevel", "quiet",
                 parameters.getMediaFile().get().getAbsolutePath()
             );
             spec.setIgnoreExitValue(true);
         });
     }
 }
 </pre></div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>8.1</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="FlowScope.html" title="interface in org.gradle.api.flow"><code>FlowScope</code></a>, 
<a href="FlowProviders.html" title="interface in org.gradle.api.flow"><code>FlowProviders</code></a></dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t3" class="tableTab"><span><a href="javascript:show(4);">Abstract Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colSecond" scope="col">Method</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#execute-P-">execute</a></span>&#8203;(<a href="FlowAction.html" title="type parameter in FlowAction">P</a>&nbsp;parameters)</code></th>
<td class="colLast">&nbsp;</td>
</tr>
</table>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="execute-org.gradle.api.flow.FlowParameters-">
<!--   -->
</a><a name="execute-P-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>execute</h4>
<pre class="methodSignature">void&nbsp;execute&#8203;(<a href="FlowAction.html" title="type parameter in FlowAction">P</a>&nbsp;parameters)
      throws java.lang.Exception</pre>
<dl>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code>java.lang.Exception</code></dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
