<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc -->
<title>RepositoryContentDescriptor (Gradle API 8.4)</title>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<link rel="stylesheet" type="text/css" href="../../../../../javadoc.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../jquery/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
<script type="text/javascript" src="../../../../../jquery/jszip/dist/jszip.min.js"></script>
<script type="text/javascript" src="../../../../../jquery/jszip-utils/dist/jszip-utils.min.js"></script>
<!--[if IE]>
<script type="text/javascript" src="../../../../../jquery/jszip-utils/dist/jszip-utils-ie.min.js"></script>
<![endif]-->
<script type="text/javascript" src="../../../../../jquery/jquery-3.6.0.min.js"></script>
<script type="text/javascript" src="../../../../../jquery/jquery-ui.min.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="RepositoryContentDescriptor (Gradle API 8.4)";
        }
    }
    catch(err) {
    }
//-->
var data = {"i0":6,"i1":6,"i2":6,"i3":6,"i4":6,"i5":6,"i6":6,"i7":6,"i8":6,"i9":6};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],4:["t3","Abstract Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
var pathtoroot = "../../../../../";
var useModuleDirectories = false;
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="fixedNav">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../../allclasses.html">All&nbsp;Classes</a></li>
</ul>
<ul class="navListSearch">
<li><label for="search">SEARCH:</label>
<input type="text" id="search" value="search" disabled="disabled">
<input type="reset" id="reset" value="reset" disabled="disabled">
</li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
</div>
<div class="navPadding">&nbsp;</div>
<script type="text/javascript"><!--
$('.navPadding').css('padding-top', $('.fixedNav').css("height"));
//-->
</script>
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle"><span class="packageLabelInType">Package</span>&nbsp;<a href="package-summary.html">org.gradle.api.artifacts.repositories</a></div>
<h2 title="Interface RepositoryContentDescriptor" class="title">Interface RepositoryContentDescriptor</h2>
</div>
<div class="contentContainer">
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Superinterfaces:</dt>
<dd><code><a href="InclusiveRepositoryContentDescriptor.html" title="interface in org.gradle.api.artifacts.repositories">InclusiveRepositoryContentDescriptor</a></code></dd>
</dl>
<dl>
<dt>All Known Subinterfaces:</dt>
<dd><code><a href="MavenRepositoryContentDescriptor.html" title="interface in org.gradle.api.artifacts.repositories">MavenRepositoryContentDescriptor</a></code></dd>
</dl>
<hr>
<pre>public interface <span class="typeNameLabel">RepositoryContentDescriptor</span>
extends <a href="InclusiveRepositoryContentDescriptor.html" title="interface in org.gradle.api.artifacts.repositories">InclusiveRepositoryContentDescriptor</a></pre>
<div class="block"><p>Descriptor of a repository content, used to avoid reaching to
 an external repository when not needed.</p>

 <p>Excludes are applied after includes. This means that by default, everything is included and nothing excluded.
 If includes are added, then if the module doesn't match any of the includes, it's excluded. Then if it does, but
 it also matches one of the excludes, it's also excluded.</p></div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>5.1</dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t3" class="tableTab"><span><a href="javascript:show(4);">Abstract Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colSecond" scope="col">Method</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#excludeGroup-java.lang.String-">excludeGroup</a></span>&#8203;(java.lang.String&nbsp;group)</code></th>
<td class="colLast">
<div class="block">Declares that an entire group shouldn't be searched for in this repository.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#excludeGroupAndSubgroups-java.lang.String-">excludeGroupAndSubgroups</a></span>&#8203;(java.lang.String&nbsp;groupPrefix)</code></th>
<td class="colLast">
<div class="block">Declares that an entire group and its subgroups shouldn't be searched for in this repository.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#excludeGroupByRegex-java.lang.String-">excludeGroupByRegex</a></span>&#8203;(java.lang.String&nbsp;groupRegex)</code></th>
<td class="colLast">
<div class="block">Declares that an entire group shouldn't be searched for in this repository.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#excludeModule-java.lang.String-java.lang.String-">excludeModule</a></span>&#8203;(java.lang.String&nbsp;group,
             java.lang.String&nbsp;moduleName)</code></th>
<td class="colLast">
<div class="block">Declares that an entire module shouldn't be searched for in this repository.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#excludeModuleByRegex-java.lang.String-java.lang.String-">excludeModuleByRegex</a></span>&#8203;(java.lang.String&nbsp;groupRegex,
                    java.lang.String&nbsp;moduleNameRegex)</code></th>
<td class="colLast">
<div class="block">Declares that an entire module shouldn't be searched for in this repository, using regular expressions.</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#excludeVersion-java.lang.String-java.lang.String-java.lang.String-">excludeVersion</a></span>&#8203;(java.lang.String&nbsp;group,
              java.lang.String&nbsp;moduleName,
              java.lang.String&nbsp;version)</code></th>
<td class="colLast">
<div class="block">Declares that a specific module version shouldn't be searched for in this repository.</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#excludeVersionByRegex-java.lang.String-java.lang.String-java.lang.String-">excludeVersionByRegex</a></span>&#8203;(java.lang.String&nbsp;groupRegex,
                     java.lang.String&nbsp;moduleNameRegex,
                     java.lang.String&nbsp;versionRegex)</code></th>
<td class="colLast">
<div class="block">Declares that a specific module version shouldn't be searched for in this repository, using regular expressions.</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#notForConfigurations-java.lang.String...-">notForConfigurations</a></span>&#8203;(java.lang.String...&nbsp;configurationNames)</code></th>
<td class="colLast">
<div class="block">Declares that this repository should not be used for a specific
 set of configurations.</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>&lt;T&gt;&nbsp;void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#onlyForAttribute-org.gradle.api.attributes.Attribute-T...-">onlyForAttribute</a></span>&#8203;(<a href="../../attributes/Attribute.html" title="class in org.gradle.api.attributes">Attribute</a>&lt;T&gt;&nbsp;attribute,
                T...&nbsp;validValues)</code></th>
<td class="colLast">
<div class="block">Declares that this repository will only be searched if the consumer requires a
 specific attribute.</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#onlyForConfigurations-java.lang.String...-">onlyForConfigurations</a></span>&#8203;(java.lang.String...&nbsp;configurationNames)</code></th>
<td class="colLast">
<div class="block">Declares that this repository should only be used for a specific
 set of configurations.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.org.gradle.api.artifacts.repositories.InclusiveRepositoryContentDescriptor">
<!--   -->
</a>
<h3>Methods inherited from interface&nbsp;org.gradle.api.artifacts.repositories.<a href="InclusiveRepositoryContentDescriptor.html" title="interface in org.gradle.api.artifacts.repositories">InclusiveRepositoryContentDescriptor</a></h3>
<code><a href="InclusiveRepositoryContentDescriptor.html#includeGroup-java.lang.String-">includeGroup</a>, <a href="InclusiveRepositoryContentDescriptor.html#includeGroupAndSubgroups-java.lang.String-">includeGroupAndSubgroups</a>, <a href="InclusiveRepositoryContentDescriptor.html#includeGroupByRegex-java.lang.String-">includeGroupByRegex</a>, <a href="InclusiveRepositoryContentDescriptor.html#includeModule-java.lang.String-java.lang.String-">includeModule</a>, <a href="InclusiveRepositoryContentDescriptor.html#includeModuleByRegex-java.lang.String-java.lang.String-">includeModuleByRegex</a>, <a href="InclusiveRepositoryContentDescriptor.html#includeVersion-java.lang.String-java.lang.String-java.lang.String-">includeVersion</a>, <a href="InclusiveRepositoryContentDescriptor.html#includeVersionByRegex-java.lang.String-java.lang.String-java.lang.String-">includeVersionByRegex</a></code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="excludeGroup-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>excludeGroup</h4>
<pre class="methodSignature">void&nbsp;excludeGroup&#8203;(java.lang.String&nbsp;group)</pre>
<div class="block">Declares that an entire group shouldn't be searched for in this repository.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>group</code> - the group name</dd>
</dl>
</li>
</ul>
<a name="excludeGroupAndSubgroups-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>excludeGroupAndSubgroups</h4>
<pre class="methodSignature"><a href="../../Incubating.html" title="annotation in org.gradle.api">@Incubating</a>
void&nbsp;excludeGroupAndSubgroups&#8203;(java.lang.String&nbsp;groupPrefix)</pre>
<div class="block">Declares that an entire group and its subgroups shouldn't be searched for in this repository.

 <p>
 A subgroup is a group that starts with the given prefix and has a dot immediately after the prefix.
 For example, if the prefix is <code>org.gradle</code>, then <code>org.gradle</code> is matched as a group,
 and <code>org.gradle.foo</code> and <code>org.gradle.foo.bar</code> are matched as subgroups. <code>org.gradlefoo</code>
 is not matched as a subgroup.
 </p></div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>groupPrefix</code> - the group prefix to include</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>8.1</dd>
</dl>
</li>
</ul>
<a name="excludeGroupByRegex-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>excludeGroupByRegex</h4>
<pre class="methodSignature">void&nbsp;excludeGroupByRegex&#8203;(java.lang.String&nbsp;groupRegex)</pre>
<div class="block">Declares that an entire group shouldn't be searched for in this repository.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>groupRegex</code> - the group name regular expression</dd>
</dl>
</li>
</ul>
<a name="excludeModule-java.lang.String-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>excludeModule</h4>
<pre class="methodSignature">void&nbsp;excludeModule&#8203;(java.lang.String&nbsp;group,
                   java.lang.String&nbsp;moduleName)</pre>
<div class="block">Declares that an entire module shouldn't be searched for in this repository.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>group</code> - the group name</dd>
<dd><code>moduleName</code> - the module name</dd>
</dl>
</li>
</ul>
<a name="excludeModuleByRegex-java.lang.String-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>excludeModuleByRegex</h4>
<pre class="methodSignature">void&nbsp;excludeModuleByRegex&#8203;(java.lang.String&nbsp;groupRegex,
                          java.lang.String&nbsp;moduleNameRegex)</pre>
<div class="block">Declares that an entire module shouldn't be searched for in this repository, using regular expressions.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>groupRegex</code> - the group name regular expression</dd>
<dd><code>moduleNameRegex</code> - the module name regular expression</dd>
</dl>
</li>
</ul>
<a name="excludeVersion-java.lang.String-java.lang.String-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>excludeVersion</h4>
<pre class="methodSignature">void&nbsp;excludeVersion&#8203;(java.lang.String&nbsp;group,
                    java.lang.String&nbsp;moduleName,
                    java.lang.String&nbsp;version)</pre>
<div class="block">Declares that a specific module version shouldn't be searched for in this repository.
 <p>
 The version notation for a regex will be matched against a single version and does not support range notations.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>group</code> - the group name</dd>
<dd><code>moduleName</code> - the module name</dd>
<dd><code>version</code> - the module version</dd>
</dl>
</li>
</ul>
<a name="excludeVersionByRegex-java.lang.String-java.lang.String-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>excludeVersionByRegex</h4>
<pre class="methodSignature">void&nbsp;excludeVersionByRegex&#8203;(java.lang.String&nbsp;groupRegex,
                           java.lang.String&nbsp;moduleNameRegex,
                           java.lang.String&nbsp;versionRegex)</pre>
<div class="block">Declares that a specific module version shouldn't be searched for in this repository, using regular expressions.
 <p>
 The version notation for a regex will be matched against a single version and does not support range notations.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>groupRegex</code> - the group name</dd>
<dd><code>moduleNameRegex</code> - the module name</dd>
<dd><code>versionRegex</code> - the module version</dd>
</dl>
</li>
</ul>
<a name="onlyForConfigurations-java.lang.String...-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>onlyForConfigurations</h4>
<pre class="methodSignature">void&nbsp;onlyForConfigurations&#8203;(java.lang.String...&nbsp;configurationNames)</pre>
<div class="block">Declares that this repository should only be used for a specific
 set of configurations. Defaults to any configuration</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>configurationNames</code> - the names of the configurations the repository will be used for</dd>
</dl>
</li>
</ul>
<a name="notForConfigurations-java.lang.String...-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>notForConfigurations</h4>
<pre class="methodSignature">void&nbsp;notForConfigurations&#8203;(java.lang.String...&nbsp;configurationNames)</pre>
<div class="block">Declares that this repository should not be used for a specific
 set of configurations. Defaults to any configuration</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>configurationNames</code> - the names of the configurations the repository will not be used for</dd>
</dl>
</li>
</ul>
<a name="onlyForAttribute-org.gradle.api.attributes.Attribute-java.lang.Object:A-">
<!--   -->
</a><a name="onlyForAttribute-org.gradle.api.attributes.Attribute-T...-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>onlyForAttribute</h4>
<pre class="methodSignature">&lt;T&gt;&nbsp;void&nbsp;onlyForAttribute&#8203;(<a href="../../attributes/Attribute.html" title="class in org.gradle.api.attributes">Attribute</a>&lt;T&gt;&nbsp;attribute,
                          T...&nbsp;validValues)</pre>
<div class="block">Declares that this repository will only be searched if the consumer requires a
 specific attribute.</div>
<dl>
<dt><span class="paramLabel">Type Parameters:</span></dt>
<dd><code>T</code> - the type of the attribute</dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>attribute</code> - the attribute</dd>
<dd><code>validValues</code> - the list of accepted values</dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../../allclasses.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
