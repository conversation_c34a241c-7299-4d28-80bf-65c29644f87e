<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc -->
<title>CodeNarc (Gradle API 8.4)</title>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<link rel="stylesheet" type="text/css" href="../../../../../javadoc.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../jquery/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
<script type="text/javascript" src="../../../../../jquery/jszip/dist/jszip.min.js"></script>
<script type="text/javascript" src="../../../../../jquery/jszip-utils/dist/jszip-utils.min.js"></script>
<!--[if IE]>
<script type="text/javascript" src="../../../../../jquery/jszip-utils/dist/jszip-utils-ie.min.js"></script>
<![endif]-->
<script type="text/javascript" src="../../../../../jquery/jquery-3.6.0.min.js"></script>
<script type="text/javascript" src="../../../../../jquery/jquery-ui.min.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="CodeNarc (Gradle API 8.4)";
        }
    }
    catch(err) {
    }
//-->
var data = {"i0":10,"i1":10,"i2":10,"i3":10,"i4":10,"i5":10,"i6":10,"i7":10,"i8":10,"i9":10,"i10":10,"i11":10,"i12":10,"i13":10,"i14":10,"i15":10,"i16":10,"i17":10,"i18":10};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
var pathtoroot = "../../../../../";
var useModuleDirectories = false;
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="fixedNav">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../../allclasses.html">All&nbsp;Classes</a></li>
</ul>
<ul class="navListSearch">
<li><label for="search">SEARCH:</label>
<input type="text" id="search" value="search" disabled="disabled">
<input type="reset" id="reset" value="reset" disabled="disabled">
</li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li><a href="#nested.class.summary">Nested</a>&nbsp;|&nbsp;</li>
<li><a href="#field.summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
</div>
<div class="navPadding">&nbsp;</div>
<script type="text/javascript"><!--
$('.navPadding').css('padding-top', $('.fixedNav').css("height"));
//-->
</script>
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle"><span class="packageLabelInType">Package</span>&nbsp;<a href="package-summary.html">org.gradle.api.plugins.quality</a></div>
<h2 title="Class CodeNarc" class="title">Class CodeNarc</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>org.gradle.api.internal.AbstractTask</li>
<li>
<ul class="inheritance">
<li><a href="../../DefaultTask.html" title="class in org.gradle.api">org.gradle.api.DefaultTask</a></li>
<li>
<ul class="inheritance">
<li>org.gradle.api.internal.ConventionTask</li>
<li>
<ul class="inheritance">
<li><a href="../../tasks/SourceTask.html" title="class in org.gradle.api.tasks">org.gradle.api.tasks.SourceTask</a></li>
<li>
<ul class="inheritance">
<li><a href="AbstractCodeQualityTask.html" title="class in org.gradle.api.plugins.quality">org.gradle.api.plugins.quality.AbstractCodeQualityTask</a></li>
<li>
<ul class="inheritance">
<li>org.gradle.api.plugins.quality.CodeNarc</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Implemented Interfaces:</dt>
<dd><code>java.lang.Comparable&lt;<a href="../../Task.html" title="interface in org.gradle.api">Task</a>&gt;</code>, <code>org.gradle.api.internal.DynamicObjectAware</code>, <code>org.gradle.api.internal.IConventionAware</code>, <code>org.gradle.api.internal.TaskInternal</code>, <code><a href="../ExtensionAware.html" title="interface in org.gradle.api.plugins">ExtensionAware</a></code>, <code><a href="../../reporting/Reporting.html" title="interface in org.gradle.api.reporting">Reporting</a>&lt;<a href="CodeNarcReports.html" title="interface in org.gradle.api.plugins.quality">CodeNarcReports</a>&gt;</code>, <code><a href="../../Task.html" title="interface in org.gradle.api">Task</a></code>, <code><a href="../../tasks/util/PatternFilterable.html" title="interface in org.gradle.api.tasks.util">PatternFilterable</a></code>, <code><a href="../../tasks/VerificationTask.html" title="interface in org.gradle.api.tasks">VerificationTask</a></code>, <code><a href="../../../util/Configurable.html" title="interface in org.gradle.util">Configurable</a>&lt;<a href="../../Task.html" title="interface in org.gradle.api">Task</a>&gt;</code></dd>
</dl>
<hr>
<pre><a href="../../tasks/CacheableTask.html" title="annotation in org.gradle.api.tasks">@CacheableTask</a>
public abstract class <span class="typeNameLabel">CodeNarc</span>
extends <a href="AbstractCodeQualityTask.html" title="class in org.gradle.api.plugins.quality">AbstractCodeQualityTask</a>
implements <a href="../../reporting/Reporting.html" title="interface in org.gradle.api.reporting">Reporting</a>&lt;<a href="CodeNarcReports.html" title="interface in org.gradle.api.plugins.quality">CodeNarcReports</a>&gt;</pre>
<div class="block">Runs CodeNarc against some source files.</div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== NESTED CLASS SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="nested.class.summary">
<!--   -->
</a>
<h3>Nested Class Summary</h3>
<ul class="blockList">
<li class="blockList"><a name="nested.classes.inherited.from.class.org.gradle.api.Task">
<!--   -->
</a>
<h3>Nested classes/interfaces inherited from interface&nbsp;org.gradle.api.<a href="../../Task.html" title="interface in org.gradle.api">Task</a></h3>
<code><a href="../../Task.Namer.html" title="class in org.gradle.api">Task.Namer</a></code></li>
</ul>
</li>
</ul>
<!-- =========== FIELD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.summary">
<!--   -->
</a>
<h3>Field Summary</h3>
<ul class="blockList">
<li class="blockList"><a name="fields.inherited.from.class.org.gradle.api.Task">
<!--   -->
</a>
<h3>Fields inherited from interface&nbsp;org.gradle.api.<a href="../../Task.html" title="interface in org.gradle.api">Task</a></h3>
<code><a href="../../Task.html#TASK_ACTION">TASK_ACTION</a>, <a href="../../Task.html#TASK_CONSTRUCTOR_ARGS">TASK_CONSTRUCTOR_ARGS</a>, <a href="../../Task.html#TASK_DEPENDS_ON">TASK_DEPENDS_ON</a>, <a href="../../Task.html#TASK_DESCRIPTION">TASK_DESCRIPTION</a>, <a href="../../Task.html#TASK_GROUP">TASK_GROUP</a>, <a href="../../Task.html#TASK_NAME">TASK_NAME</a>, <a href="../../Task.html#TASK_OVERWRITE">TASK_OVERWRITE</a>, <a href="../../Task.html#TASK_TYPE">TASK_TYPE</a></code></li>
</ul>
</li>
</ul>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="memberSummary" summary="Constructor Summary table, listing constructors, and an explanation">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Constructor</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tr class="altColor">
<th class="colConstructorName" scope="row"><code><span class="memberNameLink"><a href="#CodeNarc--">CodeNarc</a></span>()</code></th>
<td class="colLast">&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colSecond" scope="col">Method</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code><a href="../../file/FileCollection.html" title="interface in org.gradle.api.file">FileCollection</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getCodenarcClasspath--">getCodenarcClasspath</a></span>()</code></th>
<td class="colLast">
<div class="block">The class path containing the CodeNarc library to be used.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code><a href="../../file/FileCollection.html" title="interface in org.gradle.api.file">FileCollection</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getCompilationClasspath--">getCompilationClasspath</a></span>()</code></th>
<td class="colLast">
<div class="block">The class path to be used by CodeNarc when compiling classes during analysis.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code><a href="../../resources/TextResource.html" title="interface in org.gradle.api.resources">TextResource</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getConfig--">getConfig</a></span>()</code></th>
<td class="colLast">
<div class="block">The CodeNarc configuration to use.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>java.io.File</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getConfigFile--">getConfigFile</a></span>()</code></th>
<td class="colLast">
<div class="block">The CodeNarc configuration file to use.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>int</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getMaxPriority1Violations--">getMaxPriority1Violations</a></span>()</code></th>
<td class="colLast">
<div class="block">The maximum number of priority 1 violations allowed before failing the build.</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>int</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getMaxPriority2Violations--">getMaxPriority2Violations</a></span>()</code></th>
<td class="colLast">
<div class="block">The maximum number of priority 2 violations allowed before failing the build.</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>int</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getMaxPriority3Violations--">getMaxPriority3Violations</a></span>()</code></th>
<td class="colLast">
<div class="block">The maximum number of priority 3 violations allowed before failing the build.</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code><a href="CodeNarcReports.html" title="interface in org.gradle.api.plugins.quality">CodeNarcReports</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getReports--">getReports</a></span>()</code></th>
<td class="colLast">
<div class="block">The reports to be generated by this task.</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code><a href="../../file/FileTree.html" title="interface in org.gradle.api.file">FileTree</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getSource--">getSource</a></span>()</code></th>
<td class="colLast">
<div class="block">Returns the source for this task, after the include and exclude patterns have been applied.</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code><a href="CodeNarcReports.html" title="interface in org.gradle.api.plugins.quality">CodeNarcReports</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#reports-groovy.lang.Closure-">reports</a></span>&#8203;(<a href="https://docs.groovy-lang.org/docs/groovy-3.0.17/html/gapi/groovy/lang/Closure.html?is-external=true" title="class or interface in groovy.lang" class="externalLink">Closure</a>&nbsp;closure)</code></th>
<td class="colLast">
<div class="block">Configures the reports to be generated by this task.</div>
</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code><a href="CodeNarcReports.html" title="interface in org.gradle.api.plugins.quality">CodeNarcReports</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#reports-org.gradle.api.Action-">reports</a></span>&#8203;(<a href="../../Action.html" title="interface in org.gradle.api">Action</a>&lt;? super <a href="CodeNarcReports.html" title="interface in org.gradle.api.plugins.quality">CodeNarcReports</a>&gt;&nbsp;configureAction)</code></th>
<td class="colLast">
<div class="block">Configures the reports to be generated by this task.</div>
</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#run--">run</a></span>()</code></th>
<td class="colLast">&nbsp;</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setCodenarcClasspath-org.gradle.api.file.FileCollection-">setCodenarcClasspath</a></span>&#8203;(<a href="../../file/FileCollection.html" title="interface in org.gradle.api.file">FileCollection</a>&nbsp;codenarcClasspath)</code></th>
<td class="colLast">
<div class="block">The class path containing the CodeNarc library to be used.</div>
</td>
</tr>
<tr id="i13" class="rowColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setCompilationClasspath-org.gradle.api.file.FileCollection-">setCompilationClasspath</a></span>&#8203;(<a href="../../file/FileCollection.html" title="interface in org.gradle.api.file">FileCollection</a>&nbsp;compilationClasspath)</code></th>
<td class="colLast">
<div class="block">The class path to be used by CodeNarc when compiling classes during analysis.</div>
</td>
</tr>
<tr id="i14" class="altColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setConfig-org.gradle.api.resources.TextResource-">setConfig</a></span>&#8203;(<a href="../../resources/TextResource.html" title="interface in org.gradle.api.resources">TextResource</a>&nbsp;config)</code></th>
<td class="colLast">
<div class="block">The CodeNarc configuration to use.</div>
</td>
</tr>
<tr id="i15" class="rowColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setConfigFile-java.io.File-">setConfigFile</a></span>&#8203;(java.io.File&nbsp;configFile)</code></th>
<td class="colLast">
<div class="block">The CodeNarc configuration file to use.</div>
</td>
</tr>
<tr id="i16" class="altColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setMaxPriority1Violations-int-">setMaxPriority1Violations</a></span>&#8203;(int&nbsp;maxPriority1Violations)</code></th>
<td class="colLast">
<div class="block">The maximum number of priority 1 violations allowed before failing the build.</div>
</td>
</tr>
<tr id="i17" class="rowColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setMaxPriority2Violations-int-">setMaxPriority2Violations</a></span>&#8203;(int&nbsp;maxPriority2Violations)</code></th>
<td class="colLast">
<div class="block">The maximum number of priority 2 violations allowed before failing the build.</div>
</td>
</tr>
<tr id="i18" class="altColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setMaxPriority3Violations-int-">setMaxPriority3Violations</a></span>&#8203;(int&nbsp;maxPriority3Violations)</code></th>
<td class="colLast">
<div class="block">The maximum number of priority 3 violations allowed before failing the build.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.org.gradle.api.plugins.quality.AbstractCodeQualityTask">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;org.gradle.api.plugins.quality.<a href="AbstractCodeQualityTask.html" title="class in org.gradle.api.plugins.quality">AbstractCodeQualityTask</a></h3>
<code><a href="AbstractCodeQualityTask.html#configureForkOptions-org.gradle.process.JavaForkOptions-">configureForkOptions</a>, <a href="AbstractCodeQualityTask.html#getIgnoreFailures--">getIgnoreFailures</a>, <a href="AbstractCodeQualityTask.html#getIgnoreFailuresProperty--">getIgnoreFailuresProperty</a>, <a href="AbstractCodeQualityTask.html#getJavaLauncher--">getJavaLauncher</a>, <a href="AbstractCodeQualityTask.html#getMaxHeapSize--">getMaxHeapSize</a>, <a href="AbstractCodeQualityTask.html#getMinHeapSize--">getMinHeapSize</a>, <a href="AbstractCodeQualityTask.html#getObjectFactory--">getObjectFactory</a>, <a href="AbstractCodeQualityTask.html#getToolchainService--">getToolchainService</a>, <a href="AbstractCodeQualityTask.html#getWorkerExecutor--">getWorkerExecutor</a>, <a href="AbstractCodeQualityTask.html#setIgnoreFailures-boolean-">setIgnoreFailures</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.org.gradle.api.tasks.SourceTask">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;org.gradle.api.tasks.<a href="../../tasks/SourceTask.html" title="class in org.gradle.api.tasks">SourceTask</a></h3>
<code><a href="../../tasks/SourceTask.html#exclude-groovy.lang.Closure-">exclude</a>, <a href="../../tasks/SourceTask.html#exclude-java.lang.Iterable-">exclude</a>, <a href="../../tasks/SourceTask.html#exclude-java.lang.String...-">exclude</a>, <a href="../../tasks/SourceTask.html#exclude-org.gradle.api.specs.Spec-">exclude</a>, <a href="../../tasks/SourceTask.html#getExcludes--">getExcludes</a>, <a href="../../tasks/SourceTask.html#getIncludes--">getIncludes</a>, <a href="../../tasks/SourceTask.html#getPatternSet--">getPatternSet</a>, <a href="../../tasks/SourceTask.html#getPatternSetFactory--">getPatternSetFactory</a>, <a href="../../tasks/SourceTask.html#include-groovy.lang.Closure-">include</a>, <a href="../../tasks/SourceTask.html#include-java.lang.Iterable-">include</a>, <a href="../../tasks/SourceTask.html#include-java.lang.String...-">include</a>, <a href="../../tasks/SourceTask.html#include-org.gradle.api.specs.Spec-">include</a>, <a href="../../tasks/SourceTask.html#setExcludes-java.lang.Iterable-">setExcludes</a>, <a href="../../tasks/SourceTask.html#setIncludes-java.lang.Iterable-">setIncludes</a>, <a href="../../tasks/SourceTask.html#setSource-java.lang.Object-">setSource</a>, <a href="../../tasks/SourceTask.html#setSource-org.gradle.api.file.FileTree-">setSource</a>, <a href="../../tasks/SourceTask.html#source-java.lang.Object...-">source</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.org.gradle.api.internal.ConventionTask">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;org.gradle.api.internal.ConventionTask</h3>
<code>conventionMapping, conventionMapping, getConventionMapping</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.org.gradle.api.DefaultTask">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;org.gradle.api.<a href="../../DefaultTask.html" title="class in org.gradle.api">DefaultTask</a></h3>
<code><a href="../../DefaultTask.html#compareTo-org.gradle.api.Task-">compareTo</a>, <a href="../../DefaultTask.html#configure-groovy.lang.Closure-">configure</a>, <a href="../../DefaultTask.html#dependsOn-java.lang.Object...-">dependsOn</a>, <a href="../../DefaultTask.html#doFirst-groovy.lang.Closure-">doFirst</a>, <a href="../../DefaultTask.html#doFirst-java.lang.String-org.gradle.api.Action-">doFirst</a>, <a href="../../DefaultTask.html#doFirst-org.gradle.api.Action-">doFirst</a>, <a href="../../DefaultTask.html#doLast-groovy.lang.Closure-">doLast</a>, <a href="../../DefaultTask.html#doLast-java.lang.String-org.gradle.api.Action-">doLast</a>, <a href="../../DefaultTask.html#doLast-org.gradle.api.Action-">doLast</a>, <a href="../../DefaultTask.html#finalizedBy-java.lang.Object...-">finalizedBy</a>, <a href="../../DefaultTask.html#getActions--">getActions</a>, <a href="../../DefaultTask.html#getAnt--">getAnt</a>, <a href="../../DefaultTask.html#getDependsOn--">getDependsOn</a>, <a href="../../DefaultTask.html#getDescription--">getDescription</a>, <a href="../../DefaultTask.html#getDestroyables--">getDestroyables</a>, <a href="../../DefaultTask.html#getDidWork--">getDidWork</a>, <a href="../../DefaultTask.html#getEnabled--">getEnabled</a>, <a href="../../DefaultTask.html#getExtensions--">getExtensions</a>, <a href="../../DefaultTask.html#getFinalizedBy--">getFinalizedBy</a>, <a href="../../DefaultTask.html#getGroup--">getGroup</a>, <a href="../../DefaultTask.html#getInputs--">getInputs</a>, <a href="../../DefaultTask.html#getLocalState--">getLocalState</a>, <a href="../../DefaultTask.html#getLogger--">getLogger</a>, <a href="../../DefaultTask.html#getLogging--">getLogging</a>, <a href="../../DefaultTask.html#getMustRunAfter--">getMustRunAfter</a>, <a href="../../DefaultTask.html#getName--">getName</a>, <a href="../../DefaultTask.html#getOutputs--">getOutputs</a>, <a href="../../DefaultTask.html#getPath--">getPath</a>, <a href="../../DefaultTask.html#getProject--">getProject</a>, <a href="../../DefaultTask.html#getShouldRunAfter--">getShouldRunAfter</a>, <a href="../../DefaultTask.html#getState--">getState</a>, <a href="../../DefaultTask.html#getTaskDependencies--">getTaskDependencies</a>, <a href="../../DefaultTask.html#getTemporaryDir--">getTemporaryDir</a>, <a href="../../DefaultTask.html#getTimeout--">getTimeout</a>, <a href="../../DefaultTask.html#hasProperty-java.lang.String-">hasProperty</a>, <a href="../../DefaultTask.html#mustRunAfter-java.lang.Object...-">mustRunAfter</a>, <a href="../../DefaultTask.html#onlyIf-groovy.lang.Closure-">onlyIf</a>, <a href="../../DefaultTask.html#onlyIf-java.lang.String-org.gradle.api.specs.Spec-">onlyIf</a>, <a href="../../DefaultTask.html#onlyIf-org.gradle.api.specs.Spec-">onlyIf</a>, <a href="../../DefaultTask.html#property-java.lang.String-">property</a>, <a href="../../DefaultTask.html#setActions-java.util.List-">setActions</a>, <a href="../../DefaultTask.html#setDependsOn-java.lang.Iterable-">setDependsOn</a>, <a href="../../DefaultTask.html#setDescription-java.lang.String-">setDescription</a>, <a href="../../DefaultTask.html#setDidWork-boolean-">setDidWork</a>, <a href="../../DefaultTask.html#setEnabled-boolean-">setEnabled</a>, <a href="../../DefaultTask.html#setFinalizedBy-java.lang.Iterable-">setFinalizedBy</a>, <a href="../../DefaultTask.html#setGroup-java.lang.String-">setGroup</a>, <a href="../../DefaultTask.html#setMustRunAfter-java.lang.Iterable-">setMustRunAfter</a>, <a href="../../DefaultTask.html#setOnlyIf-groovy.lang.Closure-">setOnlyIf</a>, <a href="../../DefaultTask.html#setOnlyIf-java.lang.String-org.gradle.api.specs.Spec-">setOnlyIf</a>, <a href="../../DefaultTask.html#setOnlyIf-org.gradle.api.specs.Spec-">setOnlyIf</a>, <a href="../../DefaultTask.html#setProperty-java.lang.String-java.lang.Object-">setProperty</a>, <a href="../../DefaultTask.html#setShouldRunAfter-java.lang.Iterable-">setShouldRunAfter</a>, <a href="../../DefaultTask.html#shouldRunAfter-java.lang.Object...-">shouldRunAfter</a>, <a href="../../DefaultTask.html#usesService-org.gradle.api.provider.Provider-">usesService</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.org.gradle.api.internal.AbstractTask">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;org.gradle.api.internal.AbstractTask</h3>
<code>acceptServiceReferences, appendParallelSafeAction, doNotTrackState, getAsDynamicObject, getConvention, getIdentityPath, getImpliesSubProjects, getLifecycleDependencies, getOnlyIf, getReasonNotToTrackState, getReasonTaskIsIncompatibleWithConfigurationCache, getRequiredServices, getServices, getSharedResources, getStandardOutputCapture, getTaskActions, getTaskIdentity, getTemporaryDirFactory, hasTaskActions, injectIntoNewInstance, isCompatibleWithConfigurationCache, isEnabled, isHasCustomActions, notCompatibleWithConfigurationCache, prependParallelSafeAction, setImpliesSubProjects</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>clone, equals, finalize, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.org.gradle.api.Task">
<!--   -->
</a>
<h3>Methods inherited from interface&nbsp;org.gradle.api.<a href="../../Task.html" title="interface in org.gradle.api">Task</a></h3>
<code><a href="../../Task.html#doNotTrackState-java.lang.String-">doNotTrackState</a>, <a href="../../Task.html#getConvention--">getConvention</a>, <a href="../../Task.html#notCompatibleWithConfigurationCache-java.lang.String-">notCompatibleWithConfigurationCache</a></code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a name="CodeNarc--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>CodeNarc</h4>
<pre>public&nbsp;CodeNarc()</pre>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="getConfigFile--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getConfigFile</h4>
<pre class="methodSignature"><a href="../../tasks/Internal.html" title="annotation in org.gradle.api.tasks">@Internal</a>
public&nbsp;java.io.File&nbsp;getConfigFile()</pre>
<div class="block">The CodeNarc configuration file to use.</div>
</li>
</ul>
<a name="getSource--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getSource</h4>
<pre class="methodSignature"><a href="../../tasks/PathSensitive.html" title="annotation in org.gradle.api.tasks">@PathSensitive</a>(<a href="../../tasks/PathSensitivity.html#RELATIVE">RELATIVE</a>)
public&nbsp;<a href="../../file/FileTree.html" title="interface in org.gradle.api.file">FileTree</a>&nbsp;getSource()</pre>
<div class="block">Returns the source for this task, after the include and exclude patterns have been applied. Ignores source files which do not exist.

 <p>
 The <a href="../../tasks/PathSensitivity.html" title="enum in org.gradle.api.tasks"><code>PathSensitivity</code></a> for the sources is configured to be <a href="../../tasks/PathSensitivity.html#ABSOLUTE"><code>PathSensitivity.ABSOLUTE</code></a>.
 If your sources are less strict, please change it accordingly by overriding this method in your subclass.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code><a href="../../tasks/SourceTask.html#getSource--">getSource</a></code>&nbsp;in class&nbsp;<code><a href="../../tasks/SourceTask.html" title="class in org.gradle.api.tasks">SourceTask</a></code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The source.</dd>
</dl>
</li>
</ul>
<a name="setConfigFile-java.io.File-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setConfigFile</h4>
<pre class="methodSignature">public&nbsp;void&nbsp;setConfigFile&#8203;(java.io.File&nbsp;configFile)</pre>
<div class="block">The CodeNarc configuration file to use.</div>
</li>
</ul>
<a name="run--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>run</h4>
<pre class="methodSignature">public&nbsp;void&nbsp;run()</pre>
</li>
</ul>
<a name="reports-groovy.lang.Closure-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>reports</h4>
<pre class="methodSignature">public&nbsp;<a href="CodeNarcReports.html" title="interface in org.gradle.api.plugins.quality">CodeNarcReports</a>&nbsp;reports&#8203;(<a href="https://docs.groovy-lang.org/docs/groovy-3.0.17/html/gapi/groovy/lang/Closure.html?is-external=true" title="class or interface in groovy.lang" class="externalLink">Closure</a>&nbsp;closure)</pre>
<div class="block">Configures the reports to be generated by this task.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../reporting/Reporting.html#reports-groovy.lang.Closure-">reports</a></code>&nbsp;in interface&nbsp;<code><a href="../../reporting/Reporting.html" title="interface in org.gradle.api.reporting">Reporting</a>&lt;<a href="CodeNarcReports.html" title="interface in org.gradle.api.plugins.quality">CodeNarcReports</a>&gt;</code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>closure</code> - The configuration</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The report container</dd>
</dl>
</li>
</ul>
<a name="reports-org.gradle.api.Action-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>reports</h4>
<pre class="methodSignature">public&nbsp;<a href="CodeNarcReports.html" title="interface in org.gradle.api.plugins.quality">CodeNarcReports</a>&nbsp;reports&#8203;(<a href="../../Action.html" title="interface in org.gradle.api">Action</a>&lt;? super <a href="CodeNarcReports.html" title="interface in org.gradle.api.plugins.quality">CodeNarcReports</a>&gt;&nbsp;configureAction)</pre>
<div class="block">Configures the reports to be generated by this task.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../reporting/Reporting.html#reports-org.gradle.api.Action-">reports</a></code>&nbsp;in interface&nbsp;<code><a href="../../reporting/Reporting.html" title="interface in org.gradle.api.reporting">Reporting</a>&lt;<a href="CodeNarcReports.html" title="interface in org.gradle.api.plugins.quality">CodeNarcReports</a>&gt;</code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>configureAction</code> - The configuration</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The report container</dd>
</dl>
</li>
</ul>
<a name="getCodenarcClasspath--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCodenarcClasspath</h4>
<pre class="methodSignature"><a href="../../tasks/Classpath.html" title="annotation in org.gradle.api.tasks">@Classpath</a>
public&nbsp;<a href="../../file/FileCollection.html" title="interface in org.gradle.api.file">FileCollection</a>&nbsp;getCodenarcClasspath()</pre>
<div class="block">The class path containing the CodeNarc library to be used.</div>
</li>
</ul>
<a name="setCodenarcClasspath-org.gradle.api.file.FileCollection-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCodenarcClasspath</h4>
<pre class="methodSignature">public&nbsp;void&nbsp;setCodenarcClasspath&#8203;(<a href="../../file/FileCollection.html" title="interface in org.gradle.api.file">FileCollection</a>&nbsp;codenarcClasspath)</pre>
<div class="block">The class path containing the CodeNarc library to be used.</div>
</li>
</ul>
<a name="getCompilationClasspath--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCompilationClasspath</h4>
<pre class="methodSignature"><a href="../../tasks/Classpath.html" title="annotation in org.gradle.api.tasks">@Classpath</a>
public&nbsp;<a href="../../file/FileCollection.html" title="interface in org.gradle.api.file">FileCollection</a>&nbsp;getCompilationClasspath()</pre>
<div class="block">The class path to be used by CodeNarc when compiling classes during analysis.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>4.2</dd>
</dl>
</li>
</ul>
<a name="setCompilationClasspath-org.gradle.api.file.FileCollection-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCompilationClasspath</h4>
<pre class="methodSignature">public&nbsp;void&nbsp;setCompilationClasspath&#8203;(<a href="../../file/FileCollection.html" title="interface in org.gradle.api.file">FileCollection</a>&nbsp;compilationClasspath)</pre>
<div class="block">The class path to be used by CodeNarc when compiling classes during analysis.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>4.2</dd>
</dl>
</li>
</ul>
<a name="getConfig--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getConfig</h4>
<pre class="methodSignature">public&nbsp;<a href="../../resources/TextResource.html" title="interface in org.gradle.api.resources">TextResource</a>&nbsp;getConfig()</pre>
<div class="block">The CodeNarc configuration to use. Replaces the <code>configFile</code> property.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>2.2</dd>
</dl>
</li>
</ul>
<a name="setConfig-org.gradle.api.resources.TextResource-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setConfig</h4>
<pre class="methodSignature">public&nbsp;void&nbsp;setConfig&#8203;(<a href="../../resources/TextResource.html" title="interface in org.gradle.api.resources">TextResource</a>&nbsp;config)</pre>
<div class="block">The CodeNarc configuration to use. Replaces the <code>configFile</code> property.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>2.2</dd>
</dl>
</li>
</ul>
<a name="getMaxPriority1Violations--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getMaxPriority1Violations</h4>
<pre class="methodSignature"><a href="../../tasks/Input.html" title="annotation in org.gradle.api.tasks">@Input</a>
public&nbsp;int&nbsp;getMaxPriority1Violations()</pre>
<div class="block">The maximum number of priority 1 violations allowed before failing the build.</div>
</li>
</ul>
<a name="setMaxPriority1Violations-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setMaxPriority1Violations</h4>
<pre class="methodSignature">public&nbsp;void&nbsp;setMaxPriority1Violations&#8203;(int&nbsp;maxPriority1Violations)</pre>
<div class="block">The maximum number of priority 1 violations allowed before failing the build.</div>
</li>
</ul>
<a name="getMaxPriority2Violations--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getMaxPriority2Violations</h4>
<pre class="methodSignature"><a href="../../tasks/Input.html" title="annotation in org.gradle.api.tasks">@Input</a>
public&nbsp;int&nbsp;getMaxPriority2Violations()</pre>
<div class="block">The maximum number of priority 2 violations allowed before failing the build.</div>
</li>
</ul>
<a name="setMaxPriority2Violations-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setMaxPriority2Violations</h4>
<pre class="methodSignature">public&nbsp;void&nbsp;setMaxPriority2Violations&#8203;(int&nbsp;maxPriority2Violations)</pre>
<div class="block">The maximum number of priority 2 violations allowed before failing the build.</div>
</li>
</ul>
<a name="getMaxPriority3Violations--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getMaxPriority3Violations</h4>
<pre class="methodSignature"><a href="../../tasks/Input.html" title="annotation in org.gradle.api.tasks">@Input</a>
public&nbsp;int&nbsp;getMaxPriority3Violations()</pre>
<div class="block">The maximum number of priority 3 violations allowed before failing the build.</div>
</li>
</ul>
<a name="setMaxPriority3Violations-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setMaxPriority3Violations</h4>
<pre class="methodSignature">public&nbsp;void&nbsp;setMaxPriority3Violations&#8203;(int&nbsp;maxPriority3Violations)</pre>
<div class="block">The maximum number of priority 3 violations allowed before failing the build.</div>
</li>
</ul>
<a name="getReports--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>getReports</h4>
<pre class="methodSignature">public&nbsp;<a href="CodeNarcReports.html" title="interface in org.gradle.api.plugins.quality">CodeNarcReports</a>&nbsp;getReports()</pre>
<div class="block">The reports to be generated by this task.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../reporting/Reporting.html#getReports--">getReports</a></code>&nbsp;in interface&nbsp;<code><a href="../../reporting/Reporting.html" title="interface in org.gradle.api.reporting">Reporting</a>&lt;<a href="CodeNarcReports.html" title="interface in org.gradle.api.plugins.quality">CodeNarcReports</a>&gt;</code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The report container</dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../../allclasses.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li><a href="#nested.class.summary">Nested</a>&nbsp;|&nbsp;</li>
<li><a href="#field.summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
