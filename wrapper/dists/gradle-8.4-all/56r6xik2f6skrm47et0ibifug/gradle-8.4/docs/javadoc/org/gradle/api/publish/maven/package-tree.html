<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc -->
<title>org.gradle.api.publish.maven Class Hierarchy (Gradle API 8.4)</title>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<link rel="stylesheet" type="text/css" href="../../../../../javadoc.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../jquery/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
<script type="text/javascript" src="../../../../../jquery/jszip/dist/jszip.min.js"></script>
<script type="text/javascript" src="../../../../../jquery/jszip-utils/dist/jszip-utils.min.js"></script>
<!--[if IE]>
<script type="text/javascript" src="../../../../../jquery/jszip-utils/dist/jszip-utils-ie.min.js"></script>
<![endif]-->
<script type="text/javascript" src="../../../../../jquery/jquery-3.6.0.min.js"></script>
<script type="text/javascript" src="../../../../../jquery/jquery-ui.min.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="org.gradle.api.publish.maven Class Hierarchy (Gradle API 8.4)";
        }
    }
    catch(err) {
    }
//-->
var pathtoroot = "../../../../../";
var useModuleDirectories = false;
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="fixedNav">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li>Class</li>
<li class="navBarCell1Rev">Tree</li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../../allclasses.html">All&nbsp;Classes</a></li>
</ul>
<ul class="navListSearch">
<li><label for="search">SEARCH:</label>
<input type="text" id="search" value="search" disabled="disabled">
<input type="reset" id="reset" value="reset" disabled="disabled">
</li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
</div>
<div class="navPadding">&nbsp;</div>
<script type="text/javascript"><!--
$('.navPadding').css('padding-top', $('.fixedNav').css("height"));
//-->
</script>
<div class="header">
<h1 class="title">Hierarchy For Package org.gradle.api.publish.maven</h1>
<span class="packageHierarchyLabel">Package Hierarchies:</span>
<ul class="horizontal">
<li><a href="../../../../../overview-tree.html">All Packages</a></li>
</ul>
</div>
<div class="contentContainer">
<h2 title="Class Hierarchy">Class Hierarchy</h2>
<ul>
<li class="circle">java.lang.Object
<ul>
<li class="circle">java.lang.Throwable (implements java.io.Serializable)
<ul>
<li class="circle">java.lang.Exception
<ul>
<li class="circle">java.lang.RuntimeException
<ul>
<li class="circle">org.gradle.api.<a href="../../GradleException.html" title="class in org.gradle.api"><span class="typeNameLink">GradleException</span></a>
<ul>
<li class="circle">org.gradle.api.<a href="../../InvalidUserDataException.html" title="class in org.gradle.api"><span class="typeNameLink">InvalidUserDataException</span></a>
<ul>
<li class="circle">org.gradle.api.publish.maven.<a href="InvalidMavenPublicationException.html" title="class in org.gradle.api.publish.maven"><span class="typeNameLink">InvalidMavenPublicationException</span></a></li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
<h2 title="Interface Hierarchy">Interface Hierarchy</h2>
<ul>
<li class="circle">org.gradle.api.<a href="../../Buildable.html" title="interface in org.gradle.api"><span class="typeNameLink">Buildable</span></a>
<ul>
<li class="circle">org.gradle.api.publish.<a href="../PublicationArtifact.html" title="interface in org.gradle.api.publish"><span class="typeNameLink">PublicationArtifact</span></a>
<ul>
<li class="circle">org.gradle.api.publish.maven.<a href="MavenArtifact.html" title="interface in org.gradle.api.publish.maven"><span class="typeNameLink">MavenArtifact</span></a></li>
</ul>
</li>
</ul>
</li>
<li class="circle">java.lang.Iterable&lt;T&gt;
<ul>
<li class="circle">java.util.Collection&lt;E&gt;
<ul>
<li class="circle">org.gradle.api.<a href="../../DomainObjectCollection.html" title="interface in org.gradle.api"><span class="typeNameLink">DomainObjectCollection</span></a>&lt;T&gt;
<ul>
<li class="circle">org.gradle.api.<a href="../../DomainObjectSet.html" title="interface in org.gradle.api"><span class="typeNameLink">DomainObjectSet</span></a>&lt;T&gt; (also extends java.util.Set&lt;E&gt;)
<ul>
<li class="circle">org.gradle.api.publish.maven.<a href="MavenArtifactSet.html" title="interface in org.gradle.api.publish.maven"><span class="typeNameLink">MavenArtifactSet</span></a></li>
</ul>
</li>
</ul>
</li>
<li class="circle">java.util.Set&lt;E&gt;
<ul>
<li class="circle">org.gradle.api.<a href="../../DomainObjectSet.html" title="interface in org.gradle.api"><span class="typeNameLink">DomainObjectSet</span></a>&lt;T&gt; (also extends org.gradle.api.<a href="../../DomainObjectCollection.html" title="interface in org.gradle.api">DomainObjectCollection</a>&lt;T&gt;)
<ul>
<li class="circle">org.gradle.api.publish.maven.<a href="MavenArtifactSet.html" title="interface in org.gradle.api.publish.maven"><span class="typeNameLink">MavenArtifactSet</span></a></li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
<li class="circle">org.gradle.api.publish.maven.<a href="MavenDependency.html" title="interface in org.gradle.api.publish.maven"><span class="typeNameLink">MavenDependency</span></a></li>
<li class="circle">org.gradle.api.publish.maven.<a href="MavenPom.html" title="interface in org.gradle.api.publish.maven"><span class="typeNameLink">MavenPom</span></a></li>
<li class="circle">org.gradle.api.publish.maven.<a href="MavenPomCiManagement.html" title="interface in org.gradle.api.publish.maven"><span class="typeNameLink">MavenPomCiManagement</span></a></li>
<li class="circle">org.gradle.api.publish.maven.<a href="MavenPomContributor.html" title="interface in org.gradle.api.publish.maven"><span class="typeNameLink">MavenPomContributor</span></a></li>
<li class="circle">org.gradle.api.publish.maven.<a href="MavenPomContributorSpec.html" title="interface in org.gradle.api.publish.maven"><span class="typeNameLink">MavenPomContributorSpec</span></a></li>
<li class="circle">org.gradle.api.publish.maven.<a href="MavenPomDeveloper.html" title="interface in org.gradle.api.publish.maven"><span class="typeNameLink">MavenPomDeveloper</span></a></li>
<li class="circle">org.gradle.api.publish.maven.<a href="MavenPomDeveloperSpec.html" title="interface in org.gradle.api.publish.maven"><span class="typeNameLink">MavenPomDeveloperSpec</span></a></li>
<li class="circle">org.gradle.api.publish.maven.<a href="MavenPomDistributionManagement.html" title="interface in org.gradle.api.publish.maven"><span class="typeNameLink">MavenPomDistributionManagement</span></a></li>
<li class="circle">org.gradle.api.publish.maven.<a href="MavenPomIssueManagement.html" title="interface in org.gradle.api.publish.maven"><span class="typeNameLink">MavenPomIssueManagement</span></a></li>
<li class="circle">org.gradle.api.publish.maven.<a href="MavenPomLicense.html" title="interface in org.gradle.api.publish.maven"><span class="typeNameLink">MavenPomLicense</span></a></li>
<li class="circle">org.gradle.api.publish.maven.<a href="MavenPomLicenseSpec.html" title="interface in org.gradle.api.publish.maven"><span class="typeNameLink">MavenPomLicenseSpec</span></a></li>
<li class="circle">org.gradle.api.publish.maven.<a href="MavenPomMailingList.html" title="interface in org.gradle.api.publish.maven"><span class="typeNameLink">MavenPomMailingList</span></a></li>
<li class="circle">org.gradle.api.publish.maven.<a href="MavenPomMailingListSpec.html" title="interface in org.gradle.api.publish.maven"><span class="typeNameLink">MavenPomMailingListSpec</span></a></li>
<li class="circle">org.gradle.api.publish.maven.<a href="MavenPomOrganization.html" title="interface in org.gradle.api.publish.maven"><span class="typeNameLink">MavenPomOrganization</span></a></li>
<li class="circle">org.gradle.api.publish.maven.<a href="MavenPomRelocation.html" title="interface in org.gradle.api.publish.maven"><span class="typeNameLink">MavenPomRelocation</span></a></li>
<li class="circle">org.gradle.api.publish.maven.<a href="MavenPomScm.html" title="interface in org.gradle.api.publish.maven"><span class="typeNameLink">MavenPomScm</span></a></li>
<li class="circle">org.gradle.api.<a href="../../Named.html" title="interface in org.gradle.api"><span class="typeNameLink">Named</span></a>
<ul>
<li class="circle">org.gradle.api.publish.<a href="../Publication.html" title="interface in org.gradle.api.publish"><span class="typeNameLink">Publication</span></a>
<ul>
<li class="circle">org.gradle.api.publish.maven.<a href="MavenPublication.html" title="interface in org.gradle.api.publish.maven"><span class="typeNameLink">MavenPublication</span></a></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li>Class</li>
<li class="navBarCell1Rev">Tree</li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../../allclasses.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
