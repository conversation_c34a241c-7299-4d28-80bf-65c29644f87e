<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc -->
<title>TaskOutputs (Gradle API 8.4)</title>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<link rel="stylesheet" type="text/css" href="../../../../javadoc.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../jquery/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
<script type="text/javascript" src="../../../../jquery/jszip/dist/jszip.min.js"></script>
<script type="text/javascript" src="../../../../jquery/jszip-utils/dist/jszip-utils.min.js"></script>
<!--[if IE]>
<script type="text/javascript" src="../../../../jquery/jszip-utils/dist/jszip-utils-ie.min.js"></script>
<![endif]-->
<script type="text/javascript" src="../../../../jquery/jquery-3.6.0.min.js"></script>
<script type="text/javascript" src="../../../../jquery/jquery-ui.min.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="TaskOutputs (Gradle API 8.4)";
        }
    }
    catch(err) {
    }
//-->
var data = {"i0":6,"i1":6,"i2":6,"i3":6,"i4":6,"i5":6,"i6":6,"i7":6,"i8":6,"i9":6,"i10":6};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],4:["t3","Abstract Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
var pathtoroot = "../../../../";
var useModuleDirectories = false;
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="fixedNav">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses.html">All&nbsp;Classes</a></li>
</ul>
<ul class="navListSearch">
<li><label for="search">SEARCH:</label>
<input type="text" id="search" value="search" disabled="disabled">
<input type="reset" id="reset" value="reset" disabled="disabled">
</li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
</div>
<div class="navPadding">&nbsp;</div>
<script type="text/javascript"><!--
$('.navPadding').css('padding-top', $('.fixedNav').css("height"));
//-->
</script>
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle"><span class="packageLabelInType">Package</span>&nbsp;<a href="package-summary.html">org.gradle.api.tasks</a></div>
<h2 title="Interface TaskOutputs" class="title">Interface TaskOutputs</h2>
</div>
<div class="contentContainer">
<div class="description">
<ul class="blockList">
<li class="blockList">
<hr>
<pre>public interface <span class="typeNameLabel">TaskOutputs</span></pre>
<div class="block"><p>A <code>TaskOutputs</code> represents the outputs of a task.</p>

 <p>You can obtain a <code>TaskOutputs</code> instance using <a href="../Task.html#getOutputs--"><code>Task.getOutputs()</code></a>.</p></div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t3" class="tableTab"><span><a href="javascript:show(4);">Abstract Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colSecond" scope="col">Method</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#cacheIf-java.lang.String-org.gradle.api.specs.Spec-">cacheIf</a></span>&#8203;(java.lang.String&nbsp;cachingEnabledReason,
       <a href="../specs/Spec.html" title="interface in org.gradle.api.specs">Spec</a>&lt;? super <a href="../Task.html" title="interface in org.gradle.api">Task</a>&gt;&nbsp;spec)</code></th>
<td class="colLast">
<div class="block">Cache the results of the task only if the given spec is satisfied.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#cacheIf-org.gradle.api.specs.Spec-">cacheIf</a></span>&#8203;(<a href="../specs/Spec.html" title="interface in org.gradle.api.specs">Spec</a>&lt;? super <a href="../Task.html" title="interface in org.gradle.api">Task</a>&gt;&nbsp;spec)</code></th>
<td class="colLast">
<div class="block">Cache the results of the task only if the given spec is satisfied.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code><a href="TaskOutputFilePropertyBuilder.html" title="interface in org.gradle.api.tasks">TaskOutputFilePropertyBuilder</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#dir-java.lang.Object-">dir</a></span>&#8203;(java.lang.Object&nbsp;path)</code></th>
<td class="colLast">
<div class="block">Registers an output directory for this task.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code><a href="TaskOutputFilePropertyBuilder.html" title="interface in org.gradle.api.tasks">TaskOutputFilePropertyBuilder</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#dirs-java.lang.Object...-">dirs</a></span>&#8203;(java.lang.Object...&nbsp;paths)</code></th>
<td class="colLast">
<div class="block">Registers some output directories for this task.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#doNotCacheIf-java.lang.String-org.gradle.api.specs.Spec-">doNotCacheIf</a></span>&#8203;(java.lang.String&nbsp;cachingDisabledReason,
            <a href="../specs/Spec.html" title="interface in org.gradle.api.specs">Spec</a>&lt;? super <a href="../Task.html" title="interface in org.gradle.api">Task</a>&gt;&nbsp;spec)</code></th>
<td class="colLast">
<div class="block">Disable caching the results of the task if the given spec is satisfied.</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code><a href="TaskOutputFilePropertyBuilder.html" title="interface in org.gradle.api.tasks">TaskOutputFilePropertyBuilder</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#file-java.lang.Object-">file</a></span>&#8203;(java.lang.Object&nbsp;path)</code></th>
<td class="colLast">
<div class="block">Registers some output file for this task.</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code><a href="TaskOutputFilePropertyBuilder.html" title="interface in org.gradle.api.tasks">TaskOutputFilePropertyBuilder</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#files-java.lang.Object...-">files</a></span>&#8203;(java.lang.Object...&nbsp;paths)</code></th>
<td class="colLast">
<div class="block">Registers some output files for this task.</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code><a href="../file/FileCollection.html" title="interface in org.gradle.api.file">FileCollection</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getFiles--">getFiles</a></span>()</code></th>
<td class="colLast">
<div class="block">Returns the output files of this task.</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getHasOutput--">getHasOutput</a></span>()</code></th>
<td class="colLast">
<div class="block">Returns true if this task has declared any outputs.</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#upToDateWhen-groovy.lang.Closure-">upToDateWhen</a></span>&#8203;(<a href="https://docs.groovy-lang.org/docs/groovy-3.0.17/html/gapi/groovy/lang/Closure.html?is-external=true" title="class or interface in groovy.lang" class="externalLink">Closure</a>&nbsp;upToDateClosure)</code></th>
<td class="colLast">
<div class="block">
     Adds a predicate to determine whether previous outputs of this task can be reused.</div>
</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#upToDateWhen-org.gradle.api.specs.Spec-">upToDateWhen</a></span>&#8203;(<a href="../specs/Spec.html" title="interface in org.gradle.api.specs">Spec</a>&lt;? super <a href="../Task.html" title="interface in org.gradle.api">Task</a>&gt;&nbsp;upToDateSpec)</code></th>
<td class="colLast">
<div class="block">
     Adds a predicate to determine whether previous outputs of this task can be reused.</div>
</td>
</tr>
</table>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="upToDateWhen-groovy.lang.Closure-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>upToDateWhen</h4>
<pre class="methodSignature">void&nbsp;upToDateWhen&#8203;(<a href="https://docs.groovy-lang.org/docs/groovy-3.0.17/html/gapi/groovy/lang/Closure.html?is-external=true" title="class or interface in groovy.lang" class="externalLink">Closure</a>&nbsp;upToDateClosure)</pre>
<div class="block"><p>
     Adds a predicate to determine whether previous outputs of this task can be reused.
     The given closure is executed at task execution time.
     The closure is passed the task as a parameter.
     If the closure returns false, previous outputs of this task cannot be reused and the task will be executed.
     That means the task is out-of-date and no outputs will be loaded from the build cache.
 </p>

 <p>
     You can add multiple such predicates.
     The task outputs cannot be reused when any predicate returns false.
 </p></div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>upToDateClosure</code> - The closure to use to determine whether the task outputs are up-to-date.</dd>
</dl>
</li>
</ul>
<a name="upToDateWhen-org.gradle.api.specs.Spec-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>upToDateWhen</h4>
<pre class="methodSignature">void&nbsp;upToDateWhen&#8203;(<a href="../specs/Spec.html" title="interface in org.gradle.api.specs">Spec</a>&lt;? super <a href="../Task.html" title="interface in org.gradle.api">Task</a>&gt;&nbsp;upToDateSpec)</pre>
<div class="block"><p>
     Adds a predicate to determine whether previous outputs of this task can be reused.
     The given spec is evaluated at task execution time.
     If the spec returns false, previous outputs of this task cannot be reused and the task will be executed.
     That means the task is out-of-date and no outputs will be loaded from the build cache.
 </p>

 <p>
     You can add multiple such predicates.
     The task outputs cannot be reused when any predicate returns false.
 </p></div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>upToDateSpec</code> - The spec to use to determine whether the task outputs are up-to-date.</dd>
</dl>
</li>
</ul>
<a name="cacheIf-org.gradle.api.specs.Spec-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>cacheIf</h4>
<pre class="methodSignature">void&nbsp;cacheIf&#8203;(<a href="../specs/Spec.html" title="interface in org.gradle.api.specs">Spec</a>&lt;? super <a href="../Task.html" title="interface in org.gradle.api">Task</a>&gt;&nbsp;spec)</pre>
<div class="block"><p>Cache the results of the task only if the given spec is satisfied. If the spec is not satisfied,
 the results of the task will not be cached.</p>

 <p>You may add multiple such predicates. The results of the task are not cached if any of the predicates return <code>false</code>,
 or if any of the predicates passed to <a href="#doNotCacheIf-java.lang.String-org.gradle.api.specs.Spec-"><code>doNotCacheIf(String, Spec)</code></a> returns <code>true</code>. If <code>cacheIf()</code> is not specified,
 the task will not be cached unless the @<a href="CacheableTask.html" title="annotation in org.gradle.api.tasks"><code>CacheableTask</code></a> annotation is present on the task type.</p>

 <p>Consider using <a href="#cacheIf-java.lang.String-org.gradle.api.specs.Spec-"><code>cacheIf(String, Spec)</code></a> instead for also providing a reason for enabling caching.</p></div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>spec</code> - specifies if the results of the task should be cached.</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>3.0</dd>
</dl>
</li>
</ul>
<a name="cacheIf-java.lang.String-org.gradle.api.specs.Spec-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>cacheIf</h4>
<pre class="methodSignature">void&nbsp;cacheIf&#8203;(java.lang.String&nbsp;cachingEnabledReason,
             <a href="../specs/Spec.html" title="interface in org.gradle.api.specs">Spec</a>&lt;? super <a href="../Task.html" title="interface in org.gradle.api">Task</a>&gt;&nbsp;spec)</pre>
<div class="block"><p>Cache the results of the task only if the given spec is satisfied. If the spec is not satisfied,
 the results of the task will not be cached.</p>

 <p>You may add multiple such predicates. The results of the task are not cached if any of the predicates return <code>false</code>,
 or if any of the predicates passed to <a href="#doNotCacheIf-java.lang.String-org.gradle.api.specs.Spec-"><code>doNotCacheIf(String, Spec)</code></a> returns <code>true</code>. If <code>cacheIf()</code> is not specified,
 the task will not be cached unless the @<a href="CacheableTask.html" title="annotation in org.gradle.api.tasks"><code>CacheableTask</code></a> annotation is present on the task type.</p></div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>cachingEnabledReason</code> - the reason why caching would be enabled by the spec.</dd>
<dd><code>spec</code> - specifies if the results of the task should be cached.</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>3.4</dd>
</dl>
</li>
</ul>
<a name="doNotCacheIf-java.lang.String-org.gradle.api.specs.Spec-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>doNotCacheIf</h4>
<pre class="methodSignature">void&nbsp;doNotCacheIf&#8203;(java.lang.String&nbsp;cachingDisabledReason,
                  <a href="../specs/Spec.html" title="interface in org.gradle.api.specs">Spec</a>&lt;? super <a href="../Task.html" title="interface in org.gradle.api">Task</a>&gt;&nbsp;spec)</pre>
<div class="block"><p>Disable caching the results of the task if the given spec is satisfied. The spec will be evaluated at task execution time, not
 during configuration.</p>

 <p>As opposed to <a href="#cacheIf-java.lang.String-org.gradle.api.specs.Spec-"><code>cacheIf(String, Spec)</code></a>, this method never enables caching for a task, it can only be used to disable caching.</p>

 <p>You may add multiple such predicates. The results of the task are not cached if any of the predicates return <code>true</code>,
 or if any of the predicates passed to <a href="#cacheIf-java.lang.String-org.gradle.api.specs.Spec-"><code>cacheIf(String, Spec)</code></a> returns <code>false</code>.</p></div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>cachingDisabledReason</code> - the reason why caching would be disabled by the spec.</dd>
<dd><code>spec</code> - specifies if the results of the task should not be cached.</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>3.4</dd>
</dl>
</li>
</ul>
<a name="getHasOutput--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getHasOutput</h4>
<pre class="methodSignature">boolean&nbsp;getHasOutput()</pre>
<div class="block">Returns true if this task has declared any outputs. Note that a task may be able to produce output files and
 still have an empty set of output files.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>true if this task has declared any outputs, otherwise false.</dd>
</dl>
</li>
</ul>
<a name="getFiles--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getFiles</h4>
<pre class="methodSignature"><a href="../file/FileCollection.html" title="interface in org.gradle.api.file">FileCollection</a>&nbsp;getFiles()</pre>
<div class="block">Returns the output files of this task.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The output files. Returns an empty collection if this task has no output files.</dd>
</dl>
</li>
</ul>
<a name="files-java.lang.Object...-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>files</h4>
<pre class="methodSignature"><a href="TaskOutputFilePropertyBuilder.html" title="interface in org.gradle.api.tasks">TaskOutputFilePropertyBuilder</a>&nbsp;files&#8203;(java.lang.Object...&nbsp;paths)</pre>
<div class="block">Registers some output files for this task.

 <p>When the given <code>paths</code> is a <code>Map</code>, then each output file
 will be associated with an identity.
 The keys of the map must be non-empty strings.
 The values of the map will be evaluated to individual files as per
 <a href="../Project.html#file-java.lang.Object-"><code>Project.file(Object)</code></a>.</p>

 <p>Otherwise the given files will be evaluated as per
 <a href="../Project.html#files-java.lang.Object...-"><code>Project.files(Object...)</code></a>.</p></div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>paths</code> - The output files.</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="CacheableTask.html" title="annotation in org.gradle.api.tasks"><code>CacheableTask</code></a></dd>
</dl>
</li>
</ul>
<a name="dirs-java.lang.Object...-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>dirs</h4>
<pre class="methodSignature"><a href="TaskOutputFilePropertyBuilder.html" title="interface in org.gradle.api.tasks">TaskOutputFilePropertyBuilder</a>&nbsp;dirs&#8203;(java.lang.Object...&nbsp;paths)</pre>
<div class="block">Registers some output directories for this task.

 <p>When the given <code>paths</code> is a <code>Map</code>, then each output directory
 will be associated with an identity.
 The keys of the map must be non-empty strings.
 The values of the map will be evaluated to individual directories as per
 <a href="../Project.html#file-java.lang.Object-"><code>Project.file(Object)</code></a>.</p>

 <p>Otherwise the given directories will be evaluated as per
 <a href="../Project.html#files-java.lang.Object...-"><code>Project.files(Object...)</code></a>.</p></div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>paths</code> - The output files.</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>3.3</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="CacheableTask.html" title="annotation in org.gradle.api.tasks"><code>CacheableTask</code></a></dd>
</dl>
</li>
</ul>
<a name="file-java.lang.Object-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>file</h4>
<pre class="methodSignature"><a href="TaskOutputFilePropertyBuilder.html" title="interface in org.gradle.api.tasks">TaskOutputFilePropertyBuilder</a>&nbsp;file&#8203;(java.lang.Object&nbsp;path)</pre>
<div class="block">Registers some output file for this task.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>path</code> - The output file. The given path is evaluated as per <a href="../Project.html#file-java.lang.Object-"><code>Project.file(Object)</code></a>.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>a property builder to further configure this property.</dd>
</dl>
</li>
</ul>
<a name="dir-java.lang.Object-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>dir</h4>
<pre class="methodSignature"><a href="TaskOutputFilePropertyBuilder.html" title="interface in org.gradle.api.tasks">TaskOutputFilePropertyBuilder</a>&nbsp;dir&#8203;(java.lang.Object&nbsp;path)</pre>
<div class="block">Registers an output directory for this task.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>path</code> - The output directory. The given path is evaluated as per <a href="../Project.html#file-java.lang.Object-"><code>Project.file(Object)</code></a>.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>a property builder to further configure this property.</dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
