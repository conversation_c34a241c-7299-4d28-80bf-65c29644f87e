<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc -->
<title>ConfigurableFileCollection (Gradle API 8.4)</title>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<link rel="stylesheet" type="text/css" href="../../../../javadoc.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../jquery/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
<script type="text/javascript" src="../../../../jquery/jszip/dist/jszip.min.js"></script>
<script type="text/javascript" src="../../../../jquery/jszip-utils/dist/jszip-utils.min.js"></script>
<!--[if IE]>
<script type="text/javascript" src="../../../../jquery/jszip-utils/dist/jszip-utils-ie.min.js"></script>
<![endif]-->
<script type="text/javascript" src="../../../../jquery/jquery-3.6.0.min.js"></script>
<script type="text/javascript" src="../../../../jquery/jquery-ui.min.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="ConfigurableFileCollection (Gradle API 8.4)";
        }
    }
    catch(err) {
    }
//-->
var data = {"i0":6,"i1":6,"i2":6,"i3":6,"i4":6,"i5":6,"i6":6};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],4:["t3","Abstract Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
var pathtoroot = "../../../../";
var useModuleDirectories = false;
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="fixedNav">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses.html">All&nbsp;Classes</a></li>
</ul>
<ul class="navListSearch">
<li><label for="search">SEARCH:</label>
<input type="text" id="search" value="search" disabled="disabled">
<input type="reset" id="reset" value="reset" disabled="disabled">
</li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li><a href="#nested.class.summary">Nested</a>&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
</div>
<div class="navPadding">&nbsp;</div>
<script type="text/javascript"><!--
$('.navPadding').css('padding-top', $('.fixedNav').css("height"));
//-->
</script>
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle"><span class="packageLabelInType">Package</span>&nbsp;<a href="package-summary.html">org.gradle.api.file</a></div>
<h2 title="Interface ConfigurableFileCollection" class="title">Interface ConfigurableFileCollection</h2>
</div>
<div class="contentContainer">
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Superinterfaces:</dt>
<dd><code><a href="../tasks/AntBuilderAware.html" title="interface in org.gradle.api.tasks">AntBuilderAware</a></code>, <code><a href="../Buildable.html" title="interface in org.gradle.api">Buildable</a></code>, <code><a href="FileCollection.html" title="interface in org.gradle.api.file">FileCollection</a></code>, <code><a href="../provider/HasConfigurableValue.html" title="interface in org.gradle.api.provider">HasConfigurableValue</a></code>, <code>java.lang.Iterable&lt;java.io.File&gt;</code></dd>
</dl>
<hr>
<pre><a href="../SupportsKotlinAssignmentOverloading.html" title="annotation in org.gradle.api">@SupportsKotlinAssignmentOverloading</a>
public interface <span class="typeNameLabel">ConfigurableFileCollection</span>
extends <a href="FileCollection.html" title="interface in org.gradle.api.file">FileCollection</a>, <a href="../provider/HasConfigurableValue.html" title="interface in org.gradle.api.provider">HasConfigurableValue</a></pre>
<div class="block"><p>A <code>ConfigurableFileCollection</code> is a mutable <code>FileCollection</code>.</p>

 <p>You can obtain an instance of <code>ConfigurableFileCollection</code> by calling <a href="../Project.html#files-java.lang.Object...-"><code>Project.files(Object...)</code></a> or <a href="../model/ObjectFactory.html#fileCollection--"><code>ObjectFactory.fileCollection()</code></a>.</p>

 <p><b>Note:</b> This interface is not intended for implementation by build script or plugin authors.</p></div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== NESTED CLASS SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="nested.class.summary">
<!--   -->
</a>
<h3>Nested Class Summary</h3>
<ul class="blockList">
<li class="blockList"><a name="nested.classes.inherited.from.class.org.gradle.api.file.FileCollection">
<!--   -->
</a>
<h3>Nested classes/interfaces inherited from interface&nbsp;org.gradle.api.file.<a href="FileCollection.html" title="interface in org.gradle.api.file">FileCollection</a></h3>
<code><a href="FileCollection.AntType.html" title="enum in org.gradle.api.file">FileCollection.AntType</a></code></li>
</ul>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t3" class="tableTab"><span><a href="javascript:show(4);">Abstract Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colSecond" scope="col">Method</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code><a href="ConfigurableFileCollection.html" title="interface in org.gradle.api.file">ConfigurableFileCollection</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#builtBy-java.lang.Object...-">builtBy</a></span>&#8203;(java.lang.Object...&nbsp;tasks)</code></th>
<td class="colLast">
<div class="block">Registers some tasks which build the files of this collection.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code><a href="ConfigurableFileCollection.html" title="interface in org.gradle.api.file">ConfigurableFileCollection</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#from-java.lang.Object...-">from</a></span>&#8203;(java.lang.Object...&nbsp;paths)</code></th>
<td class="colLast">
<div class="block">Adds a set of source paths to this collection.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>java.util.Set&lt;java.lang.Object&gt;</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getBuiltBy--">getBuiltBy</a></span>()</code></th>
<td class="colLast">
<div class="block">Returns the set of tasks which build the files of this collection.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>java.util.Set&lt;java.lang.Object&gt;</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getFrom--">getFrom</a></span>()</code></th>
<td class="colLast">
<div class="block">Returns the set of source paths for this collection.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code><a href="ConfigurableFileCollection.html" title="interface in org.gradle.api.file">ConfigurableFileCollection</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setBuiltBy-java.lang.Iterable-">setBuiltBy</a></span>&#8203;(java.lang.Iterable&lt;?&gt;&nbsp;tasks)</code></th>
<td class="colLast">
<div class="block">Sets the tasks which build the files of this collection.</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setFrom-java.lang.Iterable-">setFrom</a></span>&#8203;(java.lang.Iterable&lt;?&gt;&nbsp;paths)</code></th>
<td class="colLast">
<div class="block">Sets the source paths for this collection.</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setFrom-java.lang.Object...-">setFrom</a></span>&#8203;(java.lang.Object...&nbsp;paths)</code></th>
<td class="colLast">
<div class="block">Sets the source paths for this collection.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.org.gradle.api.Buildable">
<!--   -->
</a>
<h3>Methods inherited from interface&nbsp;org.gradle.api.<a href="../Buildable.html" title="interface in org.gradle.api">Buildable</a></h3>
<code><a href="../Buildable.html#getBuildDependencies--">getBuildDependencies</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.org.gradle.api.file.FileCollection">
<!--   -->
</a>
<h3>Methods inherited from interface&nbsp;org.gradle.api.file.<a href="FileCollection.html" title="interface in org.gradle.api.file">FileCollection</a></h3>
<code><a href="FileCollection.html#addToAntBuilder-java.lang.Object-java.lang.String-">addToAntBuilder</a>, <a href="FileCollection.html#addToAntBuilder-java.lang.Object-java.lang.String-org.gradle.api.file.FileCollection.AntType-">addToAntBuilder</a>, <a href="FileCollection.html#contains-java.io.File-">contains</a>, <a href="FileCollection.html#filter-groovy.lang.Closure-">filter</a>, <a href="FileCollection.html#filter-org.gradle.api.specs.Spec-">filter</a>, <a href="FileCollection.html#getAsFileTree--">getAsFileTree</a>, <a href="FileCollection.html#getAsPath--">getAsPath</a>, <a href="FileCollection.html#getElements--">getElements</a>, <a href="FileCollection.html#getFiles--">getFiles</a>, <a href="FileCollection.html#getSingleFile--">getSingleFile</a>, <a href="FileCollection.html#isEmpty--">isEmpty</a>, <a href="FileCollection.html#minus-org.gradle.api.file.FileCollection-">minus</a>, <a href="FileCollection.html#plus-org.gradle.api.file.FileCollection-">plus</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.org.gradle.api.provider.HasConfigurableValue">
<!--   -->
</a>
<h3>Methods inherited from interface&nbsp;org.gradle.api.provider.<a href="../provider/HasConfigurableValue.html" title="interface in org.gradle.api.provider">HasConfigurableValue</a></h3>
<code><a href="../provider/HasConfigurableValue.html#disallowChanges--">disallowChanges</a>, <a href="../provider/HasConfigurableValue.html#disallowUnsafeRead--">disallowUnsafeRead</a>, <a href="../provider/HasConfigurableValue.html#finalizeValue--">finalizeValue</a>, <a href="../provider/HasConfigurableValue.html#finalizeValueOnRead--">finalizeValueOnRead</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Iterable">
<!--   -->
</a>
<h3>Methods inherited from interface&nbsp;java.lang.Iterable</h3>
<code>forEach, iterator, spliterator</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="getFrom--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getFrom</h4>
<pre class="methodSignature">java.util.Set&lt;java.lang.Object&gt;&nbsp;getFrom()</pre>
<div class="block">Returns the set of source paths for this collection. The paths are evaluated as per <a href="../Project.html#files-java.lang.Object...-"><code>Project.files(Object...)</code></a>.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The set of source paths. Returns an empty set if none.</dd>
</dl>
</li>
</ul>
<a name="setFrom-java.lang.Iterable-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setFrom</h4>
<pre class="methodSignature">void&nbsp;setFrom&#8203;(java.lang.Iterable&lt;?&gt;&nbsp;paths)</pre>
<div class="block">Sets the source paths for this collection. The given paths are evaluated as per <a href="../Project.html#files-java.lang.Object...-"><code>Project.files(Object...)</code></a>.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>paths</code> - The paths.</dd>
</dl>
</li>
</ul>
<a name="setFrom-java.lang.Object...-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setFrom</h4>
<pre class="methodSignature">void&nbsp;setFrom&#8203;(java.lang.Object...&nbsp;paths)</pre>
<div class="block">Sets the source paths for this collection. The given paths are evaluated as per <a href="../Project.html#files-java.lang.Object...-"><code>Project.files(Object...)</code></a>.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>paths</code> - The paths.</dd>
</dl>
</li>
</ul>
<a name="from-java.lang.Object...-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>from</h4>
<pre class="methodSignature"><a href="ConfigurableFileCollection.html" title="interface in org.gradle.api.file">ConfigurableFileCollection</a>&nbsp;from&#8203;(java.lang.Object...&nbsp;paths)</pre>
<div class="block">Adds a set of source paths to this collection. The given paths are evaluated as per <a href="../Project.html#files-java.lang.Object...-"><code>Project.files(Object...)</code></a>.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>paths</code> - The files to add.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>this</dd>
</dl>
</li>
</ul>
<a name="getBuiltBy--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getBuiltBy</h4>
<pre class="methodSignature">java.util.Set&lt;java.lang.Object&gt;&nbsp;getBuiltBy()</pre>
<div class="block">Returns the set of tasks which build the files of this collection.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The set. Returns an empty set when there are no such tasks.</dd>
</dl>
</li>
</ul>
<a name="setBuiltBy-java.lang.Iterable-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setBuiltBy</h4>
<pre class="methodSignature"><a href="ConfigurableFileCollection.html" title="interface in org.gradle.api.file">ConfigurableFileCollection</a>&nbsp;setBuiltBy&#8203;(java.lang.Iterable&lt;?&gt;&nbsp;tasks)</pre>
<div class="block">Sets the tasks which build the files of this collection.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>tasks</code> - The tasks. These are evaluated as per <a href="../Task.html#dependsOn-java.lang.Object...-"><code>Task.dependsOn(Object...)</code></a>.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>this</dd>
</dl>
</li>
</ul>
<a name="builtBy-java.lang.Object...-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>builtBy</h4>
<pre class="methodSignature"><a href="ConfigurableFileCollection.html" title="interface in org.gradle.api.file">ConfigurableFileCollection</a>&nbsp;builtBy&#8203;(java.lang.Object...&nbsp;tasks)</pre>
<div class="block">Registers some tasks which build the files of this collection.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>tasks</code> - The tasks. These are evaluated as per <a href="../Task.html#dependsOn-java.lang.Object...-"><code>Task.dependsOn(Object...)</code></a>.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>this</dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li><a href="#nested.class.summary">Nested</a>&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
