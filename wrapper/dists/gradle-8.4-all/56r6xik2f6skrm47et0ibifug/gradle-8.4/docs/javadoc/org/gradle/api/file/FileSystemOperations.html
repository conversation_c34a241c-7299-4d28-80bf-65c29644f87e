<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc -->
<title>FileSystemOperations (Gradle API 8.4)</title>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<link rel="stylesheet" type="text/css" href="../../../../javadoc.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../jquery/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
<script type="text/javascript" src="../../../../jquery/jszip/dist/jszip.min.js"></script>
<script type="text/javascript" src="../../../../jquery/jszip-utils/dist/jszip-utils.min.js"></script>
<!--[if IE]>
<script type="text/javascript" src="../../../../jquery/jszip-utils/dist/jszip-utils-ie.min.js"></script>
<![endif]-->
<script type="text/javascript" src="../../../../jquery/jquery-3.6.0.min.js"></script>
<script type="text/javascript" src="../../../../jquery/jquery-ui.min.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="FileSystemOperations (Gradle API 8.4)";
        }
    }
    catch(err) {
    }
//-->
var data = {"i0":6,"i1":6,"i2":6,"i3":6,"i4":6,"i5":6,"i6":6,"i7":6};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],4:["t3","Abstract Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
var pathtoroot = "../../../../";
var useModuleDirectories = false;
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="fixedNav">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses.html">All&nbsp;Classes</a></li>
</ul>
<ul class="navListSearch">
<li><label for="search">SEARCH:</label>
<input type="text" id="search" value="search" disabled="disabled">
<input type="reset" id="reset" value="reset" disabled="disabled">
</li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
</div>
<div class="navPadding">&nbsp;</div>
<script type="text/javascript"><!--
$('.navPadding').css('padding-top', $('.fixedNav').css("height"));
//-->
</script>
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle"><span class="packageLabelInType">Package</span>&nbsp;<a href="package-summary.html">org.gradle.api.file</a></div>
<h2 title="Interface FileSystemOperations" class="title">Interface FileSystemOperations</h2>
</div>
<div class="contentContainer">
<div class="description">
<ul class="blockList">
<li class="blockList">
<hr>
<pre>public interface <span class="typeNameLabel">FileSystemOperations</span></pre>
<div class="block">Operations on the file system.

 <p>An instance of this type can be injected into a task, plugin or other object by annotating a public constructor or property getter method with <code>javax.inject.Inject</code>.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>6.0</dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t3" class="tableTab"><span><a href="javascript:show(4);">Abstract Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colSecond" scope="col">Method</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code><a href="../tasks/WorkResult.html" title="interface in org.gradle.api.tasks">WorkResult</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#copy-org.gradle.api.Action-">copy</a></span>&#8203;(<a href="../Action.html" title="interface in org.gradle.api">Action</a>&lt;? super <a href="CopySpec.html" title="interface in org.gradle.api.file">CopySpec</a>&gt;&nbsp;action)</code></th>
<td class="colLast">
<div class="block">Copies the specified files.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code><a href="../tasks/WorkResult.html" title="interface in org.gradle.api.tasks">WorkResult</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#delete-org.gradle.api.Action-">delete</a></span>&#8203;(<a href="../Action.html" title="interface in org.gradle.api">Action</a>&lt;? super <a href="DeleteSpec.html" title="interface in org.gradle.api.file">DeleteSpec</a>&gt;&nbsp;action)</code></th>
<td class="colLast">
<div class="block">Deletes the specified files.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code><a href="ConfigurableFilePermissions.html" title="interface in org.gradle.api.file">ConfigurableFilePermissions</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#directoryPermissions-org.gradle.api.Action-">directoryPermissions</a></span>&#8203;(<a href="../Action.html" title="interface in org.gradle.api">Action</a>&lt;? super <a href="ConfigurableFilePermissions.html" title="interface in org.gradle.api.file">ConfigurableFilePermissions</a>&gt;&nbsp;configureAction)</code></th>
<td class="colLast">
<div class="block">Creates and configures directory access permissions.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code><a href="ConfigurableFilePermissions.html" title="interface in org.gradle.api.file">ConfigurableFilePermissions</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#filePermissions-org.gradle.api.Action-">filePermissions</a></span>&#8203;(<a href="../Action.html" title="interface in org.gradle.api">Action</a>&lt;? super <a href="ConfigurableFilePermissions.html" title="interface in org.gradle.api.file">ConfigurableFilePermissions</a>&gt;&nbsp;configureAction)</code></th>
<td class="colLast">
<div class="block">Creates and configures file access permissions.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code><a href="ConfigurableFilePermissions.html" title="interface in org.gradle.api.file">ConfigurableFilePermissions</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#permissions-int-">permissions</a></span>&#8203;(int&nbsp;unixNumeric)</code></th>
<td class="colLast">
<div class="block">Creates file/directory access permissions and initializes them via a Unix style numeric permissions.</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code><a href="ConfigurableFilePermissions.html" title="interface in org.gradle.api.file">ConfigurableFilePermissions</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#permissions-java.lang.String-">permissions</a></span>&#8203;(java.lang.String&nbsp;unixNumericOrSymbolic)</code></th>
<td class="colLast">
<div class="block">Creates file/directory access permissions and initializes them via a Unix style permission string.</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code><a href="../provider/Provider.html" title="interface in org.gradle.api.provider">Provider</a>&lt;<a href="ConfigurableFilePermissions.html" title="interface in org.gradle.api.file">ConfigurableFilePermissions</a>&gt;</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#permissions-org.gradle.api.provider.Provider-">permissions</a></span>&#8203;(<a href="../provider/Provider.html" title="interface in org.gradle.api.provider">Provider</a>&lt;java.lang.String&gt;&nbsp;permissions)</code></th>
<td class="colLast">
<div class="block"><a href="../provider/Provider.html" title="interface in org.gradle.api.provider"><code>Provider</code></a> based version of <a href="#permissions-java.lang.String-"><code>permissions(String)</code></a>,  to facilitate wiring into property chains.</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code><a href="../tasks/WorkResult.html" title="interface in org.gradle.api.tasks">WorkResult</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#sync-org.gradle.api.Action-">sync</a></span>&#8203;(<a href="../Action.html" title="interface in org.gradle.api">Action</a>&lt;? super <a href="CopySpec.html" title="interface in org.gradle.api.file">CopySpec</a>&gt;&nbsp;action)</code></th>
<td class="colLast">
<div class="block">Synchronizes the contents of a destination directory with some source directories and files.</div>
</td>
</tr>
</table>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="copy-org.gradle.api.Action-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>copy</h4>
<pre class="methodSignature"><a href="../tasks/WorkResult.html" title="interface in org.gradle.api.tasks">WorkResult</a>&nbsp;copy&#8203;(<a href="../Action.html" title="interface in org.gradle.api">Action</a>&lt;? super <a href="CopySpec.html" title="interface in org.gradle.api.file">CopySpec</a>&gt;&nbsp;action)</pre>
<div class="block">Copies the specified files.
 The given action is used to configure a <a href="CopySpec.html" title="interface in org.gradle.api.file"><code>CopySpec</code></a>, which is then used to copy the files.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>action</code> - Action to configure the CopySpec</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd><a href="../tasks/WorkResult.html" title="interface in org.gradle.api.tasks"><code>WorkResult</code></a> that can be used to check if the copy did any work.</dd>
</dl>
</li>
</ul>
<a name="sync-org.gradle.api.Action-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>sync</h4>
<pre class="methodSignature"><a href="../tasks/WorkResult.html" title="interface in org.gradle.api.tasks">WorkResult</a>&nbsp;sync&#8203;(<a href="../Action.html" title="interface in org.gradle.api">Action</a>&lt;? super <a href="CopySpec.html" title="interface in org.gradle.api.file">CopySpec</a>&gt;&nbsp;action)</pre>
<div class="block">Synchronizes the contents of a destination directory with some source directories and files.
 The given action is used to configure a <a href="CopySpec.html" title="interface in org.gradle.api.file"><code>CopySpec</code></a>, which is then used to synchronize the files.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>action</code> - action Action to configure the CopySpec.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd><a href="../tasks/WorkResult.html" title="interface in org.gradle.api.tasks"><code>WorkResult</code></a> that can be used to check if the sync did any work.</dd>
</dl>
</li>
</ul>
<a name="delete-org.gradle.api.Action-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>delete</h4>
<pre class="methodSignature"><a href="../tasks/WorkResult.html" title="interface in org.gradle.api.tasks">WorkResult</a>&nbsp;delete&#8203;(<a href="../Action.html" title="interface in org.gradle.api">Action</a>&lt;? super <a href="DeleteSpec.html" title="interface in org.gradle.api.file">DeleteSpec</a>&gt;&nbsp;action)</pre>
<div class="block">Deletes the specified files.
 The given action is used to configure a <a href="DeleteSpec.html" title="interface in org.gradle.api.file"><code>DeleteSpec</code></a>, which is then used to delete the files.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>action</code> - Action to configure the DeleteSpec</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd><a href="../tasks/WorkResult.html" title="interface in org.gradle.api.tasks"><code>WorkResult</code></a> that can be used to check if delete did any work.</dd>
</dl>
</li>
</ul>
<a name="filePermissions-org.gradle.api.Action-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>filePermissions</h4>
<pre class="methodSignature"><a href="../Incubating.html" title="annotation in org.gradle.api">@Incubating</a>
<a href="ConfigurableFilePermissions.html" title="interface in org.gradle.api.file">ConfigurableFilePermissions</a>&nbsp;filePermissions&#8203;(<a href="../Action.html" title="interface in org.gradle.api">Action</a>&lt;? super <a href="ConfigurableFilePermissions.html" title="interface in org.gradle.api.file">ConfigurableFilePermissions</a>&gt;&nbsp;configureAction)</pre>
<div class="block">Creates and configures file access permissions. Differs from directory permissions due to
 the default value the permissions start out with before the configuration is applied.
 For details see <a href="ConfigurableFilePermissions.html" title="interface in org.gradle.api.file"><code>ConfigurableFilePermissions</code></a>.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>configureAction</code> - The configuration that gets applied to the newly created <code>FilePermissions</code>.</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>8.3</dd>
</dl>
</li>
</ul>
<a name="directoryPermissions-org.gradle.api.Action-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>directoryPermissions</h4>
<pre class="methodSignature"><a href="../Incubating.html" title="annotation in org.gradle.api">@Incubating</a>
<a href="ConfigurableFilePermissions.html" title="interface in org.gradle.api.file">ConfigurableFilePermissions</a>&nbsp;directoryPermissions&#8203;(<a href="../Action.html" title="interface in org.gradle.api">Action</a>&lt;? super <a href="ConfigurableFilePermissions.html" title="interface in org.gradle.api.file">ConfigurableFilePermissions</a>&gt;&nbsp;configureAction)</pre>
<div class="block">Creates and configures directory access permissions. Differs from file permissions due to
 the default value the permissions start out with before the configuration is applied.
 For details see <a href="ConfigurableFilePermissions.html" title="interface in org.gradle.api.file"><code>ConfigurableFilePermissions</code></a>.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>configureAction</code> - The configuration that gets applied to the newly created <code>FilePermissions</code>.</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>8.3</dd>
</dl>
</li>
</ul>
<a name="permissions-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>permissions</h4>
<pre class="methodSignature"><a href="../Incubating.html" title="annotation in org.gradle.api">@Incubating</a>
<a href="ConfigurableFilePermissions.html" title="interface in org.gradle.api.file">ConfigurableFilePermissions</a>&nbsp;permissions&#8203;(java.lang.String&nbsp;unixNumericOrSymbolic)</pre>
<div class="block">Creates file/directory access permissions and initializes them via a Unix style permission string.
 For details see <a href="ConfigurableFilePermissions.html#unix-java.lang.String-"><code>ConfigurableFilePermissions.unix(String)</code></a>.
 <p>
 Doesn't have separate variants for files and directories, like other configuration methods,
 because the Unix style permission input completely overwrites the default values, so
 the distinction doesn't matter.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>8.3</dd>
</dl>
</li>
</ul>
<a name="permissions-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>permissions</h4>
<pre class="methodSignature"><a href="../Incubating.html" title="annotation in org.gradle.api">@Incubating</a>
<a href="ConfigurableFilePermissions.html" title="interface in org.gradle.api.file">ConfigurableFilePermissions</a>&nbsp;permissions&#8203;(int&nbsp;unixNumeric)</pre>
<div class="block">Creates file/directory access permissions and initializes them via a Unix style numeric permissions.
 For details see <a href="ConfigurableFilePermissions.html#unix-int-"><code>ConfigurableFilePermissions.unix(int)</code></a>.
 <p>
 Doesn't have separate variants for files and directories, like other configuration methods,
 because the Unix style permission input completely overwrites the default values, so
 the distinction doesn't matter.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>8.3</dd>
</dl>
</li>
</ul>
<a name="permissions-org.gradle.api.provider.Provider-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>permissions</h4>
<pre class="methodSignature"><a href="../Incubating.html" title="annotation in org.gradle.api">@Incubating</a>
<a href="../provider/Provider.html" title="interface in org.gradle.api.provider">Provider</a>&lt;<a href="ConfigurableFilePermissions.html" title="interface in org.gradle.api.file">ConfigurableFilePermissions</a>&gt;&nbsp;permissions&#8203;(<a href="../provider/Provider.html" title="interface in org.gradle.api.provider">Provider</a>&lt;java.lang.String&gt;&nbsp;permissions)</pre>
<div class="block"><a href="../provider/Provider.html" title="interface in org.gradle.api.provider"><code>Provider</code></a> based version of <a href="#permissions-java.lang.String-"><code>permissions(String)</code></a>,  to facilitate wiring into property chains.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>8.3</dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
