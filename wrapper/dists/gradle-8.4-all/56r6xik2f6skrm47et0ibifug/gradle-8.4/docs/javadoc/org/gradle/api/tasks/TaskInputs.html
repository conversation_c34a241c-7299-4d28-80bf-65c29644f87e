<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc -->
<title>TaskInputs (Gradle API 8.4)</title>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<link rel="stylesheet" type="text/css" href="../../../../javadoc.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../jquery/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
<script type="text/javascript" src="../../../../jquery/jszip/dist/jszip.min.js"></script>
<script type="text/javascript" src="../../../../jquery/jszip-utils/dist/jszip-utils.min.js"></script>
<!--[if IE]>
<script type="text/javascript" src="../../../../jquery/jszip-utils/dist/jszip-utils-ie.min.js"></script>
<![endif]-->
<script type="text/javascript" src="../../../../jquery/jquery-3.6.0.min.js"></script>
<script type="text/javascript" src="../../../../jquery/jquery-ui.min.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="TaskInputs (Gradle API 8.4)";
        }
    }
    catch(err) {
    }
//-->
var data = {"i0":6,"i1":6,"i2":6,"i3":6,"i4":6,"i5":6,"i6":6,"i7":6,"i8":6,"i9":6};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],4:["t3","Abstract Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
var pathtoroot = "../../../../";
var useModuleDirectories = false;
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="fixedNav">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses.html">All&nbsp;Classes</a></li>
</ul>
<ul class="navListSearch">
<li><label for="search">SEARCH:</label>
<input type="text" id="search" value="search" disabled="disabled">
<input type="reset" id="reset" value="reset" disabled="disabled">
</li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
</div>
<div class="navPadding">&nbsp;</div>
<script type="text/javascript"><!--
$('.navPadding').css('padding-top', $('.fixedNav').css("height"));
//-->
</script>
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle"><span class="packageLabelInType">Package</span>&nbsp;<a href="package-summary.html">org.gradle.api.tasks</a></div>
<h2 title="Interface TaskInputs" class="title">Interface TaskInputs</h2>
</div>
<div class="contentContainer">
<div class="description">
<ul class="blockList">
<li class="blockList">
<hr>
<pre>public interface <span class="typeNameLabel">TaskInputs</span></pre>
<div class="block"><p>A <code>TaskInputs</code> represents the inputs for a task.</p>

 <p>You can obtain a <code>TaskInputs</code> instance using <a href="../Task.html#getInputs--"><code>Task.getInputs()</code></a>.</p></div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t3" class="tableTab"><span><a href="javascript:show(4);">Abstract Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colSecond" scope="col">Method</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code><a href="TaskInputFilePropertyBuilder.html" title="interface in org.gradle.api.tasks">TaskInputFilePropertyBuilder</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#dir-java.lang.Object-">dir</a></span>&#8203;(java.lang.Object&nbsp;dirPath)</code></th>
<td class="colLast">
<div class="block">Registers an input directory hierarchy.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code><a href="TaskInputFilePropertyBuilder.html" title="interface in org.gradle.api.tasks">TaskInputFilePropertyBuilder</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#file-java.lang.Object-">file</a></span>&#8203;(java.lang.Object&nbsp;path)</code></th>
<td class="colLast">
<div class="block">Registers some input file for this task.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code><a href="TaskInputFilePropertyBuilder.html" title="interface in org.gradle.api.tasks">TaskInputFilePropertyBuilder</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#files-java.lang.Object...-">files</a></span>&#8203;(java.lang.Object...&nbsp;paths)</code></th>
<td class="colLast">
<div class="block">Registers some input files for this task.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code><a href="../file/FileCollection.html" title="interface in org.gradle.api.file">FileCollection</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getFiles--">getFiles</a></span>()</code></th>
<td class="colLast">
<div class="block">Returns the input files of this task.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getHasInputs--">getHasInputs</a></span>()</code></th>
<td class="colLast">
<div class="block">Returns true if this task has declared the inputs that it consumes.</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getHasSourceFiles--">getHasSourceFiles</a></span>()</code></th>
<td class="colLast">
<div class="block">Returns true if this task has declared that it accepts source files.</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>java.util.Map&lt;java.lang.String,&#8203;java.lang.Object&gt;</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getProperties--">getProperties</a></span>()</code></th>
<td class="colLast">
<div class="block">Returns a map of input properties for this task.</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code><a href="../file/FileCollection.html" title="interface in org.gradle.api.file">FileCollection</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getSourceFiles--">getSourceFiles</a></span>()</code></th>
<td class="colLast">
<div class="block">Returns the set of source files for this task.</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code><a href="TaskInputs.html" title="interface in org.gradle.api.tasks">TaskInputs</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#properties-java.util.Map-">properties</a></span>&#8203;(java.util.Map&lt;java.lang.String,&#8203;?&gt;&nbsp;properties)</code></th>
<td class="colLast">
<div class="block">Registers a set of input properties for this task.</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code><a href="TaskInputPropertyBuilder.html" title="interface in org.gradle.api.tasks">TaskInputPropertyBuilder</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#property-java.lang.String-java.lang.Object-">property</a></span>&#8203;(java.lang.String&nbsp;name,
        java.lang.Object&nbsp;value)</code></th>
<td class="colLast">
<div class="block">Registers an input property for this task.</div>
</td>
</tr>
</table>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="getHasInputs--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getHasInputs</h4>
<pre class="methodSignature">boolean&nbsp;getHasInputs()</pre>
<div class="block">Returns true if this task has declared the inputs that it consumes.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>true if this task has declared any inputs.</dd>
</dl>
</li>
</ul>
<a name="getFiles--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getFiles</h4>
<pre class="methodSignature"><a href="../file/FileCollection.html" title="interface in org.gradle.api.file">FileCollection</a>&nbsp;getFiles()</pre>
<div class="block">Returns the input files of this task.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The input files. Returns an empty collection if this task has no input files.</dd>
</dl>
</li>
</ul>
<a name="files-java.lang.Object...-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>files</h4>
<pre class="methodSignature"><a href="TaskInputFilePropertyBuilder.html" title="interface in org.gradle.api.tasks">TaskInputFilePropertyBuilder</a>&nbsp;files&#8203;(java.lang.Object...&nbsp;paths)</pre>
<div class="block">Registers some input files for this task.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>paths</code> - The input files. The given paths are evaluated as per <a href="../Project.html#files-java.lang.Object...-"><code>Project.files(Object...)</code></a>.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>a property builder to further configure the property.</dd>
</dl>
</li>
</ul>
<a name="file-java.lang.Object-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>file</h4>
<pre class="methodSignature"><a href="TaskInputFilePropertyBuilder.html" title="interface in org.gradle.api.tasks">TaskInputFilePropertyBuilder</a>&nbsp;file&#8203;(java.lang.Object&nbsp;path)</pre>
<div class="block">Registers some input file for this task.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>path</code> - The input file. The given path is evaluated as per <a href="../Project.html#file-java.lang.Object-"><code>Project.file(Object)</code></a>.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>a property builder to further configure the property.</dd>
</dl>
</li>
</ul>
<a name="dir-java.lang.Object-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>dir</h4>
<pre class="methodSignature"><a href="TaskInputFilePropertyBuilder.html" title="interface in org.gradle.api.tasks">TaskInputFilePropertyBuilder</a>&nbsp;dir&#8203;(java.lang.Object&nbsp;dirPath)</pre>
<div class="block">Registers an input directory hierarchy. All files found under the given directory are treated as input files for
 this task.

 <p>An input directory hierarchy ignores empty directories by default. See <a href="TaskInputFilePropertyBuilder.html#ignoreEmptyDirectories--"><code>TaskInputFilePropertyBuilder.ignoreEmptyDirectories()</code></a>.</p></div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>dirPath</code> - The directory. The path is evaluated as per <a href="../Project.html#file-java.lang.Object-"><code>Project.file(Object)</code></a>.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>a property builder to further configure the property.</dd>
</dl>
</li>
</ul>
<a name="getProperties--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getProperties</h4>
<pre class="methodSignature">java.util.Map&lt;java.lang.String,&#8203;java.lang.Object&gt;&nbsp;getProperties()</pre>
<div class="block">Returns a map of input properties for this task.

 The returned map is unmodifiable, and does not reflect further changes to the task's properties.
 Trying to modify the map will result in an <code>UnsupportedOperationException</code> being thrown.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The properties.</dd>
</dl>
</li>
</ul>
<a name="property-java.lang.String-java.lang.Object-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>property</h4>
<pre class="methodSignature"><a href="TaskInputPropertyBuilder.html" title="interface in org.gradle.api.tasks">TaskInputPropertyBuilder</a>&nbsp;property&#8203;(java.lang.String&nbsp;name,
                                  @Nullable
                                  java.lang.Object&nbsp;value)</pre>
<div class="block"><p>Registers an input property for this task. This value is persisted when the task executes, and is compared
 against the property value for later invocations of the task, to determine if the task is up-to-date.</p>

 <p>The given value must be a simple value, like a String or Integer, or serializable. For complex values,
  Gradle compares the serialized forms for detecting changes and the <code>equals()</code> method is ignored.

 <p>If the value is not known when registering the input, a <a href="../provider/Provider.html" title="interface in org.gradle.api.provider"><code>Provider</code></a> can be
 passed instead. Gradle will then resolve the provider at the latest possible time in order to determine the actual
 property value.</p></div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>name</code> - The name of the property. Must not be null.</dd>
<dd><code>value</code> - The value for the property. Can be null.</dd>
</dl>
</li>
</ul>
<a name="properties-java.util.Map-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>properties</h4>
<pre class="methodSignature"><a href="TaskInputs.html" title="interface in org.gradle.api.tasks">TaskInputs</a>&nbsp;properties&#8203;(java.util.Map&lt;java.lang.String,&#8203;?&gt;&nbsp;properties)</pre>
<div class="block">Registers a set of input properties for this task. See <a href="#property-java.lang.String-java.lang.Object-"><code>property(String, Object)</code></a> for details.

 <p><strong>Note:</strong> do not use the return value to chain calls.
 Instead always use call via <a href="../Task.html#getInputs--"><code>Task.getInputs()</code></a>.</p></div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>properties</code> - The properties.</dd>
</dl>
</li>
</ul>
<a name="getHasSourceFiles--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getHasSourceFiles</h4>
<pre class="methodSignature">boolean&nbsp;getHasSourceFiles()</pre>
<div class="block">Returns true if this task has declared that it accepts source files.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>true if this task has source files, false if not.</dd>
</dl>
</li>
</ul>
<a name="getSourceFiles--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>getSourceFiles</h4>
<pre class="methodSignature"><a href="../file/FileCollection.html" title="interface in org.gradle.api.file">FileCollection</a>&nbsp;getSourceFiles()</pre>
<div class="block">Returns the set of source files for this task. These are the subset of input files which the task actually does work on.
 A task is skipped if it has declared it accepts source files, and this collection is empty.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The set of source files for this task.</dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
