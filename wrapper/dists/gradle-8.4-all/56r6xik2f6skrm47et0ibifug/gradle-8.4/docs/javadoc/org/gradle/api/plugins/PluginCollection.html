<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc -->
<title>PluginCollection (Gradle API 8.4)</title>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<link rel="stylesheet" type="text/css" href="../../../../javadoc.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../jquery/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
<script type="text/javascript" src="../../../../jquery/jszip/dist/jszip.min.js"></script>
<script type="text/javascript" src="../../../../jquery/jszip-utils/dist/jszip-utils.min.js"></script>
<!--[if IE]>
<script type="text/javascript" src="../../../../jquery/jszip-utils/dist/jszip-utils-ie.min.js"></script>
<![endif]-->
<script type="text/javascript" src="../../../../jquery/jquery-3.6.0.min.js"></script>
<script type="text/javascript" src="../../../../jquery/jquery-ui.min.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="PluginCollection (Gradle API 8.4)";
        }
    }
    catch(err) {
    }
//-->
var data = {"i0":38,"i1":38,"i2":38,"i3":6,"i4":6,"i5":38,"i6":38,"i7":6,"i8":6,"i9":6};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],4:["t3","Abstract Methods"],32:["t6","Deprecated Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
var pathtoroot = "../../../../";
var useModuleDirectories = false;
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="fixedNav">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses.html">All&nbsp;Classes</a></li>
</ul>
<ul class="navListSearch">
<li><label for="search">SEARCH:</label>
<input type="text" id="search" value="search" disabled="disabled">
<input type="reset" id="reset" value="reset" disabled="disabled">
</li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
</div>
<div class="navPadding">&nbsp;</div>
<script type="text/javascript"><!--
$('.navPadding').css('padding-top', $('.fixedNav').css("height"));
//-->
</script>
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle"><span class="packageLabelInType">Package</span>&nbsp;<a href="package-summary.html">org.gradle.api.plugins</a></div>
<h2 title="Interface PluginCollection" class="title">Interface PluginCollection&lt;T extends <a href="../Plugin.html" title="interface in org.gradle.api">Plugin</a>&gt;</h2>
</div>
<div class="contentContainer">
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt><span class="paramLabel">Type Parameters:</span></dt>
<dd><code>T</code> - The type of plugins which this collection contains.</dd>
</dl>
<dl>
<dt>All Superinterfaces:</dt>
<dd><code>java.util.Collection&lt;T&gt;</code>, <code><a href="../DomainObjectCollection.html" title="interface in org.gradle.api">DomainObjectCollection</a>&lt;T&gt;</code>, <code><a href="../DomainObjectSet.html" title="interface in org.gradle.api">DomainObjectSet</a>&lt;T&gt;</code>, <code>java.lang.Iterable&lt;T&gt;</code>, <code>java.util.Set&lt;T&gt;</code></dd>
</dl>
<dl>
<dt>All Known Subinterfaces:</dt>
<dd><code><a href="PluginContainer.html" title="interface in org.gradle.api.plugins">PluginContainer</a></code></dd>
</dl>
<hr>
<pre>public interface <span class="typeNameLabel">PluginCollection&lt;T extends <a href="../Plugin.html" title="interface in org.gradle.api">Plugin</a>&gt;</span>
extends <a href="../DomainObjectSet.html" title="interface in org.gradle.api">DomainObjectSet</a>&lt;T&gt;</pre>
<div class="block"><p>A <code>PluginCollection</code> represents a collection of <a href="../Plugin.html" title="interface in org.gradle.api"><code>Plugin</code></a> instances.</p></div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t3" class="tableTab"><span><a href="javascript:show(4);">Abstract Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t6" class="tableTab"><span><a href="javascript:show(32);">Deprecated Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colSecond" scope="col">Method</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#add-T-">add</a></span>&#8203;(<a href="PluginCollection.html" title="type parameter in PluginCollection">T</a>&nbsp;plugin)</code></th>
<td class="colLast">
<div class="block"><span class="deprecatedLabel">Deprecated.</span>
<div class="deprecationComment">Use <a href="PluginManager.html#apply-java.lang.Class-"><code>PluginManager.apply(Class)</code></a> instead.</div>
</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#addAll-java.util.Collection-">addAll</a></span>&#8203;(java.util.Collection&lt;? extends <a href="PluginCollection.html" title="type parameter in PluginCollection">T</a>&gt;&nbsp;c)</code></th>
<td class="colLast">
<div class="block"><span class="deprecatedLabel">Deprecated.</span>
<div class="deprecationComment">Use <a href="PluginManager.html#apply-java.lang.Class-"><code>PluginManager.apply(Class)</code></a> instead.</div>
</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#clear--">clear</a></span>()</code></th>
<td class="colLast">
<div class="block"><span class="deprecatedLabel">Deprecated.</span>
<div class="deprecationComment">plugins cannot be removed.</div>
</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code><a href="PluginCollection.html" title="interface in org.gradle.api.plugins">PluginCollection</a>&lt;<a href="PluginCollection.html" title="type parameter in PluginCollection">T</a>&gt;</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#matching-groovy.lang.Closure-">matching</a></span>&#8203;(<a href="https://docs.groovy-lang.org/docs/groovy-3.0.17/html/gapi/groovy/lang/Closure.html?is-external=true" title="class or interface in groovy.lang" class="externalLink">Closure</a>&nbsp;closure)</code></th>
<td class="colLast">
<div class="block">Returns a collection which contains the objects in this collection which meet the given closure specification.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code><a href="PluginCollection.html" title="interface in org.gradle.api.plugins">PluginCollection</a>&lt;<a href="PluginCollection.html" title="type parameter in PluginCollection">T</a>&gt;</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#matching-org.gradle.api.specs.Spec-">matching</a></span>&#8203;(<a href="../specs/Spec.html" title="interface in org.gradle.api.specs">Spec</a>&lt;? super <a href="PluginCollection.html" title="type parameter in PluginCollection">T</a>&gt;&nbsp;spec)</code></th>
<td class="colLast">
<div class="block">Returns a collection which contains the objects in this collection which meet the given specification.</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#remove-java.lang.Object-">remove</a></span>&#8203;(java.lang.Object&nbsp;o)</code></th>
<td class="colLast">
<div class="block"><span class="deprecatedLabel">Deprecated.</span>
<div class="deprecationComment">plugins cannot be removed.</div>
</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#removeAll-java.util.Collection-">removeAll</a></span>&#8203;(java.util.Collection&lt;?&gt;&nbsp;c)</code></th>
<td class="colLast">
<div class="block"><span class="deprecatedLabel">Deprecated.</span>
<div class="deprecationComment">plugins cannot be removed.</div>
</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#whenPluginAdded-groovy.lang.Closure-">whenPluginAdded</a></span>&#8203;(<a href="https://docs.groovy-lang.org/docs/groovy-3.0.17/html/gapi/groovy/lang/Closure.html?is-external=true" title="class or interface in groovy.lang" class="externalLink">Closure</a>&nbsp;closure)</code></th>
<td class="colLast">
<div class="block">Adds a closure to be called when a plugin is added to this collection.</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code><a href="../Action.html" title="interface in org.gradle.api">Action</a>&lt;? super <a href="PluginCollection.html" title="type parameter in PluginCollection">T</a>&gt;</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#whenPluginAdded-org.gradle.api.Action-">whenPluginAdded</a></span>&#8203;(<a href="../Action.html" title="interface in org.gradle.api">Action</a>&lt;? super <a href="PluginCollection.html" title="type parameter in PluginCollection">T</a>&gt;&nbsp;action)</code></th>
<td class="colLast">
<div class="block">Adds an <code>Action</code> to be executed when a plugin is added to this collection.</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>&lt;S extends <a href="PluginCollection.html" title="type parameter in PluginCollection">T</a>&gt;<br><a href="PluginCollection.html" title="interface in org.gradle.api.plugins">PluginCollection</a>&lt;S&gt;</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#withType-java.lang.Class-">withType</a></span>&#8203;(java.lang.Class&lt;S&gt;&nbsp;type)</code></th>
<td class="colLast">
<div class="block">Returns a collection containing the objects in this collection of the given type.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.util.Collection">
<!--   -->
</a>
<h3>Methods inherited from interface&nbsp;java.util.Collection</h3>
<code>parallelStream, removeIf, stream, toArray</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.org.gradle.api.DomainObjectCollection">
<!--   -->
</a>
<h3>Methods inherited from interface&nbsp;org.gradle.api.<a href="../DomainObjectCollection.html" title="interface in org.gradle.api">DomainObjectCollection</a></h3>
<code><a href="../DomainObjectCollection.html#addAllLater-org.gradle.api.provider.Provider-">addAllLater</a>, <a href="../DomainObjectCollection.html#addLater-org.gradle.api.provider.Provider-">addLater</a>, <a href="../DomainObjectCollection.html#all-groovy.lang.Closure-">all</a>, <a href="../DomainObjectCollection.html#all-org.gradle.api.Action-">all</a>, <a href="../DomainObjectCollection.html#configureEach-org.gradle.api.Action-">configureEach</a>, <a href="../DomainObjectCollection.html#whenObjectAdded-groovy.lang.Closure-">whenObjectAdded</a>, <a href="../DomainObjectCollection.html#whenObjectAdded-org.gradle.api.Action-">whenObjectAdded</a>, <a href="../DomainObjectCollection.html#whenObjectRemoved-groovy.lang.Closure-">whenObjectRemoved</a>, <a href="../DomainObjectCollection.html#whenObjectRemoved-org.gradle.api.Action-">whenObjectRemoved</a>, <a href="../DomainObjectCollection.html#withType-java.lang.Class-groovy.lang.Closure-">withType</a>, <a href="../DomainObjectCollection.html#withType-java.lang.Class-org.gradle.api.Action-">withType</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.org.gradle.api.DomainObjectSet">
<!--   -->
</a>
<h3>Methods inherited from interface&nbsp;org.gradle.api.<a href="../DomainObjectSet.html" title="interface in org.gradle.api">DomainObjectSet</a></h3>
<code><a href="../DomainObjectSet.html#findAll-groovy.lang.Closure-">findAll</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Iterable">
<!--   -->
</a>
<h3>Methods inherited from interface&nbsp;java.lang.Iterable</h3>
<code>forEach</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.util.Set">
<!--   -->
</a>
<h3>Methods inherited from interface&nbsp;java.util.Set</h3>
<code>contains, containsAll, equals, hashCode, isEmpty, iterator, retainAll, size, spliterator, toArray, toArray</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="matching-org.gradle.api.specs.Spec-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>matching</h4>
<pre class="methodSignature"><a href="PluginCollection.html" title="interface in org.gradle.api.plugins">PluginCollection</a>&lt;<a href="PluginCollection.html" title="type parameter in PluginCollection">T</a>&gt;&nbsp;matching&#8203;(<a href="../specs/Spec.html" title="interface in org.gradle.api.specs">Spec</a>&lt;? super <a href="PluginCollection.html" title="type parameter in PluginCollection">T</a>&gt;&nbsp;spec)</pre>
<div class="block">Returns a collection which contains the objects in this collection which meet the given specification. The
 returned collection is live, so that when matching objects are added to this collection, they are also visible in
 the filtered collection.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../DomainObjectCollection.html#matching-org.gradle.api.specs.Spec-">matching</a></code>&nbsp;in interface&nbsp;<code><a href="../DomainObjectCollection.html" title="interface in org.gradle.api">DomainObjectCollection</a>&lt;<a href="PluginCollection.html" title="type parameter in PluginCollection">T</a> extends <a href="../Plugin.html" title="interface in org.gradle.api">Plugin</a>&gt;</code></dd>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../DomainObjectSet.html#matching-org.gradle.api.specs.Spec-">matching</a></code>&nbsp;in interface&nbsp;<code><a href="../DomainObjectSet.html" title="interface in org.gradle.api">DomainObjectSet</a>&lt;<a href="PluginCollection.html" title="type parameter in PluginCollection">T</a> extends <a href="../Plugin.html" title="interface in org.gradle.api">Plugin</a>&gt;</code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>spec</code> - The specification to use.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The collection of matching objects. Returns an empty collection if there are no such objects in this
         collection.</dd>
</dl>
</li>
</ul>
<a name="matching-groovy.lang.Closure-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>matching</h4>
<pre class="methodSignature"><a href="PluginCollection.html" title="interface in org.gradle.api.plugins">PluginCollection</a>&lt;<a href="PluginCollection.html" title="type parameter in PluginCollection">T</a>&gt;&nbsp;matching&#8203;(<a href="https://docs.groovy-lang.org/docs/groovy-3.0.17/html/gapi/groovy/lang/Closure.html?is-external=true" title="class or interface in groovy.lang" class="externalLink">Closure</a>&nbsp;closure)</pre>
<div class="block">Returns a collection which contains the objects in this collection which meet the given closure specification. The
 returned collection is live, so that when matching objects are added to this collection, they are also visible in
 the filtered collection.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../DomainObjectCollection.html#matching-groovy.lang.Closure-">matching</a></code>&nbsp;in interface&nbsp;<code><a href="../DomainObjectCollection.html" title="interface in org.gradle.api">DomainObjectCollection</a>&lt;<a href="PluginCollection.html" title="type parameter in PluginCollection">T</a> extends <a href="../Plugin.html" title="interface in org.gradle.api">Plugin</a>&gt;</code></dd>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../DomainObjectSet.html#matching-groovy.lang.Closure-">matching</a></code>&nbsp;in interface&nbsp;<code><a href="../DomainObjectSet.html" title="interface in org.gradle.api">DomainObjectSet</a>&lt;<a href="PluginCollection.html" title="type parameter in PluginCollection">T</a> extends <a href="../Plugin.html" title="interface in org.gradle.api">Plugin</a>&gt;</code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>closure</code> - The specification to use. The closure gets a collection element as an argument.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The collection of matching objects. Returns an empty collection if there are no such objects in this
         collection.</dd>
</dl>
</li>
</ul>
<a name="withType-java.lang.Class-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>withType</h4>
<pre class="methodSignature">&lt;S extends <a href="PluginCollection.html" title="type parameter in PluginCollection">T</a>&gt;&nbsp;<a href="PluginCollection.html" title="interface in org.gradle.api.plugins">PluginCollection</a>&lt;S&gt;&nbsp;withType&#8203;(java.lang.Class&lt;S&gt;&nbsp;type)</pre>
<div class="block">Returns a collection containing the objects in this collection of the given type.  The returned collection is
 live, so that when matching objects are later added to this collection, they are also visible in the filtered
 collection.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../DomainObjectCollection.html#withType-java.lang.Class-">withType</a></code>&nbsp;in interface&nbsp;<code><a href="../DomainObjectCollection.html" title="interface in org.gradle.api">DomainObjectCollection</a>&lt;<a href="PluginCollection.html" title="type parameter in PluginCollection">T</a> extends <a href="../Plugin.html" title="interface in org.gradle.api">Plugin</a>&gt;</code></dd>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../DomainObjectSet.html#withType-java.lang.Class-">withType</a></code>&nbsp;in interface&nbsp;<code><a href="../DomainObjectSet.html" title="interface in org.gradle.api">DomainObjectSet</a>&lt;<a href="PluginCollection.html" title="type parameter in PluginCollection">T</a> extends <a href="../Plugin.html" title="interface in org.gradle.api">Plugin</a>&gt;</code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>type</code> - The type of objects to find.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The matching objects. Returns an empty collection if there are no such objects in this collection.</dd>
</dl>
</li>
</ul>
<a name="whenPluginAdded-org.gradle.api.Action-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>whenPluginAdded</h4>
<pre class="methodSignature"><a href="../Action.html" title="interface in org.gradle.api">Action</a>&lt;? super <a href="PluginCollection.html" title="type parameter in PluginCollection">T</a>&gt;&nbsp;whenPluginAdded&#8203;(<a href="../Action.html" title="interface in org.gradle.api">Action</a>&lt;? super <a href="PluginCollection.html" title="type parameter in PluginCollection">T</a>&gt;&nbsp;action)</pre>
<div class="block">Adds an <code>Action</code> to be executed when a plugin is added to this collection.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>action</code> - The action to be executed</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the supplied action</dd>
</dl>
</li>
</ul>
<a name="whenPluginAdded-groovy.lang.Closure-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>whenPluginAdded</h4>
<pre class="methodSignature">void&nbsp;whenPluginAdded&#8203;(<a href="https://docs.groovy-lang.org/docs/groovy-3.0.17/html/gapi/groovy/lang/Closure.html?is-external=true" title="class or interface in groovy.lang" class="externalLink">Closure</a>&nbsp;closure)</pre>
<div class="block">Adds a closure to be called when a plugin is added to this collection. The plugin is passed to the closure as the
 parameter.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>closure</code> - The closure to be called</dd>
</dl>
</li>
</ul>
<a name="add-org.gradle.api.Plugin-">
<!--   -->
</a><a name="add-T-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>add</h4>
<pre class="methodSignature">@Deprecated
boolean&nbsp;add&#8203;(<a href="PluginCollection.html" title="type parameter in PluginCollection">T</a>&nbsp;plugin)</pre>
<div class="deprecationBlock"><span class="deprecatedLabel">Deprecated.</span>
<div class="deprecationComment">Use <a href="PluginManager.html#apply-java.lang.Class-"><code>PluginManager.apply(Class)</code></a> instead.</div>
</div>
<div class="block">Unsupported.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code>add</code>&nbsp;in interface&nbsp;<code>java.util.Collection&lt;<a href="PluginCollection.html" title="type parameter in PluginCollection">T</a> extends <a href="../Plugin.html" title="interface in org.gradle.api">Plugin</a>&gt;</code></dd>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code>add</code>&nbsp;in interface&nbsp;<code>java.util.Set&lt;<a href="PluginCollection.html" title="type parameter in PluginCollection">T</a> extends <a href="../Plugin.html" title="interface in org.gradle.api">Plugin</a>&gt;</code></dd>
</dl>
</li>
</ul>
<a name="addAll-java.util.Collection-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>addAll</h4>
<pre class="methodSignature">@Deprecated
boolean&nbsp;addAll&#8203;(java.util.Collection&lt;? extends <a href="PluginCollection.html" title="type parameter in PluginCollection">T</a>&gt;&nbsp;c)</pre>
<div class="deprecationBlock"><span class="deprecatedLabel">Deprecated.</span>
<div class="deprecationComment">Use <a href="PluginManager.html#apply-java.lang.Class-"><code>PluginManager.apply(Class)</code></a> instead.</div>
</div>
<div class="block">Unsupported.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code>addAll</code>&nbsp;in interface&nbsp;<code>java.util.Collection&lt;<a href="PluginCollection.html" title="type parameter in PluginCollection">T</a> extends <a href="../Plugin.html" title="interface in org.gradle.api">Plugin</a>&gt;</code></dd>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code>addAll</code>&nbsp;in interface&nbsp;<code>java.util.Set&lt;<a href="PluginCollection.html" title="type parameter in PluginCollection">T</a> extends <a href="../Plugin.html" title="interface in org.gradle.api">Plugin</a>&gt;</code></dd>
</dl>
</li>
</ul>
<a name="remove-java.lang.Object-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>remove</h4>
<pre class="methodSignature">@Deprecated
boolean&nbsp;remove&#8203;(java.lang.Object&nbsp;o)</pre>
<div class="deprecationBlock"><span class="deprecatedLabel">Deprecated.</span>
<div class="deprecationComment">plugins cannot be removed.</div>
</div>
<div class="block">Unsupported.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code>remove</code>&nbsp;in interface&nbsp;<code>java.util.Collection&lt;<a href="PluginCollection.html" title="type parameter in PluginCollection">T</a> extends <a href="../Plugin.html" title="interface in org.gradle.api">Plugin</a>&gt;</code></dd>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code>remove</code>&nbsp;in interface&nbsp;<code>java.util.Set&lt;<a href="PluginCollection.html" title="type parameter in PluginCollection">T</a> extends <a href="../Plugin.html" title="interface in org.gradle.api">Plugin</a>&gt;</code></dd>
</dl>
</li>
</ul>
<a name="removeAll-java.util.Collection-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>removeAll</h4>
<pre class="methodSignature">@Deprecated
boolean&nbsp;removeAll&#8203;(java.util.Collection&lt;?&gt;&nbsp;c)</pre>
<div class="deprecationBlock"><span class="deprecatedLabel">Deprecated.</span>
<div class="deprecationComment">plugins cannot be removed.</div>
</div>
<div class="block">Unsupported.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code>removeAll</code>&nbsp;in interface&nbsp;<code>java.util.Collection&lt;<a href="PluginCollection.html" title="type parameter in PluginCollection">T</a> extends <a href="../Plugin.html" title="interface in org.gradle.api">Plugin</a>&gt;</code></dd>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code>removeAll</code>&nbsp;in interface&nbsp;<code>java.util.Set&lt;<a href="PluginCollection.html" title="type parameter in PluginCollection">T</a> extends <a href="../Plugin.html" title="interface in org.gradle.api">Plugin</a>&gt;</code></dd>
</dl>
</li>
</ul>
<a name="clear--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>clear</h4>
<pre class="methodSignature">@Deprecated
void&nbsp;clear()</pre>
<div class="deprecationBlock"><span class="deprecatedLabel">Deprecated.</span>
<div class="deprecationComment">plugins cannot be removed.</div>
</div>
<div class="block">Unsupported.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code>clear</code>&nbsp;in interface&nbsp;<code>java.util.Collection&lt;<a href="PluginCollection.html" title="type parameter in PluginCollection">T</a> extends <a href="../Plugin.html" title="interface in org.gradle.api">Plugin</a>&gt;</code></dd>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code>clear</code>&nbsp;in interface&nbsp;<code>java.util.Set&lt;<a href="PluginCollection.html" title="type parameter in PluginCollection">T</a> extends <a href="../Plugin.html" title="interface in org.gradle.api">Plugin</a>&gt;</code></dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
