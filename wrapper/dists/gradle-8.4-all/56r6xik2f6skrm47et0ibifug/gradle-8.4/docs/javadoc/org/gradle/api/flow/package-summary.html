<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc -->
<title>org.gradle.api.flow (Gradle API 8.4)</title>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<link rel="stylesheet" type="text/css" href="../../../../javadoc.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../jquery/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
<script type="text/javascript" src="../../../../jquery/jszip/dist/jszip.min.js"></script>
<script type="text/javascript" src="../../../../jquery/jszip-utils/dist/jszip-utils.min.js"></script>
<!--[if IE]>
<script type="text/javascript" src="../../../../jquery/jszip-utils/dist/jszip-utils-ie.min.js"></script>
<![endif]-->
<script type="text/javascript" src="../../../../jquery/jquery-3.6.0.min.js"></script>
<script type="text/javascript" src="../../../../jquery/jquery-ui.min.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="org.gradle.api.flow (Gradle API 8.4)";
        }
    }
    catch(err) {
    }
//-->
var pathtoroot = "../../../../";
var useModuleDirectories = false;
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="fixedNav">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../index.html">Overview</a></li>
<li class="navBarCell1Rev">Package</li>
<li>Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses.html">All&nbsp;Classes</a></li>
</ul>
<ul class="navListSearch">
<li><label for="search">SEARCH:</label>
<input type="text" id="search" value="search" disabled="disabled">
<input type="reset" id="reset" value="reset" disabled="disabled">
</li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
</div>
<div class="navPadding">&nbsp;</div>
<script type="text/javascript"><!--
$('.navPadding').css('padding-top', $('.fixedNav').css("height"));
//-->
</script>
<div class="header">
<p><a href="../NonNullApi.html" title="annotation in org.gradle.api">@NonNullApi</a>
</p>
<h1 title="Package" class="title">Package&nbsp;org.gradle.api.flow</h1>
</div>
<div class="contentContainer"><a name="package.description">
<!--   -->
</a>
<div class="block">Gradle Flow API.</div>
<ul class="blockList">
<li class="blockList">
<table class="typeSummary" summary="Interface Summary table, listing interfaces, and an explanation">
<caption><span>Interface Summary</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Interface</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tbody>
<tr class="altColor">
<th class="colFirst" scope="row"><a href="BuildWorkResult.html" title="interface in org.gradle.api.flow">BuildWorkResult</a></th>
<td class="colLast">
<div class="block">Summary result of the execution of the work scheduled for the current build.</div>
</td>
</tr>
<tr class="rowColor">
<th class="colFirst" scope="row"><a href="FlowAction.html" title="interface in org.gradle.api.flow">FlowAction</a>&lt;P extends <a href="FlowParameters.html" title="interface in org.gradle.api.flow">FlowParameters</a>&gt;</th>
<td class="colLast">
<div class="block">A dataflow action.</div>
</td>
</tr>
<tr class="altColor">
<th class="colFirst" scope="row"><a href="FlowActionSpec.html" title="interface in org.gradle.api.flow">FlowActionSpec</a>&lt;P extends <a href="FlowParameters.html" title="interface in org.gradle.api.flow">FlowParameters</a>&gt;</th>
<td class="colLast">
<div class="block">Allows configuring the parameters for a <a href="FlowAction.html" title="interface in org.gradle.api.flow"><code>dataflow action</code></a>.</div>
</td>
</tr>
<tr class="rowColor">
<th class="colFirst" scope="row"><a href="FlowParameters.html" title="interface in org.gradle.api.flow">FlowParameters</a></th>
<td class="colLast">
<div class="block">Marker interface for <a href="FlowAction.html" title="interface in org.gradle.api.flow"><code>dataflow action</code></a> parameters.</div>
</td>
</tr>
<tr class="altColor">
<th class="colFirst" scope="row"><a href="FlowProviders.html" title="interface in org.gradle.api.flow">FlowProviders</a></th>
<td class="colLast">
<div class="block">Exposes build lifecycle events as <a href="../provider/Provider.html" title="interface in org.gradle.api.provider"><code>providers</code></a> so they can be used as inputs
 to <a href="FlowAction.html" title="interface in org.gradle.api.flow"><code>dataflow actions</code></a>.</div>
</td>
</tr>
<tr class="rowColor">
<th class="colFirst" scope="row"><a href="FlowScope.html" title="interface in org.gradle.api.flow">FlowScope</a></th>
<td class="colLast">
<div class="block">Augments the cached work graph with <a href="FlowAction.html" title="interface in org.gradle.api.flow"><code>dataflow actions</code></a>, anonymous, parameterized and
 isolated pieces of work that are triggered solely based on the availability of their input parameters.</div>
</td>
</tr>
<tr class="altColor">
<th class="colFirst" scope="row"><a href="FlowScope.Registration.html" title="interface in org.gradle.api.flow">FlowScope.Registration</a>&lt;P extends <a href="FlowParameters.html" title="interface in org.gradle.api.flow">FlowParameters</a>&gt;</th>
<td class="colLast">
<div class="block">Represents a registered <a href="FlowAction.html" title="interface in org.gradle.api.flow"><code>dataflow action</code></a>.</div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList">
<table class="typeSummary" summary="Class Summary table, listing classes, and an explanation">
<caption><span>Class Summary</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Class</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tbody>
<tr class="altColor">
<th class="colFirst" scope="row"><a href="FlowParameters.None.html" title="class in org.gradle.api.flow">FlowParameters.None</a></th>
<td class="colLast">
<div class="block">Used for <a href="FlowAction.html" title="interface in org.gradle.api.flow"><code>dataflow actions</code></a> without parameters.</div>
</td>
</tr>
</tbody>
</table>
</li>
</ul>
</div>
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../index.html">Overview</a></li>
<li class="navBarCell1Rev">Package</li>
<li>Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
