<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc -->
<title>ArtifactResolutionQuery (Gradle API 8.4)</title>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<link rel="stylesheet" type="text/css" href="../../../../../javadoc.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../jquery/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
<script type="text/javascript" src="../../../../../jquery/jszip/dist/jszip.min.js"></script>
<script type="text/javascript" src="../../../../../jquery/jszip-utils/dist/jszip-utils.min.js"></script>
<!--[if IE]>
<script type="text/javascript" src="../../../../../jquery/jszip-utils/dist/jszip-utils-ie.min.js"></script>
<![endif]-->
<script type="text/javascript" src="../../../../../jquery/jquery-3.6.0.min.js"></script>
<script type="text/javascript" src="../../../../../jquery/jquery-ui.min.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="ArtifactResolutionQuery (Gradle API 8.4)";
        }
    }
    catch(err) {
    }
//-->
var data = {"i0":6,"i1":6,"i2":6,"i3":6,"i4":6,"i5":6};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],4:["t3","Abstract Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
var pathtoroot = "../../../../../";
var useModuleDirectories = false;
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="fixedNav">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../../allclasses.html">All&nbsp;Classes</a></li>
</ul>
<ul class="navListSearch">
<li><label for="search">SEARCH:</label>
<input type="text" id="search" value="search" disabled="disabled">
<input type="reset" id="reset" value="reset" disabled="disabled">
</li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
</div>
<div class="navPadding">&nbsp;</div>
<script type="text/javascript"><!--
$('.navPadding').css('padding-top', $('.fixedNav').css("height"));
//-->
</script>
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle"><span class="packageLabelInType">Package</span>&nbsp;<a href="package-summary.html">org.gradle.api.artifacts.query</a></div>
<h2 title="Interface ArtifactResolutionQuery" class="title">Interface ArtifactResolutionQuery</h2>
</div>
<div class="contentContainer">
<div class="description">
<ul class="blockList">
<li class="blockList">
<hr>
<pre>public interface <span class="typeNameLabel">ArtifactResolutionQuery</span></pre>
<div class="block">A builder to construct a query that can resolve selected software artifacts of the specified components.

 <pre class='autoTested'>
 plugins {
     id 'java'
 }

 task resolveCompileSources {
     doLast {
         def componentIds = configurations.compileClasspath.incoming.resolutionResult.allDependencies.collect { it.selected.id }

         def result = dependencies.createArtifactResolutionQuery()
                                  .forComponents(componentIds)
                                  .withArtifacts(JvmLibrary, SourcesArtifact, JavadocArtifact)
                                  .execute()

         for (component in result.resolvedComponents) {
             component.getArtifacts(SourcesArtifact).each { println "Source artifact for ${component.id}: ${it.file}" }
         }
     }
 }
 </pre></div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>2.0</dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t3" class="tableTab"><span><a href="javascript:show(4);">Abstract Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colSecond" scope="col">Method</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code><a href="../result/ArtifactResolutionResult.html" title="interface in org.gradle.api.artifacts.result">ArtifactResolutionResult</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#execute--">execute</a></span>()</code></th>
<td class="colLast">
<div class="block">Actually execute the query, returning a query result.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code><a href="ArtifactResolutionQuery.html" title="interface in org.gradle.api.artifacts.query">ArtifactResolutionQuery</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#forComponents-java.lang.Iterable-">forComponents</a></span>&#8203;(java.lang.Iterable&lt;? extends <a href="../component/ComponentIdentifier.html" title="interface in org.gradle.api.artifacts.component">ComponentIdentifier</a>&gt;&nbsp;componentIds)</code></th>
<td class="colLast">
<div class="block">Specifies the set of components to include in the result.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code><a href="ArtifactResolutionQuery.html" title="interface in org.gradle.api.artifacts.query">ArtifactResolutionQuery</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#forComponents-org.gradle.api.artifacts.component.ComponentIdentifier...-">forComponents</a></span>&#8203;(<a href="../component/ComponentIdentifier.html" title="interface in org.gradle.api.artifacts.component">ComponentIdentifier</a>...&nbsp;componentIds)</code></th>
<td class="colLast">
<div class="block">Specifies the set of components to include in the result.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code><a href="ArtifactResolutionQuery.html" title="interface in org.gradle.api.artifacts.query">ArtifactResolutionQuery</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#forModule-java.lang.String-java.lang.String-java.lang.String-">forModule</a></span>&#8203;(java.lang.String&nbsp;group,
         java.lang.String&nbsp;name,
         java.lang.String&nbsp;version)</code></th>
<td class="colLast">
<div class="block">Specifies a module component to include in the result using its GAV coordinates.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code><a href="ArtifactResolutionQuery.html" title="interface in org.gradle.api.artifacts.query">ArtifactResolutionQuery</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#withArtifacts-java.lang.Class-java.lang.Class...-">withArtifacts</a></span>&#8203;(java.lang.Class&lt;? extends <a href="../../component/Component.html" title="interface in org.gradle.api.component">Component</a>&gt;&nbsp;componentType,
             java.lang.Class&lt;? extends <a href="../../component/Artifact.html" title="interface in org.gradle.api.component">Artifact</a>&gt;...&nbsp;artifactTypes)</code></th>
<td class="colLast">
<div class="block">Defines the type of component that is expected in the result, and the artifacts to retrieve for components of this type.</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code><a href="ArtifactResolutionQuery.html" title="interface in org.gradle.api.artifacts.query">ArtifactResolutionQuery</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#withArtifacts-java.lang.Class-java.util.Collection-">withArtifacts</a></span>&#8203;(java.lang.Class&lt;? extends <a href="../../component/Component.html" title="interface in org.gradle.api.component">Component</a>&gt;&nbsp;componentType,
             java.util.Collection&lt;java.lang.Class&lt;? extends <a href="../../component/Artifact.html" title="interface in org.gradle.api.component">Artifact</a>&gt;&gt;&nbsp;artifactTypes)</code></th>
<td class="colLast">
<div class="block">Defines the type of component that is expected in the result, and the artifacts to retrieve for components of this type.</div>
</td>
</tr>
</table>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="forComponents-java.lang.Iterable-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>forComponents</h4>
<pre class="methodSignature"><a href="ArtifactResolutionQuery.html" title="interface in org.gradle.api.artifacts.query">ArtifactResolutionQuery</a>&nbsp;forComponents&#8203;(java.lang.Iterable&lt;? extends <a href="../component/ComponentIdentifier.html" title="interface in org.gradle.api.artifacts.component">ComponentIdentifier</a>&gt;&nbsp;componentIds)</pre>
<div class="block">Specifies the set of components to include in the result.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>componentIds</code> - The identifiers of the components to be queried.</dd>
</dl>
</li>
</ul>
<a name="forComponents-org.gradle.api.artifacts.component.ComponentIdentifier...-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>forComponents</h4>
<pre class="methodSignature"><a href="ArtifactResolutionQuery.html" title="interface in org.gradle.api.artifacts.query">ArtifactResolutionQuery</a>&nbsp;forComponents&#8203;(<a href="../component/ComponentIdentifier.html" title="interface in org.gradle.api.artifacts.component">ComponentIdentifier</a>...&nbsp;componentIds)</pre>
<div class="block">Specifies the set of components to include in the result.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>componentIds</code> - The identifiers of the components to be queried.</dd>
</dl>
</li>
</ul>
<a name="forModule-java.lang.String-java.lang.String-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>forModule</h4>
<pre class="methodSignature"><a href="ArtifactResolutionQuery.html" title="interface in org.gradle.api.artifacts.query">ArtifactResolutionQuery</a>&nbsp;forModule&#8203;(java.lang.String&nbsp;group,
                                  java.lang.String&nbsp;name,
                                  java.lang.String&nbsp;version)</pre>
<div class="block">Specifies a module component to include in the result using its GAV coordinates.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>group</code> - Module group.</dd>
<dd><code>name</code> - Module name.</dd>
<dd><code>version</code> - Module version.</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>4.5</dd>
</dl>
</li>
</ul>
<a name="withArtifacts-java.lang.Class-java.lang.Class...-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>withArtifacts</h4>
<pre class="methodSignature"><a href="ArtifactResolutionQuery.html" title="interface in org.gradle.api.artifacts.query">ArtifactResolutionQuery</a>&nbsp;withArtifacts&#8203;(java.lang.Class&lt;? extends <a href="../../component/Component.html" title="interface in org.gradle.api.component">Component</a>&gt;&nbsp;componentType,
                                      java.lang.Class&lt;? extends <a href="../../component/Artifact.html" title="interface in org.gradle.api.component">Artifact</a>&gt;...&nbsp;artifactTypes)</pre>
<div class="block">Defines the type of component that is expected in the result, and the artifacts to retrieve for components of this type.

 Presently, only a single component type and set of artifacts is permitted.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>componentType</code> - The expected type of the component.</dd>
<dd><code>artifactTypes</code> - The artifacts to retrieve for the queried components.</dd>
</dl>
</li>
</ul>
<a name="withArtifacts-java.lang.Class-java.util.Collection-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>withArtifacts</h4>
<pre class="methodSignature"><a href="ArtifactResolutionQuery.html" title="interface in org.gradle.api.artifacts.query">ArtifactResolutionQuery</a>&nbsp;withArtifacts&#8203;(java.lang.Class&lt;? extends <a href="../../component/Component.html" title="interface in org.gradle.api.component">Component</a>&gt;&nbsp;componentType,
                                      java.util.Collection&lt;java.lang.Class&lt;? extends <a href="../../component/Artifact.html" title="interface in org.gradle.api.component">Artifact</a>&gt;&gt;&nbsp;artifactTypes)</pre>
<div class="block">Defines the type of component that is expected in the result, and the artifacts to retrieve for components of this type.

 Presently, only a single component type and set of artifacts is permitted.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>componentType</code> - The expected type of the component.</dd>
<dd><code>artifactTypes</code> - The artifacts to retrieve for the queried components.</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>4.5</dd>
</dl>
</li>
</ul>
<a name="execute--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>execute</h4>
<pre class="methodSignature"><a href="../result/ArtifactResolutionResult.html" title="interface in org.gradle.api.artifacts.result">ArtifactResolutionResult</a>&nbsp;execute()</pre>
<div class="block">Actually execute the query, returning a query result.
 Note that <a href="#withArtifacts-java.lang.Class-java.lang.Class...-"><code>withArtifacts(Class, Class[])</code></a> must be called before executing the query.</div>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../../allclasses.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
