<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc -->
<title>SyncSpec (Gradle API 8.4)</title>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<link rel="stylesheet" type="text/css" href="../../../../javadoc.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../jquery/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
<script type="text/javascript" src="../../../../jquery/jszip/dist/jszip.min.js"></script>
<script type="text/javascript" src="../../../../jquery/jszip-utils/dist/jszip-utils.min.js"></script>
<!--[if IE]>
<script type="text/javascript" src="../../../../jquery/jszip-utils/dist/jszip-utils-ie.min.js"></script>
<![endif]-->
<script type="text/javascript" src="../../../../jquery/jquery-3.6.0.min.js"></script>
<script type="text/javascript" src="../../../../jquery/jquery-ui.min.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="SyncSpec (Gradle API 8.4)";
        }
    }
    catch(err) {
    }
//-->
var data = {"i0":6,"i1":6};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],4:["t3","Abstract Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
var pathtoroot = "../../../../";
var useModuleDirectories = false;
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="fixedNav">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses.html">All&nbsp;Classes</a></li>
</ul>
<ul class="navListSearch">
<li><label for="search">SEARCH:</label>
<input type="text" id="search" value="search" disabled="disabled">
<input type="reset" id="reset" value="reset" disabled="disabled">
</li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
</div>
<div class="navPadding">&nbsp;</div>
<script type="text/javascript"><!--
$('.navPadding').css('padding-top', $('.fixedNav').css("height"));
//-->
</script>
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle"><span class="packageLabelInType">Package</span>&nbsp;<a href="package-summary.html">org.gradle.api.file</a></div>
<h2 title="Interface SyncSpec" class="title">Interface SyncSpec</h2>
</div>
<div class="contentContainer">
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Superinterfaces:</dt>
<dd><code><a href="ContentFilterable.html" title="interface in org.gradle.api.file">ContentFilterable</a></code>, <code><a href="CopyProcessingSpec.html" title="interface in org.gradle.api.file">CopyProcessingSpec</a></code>, <code><a href="CopySourceSpec.html" title="interface in org.gradle.api.file">CopySourceSpec</a></code>, <code><a href="CopySpec.html" title="interface in org.gradle.api.file">CopySpec</a></code>, <code><a href="../tasks/util/PatternFilterable.html" title="interface in org.gradle.api.tasks.util">PatternFilterable</a></code></dd>
</dl>
<hr>
<pre>public interface <span class="typeNameLabel">SyncSpec</span>
extends <a href="CopySpec.html" title="interface in org.gradle.api.file">CopySpec</a></pre>
<div class="block">Synchronizes the contents of a destination directory with some source directories and files.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>7.5</dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t3" class="tableTab"><span><a href="javascript:show(4);">Abstract Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colSecond" scope="col">Method</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code><a href="../tasks/util/PatternFilterable.html" title="interface in org.gradle.api.tasks.util">PatternFilterable</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getPreserve--">getPreserve</a></span>()</code></th>
<td class="colLast">
<div class="block">Returns the filter that defines which files to preserve in the destination directory.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code><a href="SyncSpec.html" title="interface in org.gradle.api.file">SyncSpec</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#preserve-org.gradle.api.Action-">preserve</a></span>&#8203;(<a href="../Action.html" title="interface in org.gradle.api">Action</a>&lt;? super <a href="../tasks/util/PatternFilterable.html" title="interface in org.gradle.api.tasks.util">PatternFilterable</a>&gt;&nbsp;action)</code></th>
<td class="colLast">
<div class="block">Configures the filter that defines which files to preserve in the destination directory.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.org.gradle.api.file.CopyProcessingSpec">
<!--   -->
</a>
<h3>Methods inherited from interface&nbsp;org.gradle.api.file.<a href="CopyProcessingSpec.html" title="interface in org.gradle.api.file">CopyProcessingSpec</a></h3>
<code><a href="CopyProcessingSpec.html#dirPermissions-org.gradle.api.Action-">dirPermissions</a>, <a href="CopyProcessingSpec.html#filePermissions-org.gradle.api.Action-">filePermissions</a>, <a href="CopyProcessingSpec.html#getDirMode--">getDirMode</a>, <a href="CopyProcessingSpec.html#getDirPermissions--">getDirPermissions</a>, <a href="CopyProcessingSpec.html#getFileMode--">getFileMode</a>, <a href="CopyProcessingSpec.html#getFilePermissions--">getFilePermissions</a>, <a href="CopyProcessingSpec.html#setDirMode-java.lang.Integer-">setDirMode</a>, <a href="CopyProcessingSpec.html#setFileMode-java.lang.Integer-">setFileMode</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.org.gradle.api.file.CopySpec">
<!--   -->
</a>
<h3>Methods inherited from interface&nbsp;org.gradle.api.file.<a href="CopySpec.html" title="interface in org.gradle.api.file">CopySpec</a></h3>
<code><a href="CopySpec.html#eachFile-groovy.lang.Closure-">eachFile</a>, <a href="CopySpec.html#eachFile-org.gradle.api.Action-">eachFile</a>, <a href="CopySpec.html#exclude-groovy.lang.Closure-">exclude</a>, <a href="CopySpec.html#exclude-java.lang.Iterable-">exclude</a>, <a href="CopySpec.html#exclude-java.lang.String...-">exclude</a>, <a href="CopySpec.html#exclude-org.gradle.api.specs.Spec-">exclude</a>, <a href="CopySpec.html#expand-java.util.Map-">expand</a>, <a href="CopySpec.html#expand-java.util.Map-org.gradle.api.Action-">expand</a>, <a href="CopySpec.html#filesMatching-java.lang.Iterable-org.gradle.api.Action-">filesMatching</a>, <a href="CopySpec.html#filesMatching-java.lang.String-org.gradle.api.Action-">filesMatching</a>, <a href="CopySpec.html#filesNotMatching-java.lang.Iterable-org.gradle.api.Action-">filesNotMatching</a>, <a href="CopySpec.html#filesNotMatching-java.lang.String-org.gradle.api.Action-">filesNotMatching</a>, <a href="CopySpec.html#filter-groovy.lang.Closure-">filter</a>, <a href="CopySpec.html#filter-java.lang.Class-">filter</a>, <a href="CopySpec.html#filter-java.util.Map-java.lang.Class-">filter</a>, <a href="CopySpec.html#filter-org.gradle.api.Transformer-">filter</a>, <a href="CopySpec.html#from-java.lang.Object...-">from</a>, <a href="CopySpec.html#from-java.lang.Object-groovy.lang.Closure-">from</a>, <a href="CopySpec.html#from-java.lang.Object-org.gradle.api.Action-">from</a>, <a href="CopySpec.html#getDuplicatesStrategy--">getDuplicatesStrategy</a>, <a href="CopySpec.html#getFilteringCharset--">getFilteringCharset</a>, <a href="CopySpec.html#getIncludeEmptyDirs--">getIncludeEmptyDirs</a>, <a href="CopySpec.html#include-groovy.lang.Closure-">include</a>, <a href="CopySpec.html#include-java.lang.Iterable-">include</a>, <a href="CopySpec.html#include-java.lang.String...-">include</a>, <a href="CopySpec.html#include-org.gradle.api.specs.Spec-">include</a>, <a href="CopySpec.html#into-java.lang.Object-">into</a>, <a href="CopySpec.html#into-java.lang.Object-groovy.lang.Closure-">into</a>, <a href="CopySpec.html#into-java.lang.Object-org.gradle.api.Action-">into</a>, <a href="CopySpec.html#isCaseSensitive--">isCaseSensitive</a>, <a href="CopySpec.html#rename-groovy.lang.Closure-">rename</a>, <a href="CopySpec.html#rename-java.lang.String-java.lang.String-">rename</a>, <a href="CopySpec.html#rename-java.util.regex.Pattern-java.lang.String-">rename</a>, <a href="CopySpec.html#rename-org.gradle.api.Transformer-">rename</a>, <a href="CopySpec.html#setCaseSensitive-boolean-">setCaseSensitive</a>, <a href="CopySpec.html#setDuplicatesStrategy-org.gradle.api.file.DuplicatesStrategy-">setDuplicatesStrategy</a>, <a href="CopySpec.html#setExcludes-java.lang.Iterable-">setExcludes</a>, <a href="CopySpec.html#setFilteringCharset-java.lang.String-">setFilteringCharset</a>, <a href="CopySpec.html#setIncludeEmptyDirs-boolean-">setIncludeEmptyDirs</a>, <a href="CopySpec.html#setIncludes-java.lang.Iterable-">setIncludes</a>, <a href="CopySpec.html#with-org.gradle.api.file.CopySpec...-">with</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.org.gradle.api.tasks.util.PatternFilterable">
<!--   -->
</a>
<h3>Methods inherited from interface&nbsp;org.gradle.api.tasks.util.<a href="../tasks/util/PatternFilterable.html" title="interface in org.gradle.api.tasks.util">PatternFilterable</a></h3>
<code><a href="../tasks/util/PatternFilterable.html#getExcludes--">getExcludes</a>, <a href="../tasks/util/PatternFilterable.html#getIncludes--">getIncludes</a></code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="getPreserve--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getPreserve</h4>
<pre class="methodSignature"><a href="../tasks/Internal.html" title="annotation in org.gradle.api.tasks">@Internal</a>
<a href="../tasks/util/PatternFilterable.html" title="interface in org.gradle.api.tasks.util">PatternFilterable</a>&nbsp;getPreserve()</pre>
<div class="block">Returns the filter that defines which files to preserve in the destination directory.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the filter defining the files to preserve</dd>
</dl>
</li>
</ul>
<a name="preserve-org.gradle.api.Action-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>preserve</h4>
<pre class="methodSignature"><a href="SyncSpec.html" title="interface in org.gradle.api.file">SyncSpec</a>&nbsp;preserve&#8203;(<a href="../Action.html" title="interface in org.gradle.api">Action</a>&lt;? super <a href="../tasks/util/PatternFilterable.html" title="interface in org.gradle.api.tasks.util">PatternFilterable</a>&gt;&nbsp;action)</pre>
<div class="block">Configures the filter that defines which files to preserve in the destination directory.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>action</code> - Action for configuring the preserve filter</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>this</dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
