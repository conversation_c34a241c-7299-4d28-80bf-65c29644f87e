<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc -->
<title>Manifest (Gradle API 8.4)</title>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<link rel="stylesheet" type="text/css" href="../../../../../javadoc.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../jquery/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
<script type="text/javascript" src="../../../../../jquery/jszip/dist/jszip.min.js"></script>
<script type="text/javascript" src="../../../../../jquery/jszip-utils/dist/jszip-utils.min.js"></script>
<!--[if IE]>
<script type="text/javascript" src="../../../../../jquery/jszip-utils/dist/jszip-utils-ie.min.js"></script>
<![endif]-->
<script type="text/javascript" src="../../../../../jquery/jquery-3.6.0.min.js"></script>
<script type="text/javascript" src="../../../../../jquery/jquery-ui.min.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="Manifest (Gradle API 8.4)";
        }
    }
    catch(err) {
    }
//-->
var data = {"i0":6,"i1":6,"i2":6,"i3":6,"i4":6,"i5":6,"i6":6,"i7":6,"i8":6};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],4:["t3","Abstract Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
var pathtoroot = "../../../../../";
var useModuleDirectories = false;
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="fixedNav">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../../allclasses.html">All&nbsp;Classes</a></li>
</ul>
<ul class="navListSearch">
<li><label for="search">SEARCH:</label>
<input type="text" id="search" value="search" disabled="disabled">
<input type="reset" id="reset" value="reset" disabled="disabled">
</li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
</div>
<div class="navPadding">&nbsp;</div>
<script type="text/javascript"><!--
$('.navPadding').css('padding-top', $('.fixedNav').css("height"));
//-->
</script>
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle"><span class="packageLabelInType">Package</span>&nbsp;<a href="package-summary.html">org.gradle.api.java.archives</a></div>
<h2 title="Interface Manifest" class="title">Interface Manifest</h2>
</div>
<div class="contentContainer">
<div class="description">
<ul class="blockList">
<li class="blockList">
<hr>
<pre>public interface <span class="typeNameLabel">Manifest</span></pre>
<div class="block">Represents the manifest file of a JAR file.</div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t3" class="tableTab"><span><a href="javascript:show(4);">Abstract Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colSecond" scope="col">Method</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code><a href="Manifest.html" title="interface in org.gradle.api.java.archives">Manifest</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#attributes-java.util.Map-">attributes</a></span>&#8203;(java.util.Map&lt;java.lang.String,&#8203;?&gt;&nbsp;attributes)</code></th>
<td class="colLast">
<div class="block">Adds content to the main attributes of the manifest.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code><a href="Manifest.html" title="interface in org.gradle.api.java.archives">Manifest</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#attributes-java.util.Map-java.lang.String-">attributes</a></span>&#8203;(java.util.Map&lt;java.lang.String,&#8203;?&gt;&nbsp;attributes,
          java.lang.String&nbsp;sectionName)</code></th>
<td class="colLast">
<div class="block">Adds content to the given section of the manifest.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code><a href="Manifest.html" title="interface in org.gradle.api.java.archives">Manifest</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#from-java.lang.Object...-">from</a></span>&#8203;(java.lang.Object...&nbsp;mergePath)</code></th>
<td class="colLast">
<div class="block">Specifies other manifests to be merged into this manifest.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code><a href="Manifest.html" title="interface in org.gradle.api.java.archives">Manifest</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#from-java.lang.Object-groovy.lang.Closure-">from</a></span>&#8203;(java.lang.Object&nbsp;mergePath,
    <a href="https://docs.groovy-lang.org/docs/groovy-3.0.17/html/gapi/groovy/lang/Closure.html?is-external=true" title="class or interface in groovy.lang" class="externalLink">Closure</a>&lt;?&gt;&nbsp;closure)</code></th>
<td class="colLast">
<div class="block">Specifies other manifests to be merged into this manifest.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code><a href="Manifest.html" title="interface in org.gradle.api.java.archives">Manifest</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#from-java.lang.Object-org.gradle.api.Action-">from</a></span>&#8203;(java.lang.Object&nbsp;mergePath,
    <a href="../../Action.html" title="interface in org.gradle.api">Action</a>&lt;<a href="ManifestMergeSpec.html" title="interface in org.gradle.api.java.archives">ManifestMergeSpec</a>&gt;&nbsp;action)</code></th>
<td class="colLast">
<div class="block">Specifies other manifests to be merged into this manifest.</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code><a href="Attributes.html" title="interface in org.gradle.api.java.archives">Attributes</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getAttributes--">getAttributes</a></span>()</code></th>
<td class="colLast">
<div class="block">Returns the main attributes of the manifest.</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code><a href="Manifest.html" title="interface in org.gradle.api.java.archives">Manifest</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getEffectiveManifest--">getEffectiveManifest</a></span>()</code></th>
<td class="colLast">
<div class="block">Returns a new manifest instance where all the attribute values are expanded (e.g.</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>java.util.Map&lt;java.lang.String,&#8203;<a href="Attributes.html" title="interface in org.gradle.api.java.archives">Attributes</a>&gt;</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getSections--">getSections</a></span>()</code></th>
<td class="colLast">
<div class="block">Returns the sections of the manifest (excluding the main section).</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code><a href="Manifest.html" title="interface in org.gradle.api.java.archives">Manifest</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#writeTo-java.lang.Object-">writeTo</a></span>&#8203;(java.lang.Object&nbsp;path)</code></th>
<td class="colLast">
<div class="block">Writes the manifest into a file.</div>
</td>
</tr>
</table>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="getAttributes--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getAttributes</h4>
<pre class="methodSignature"><a href="Attributes.html" title="interface in org.gradle.api.java.archives">Attributes</a>&nbsp;getAttributes()</pre>
<div class="block">Returns the main attributes of the manifest.</div>
</li>
</ul>
<a name="getSections--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getSections</h4>
<pre class="methodSignature">java.util.Map&lt;java.lang.String,&#8203;<a href="Attributes.html" title="interface in org.gradle.api.java.archives">Attributes</a>&gt;&nbsp;getSections()</pre>
<div class="block">Returns the sections of the manifest (excluding the main section).</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>A map with the sections, where the key represents the section name and value the section attributes.</dd>
</dl>
</li>
</ul>
<a name="attributes-java.util.Map-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>attributes</h4>
<pre class="methodSignature"><a href="Manifest.html" title="interface in org.gradle.api.java.archives">Manifest</a>&nbsp;attributes&#8203;(java.util.Map&lt;java.lang.String,&#8203;?&gt;&nbsp;attributes)
             throws <a href="ManifestException.html" title="class in org.gradle.api.java.archives">ManifestException</a></pre>
<div class="block">Adds content to the main attributes of the manifest.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>attributes</code> - The values to add to the main attributes. The values can be any object. For evaluating the value objects
 their <code>Object.toString()</code> method is used. This is done lazily either before writing or when <a href="#getEffectiveManifest--"><code>getEffectiveManifest()</code></a>
 is called.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>this</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code><a href="ManifestException.html" title="class in org.gradle.api.java.archives">ManifestException</a></code> - If a key is invalid according to the manifest spec or if a key or value is null.</dd>
</dl>
</li>
</ul>
<a name="attributes-java.util.Map-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>attributes</h4>
<pre class="methodSignature"><a href="Manifest.html" title="interface in org.gradle.api.java.archives">Manifest</a>&nbsp;attributes&#8203;(java.util.Map&lt;java.lang.String,&#8203;?&gt;&nbsp;attributes,
                    java.lang.String&nbsp;sectionName)
             throws <a href="ManifestException.html" title="class in org.gradle.api.java.archives">ManifestException</a></pre>
<div class="block">Adds content to the given section of the manifest.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>attributes</code> - The values to add to the section. The values can be any object. For evaluating the value objects
 their <code>Object.toString()</code> method is used. This is done lazily either before writing or when <a href="#getEffectiveManifest--"><code>getEffectiveManifest()</code></a>
 is called.</dd>
<dd><code>sectionName</code> - The name of the section</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>this</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code><a href="ManifestException.html" title="class in org.gradle.api.java.archives">ManifestException</a></code> - If a key is invalid according to the manifest spec or if a key or value is null.</dd>
</dl>
</li>
</ul>
<a name="getEffectiveManifest--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getEffectiveManifest</h4>
<pre class="methodSignature"><a href="Manifest.html" title="interface in org.gradle.api.java.archives">Manifest</a>&nbsp;getEffectiveManifest()</pre>
<div class="block">Returns a new manifest instance where all the attribute values are expanded (e.g. their toString method is called).
 The returned manifest also contains all the attributes of the to be merged manifests specified in <a href="#from-java.lang.Object...-"><code>from(Object...)</code></a>.</div>
</li>
</ul>
<a name="writeTo-java.lang.Object-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>writeTo</h4>
<pre class="methodSignature"><a href="Manifest.html" title="interface in org.gradle.api.java.archives">Manifest</a>&nbsp;writeTo&#8203;(java.lang.Object&nbsp;path)</pre>
<div class="block">Writes the manifest into a file. The path's are resolved as defined by <a href="../../Project.html#files-java.lang.Object...-"><code>Project.files(Object...)</code></a>

 The manifest will be encoded using the character set defined by the <a href="../../../jvm/tasks/Jar.html#getManifestContentCharset--"><code>Jar.getManifestContentCharset()</code></a> property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>path</code> - The path of the file to write the manifest into.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>this</dd>
</dl>
</li>
</ul>
<a name="from-java.lang.Object...-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>from</h4>
<pre class="methodSignature"><a href="Manifest.html" title="interface in org.gradle.api.java.archives">Manifest</a>&nbsp;from&#8203;(java.lang.Object...&nbsp;mergePath)</pre>
<div class="block">Specifies other manifests to be merged into this manifest. A merge path can either be another instance of
 <a href="Manifest.html" title="interface in org.gradle.api.java.archives"><code>Manifest</code></a> or a file path as interpreted by <a href="../../Project.html#file-java.lang.Object-"><code>Project.file(Object)</code></a>.

 The merge is not happening instantaneously. It happens either before writing or when <a href="#getEffectiveManifest--"><code>getEffectiveManifest()</code></a>
 is called.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>this</dd>
</dl>
</li>
</ul>
<a name="from-java.lang.Object-groovy.lang.Closure-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>from</h4>
<pre class="methodSignature"><a href="Manifest.html" title="interface in org.gradle.api.java.archives">Manifest</a>&nbsp;from&#8203;(java.lang.Object&nbsp;mergePath,
              <a href="https://docs.groovy-lang.org/docs/groovy-3.0.17/html/gapi/groovy/lang/DelegatesTo.html?is-external=true" title="class or interface in groovy.lang" class="externalLink">@DelegatesTo</a>(<a href="ManifestMergeSpec.html" title="interface in org.gradle.api.java.archives">ManifestMergeSpec.class</a>)
              <a href="https://docs.groovy-lang.org/docs/groovy-3.0.17/html/gapi/groovy/lang/Closure.html?is-external=true" title="class or interface in groovy.lang" class="externalLink">Closure</a>&lt;?&gt;&nbsp;closure)</pre>
<div class="block">Specifies other manifests to be merged into this manifest. A merge path is interpreted as described in
 <a href="#from-java.lang.Object...-"><code>from(Object...)</code></a>.

 The merge is not happening instantaneously. It happens either before writing or when <a href="#getEffectiveManifest--"><code>getEffectiveManifest()</code></a>
 is called.

 The closure configures the underlying <a href="ManifestMergeSpec.html" title="interface in org.gradle.api.java.archives"><code>ManifestMergeSpec</code></a>.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>this</dd>
</dl>
</li>
</ul>
<a name="from-java.lang.Object-org.gradle.api.Action-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>from</h4>
<pre class="methodSignature"><a href="Manifest.html" title="interface in org.gradle.api.java.archives">Manifest</a>&nbsp;from&#8203;(java.lang.Object&nbsp;mergePath,
              <a href="../../Action.html" title="interface in org.gradle.api">Action</a>&lt;<a href="ManifestMergeSpec.html" title="interface in org.gradle.api.java.archives">ManifestMergeSpec</a>&gt;&nbsp;action)</pre>
<div class="block">Specifies other manifests to be merged into this manifest. A merge path is interpreted as described in
 <a href="#from-java.lang.Object...-"><code>from(Object...)</code></a>.

 The merge is not happening instantaneously. It happens either before writing or when <a href="#getEffectiveManifest--"><code>getEffectiveManifest()</code></a>
 is called.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>this</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>5.0</dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../../allclasses.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
