<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc -->
<title>TestFailure (Gradle API 8.4)</title>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<link rel="stylesheet" type="text/css" href="../../../../../javadoc.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../jquery/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
<script type="text/javascript" src="../../../../../jquery/jszip/dist/jszip.min.js"></script>
<script type="text/javascript" src="../../../../../jquery/jszip-utils/dist/jszip-utils.min.js"></script>
<!--[if IE]>
<script type="text/javascript" src="../../../../../jquery/jszip-utils/dist/jszip-utils-ie.min.js"></script>
<![endif]-->
<script type="text/javascript" src="../../../../../jquery/jquery-3.6.0.min.js"></script>
<script type="text/javascript" src="../../../../../jquery/jquery-ui.min.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="TestFailure (Gradle API 8.4)";
        }
    }
    catch(err) {
    }
//-->
var data = {"i0":9,"i1":9,"i2":9,"i3":9,"i4":9,"i5":6,"i6":6,"i7":6};
var tabs = {65535:["t0","All Methods"],1:["t1","Static Methods"],2:["t2","Instance Methods"],4:["t3","Abstract Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
var pathtoroot = "../../../../../";
var useModuleDirectories = false;
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="fixedNav">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../../allclasses.html">All&nbsp;Classes</a></li>
</ul>
<ul class="navListSearch">
<li><label for="search">SEARCH:</label>
<input type="text" id="search" value="search" disabled="disabled">
<input type="reset" id="reset" value="reset" disabled="disabled">
</li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
</div>
<div class="navPadding">&nbsp;</div>
<script type="text/javascript"><!--
$('.navPadding').css('padding-top', $('.fixedNav').css("height"));
//-->
</script>
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle"><span class="packageLabelInType">Package</span>&nbsp;<a href="package-summary.html">org.gradle.api.tasks.testing</a></div>
<h2 title="Class TestFailure" class="title">Class TestFailure</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>org.gradle.api.tasks.testing.TestFailure</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<hr>
<pre><a href="../../Incubating.html" title="annotation in org.gradle.api">@Incubating</a>
public abstract class <span class="typeNameLabel">TestFailure</span>
extends java.lang.Object</pre>
<div class="block">Describes a test failure. Contains a reference to the failure and some structural information retrieved by the test worker.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>7.6</dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="memberSummary" summary="Constructor Summary table, listing constructors, and an explanation">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Constructor</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tr class="altColor">
<th class="colConstructorName" scope="row"><code><span class="memberNameLink"><a href="#TestFailure--">TestFailure</a></span>()</code></th>
<td class="colLast">&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t1" class="tableTab"><span><a href="javascript:show(1);">Static Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t3" class="tableTab"><span><a href="javascript:show(4);">Abstract Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colSecond" scope="col">Method</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>static <a href="TestFailure.html" title="class in org.gradle.api.tasks.testing">TestFailure</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#fromFileComparisonFailure-java.lang.Throwable-java.lang.String-java.lang.String-byte:A-byte:A-java.util.List-">fromFileComparisonFailure</a></span>&#8203;(java.lang.Throwable&nbsp;failure,
                         java.lang.String&nbsp;expected,
                         java.lang.String&nbsp;actual,
                         byte[]&nbsp;expectedContent,
                         byte[]&nbsp;actualContent,
                         java.util.List&lt;<a href="TestFailure.html" title="class in org.gradle.api.tasks.testing">TestFailure</a>&gt;&nbsp;causes)</code></th>
<td class="colLast">
<div class="block">Todo</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>static <a href="TestFailure.html" title="class in org.gradle.api.tasks.testing">TestFailure</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#fromTestAssertionFailure-java.lang.Throwable-java.lang.String-java.lang.String-">fromTestAssertionFailure</a></span>&#8203;(java.lang.Throwable&nbsp;failure,
                        java.lang.String&nbsp;expected,
                        java.lang.String&nbsp;actual)</code></th>
<td class="colLast">
<div class="block">Creates a new TestFailure instance from an assertion failure.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>static <a href="TestFailure.html" title="class in org.gradle.api.tasks.testing">TestFailure</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#fromTestAssertionFailure-java.lang.Throwable-java.lang.String-java.lang.String-java.util.List-">fromTestAssertionFailure</a></span>&#8203;(java.lang.Throwable&nbsp;failure,
                        java.lang.String&nbsp;expected,
                        java.lang.String&nbsp;actual,
                        java.util.List&lt;<a href="TestFailure.html" title="class in org.gradle.api.tasks.testing">TestFailure</a>&gt;&nbsp;causes)</code></th>
<td class="colLast">
<div class="block">Creates a new TestFailure instance from an assertion failure.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>static <a href="TestFailure.html" title="class in org.gradle.api.tasks.testing">TestFailure</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#fromTestFrameworkFailure-java.lang.Throwable-">fromTestFrameworkFailure</a></span>&#8203;(java.lang.Throwable&nbsp;failure)</code></th>
<td class="colLast">
<div class="block">Creates a new TestFailure instance from a test framework failure.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>static <a href="TestFailure.html" title="class in org.gradle.api.tasks.testing">TestFailure</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#fromTestFrameworkFailure-java.lang.Throwable-java.util.List-">fromTestFrameworkFailure</a></span>&#8203;(java.lang.Throwable&nbsp;failure,
                        java.util.List&lt;<a href="TestFailure.html" title="class in org.gradle.api.tasks.testing">TestFailure</a>&gt;&nbsp;causes)</code></th>
<td class="colLast">
<div class="block">Creates a new TestFailure instance from a test framework failure.</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>abstract java.util.List&lt;<a href="TestFailure.html" title="class in org.gradle.api.tasks.testing">TestFailure</a>&gt;</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getCauses--">getCauses</a></span>()</code></th>
<td class="colLast">
<div class="block">Returns the list of causes.</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>abstract <a href="TestFailureDetails.html" title="interface in org.gradle.api.tasks.testing">TestFailureDetails</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getDetails--">getDetails</a></span>()</code></th>
<td class="colLast">
<div class="block">Returns structural information about the failure.</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>abstract java.lang.Throwable</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getRawFailure--">getRawFailure</a></span>()</code></th>
<td class="colLast">
<div class="block">Returns the raw failure.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>clone, equals, finalize, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a name="TestFailure--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>TestFailure</h4>
<pre>public&nbsp;TestFailure()</pre>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="getCauses--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCauses</h4>
<pre class="methodSignature">public abstract&nbsp;java.util.List&lt;<a href="TestFailure.html" title="class in org.gradle.api.tasks.testing">TestFailure</a>&gt;&nbsp;getCauses()</pre>
<div class="block">Returns the list of causes.
 <p>
 The result is typically non-empty for multi-assertion failures, e.g. for <code>org.test4j.MultipleFailuresError</code>, where the individual failures are in the returned list.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the cause failures.</dd>
</dl>
</li>
</ul>
<a name="getRawFailure--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getRawFailure</h4>
<pre class="methodSignature">public abstract&nbsp;java.lang.Throwable&nbsp;getRawFailure()</pre>
<div class="block">Returns the raw failure.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the raw failure</dd>
</dl>
</li>
</ul>
<a name="getDetails--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getDetails</h4>
<pre class="methodSignature">public abstract&nbsp;<a href="TestFailureDetails.html" title="interface in org.gradle.api.tasks.testing">TestFailureDetails</a>&nbsp;getDetails()</pre>
<div class="block">Returns structural information about the failure.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the failure structure</dd>
</dl>
</li>
</ul>
<a name="fromTestAssertionFailure-java.lang.Throwable-java.lang.String-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>fromTestAssertionFailure</h4>
<pre class="methodSignature">public static&nbsp;<a href="TestFailure.html" title="class in org.gradle.api.tasks.testing">TestFailure</a>&nbsp;fromTestAssertionFailure&#8203;(java.lang.Throwable&nbsp;failure,
                                                   java.lang.String&nbsp;expected,
                                                   java.lang.String&nbsp;actual)</pre>
<div class="block">Creates a new TestFailure instance from an assertion failure.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>failure</code> - the assertion failure</dd>
<dd><code>expected</code> - the expected value for the failure; can be <code>null</code></dd>
<dd><code>actual</code> - the actual value for the failure; can be <code>null</code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the new instance</dd>
</dl>
</li>
</ul>
<a name="fromTestAssertionFailure-java.lang.Throwable-java.lang.String-java.lang.String-java.util.List-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>fromTestAssertionFailure</h4>
<pre class="methodSignature">public static&nbsp;<a href="TestFailure.html" title="class in org.gradle.api.tasks.testing">TestFailure</a>&nbsp;fromTestAssertionFailure&#8203;(java.lang.Throwable&nbsp;failure,
                                                   java.lang.String&nbsp;expected,
                                                   java.lang.String&nbsp;actual,
                                                   java.util.List&lt;<a href="TestFailure.html" title="class in org.gradle.api.tasks.testing">TestFailure</a>&gt;&nbsp;causes)</pre>
<div class="block">Creates a new TestFailure instance from an assertion failure.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>failure</code> - the assertion failure</dd>
<dd><code>expected</code> - the expected value for the failure; can be <code>null</code></dd>
<dd><code>actual</code> - the actual value for the failure; can be <code>null</code></dd>
<dd><code>causes</code> - the list of cause failures; can be <code>null</code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the new instance</dd>
</dl>
</li>
</ul>
<a name="fromFileComparisonFailure-java.lang.Throwable-java.lang.String-java.lang.String-byte:A-byte:A-java.util.List-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>fromFileComparisonFailure</h4>
<pre class="methodSignature">public static&nbsp;<a href="TestFailure.html" title="class in org.gradle.api.tasks.testing">TestFailure</a>&nbsp;fromFileComparisonFailure&#8203;(java.lang.Throwable&nbsp;failure,
                                                    java.lang.String&nbsp;expected,
                                                    java.lang.String&nbsp;actual,
                                                    byte[]&nbsp;expectedContent,
                                                    byte[]&nbsp;actualContent,
                                                    java.util.List&lt;<a href="TestFailure.html" title="class in org.gradle.api.tasks.testing">TestFailure</a>&gt;&nbsp;causes)</pre>
<div class="block">Todo</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>8.3</dd>
</dl>
</li>
</ul>
<a name="fromTestFrameworkFailure-java.lang.Throwable-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>fromTestFrameworkFailure</h4>
<pre class="methodSignature">public static&nbsp;<a href="TestFailure.html" title="class in org.gradle.api.tasks.testing">TestFailure</a>&nbsp;fromTestFrameworkFailure&#8203;(java.lang.Throwable&nbsp;failure)</pre>
<div class="block">Creates a new TestFailure instance from a test framework failure.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>failure</code> - the failure</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the new instance</dd>
</dl>
</li>
</ul>
<a name="fromTestFrameworkFailure-java.lang.Throwable-java.util.List-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>fromTestFrameworkFailure</h4>
<pre class="methodSignature">public static&nbsp;<a href="TestFailure.html" title="class in org.gradle.api.tasks.testing">TestFailure</a>&nbsp;fromTestFrameworkFailure&#8203;(java.lang.Throwable&nbsp;failure,
                                                   java.util.List&lt;<a href="TestFailure.html" title="class in org.gradle.api.tasks.testing">TestFailure</a>&gt;&nbsp;causes)</pre>
<div class="block">Creates a new TestFailure instance from a test framework failure.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>failure</code> - the failure</dd>
<dd><code>causes</code> - the list of cause failures; can be <code>null</code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the new instance</dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../../allclasses.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
