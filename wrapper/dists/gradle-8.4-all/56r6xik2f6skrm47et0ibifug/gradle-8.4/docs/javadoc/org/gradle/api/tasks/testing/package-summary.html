<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc -->
<title>org.gradle.api.tasks.testing (Gradle API 8.4)</title>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<link rel="stylesheet" type="text/css" href="../../../../../javadoc.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../jquery/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
<script type="text/javascript" src="../../../../../jquery/jszip/dist/jszip.min.js"></script>
<script type="text/javascript" src="../../../../../jquery/jszip-utils/dist/jszip-utils.min.js"></script>
<!--[if IE]>
<script type="text/javascript" src="../../../../../jquery/jszip-utils/dist/jszip-utils-ie.min.js"></script>
<![endif]-->
<script type="text/javascript" src="../../../../../jquery/jquery-3.6.0.min.js"></script>
<script type="text/javascript" src="../../../../../jquery/jquery-ui.min.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="org.gradle.api.tasks.testing (Gradle API 8.4)";
        }
    }
    catch(err) {
    }
//-->
var pathtoroot = "../../../../../";
var useModuleDirectories = false;
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="fixedNav">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../index.html">Overview</a></li>
<li class="navBarCell1Rev">Package</li>
<li>Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../../allclasses.html">All&nbsp;Classes</a></li>
</ul>
<ul class="navListSearch">
<li><label for="search">SEARCH:</label>
<input type="text" id="search" value="search" disabled="disabled">
<input type="reset" id="reset" value="reset" disabled="disabled">
</li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
</div>
<div class="navPadding">&nbsp;</div>
<script type="text/javascript"><!--
$('.navPadding').css('padding-top', $('.fixedNav').css("height"));
//-->
</script>
<div class="header">
<h1 title="Package" class="title">Package&nbsp;org.gradle.api.tasks.testing</h1>
</div>
<div class="contentContainer"><a name="package.description">
<!--   -->
</a>
<div class="block">The unit testing <a href="../../Task.html" title="interface in org.gradle.api"><code>Task</code></a> implementations.</div>
<ul class="blockList">
<li class="blockList">
<table class="typeSummary" summary="Interface Summary table, listing interfaces, and an explanation">
<caption><span>Interface Summary</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Interface</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tbody>
<tr class="altColor">
<th class="colFirst" scope="row"><a href="AggregateTestReport.html" title="interface in org.gradle.api.tasks.testing">AggregateTestReport</a></th>
<td class="colLast">
<div class="block">A container for the inputs of an aggregated test report.</div>
</td>
</tr>
<tr class="rowColor">
<th class="colFirst" scope="row"><a href="JUnitXmlReport.html" title="interface in org.gradle.api.tasks.testing">JUnitXmlReport</a></th>
<td class="colLast">
<div class="block">The JUnit XML files, commonly used to communicate results to CI servers.</div>
</td>
</tr>
<tr class="altColor">
<th class="colFirst" scope="row"><a href="TestDescriptor.html" title="interface in org.gradle.api.tasks.testing">TestDescriptor</a></th>
<td class="colLast">
<div class="block">Describes a test.</div>
</td>
</tr>
<tr class="rowColor">
<th class="colFirst" scope="row"><a href="TestFailureDetails.html" title="interface in org.gradle.api.tasks.testing">TestFailureDetails</a></th>
<td class="colLast">
<div class="block">Contains serializable structural information about a test failure.</div>
</td>
</tr>
<tr class="altColor">
<th class="colFirst" scope="row"><a href="TestFilter.html" title="interface in org.gradle.api.tasks.testing">TestFilter</a></th>
<td class="colLast">
<div class="block">Allows filtering tests for execution.</div>
</td>
</tr>
<tr class="rowColor">
<th class="colFirst" scope="row"><a href="TestListener.html" title="interface in org.gradle.api.tasks.testing">TestListener</a></th>
<td class="colLast">
<div class="block">Interface for listening to test execution.</div>
</td>
</tr>
<tr class="altColor">
<th class="colFirst" scope="row"><a href="TestOutputEvent.html" title="interface in org.gradle.api.tasks.testing">TestOutputEvent</a></th>
<td class="colLast">
<div class="block">Standard output or standard error message logged during the execution of the test</div>
</td>
</tr>
<tr class="rowColor">
<th class="colFirst" scope="row"><a href="TestOutputListener.html" title="interface in org.gradle.api.tasks.testing">TestOutputListener</a></th>
<td class="colLast">
<div class="block">Listens to the output events like printing to standard output or error</div>
</td>
</tr>
<tr class="altColor">
<th class="colFirst" scope="row"><a href="TestResult.html" title="interface in org.gradle.api.tasks.testing">TestResult</a></th>
<td class="colLast">
<div class="block">Describes a test result.</div>
</td>
</tr>
<tr class="rowColor">
<th class="colFirst" scope="row"><a href="TestTaskReports.html" title="interface in org.gradle.api.tasks.testing">TestTaskReports</a></th>
<td class="colLast">
<div class="block">The reports produced by the <a href="Test.html" title="class in org.gradle.api.tasks.testing"><code>Test</code></a> task.</div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList">
<table class="typeSummary" summary="Class Summary table, listing classes, and an explanation">
<caption><span>Class Summary</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Class</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tbody>
<tr class="altColor">
<th class="colFirst" scope="row"><a href="AbstractTestTask.html" title="class in org.gradle.api.tasks.testing">AbstractTestTask</a></th>
<td class="colLast">
<div class="block">Abstract class for all test tasks.</div>
</td>
</tr>
<tr class="rowColor">
<th class="colFirst" scope="row"><a href="Test.html" title="class in org.gradle.api.tasks.testing">Test</a></th>
<td class="colLast">
<div class="block">Executes JUnit (3.8.x, 4.x or 5.x) or TestNG tests.</div>
</td>
</tr>
<tr class="altColor">
<th class="colFirst" scope="row"><a href="TestFailure.html" title="class in org.gradle.api.tasks.testing">TestFailure</a></th>
<td class="colLast">
<div class="block">Describes a test failure.</div>
</td>
</tr>
<tr class="rowColor">
<th class="colFirst" scope="row"><a href="TestFrameworkOptions.html" title="class in org.gradle.api.tasks.testing">TestFrameworkOptions</a></th>
<td class="colLast">
<div class="block">The base class for any test framework specific options.</div>
</td>
</tr>
<tr class="altColor">
<th class="colFirst" scope="row"><a href="TestReport.html" title="class in org.gradle.api.tasks.testing">TestReport</a></th>
<td class="colLast">
<div class="block">Generates an HTML test report from the results of one or more <a href="Test.html" title="class in org.gradle.api.tasks.testing"><code>Test</code></a> tasks.</div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList">
<table class="typeSummary" summary="Enum Summary table, listing enums, and an explanation">
<caption><span>Enum Summary</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Enum</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tbody>
<tr class="altColor">
<th class="colFirst" scope="row"><a href="TestOutputEvent.Destination.html" title="enum in org.gradle.api.tasks.testing">TestOutputEvent.Destination</a></th>
<td class="colLast">
<div class="block">Destination of the message</div>
</td>
</tr>
<tr class="rowColor">
<th class="colFirst" scope="row"><a href="TestResult.ResultType.html" title="enum in org.gradle.api.tasks.testing">TestResult.ResultType</a></th>
<td class="colLast">
<div class="block">The final status of a test.</div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList">
<table class="typeSummary" summary="Exception Summary table, listing exceptions, and an explanation">
<caption><span>Exception Summary</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Exception</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tbody>
<tr class="altColor">
<th class="colFirst" scope="row"><a href="TestExecutionException.html" title="class in org.gradle.api.tasks.testing">TestExecutionException</a></th>
<td class="colLast">
<div class="block">A <code>TestExecutionException</code> is thrown when no tests can be found that match the specified test filters.</div>
</td>
</tr>
</tbody>
</table>
</li>
</ul>
</div>
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../index.html">Overview</a></li>
<li class="navBarCell1Rev">Package</li>
<li>Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../../allclasses.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
