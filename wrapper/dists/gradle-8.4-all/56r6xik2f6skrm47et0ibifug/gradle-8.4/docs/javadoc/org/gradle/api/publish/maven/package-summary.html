<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc -->
<title>org.gradle.api.publish.maven (Gradle API 8.4)</title>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<link rel="stylesheet" type="text/css" href="../../../../../javadoc.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../jquery/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
<script type="text/javascript" src="../../../../../jquery/jszip/dist/jszip.min.js"></script>
<script type="text/javascript" src="../../../../../jquery/jszip-utils/dist/jszip-utils.min.js"></script>
<!--[if IE]>
<script type="text/javascript" src="../../../../../jquery/jszip-utils/dist/jszip-utils-ie.min.js"></script>
<![endif]-->
<script type="text/javascript" src="../../../../../jquery/jquery-3.6.0.min.js"></script>
<script type="text/javascript" src="../../../../../jquery/jquery-ui.min.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="org.gradle.api.publish.maven (Gradle API 8.4)";
        }
    }
    catch(err) {
    }
//-->
var pathtoroot = "../../../../../";
var useModuleDirectories = false;
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="fixedNav">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../index.html">Overview</a></li>
<li class="navBarCell1Rev">Package</li>
<li>Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../../allclasses.html">All&nbsp;Classes</a></li>
</ul>
<ul class="navListSearch">
<li><label for="search">SEARCH:</label>
<input type="text" id="search" value="search" disabled="disabled">
<input type="reset" id="reset" value="reset" disabled="disabled">
</li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
</div>
<div class="navPadding">&nbsp;</div>
<script type="text/javascript"><!--
$('.navPadding').css('padding-top', $('.fixedNav').css("height"));
//-->
</script>
<div class="header">
<h1 title="Package" class="title">Package&nbsp;org.gradle.api.publish.maven</h1>
</div>
<div class="contentContainer"><a name="package.description">
<!--   -->
</a>
<div class="block">Types that deal with publishing in the Maven format.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>1.4</dd>
</dl>
<ul class="blockList">
<li class="blockList">
<table class="typeSummary" summary="Interface Summary table, listing interfaces, and an explanation">
<caption><span>Interface Summary</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Interface</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tbody>
<tr class="altColor">
<th class="colFirst" scope="row"><a href="MavenArtifact.html" title="interface in org.gradle.api.publish.maven">MavenArtifact</a></th>
<td class="colLast">
<div class="block">An artifact published as part of a <a href="MavenPublication.html" title="interface in org.gradle.api.publish.maven"><code>MavenPublication</code></a>.</div>
</td>
</tr>
<tr class="rowColor">
<th class="colFirst" scope="row"><a href="MavenArtifactSet.html" title="interface in org.gradle.api.publish.maven">MavenArtifactSet</a></th>
<td class="colLast">
<div class="block">A Collection of <a href="MavenArtifact.html" title="interface in org.gradle.api.publish.maven"><code>MavenArtifact</code></a>s to be included in a <a href="MavenPublication.html" title="interface in org.gradle.api.publish.maven"><code>MavenPublication</code></a>.</div>
</td>
</tr>
<tr class="altColor">
<th class="colFirst" scope="row"><a href="MavenDependency.html" title="interface in org.gradle.api.publish.maven">MavenDependency</a></th>
<td class="colLast">Deprecated.
<div class="deprecationComment">This type is not referenced by any other public API classes.</div>
</td>
</tr>
<tr class="rowColor">
<th class="colFirst" scope="row"><a href="MavenPom.html" title="interface in org.gradle.api.publish.maven">MavenPom</a></th>
<td class="colLast">
<div class="block">The POM for a Maven publication.</div>
</td>
</tr>
<tr class="altColor">
<th class="colFirst" scope="row"><a href="MavenPomCiManagement.html" title="interface in org.gradle.api.publish.maven">MavenPomCiManagement</a></th>
<td class="colLast">
<div class="block">The CI management system of a Maven publication.</div>
</td>
</tr>
<tr class="rowColor">
<th class="colFirst" scope="row"><a href="MavenPomContributor.html" title="interface in org.gradle.api.publish.maven">MavenPomContributor</a></th>
<td class="colLast">
<div class="block">A contributor of a Maven publication.</div>
</td>
</tr>
<tr class="altColor">
<th class="colFirst" scope="row"><a href="MavenPomContributorSpec.html" title="interface in org.gradle.api.publish.maven">MavenPomContributorSpec</a></th>
<td class="colLast">
<div class="block">Allows to add contributors of a Maven publication.</div>
</td>
</tr>
<tr class="rowColor">
<th class="colFirst" scope="row"><a href="MavenPomDeveloper.html" title="interface in org.gradle.api.publish.maven">MavenPomDeveloper</a></th>
<td class="colLast">
<div class="block">A developer of a Maven publication.</div>
</td>
</tr>
<tr class="altColor">
<th class="colFirst" scope="row"><a href="MavenPomDeveloperSpec.html" title="interface in org.gradle.api.publish.maven">MavenPomDeveloperSpec</a></th>
<td class="colLast">
<div class="block">Allows to add developers to a Maven publication.</div>
</td>
</tr>
<tr class="rowColor">
<th class="colFirst" scope="row"><a href="MavenPomDistributionManagement.html" title="interface in org.gradle.api.publish.maven">MavenPomDistributionManagement</a></th>
<td class="colLast">
<div class="block">The distribution management configuration of a Maven publication.</div>
</td>
</tr>
<tr class="altColor">
<th class="colFirst" scope="row"><a href="MavenPomIssueManagement.html" title="interface in org.gradle.api.publish.maven">MavenPomIssueManagement</a></th>
<td class="colLast">
<div class="block">The issue management system of a Maven publication.</div>
</td>
</tr>
<tr class="rowColor">
<th class="colFirst" scope="row"><a href="MavenPomLicense.html" title="interface in org.gradle.api.publish.maven">MavenPomLicense</a></th>
<td class="colLast">
<div class="block">A license of a Maven publication.</div>
</td>
</tr>
<tr class="altColor">
<th class="colFirst" scope="row"><a href="MavenPomLicenseSpec.html" title="interface in org.gradle.api.publish.maven">MavenPomLicenseSpec</a></th>
<td class="colLast">
<div class="block">Allows to add licenses to a Maven publication.</div>
</td>
</tr>
<tr class="rowColor">
<th class="colFirst" scope="row"><a href="MavenPomMailingList.html" title="interface in org.gradle.api.publish.maven">MavenPomMailingList</a></th>
<td class="colLast">
<div class="block">A mailing list of a Maven publication.</div>
</td>
</tr>
<tr class="altColor">
<th class="colFirst" scope="row"><a href="MavenPomMailingListSpec.html" title="interface in org.gradle.api.publish.maven">MavenPomMailingListSpec</a></th>
<td class="colLast">
<div class="block">Allows to add mailing lists to a Maven publication.</div>
</td>
</tr>
<tr class="rowColor">
<th class="colFirst" scope="row"><a href="MavenPomOrganization.html" title="interface in org.gradle.api.publish.maven">MavenPomOrganization</a></th>
<td class="colLast">
<div class="block">The organization of a Maven publication.</div>
</td>
</tr>
<tr class="altColor">
<th class="colFirst" scope="row"><a href="MavenPomRelocation.html" title="interface in org.gradle.api.publish.maven">MavenPomRelocation</a></th>
<td class="colLast">
<div class="block">The relocation information of a Maven publication that has been moved
 to a new group and/or artifact ID.</div>
</td>
</tr>
<tr class="rowColor">
<th class="colFirst" scope="row"><a href="MavenPomScm.html" title="interface in org.gradle.api.publish.maven">MavenPomScm</a></th>
<td class="colLast">
<div class="block">The SCM (source control management) of a Maven publication.</div>
</td>
</tr>
<tr class="altColor">
<th class="colFirst" scope="row"><a href="MavenPublication.html" title="interface in org.gradle.api.publish.maven">MavenPublication</a></th>
<td class="colLast">
<div class="block">A <code>MavenPublication</code> is the representation/configuration of how Gradle should publish something in Maven format.</div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList">
<table class="typeSummary" summary="Exception Summary table, listing exceptions, and an explanation">
<caption><span>Exception Summary</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Exception</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tbody>
<tr class="altColor">
<th class="colFirst" scope="row"><a href="InvalidMavenPublicationException.html" title="class in org.gradle.api.publish.maven">InvalidMavenPublicationException</a></th>
<td class="colLast">
<div class="block">Thrown when attempting to publish with an invalid <a href="MavenPublication.html" title="interface in org.gradle.api.publish.maven"><code>MavenPublication</code></a>.</div>
</td>
</tr>
</tbody>
</table>
</li>
</ul>
</div>
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../index.html">Overview</a></li>
<li class="navBarCell1Rev">Package</li>
<li>Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../../allclasses.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
