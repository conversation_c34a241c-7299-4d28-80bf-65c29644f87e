<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc -->
<title>SourceDirectorySet (Gradle API 8.4)</title>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<link rel="stylesheet" type="text/css" href="../../../../javadoc.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../jquery/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
<script type="text/javascript" src="../../../../jquery/jszip/dist/jszip.min.js"></script>
<script type="text/javascript" src="../../../../jquery/jszip-utils/dist/jszip-utils.min.js"></script>
<!--[if IE]>
<script type="text/javascript" src="../../../../jquery/jszip-utils/dist/jszip-utils-ie.min.js"></script>
<![endif]-->
<script type="text/javascript" src="../../../../jquery/jquery-3.6.0.min.js"></script>
<script type="text/javascript" src="../../../../jquery/jquery-ui.min.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="SourceDirectorySet (Gradle API 8.4)";
        }
    }
    catch(err) {
    }
//-->
var data = {"i0":6,"i1":6,"i2":6,"i3":6,"i4":6,"i5":6,"i6":6,"i7":6,"i8":6,"i9":6,"i10":6,"i11":6};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],4:["t3","Abstract Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
var pathtoroot = "../../../../";
var useModuleDirectories = false;
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="fixedNav">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses.html">All&nbsp;Classes</a></li>
</ul>
<ul class="navListSearch">
<li><label for="search">SEARCH:</label>
<input type="text" id="search" value="search" disabled="disabled">
<input type="reset" id="reset" value="reset" disabled="disabled">
</li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li><a href="#nested.class.summary">Nested</a>&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
</div>
<div class="navPadding">&nbsp;</div>
<script type="text/javascript"><!--
$('.navPadding').css('padding-top', $('.fixedNav').css("height"));
//-->
</script>
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle"><span class="packageLabelInType">Package</span>&nbsp;<a href="package-summary.html">org.gradle.api.file</a></div>
<h2 title="Interface SourceDirectorySet" class="title">Interface SourceDirectorySet</h2>
</div>
<div class="contentContainer">
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Superinterfaces:</dt>
<dd><code><a href="../tasks/AntBuilderAware.html" title="interface in org.gradle.api.tasks">AntBuilderAware</a></code>, <code><a href="../Buildable.html" title="interface in org.gradle.api">Buildable</a></code>, <code><a href="../Describable.html" title="interface in org.gradle.api">Describable</a></code>, <code><a href="FileCollection.html" title="interface in org.gradle.api.file">FileCollection</a></code>, <code><a href="FileTree.html" title="interface in org.gradle.api.file">FileTree</a></code>, <code>java.lang.Iterable&lt;java.io.File&gt;</code>, <code><a href="../Named.html" title="interface in org.gradle.api">Named</a></code>, <code><a href="../tasks/util/PatternFilterable.html" title="interface in org.gradle.api.tasks.util">PatternFilterable</a></code></dd>
</dl>
<dl>
<dt>All Known Subinterfaces:</dt>
<dd><code><a href="../plugins/antlr/AntlrSourceDirectorySet.html" title="interface in org.gradle.api.plugins.antlr">AntlrSourceDirectorySet</a></code>, <code><a href="../tasks/GroovySourceDirectorySet.html" title="interface in org.gradle.api.tasks">GroovySourceDirectorySet</a></code>, <code><a href="../tasks/ScalaSourceDirectorySet.html" title="interface in org.gradle.api.tasks">ScalaSourceDirectorySet</a></code></dd>
</dl>
<hr>
<pre>public interface <span class="typeNameLabel">SourceDirectorySet</span>
extends <a href="FileTree.html" title="interface in org.gradle.api.file">FileTree</a>, <a href="../tasks/util/PatternFilterable.html" title="interface in org.gradle.api.tasks.util">PatternFilterable</a>, <a href="../Named.html" title="interface in org.gradle.api">Named</a>, <a href="../Describable.html" title="interface in org.gradle.api">Describable</a></pre>
<div class="block"><p>A <code>SourceDirectorySet</code> represents a set of source files composed from a set of source directories, along
 with associated include and exclude patterns.</p>

 <p><code>SourceDirectorySet</code> extends <a href="FileTree.html" title="interface in org.gradle.api.file"><code>FileTree</code></a>. The contents of the file tree represent the source files of this set, arranged in a hierarchy. The file tree is live and reflects changes to the source directories and their contents.</p>

 <p>You can create an instance of <code>SourceDirectorySet</code> using the <a href="../model/ObjectFactory.html#sourceDirectorySet-java.lang.String-java.lang.String-"><code>ObjectFactory.sourceDirectorySet(String, String)</code></a> method.</p></div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== NESTED CLASS SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="nested.class.summary">
<!--   -->
</a>
<h3>Nested Class Summary</h3>
<ul class="blockList">
<li class="blockList"><a name="nested.classes.inherited.from.class.org.gradle.api.file.FileCollection">
<!--   -->
</a>
<h3>Nested classes/interfaces inherited from interface&nbsp;org.gradle.api.file.<a href="FileCollection.html" title="interface in org.gradle.api.file">FileCollection</a></h3>
<code><a href="FileCollection.AntType.html" title="enum in org.gradle.api.file">FileCollection.AntType</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="nested.classes.inherited.from.class.org.gradle.api.Named">
<!--   -->
</a>
<h3>Nested classes/interfaces inherited from interface&nbsp;org.gradle.api.<a href="../Named.html" title="interface in org.gradle.api">Named</a></h3>
<code><a href="../Named.Namer.html" title="class in org.gradle.api">Named.Namer</a></code></li>
</ul>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t3" class="tableTab"><span><a href="javascript:show(4);">Abstract Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colSecond" scope="col">Method</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>&lt;T extends <a href="../Task.html" title="interface in org.gradle.api">Task</a>&gt;<br>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#compiledBy-org.gradle.api.tasks.TaskProvider-java.util.function.Function-">compiledBy</a></span>&#8203;(<a href="../tasks/TaskProvider.html" title="interface in org.gradle.api.tasks">TaskProvider</a>&lt;T&gt;&nbsp;taskProvider,
          java.util.function.Function&lt;T,&#8203;<a href="DirectoryProperty.html" title="interface in org.gradle.api.file">DirectoryProperty</a>&gt;&nbsp;mapping)</code></th>
<td class="colLast">
<div class="block">Define the task responsible for processing the source.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code><a href="../provider/Provider.html" title="interface in org.gradle.api.provider">Provider</a>&lt;<a href="Directory.html" title="interface in org.gradle.api.file">Directory</a>&gt;</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getClassesDirectory--">getClassesDirectory</a></span>()</code></th>
<td class="colLast">
<div class="block">Returns the directory property that is bound to the task that produces the output via <a href="#compiledBy-org.gradle.api.tasks.TaskProvider-java.util.function.Function-"><code>compiledBy(TaskProvider, Function)</code></a>.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code><a href="DirectoryProperty.html" title="interface in org.gradle.api.file">DirectoryProperty</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getDestinationDirectory--">getDestinationDirectory</a></span>()</code></th>
<td class="colLast">
<div class="block">Configure the directory to assemble the compiled classes into.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code><a href="../tasks/util/PatternFilterable.html" title="interface in org.gradle.api.tasks.util">PatternFilterable</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getFilter--">getFilter</a></span>()</code></th>
<td class="colLast">
<div class="block">Returns the filter used to select the source from the source directories.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getName--">getName</a></span>()</code></th>
<td class="colLast">
<div class="block">A concise name for the source directory set (typically used to identify it in a collection).</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code><a href="FileCollection.html" title="interface in org.gradle.api.file">FileCollection</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getSourceDirectories--">getSourceDirectories</a></span>()</code></th>
<td class="colLast">
<div class="block">Returns the source directories that make up this set, represented as a <a href="FileCollection.html" title="interface in org.gradle.api.file"><code>FileCollection</code></a>.</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>java.util.Set&lt;java.io.File&gt;</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getSrcDirs--">getSrcDirs</a></span>()</code></th>
<td class="colLast">
<div class="block">Returns the source directories that make up this set.</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>java.util.Set&lt;<a href="DirectoryTree.html" title="interface in org.gradle.api.file">DirectoryTree</a>&gt;</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getSrcDirTrees--">getSrcDirTrees</a></span>()</code></th>
<td class="colLast">
<div class="block">Returns the source directory trees that make up this set.</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code><a href="SourceDirectorySet.html" title="interface in org.gradle.api.file">SourceDirectorySet</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setSrcDirs-java.lang.Iterable-">setSrcDirs</a></span>&#8203;(java.lang.Iterable&lt;?&gt;&nbsp;srcPaths)</code></th>
<td class="colLast">
<div class="block">Sets the source directories for this set.</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code><a href="SourceDirectorySet.html" title="interface in org.gradle.api.file">SourceDirectorySet</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#source-org.gradle.api.file.SourceDirectorySet-">source</a></span>&#8203;(<a href="SourceDirectorySet.html" title="interface in org.gradle.api.file">SourceDirectorySet</a>&nbsp;source)</code></th>
<td class="colLast">
<div class="block">Adds the given source to this set.</div>
</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code><a href="SourceDirectorySet.html" title="interface in org.gradle.api.file">SourceDirectorySet</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#srcDir-java.lang.Object-">srcDir</a></span>&#8203;(java.lang.Object&nbsp;srcPath)</code></th>
<td class="colLast">
<div class="block">Adds the given source directory to this set.</div>
</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code><a href="SourceDirectorySet.html" title="interface in org.gradle.api.file">SourceDirectorySet</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#srcDirs-java.lang.Object...-">srcDirs</a></span>&#8203;(java.lang.Object...&nbsp;srcPaths)</code></th>
<td class="colLast">
<div class="block">Adds the given source directories to this set.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.org.gradle.api.Buildable">
<!--   -->
</a>
<h3>Methods inherited from interface&nbsp;org.gradle.api.<a href="../Buildable.html" title="interface in org.gradle.api">Buildable</a></h3>
<code><a href="../Buildable.html#getBuildDependencies--">getBuildDependencies</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.org.gradle.api.Describable">
<!--   -->
</a>
<h3>Methods inherited from interface&nbsp;org.gradle.api.<a href="../Describable.html" title="interface in org.gradle.api">Describable</a></h3>
<code><a href="../Describable.html#getDisplayName--">getDisplayName</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.org.gradle.api.file.FileCollection">
<!--   -->
</a>
<h3>Methods inherited from interface&nbsp;org.gradle.api.file.<a href="FileCollection.html" title="interface in org.gradle.api.file">FileCollection</a></h3>
<code><a href="FileCollection.html#addToAntBuilder-java.lang.Object-java.lang.String-">addToAntBuilder</a>, <a href="FileCollection.html#addToAntBuilder-java.lang.Object-java.lang.String-org.gradle.api.file.FileCollection.AntType-">addToAntBuilder</a>, <a href="FileCollection.html#contains-java.io.File-">contains</a>, <a href="FileCollection.html#filter-groovy.lang.Closure-">filter</a>, <a href="FileCollection.html#filter-org.gradle.api.specs.Spec-">filter</a>, <a href="FileCollection.html#getAsPath--">getAsPath</a>, <a href="FileCollection.html#getElements--">getElements</a>, <a href="FileCollection.html#getSingleFile--">getSingleFile</a>, <a href="FileCollection.html#isEmpty--">isEmpty</a>, <a href="FileCollection.html#minus-org.gradle.api.file.FileCollection-">minus</a>, <a href="FileCollection.html#plus-org.gradle.api.file.FileCollection-">plus</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.org.gradle.api.file.FileTree">
<!--   -->
</a>
<h3>Methods inherited from interface&nbsp;org.gradle.api.file.<a href="FileTree.html" title="interface in org.gradle.api.file">FileTree</a></h3>
<code><a href="FileTree.html#getAsFileTree--">getAsFileTree</a>, <a href="FileTree.html#getFiles--">getFiles</a>, <a href="FileTree.html#matching-groovy.lang.Closure-">matching</a>, <a href="FileTree.html#matching-org.gradle.api.Action-">matching</a>, <a href="FileTree.html#matching-org.gradle.api.tasks.util.PatternFilterable-">matching</a>, <a href="FileTree.html#plus-org.gradle.api.file.FileTree-">plus</a>, <a href="FileTree.html#visit-groovy.lang.Closure-">visit</a>, <a href="FileTree.html#visit-org.gradle.api.Action-">visit</a>, <a href="FileTree.html#visit-org.gradle.api.file.FileVisitor-">visit</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Iterable">
<!--   -->
</a>
<h3>Methods inherited from interface&nbsp;java.lang.Iterable</h3>
<code>forEach, iterator, spliterator</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.org.gradle.api.tasks.util.PatternFilterable">
<!--   -->
</a>
<h3>Methods inherited from interface&nbsp;org.gradle.api.tasks.util.<a href="../tasks/util/PatternFilterable.html" title="interface in org.gradle.api.tasks.util">PatternFilterable</a></h3>
<code><a href="../tasks/util/PatternFilterable.html#exclude-groovy.lang.Closure-">exclude</a>, <a href="../tasks/util/PatternFilterable.html#exclude-java.lang.Iterable-">exclude</a>, <a href="../tasks/util/PatternFilterable.html#exclude-java.lang.String...-">exclude</a>, <a href="../tasks/util/PatternFilterable.html#exclude-org.gradle.api.specs.Spec-">exclude</a>, <a href="../tasks/util/PatternFilterable.html#getExcludes--">getExcludes</a>, <a href="../tasks/util/PatternFilterable.html#getIncludes--">getIncludes</a>, <a href="../tasks/util/PatternFilterable.html#include-groovy.lang.Closure-">include</a>, <a href="../tasks/util/PatternFilterable.html#include-java.lang.Iterable-">include</a>, <a href="../tasks/util/PatternFilterable.html#include-java.lang.String...-">include</a>, <a href="../tasks/util/PatternFilterable.html#include-org.gradle.api.specs.Spec-">include</a>, <a href="../tasks/util/PatternFilterable.html#setExcludes-java.lang.Iterable-">setExcludes</a>, <a href="../tasks/util/PatternFilterable.html#setIncludes-java.lang.Iterable-">setIncludes</a></code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="getName--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getName</h4>
<pre class="methodSignature">java.lang.String&nbsp;getName()</pre>
<div class="block">A concise name for the source directory set (typically used to identify it in a collection).</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../Named.html#getName--">getName</a></code>&nbsp;in interface&nbsp;<code><a href="../Named.html" title="interface in org.gradle.api">Named</a></code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The name. Never null.</dd>
</dl>
</li>
</ul>
<a name="srcDir-java.lang.Object-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>srcDir</h4>
<pre class="methodSignature"><a href="SourceDirectorySet.html" title="interface in org.gradle.api.file">SourceDirectorySet</a>&nbsp;srcDir&#8203;(java.lang.Object&nbsp;srcPath)</pre>
<div class="block">Adds the given source directory to this set. The given directory does not need to exist. Directories that do not exist are ignored.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>srcPath</code> - The source directory. This is evaluated as per <a href="../Project.html#files-java.lang.Object...-"><code>Project.files(Object...)</code></a></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>this</dd>
</dl>
</li>
</ul>
<a name="srcDirs-java.lang.Object...-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>srcDirs</h4>
<pre class="methodSignature"><a href="SourceDirectorySet.html" title="interface in org.gradle.api.file">SourceDirectorySet</a>&nbsp;srcDirs&#8203;(java.lang.Object...&nbsp;srcPaths)</pre>
<div class="block">Adds the given source directories to this set. The given directories do not need to exist. Directories that do not exist are ignored.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>srcPaths</code> - The source directories. These are evaluated as per <a href="../Project.html#files-java.lang.Object...-"><code>Project.files(Object...)</code></a></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>this</dd>
</dl>
</li>
</ul>
<a name="getSrcDirs--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getSrcDirs</h4>
<pre class="methodSignature">java.util.Set&lt;java.io.File&gt;&nbsp;getSrcDirs()</pre>
<div class="block">Returns the source directories that make up this set. Does not filter source directories that do not exist.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The source directories. Returns an empty set when this set contains no source directories.</dd>
</dl>
</li>
</ul>
<a name="setSrcDirs-java.lang.Iterable-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setSrcDirs</h4>
<pre class="methodSignature"><a href="SourceDirectorySet.html" title="interface in org.gradle.api.file">SourceDirectorySet</a>&nbsp;setSrcDirs&#8203;(java.lang.Iterable&lt;?&gt;&nbsp;srcPaths)</pre>
<div class="block">Sets the source directories for this set.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>srcPaths</code> - The source directories. These are evaluated as per <a href="../Project.html#files-java.lang.Object...-"><code>Project.files(Object...)</code></a></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>this</dd>
</dl>
</li>
</ul>
<a name="source-org.gradle.api.file.SourceDirectorySet-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>source</h4>
<pre class="methodSignature"><a href="SourceDirectorySet.html" title="interface in org.gradle.api.file">SourceDirectorySet</a>&nbsp;source&#8203;(<a href="SourceDirectorySet.html" title="interface in org.gradle.api.file">SourceDirectorySet</a>&nbsp;source)</pre>
<div class="block">Adds the given source to this set.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>source</code> - The source to add.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>this</dd>
</dl>
</li>
</ul>
<a name="getSourceDirectories--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getSourceDirectories</h4>
<pre class="methodSignature"><a href="FileCollection.html" title="interface in org.gradle.api.file">FileCollection</a>&nbsp;getSourceDirectories()</pre>
<div class="block">Returns the source directories that make up this set, represented as a <a href="FileCollection.html" title="interface in org.gradle.api.file"><code>FileCollection</code></a>. Does not filter source directories that do not exist.
 Generally, it is preferable to use this method instead of <a href="#getSrcDirs--"><code>getSrcDirs()</code></a>, as this method does not require the source directories to be calculated when it is called. Instead, the source directories are calculated when queried. The return value of this method also maintains dependency information.

 <p>The returned collection is live and reflects changes to this source directory set.</div>
</li>
</ul>
<a name="getSrcDirTrees--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getSrcDirTrees</h4>
<pre class="methodSignature">java.util.Set&lt;<a href="DirectoryTree.html" title="interface in org.gradle.api.file">DirectoryTree</a>&gt;&nbsp;getSrcDirTrees()</pre>
<div class="block">Returns the source directory trees that make up this set. Does not filter source directories that do not exist.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The source directory trees. Returns an empty set when this set contains no source directories.</dd>
</dl>
</li>
</ul>
<a name="getFilter--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getFilter</h4>
<pre class="methodSignature"><a href="../tasks/util/PatternFilterable.html" title="interface in org.gradle.api.tasks.util">PatternFilterable</a>&nbsp;getFilter()</pre>
<div class="block">Returns the filter used to select the source from the source directories. These filter patterns are applied after
 the include and exclude patterns of this source directory set. Generally, the filter patterns are used to
 restrict the contents to certain types of files, eg <code>*.java</code>.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The filter patterns.</dd>
</dl>
</li>
</ul>
<a name="getDestinationDirectory--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getDestinationDirectory</h4>
<pre class="methodSignature"><a href="DirectoryProperty.html" title="interface in org.gradle.api.file">DirectoryProperty</a>&nbsp;getDestinationDirectory()</pre>
<div class="block">Configure the directory to assemble the compiled classes into.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The destination directory property for this set of sources.</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>6.1</dd>
</dl>
</li>
</ul>
<a name="getClassesDirectory--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getClassesDirectory</h4>
<pre class="methodSignature"><a href="../provider/Provider.html" title="interface in org.gradle.api.provider">Provider</a>&lt;<a href="Directory.html" title="interface in org.gradle.api.file">Directory</a>&gt;&nbsp;getClassesDirectory()</pre>
<div class="block">Returns the directory property that is bound to the task that produces the output via <a href="#compiledBy-org.gradle.api.tasks.TaskProvider-java.util.function.Function-"><code>compiledBy(TaskProvider, Function)</code></a>.
 Use this as part of a classpath or input to another task to ensure that the output is created before it is used.

 Note: To define the path of the output folder use <a href="#getDestinationDirectory--"><code>getDestinationDirectory()</code></a></div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The classes directory property for this set of sources.</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>6.1</dd>
</dl>
</li>
</ul>
<a name="compiledBy-org.gradle.api.tasks.TaskProvider-java.util.function.Function-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>compiledBy</h4>
<pre class="methodSignature">&lt;T extends <a href="../Task.html" title="interface in org.gradle.api">Task</a>&gt;&nbsp;void&nbsp;compiledBy&#8203;(<a href="../tasks/TaskProvider.html" title="interface in org.gradle.api.tasks">TaskProvider</a>&lt;T&gt;&nbsp;taskProvider,
                                 java.util.function.Function&lt;T,&#8203;<a href="DirectoryProperty.html" title="interface in org.gradle.api.file">DirectoryProperty</a>&gt;&nbsp;mapping)</pre>
<div class="block">Define the task responsible for processing the source.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>taskProvider</code> - the task responsible for compiling the sources (.e.g. compileJava)</dd>
<dd><code>mapping</code> - a mapping from the task to the task's output directory (e.g. AbstractCompile::getDestinationDirectory)</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>6.1</dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li><a href="#nested.class.summary">Nested</a>&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
