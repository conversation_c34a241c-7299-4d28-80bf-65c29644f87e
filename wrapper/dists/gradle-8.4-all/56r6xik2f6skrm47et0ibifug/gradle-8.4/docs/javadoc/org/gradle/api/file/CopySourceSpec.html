<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc -->
<title>CopySourceSpec (Gradle API 8.4)</title>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<link rel="stylesheet" type="text/css" href="../../../../javadoc.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../jquery/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
<script type="text/javascript" src="../../../../jquery/jszip/dist/jszip.min.js"></script>
<script type="text/javascript" src="../../../../jquery/jszip-utils/dist/jszip-utils.min.js"></script>
<!--[if IE]>
<script type="text/javascript" src="../../../../jquery/jszip-utils/dist/jszip-utils-ie.min.js"></script>
<![endif]-->
<script type="text/javascript" src="../../../../jquery/jquery-3.6.0.min.js"></script>
<script type="text/javascript" src="../../../../jquery/jquery-ui.min.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="CopySourceSpec (Gradle API 8.4)";
        }
    }
    catch(err) {
    }
//-->
var data = {"i0":6,"i1":6,"i2":6};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],4:["t3","Abstract Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
var pathtoroot = "../../../../";
var useModuleDirectories = false;
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="fixedNav">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses.html">All&nbsp;Classes</a></li>
</ul>
<ul class="navListSearch">
<li><label for="search">SEARCH:</label>
<input type="text" id="search" value="search" disabled="disabled">
<input type="reset" id="reset" value="reset" disabled="disabled">
</li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
</div>
<div class="navPadding">&nbsp;</div>
<script type="text/javascript"><!--
$('.navPadding').css('padding-top', $('.fixedNav').css("height"));
//-->
</script>
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle"><span class="packageLabelInType">Package</span>&nbsp;<a href="package-summary.html">org.gradle.api.file</a></div>
<h2 title="Interface CopySourceSpec" class="title">Interface CopySourceSpec</h2>
</div>
<div class="contentContainer">
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Known Subinterfaces:</dt>
<dd><code><a href="CopySpec.html" title="interface in org.gradle.api.file">CopySpec</a></code>, <code><a href="SyncSpec.html" title="interface in org.gradle.api.file">SyncSpec</a></code></dd>
</dl>
<dl>
<dt>All Known Implementing Classes:</dt>
<dd><code><a href="../tasks/bundling/AbstractArchiveTask.html" title="class in org.gradle.api.tasks.bundling">AbstractArchiveTask</a></code>, <code><a href="../tasks/AbstractCopyTask.html" title="class in org.gradle.api.tasks">AbstractCopyTask</a></code>, <code><a href="../tasks/Copy.html" title="class in org.gradle.api.tasks">Copy</a></code>, <code><a href="../../plugins/ear/Ear.html" title="class in org.gradle.plugins.ear">Ear</a></code>, <code><a href="../tasks/bundling/Jar.html" title="class in org.gradle.api.tasks.bundling">Jar</a></code>, <code><a href="../../jvm/tasks/Jar.html" title="class in org.gradle.jvm.tasks">Jar</a></code>, <code><a href="../../language/jvm/tasks/ProcessResources.html" title="class in org.gradle.language.jvm.tasks">ProcessResources</a></code>, <code><a href="../tasks/Sync.html" title="class in org.gradle.api.tasks">Sync</a></code>, <code><a href="../tasks/bundling/Tar.html" title="class in org.gradle.api.tasks.bundling">Tar</a></code>, <code><a href="../tasks/bundling/War.html" title="class in org.gradle.api.tasks.bundling">War</a></code>, <code><a href="../tasks/bundling/Zip.html" title="class in org.gradle.api.tasks.bundling">Zip</a></code></dd>
</dl>
<hr>
<pre>public interface <span class="typeNameLabel">CopySourceSpec</span></pre>
<div class="block">Specifies sources for a file copy.</div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t3" class="tableTab"><span><a href="javascript:show(4);">Abstract Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colSecond" scope="col">Method</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code><a href="CopySourceSpec.html" title="interface in org.gradle.api.file">CopySourceSpec</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#from-java.lang.Object...-">from</a></span>&#8203;(java.lang.Object...&nbsp;sourcePaths)</code></th>
<td class="colLast">
<div class="block">Specifies source files or directories for a copy.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code><a href="CopySourceSpec.html" title="interface in org.gradle.api.file">CopySourceSpec</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#from-java.lang.Object-groovy.lang.Closure-">from</a></span>&#8203;(java.lang.Object&nbsp;sourcePath,
    <a href="https://docs.groovy-lang.org/docs/groovy-3.0.17/html/gapi/groovy/lang/Closure.html?is-external=true" title="class or interface in groovy.lang" class="externalLink">Closure</a>&nbsp;configureClosure)</code></th>
<td class="colLast">
<div class="block">Specifies the source files or directories for a copy and creates a child <code>CopySourceSpec</code>.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code><a href="CopySourceSpec.html" title="interface in org.gradle.api.file">CopySourceSpec</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#from-java.lang.Object-org.gradle.api.Action-">from</a></span>&#8203;(java.lang.Object&nbsp;sourcePath,
    <a href="../Action.html" title="interface in org.gradle.api">Action</a>&lt;? super <a href="CopySpec.html" title="interface in org.gradle.api.file">CopySpec</a>&gt;&nbsp;configureAction)</code></th>
<td class="colLast">
<div class="block">Specifies the source files or directories for a copy and creates a child <code>CopySpec</code>.</div>
</td>
</tr>
</table>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="from-java.lang.Object...-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>from</h4>
<pre class="methodSignature"><a href="CopySourceSpec.html" title="interface in org.gradle.api.file">CopySourceSpec</a>&nbsp;from&#8203;(java.lang.Object...&nbsp;sourcePaths)</pre>
<div class="block">Specifies source files or directories for a copy. The given paths are evaluated as per <a href="../Project.html#files-java.lang.Object...-"><code>Project.files(Object...)</code></a>.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>sourcePaths</code> - Paths to source files for the copy</dd>
</dl>
</li>
</ul>
<a name="from-java.lang.Object-groovy.lang.Closure-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>from</h4>
<pre class="methodSignature"><a href="CopySourceSpec.html" title="interface in org.gradle.api.file">CopySourceSpec</a>&nbsp;from&#8203;(java.lang.Object&nbsp;sourcePath,
                    <a href="https://docs.groovy-lang.org/docs/groovy-3.0.17/html/gapi/groovy/lang/DelegatesTo.html?is-external=true" title="class or interface in groovy.lang" class="externalLink">@DelegatesTo</a>(<a href="CopySpec.html" title="interface in org.gradle.api.file">CopySpec.class</a>)
                    <a href="https://docs.groovy-lang.org/docs/groovy-3.0.17/html/gapi/groovy/lang/Closure.html?is-external=true" title="class or interface in groovy.lang" class="externalLink">Closure</a>&nbsp;configureClosure)</pre>
<div class="block">Specifies the source files or directories for a copy and creates a child <code>CopySourceSpec</code>. The given source
 path is evaluated as per <a href="../Project.html#files-java.lang.Object...-"><code>Project.files(Object...)</code></a> .</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>sourcePath</code> - Path to source for the copy</dd>
<dd><code>configureClosure</code> - closure for configuring the child CopySourceSpec</dd>
</dl>
</li>
</ul>
<a name="from-java.lang.Object-org.gradle.api.Action-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>from</h4>
<pre class="methodSignature"><a href="CopySourceSpec.html" title="interface in org.gradle.api.file">CopySourceSpec</a>&nbsp;from&#8203;(java.lang.Object&nbsp;sourcePath,
                    <a href="../Action.html" title="interface in org.gradle.api">Action</a>&lt;? super <a href="CopySpec.html" title="interface in org.gradle.api.file">CopySpec</a>&gt;&nbsp;configureAction)</pre>
<div class="block">Specifies the source files or directories for a copy and creates a child <code>CopySpec</code>. The given source
 path is evaluated as per <a href="../Project.html#files-java.lang.Object...-"><code>Project.files(Object...)</code></a> .</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>sourcePath</code> - Path to source for the copy</dd>
<dd><code>configureAction</code> - action for configuring the child CopySpec</dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
