<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc -->
<title>org.gradle.api.publish.ivy (Gradle API 8.4)</title>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<link rel="stylesheet" type="text/css" href="../../../../../javadoc.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../jquery/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
<script type="text/javascript" src="../../../../../jquery/jszip/dist/jszip.min.js"></script>
<script type="text/javascript" src="../../../../../jquery/jszip-utils/dist/jszip-utils.min.js"></script>
<!--[if IE]>
<script type="text/javascript" src="../../../../../jquery/jszip-utils/dist/jszip-utils-ie.min.js"></script>
<![endif]-->
<script type="text/javascript" src="../../../../../jquery/jquery-3.6.0.min.js"></script>
<script type="text/javascript" src="../../../../../jquery/jquery-ui.min.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="org.gradle.api.publish.ivy (Gradle API 8.4)";
        }
    }
    catch(err) {
    }
//-->
var pathtoroot = "../../../../../";
var useModuleDirectories = false;
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="fixedNav">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../index.html">Overview</a></li>
<li class="navBarCell1Rev">Package</li>
<li>Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../../allclasses.html">All&nbsp;Classes</a></li>
</ul>
<ul class="navListSearch">
<li><label for="search">SEARCH:</label>
<input type="text" id="search" value="search" disabled="disabled">
<input type="reset" id="reset" value="reset" disabled="disabled">
</li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
</div>
<div class="navPadding">&nbsp;</div>
<script type="text/javascript"><!--
$('.navPadding').css('padding-top', $('.fixedNav').css("height"));
//-->
</script>
<div class="header">
<h1 title="Package" class="title">Package&nbsp;org.gradle.api.publish.ivy</h1>
</div>
<div class="contentContainer"><a name="package.description">
<!--   -->
</a>
<div class="block">Types that deal with publishing in the Ivy format.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>1.3</dd>
</dl>
<ul class="blockList">
<li class="blockList">
<table class="typeSummary" summary="Interface Summary table, listing interfaces, and an explanation">
<caption><span>Interface Summary</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Interface</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tbody>
<tr class="altColor">
<th class="colFirst" scope="row"><a href="IvyArtifact.html" title="interface in org.gradle.api.publish.ivy">IvyArtifact</a></th>
<td class="colLast">
<div class="block">An artifact published as part of a <a href="IvyPublication.html" title="interface in org.gradle.api.publish.ivy"><code>IvyPublication</code></a>.</div>
</td>
</tr>
<tr class="rowColor">
<th class="colFirst" scope="row"><a href="IvyArtifactSet.html" title="interface in org.gradle.api.publish.ivy">IvyArtifactSet</a></th>
<td class="colLast">
<div class="block">A Collection of <a href="IvyArtifact.html" title="interface in org.gradle.api.publish.ivy"><code>IvyArtifact</code></a>s to be included in an <a href="IvyPublication.html" title="interface in org.gradle.api.publish.ivy"><code>IvyPublication</code></a>.</div>
</td>
</tr>
<tr class="altColor">
<th class="colFirst" scope="row"><a href="IvyConfiguration.html" title="interface in org.gradle.api.publish.ivy">IvyConfiguration</a></th>
<td class="colLast">
<div class="block">A configuration included in an <a href="IvyPublication.html" title="interface in org.gradle.api.publish.ivy"><code>IvyPublication</code></a>, which will be published in the ivy descriptor file generated.</div>
</td>
</tr>
<tr class="rowColor">
<th class="colFirst" scope="row"><a href="IvyConfigurationContainer.html" title="interface in org.gradle.api.publish.ivy">IvyConfigurationContainer</a></th>
<td class="colLast">
<div class="block">The set of <a href="IvyConfiguration.html" title="interface in org.gradle.api.publish.ivy"><code>IvyConfiguration</code></a>s that will be included in the <a href="IvyPublication.html" title="interface in org.gradle.api.publish.ivy"><code>IvyPublication</code></a>.</div>
</td>
</tr>
<tr class="altColor">
<th class="colFirst" scope="row"><a href="IvyDependency.html" title="interface in org.gradle.api.publish.ivy">IvyDependency</a></th>
<td class="colLast">Deprecated.
<div class="deprecationComment">This type is not referenced by any other public API classes.</div>
</td>
</tr>
<tr class="rowColor">
<th class="colFirst" scope="row"><a href="IvyExtraInfoSpec.html" title="interface in org.gradle.api.publish.ivy">IvyExtraInfoSpec</a></th>
<td class="colLast">
<div class="block">Represents a modifiable form of IvyExtraInfo so that "extra" info elements
 can be configured on an Ivy publication.</div>
</td>
</tr>
<tr class="altColor">
<th class="colFirst" scope="row"><a href="IvyModuleDescriptorAuthor.html" title="interface in org.gradle.api.publish.ivy">IvyModuleDescriptorAuthor</a></th>
<td class="colLast">
<div class="block">An author of an Ivy publication.</div>
</td>
</tr>
<tr class="rowColor">
<th class="colFirst" scope="row"><a href="IvyModuleDescriptorDescription.html" title="interface in org.gradle.api.publish.ivy">IvyModuleDescriptorDescription</a></th>
<td class="colLast">
<div class="block">The description of an Ivy publication.</div>
</td>
</tr>
<tr class="altColor">
<th class="colFirst" scope="row"><a href="IvyModuleDescriptorLicense.html" title="interface in org.gradle.api.publish.ivy">IvyModuleDescriptorLicense</a></th>
<td class="colLast">
<div class="block">A license of an Ivy publication.</div>
</td>
</tr>
<tr class="rowColor">
<th class="colFirst" scope="row"><a href="IvyModuleDescriptorSpec.html" title="interface in org.gradle.api.publish.ivy">IvyModuleDescriptorSpec</a></th>
<td class="colLast">
<div class="block">The descriptor of any Ivy publication.</div>
</td>
</tr>
<tr class="altColor">
<th class="colFirst" scope="row"><a href="IvyPublication.html" title="interface in org.gradle.api.publish.ivy">IvyPublication</a></th>
<td class="colLast">
<div class="block">An <code>IvyPublication</code> is the representation/configuration of how Gradle should publish something in Ivy format, to an Ivy repository.</div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList">
<table class="typeSummary" summary="Exception Summary table, listing exceptions, and an explanation">
<caption><span>Exception Summary</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Exception</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tbody>
<tr class="altColor">
<th class="colFirst" scope="row"><a href="InvalidIvyPublicationException.html" title="class in org.gradle.api.publish.ivy">InvalidIvyPublicationException</a></th>
<td class="colLast">
<div class="block">Thrown when attempting to publish with an invalid <a href="IvyPublication.html" title="interface in org.gradle.api.publish.ivy"><code>IvyPublication</code></a>.</div>
</td>
</tr>
</tbody>
</table>
</li>
</ul>
</div>
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../index.html">Overview</a></li>
<li class="navBarCell1Rev">Package</li>
<li>Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../../allclasses.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
