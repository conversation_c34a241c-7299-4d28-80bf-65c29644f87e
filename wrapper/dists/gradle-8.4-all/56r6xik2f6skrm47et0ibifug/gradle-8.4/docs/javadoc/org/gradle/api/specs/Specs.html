<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc -->
<title>Specs (Gradle API 8.4)</title>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<link rel="stylesheet" type="text/css" href="../../../../javadoc.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../jquery/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
<script type="text/javascript" src="../../../../jquery/jszip/dist/jszip.min.js"></script>
<script type="text/javascript" src="../../../../jquery/jszip-utils/dist/jszip-utils.min.js"></script>
<!--[if IE]>
<script type="text/javascript" src="../../../../jquery/jszip-utils/dist/jszip-utils-ie.min.js"></script>
<![endif]-->
<script type="text/javascript" src="../../../../jquery/jquery-3.6.0.min.js"></script>
<script type="text/javascript" src="../../../../jquery/jquery-ui.min.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="Specs (Gradle API 8.4)";
        }
    }
    catch(err) {
    }
//-->
var data = {"i0":9,"i1":9,"i2":9,"i3":9,"i4":9,"i5":9,"i6":9,"i7":9};
var tabs = {65535:["t0","All Methods"],1:["t1","Static Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
var pathtoroot = "../../../../";
var useModuleDirectories = false;
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="fixedNav">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses.html">All&nbsp;Classes</a></li>
</ul>
<ul class="navListSearch">
<li><label for="search">SEARCH:</label>
<input type="text" id="search" value="search" disabled="disabled">
<input type="reset" id="reset" value="reset" disabled="disabled">
</li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field.summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#field.detail">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
</div>
<div class="navPadding">&nbsp;</div>
<script type="text/javascript"><!--
$('.navPadding').css('padding-top', $('.fixedNav').css("height"));
//-->
</script>
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle"><span class="packageLabelInType">Package</span>&nbsp;<a href="package-summary.html">org.gradle.api.specs</a></div>
<h2 title="Class Specs" class="title">Class Specs</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>org.gradle.api.specs.Specs</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<hr>
<pre>public class <span class="typeNameLabel">Specs</span>
extends java.lang.Object</pre>
<div class="block">Provides a number of <a href="Spec.html" title="interface in org.gradle.api.specs"><code>Spec</code></a> implementations.</div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- =========== FIELD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.summary">
<!--   -->
</a>
<h3>Field Summary</h3>
<table class="memberSummary" summary="Field Summary table, listing fields, and an explanation">
<caption><span>Fields</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colSecond" scope="col">Field</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="Spec.html" title="interface in org.gradle.api.specs">Spec</a>&lt;java.lang.Object&gt;</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#SATISFIES_ALL">SATISFIES_ALL</a></span></code></th>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="Spec.html" title="interface in org.gradle.api.specs">Spec</a>&lt;java.lang.Object&gt;</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#SATISFIES_NONE">SATISFIES_NONE</a></span></code></th>
<td class="colLast">&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="memberSummary" summary="Constructor Summary table, listing constructors, and an explanation">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Constructor</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tr class="altColor">
<th class="colConstructorName" scope="row"><code><span class="memberNameLink"><a href="#Specs--">Specs</a></span>()</code></th>
<td class="colLast">&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t1" class="tableTab"><span><a href="javascript:show(1);">Static Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colSecond" scope="col">Method</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>static &lt;T&gt;&nbsp;<a href="Spec.html" title="interface in org.gradle.api.specs">Spec</a>&lt;T&gt;</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#convertClosureToSpec-groovy.lang.Closure-">convertClosureToSpec</a></span>&#8203;(<a href="https://docs.groovy-lang.org/docs/groovy-3.0.17/html/gapi/groovy/lang/Closure.html?is-external=true" title="class or interface in groovy.lang" class="externalLink">Closure</a>&lt;?&gt;&nbsp;closure)</code></th>
<td class="colLast">&nbsp;</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>static &lt;T&gt;&nbsp;<a href="Spec.html" title="interface in org.gradle.api.specs">Spec</a>&lt;T&gt;</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#intersect-java.util.Collection-">intersect</a></span>&#8203;(java.util.Collection&lt;? extends <a href="Spec.html" title="interface in org.gradle.api.specs">Spec</a>&lt;? super T&gt;&gt;&nbsp;specs)</code></th>
<td class="colLast">
<div class="block">Returns a spec that selects the intersection of those items selected by the given specs.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>static &lt;T&gt;&nbsp;<a href="Spec.html" title="interface in org.gradle.api.specs">Spec</a>&lt;T&gt;</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#intersect-org.gradle.api.specs.Spec...-">intersect</a></span>&#8203;(<a href="Spec.html" title="interface in org.gradle.api.specs">Spec</a>&lt;? super T&gt;...&nbsp;specs)</code></th>
<td class="colLast">
<div class="block">Returns a spec that selects the intersection of those items selected by the given specs.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>static &lt;T&gt;&nbsp;<a href="Spec.html" title="interface in org.gradle.api.specs">Spec</a>&lt;T&gt;</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#negate-org.gradle.api.specs.Spec-">negate</a></span>&#8203;(<a href="Spec.html" title="interface in org.gradle.api.specs">Spec</a>&lt;? super T&gt;&nbsp;spec)</code></th>
<td class="colLast">
<div class="block">Returns a spec that selects everything that is not selected by the given spec.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>static &lt;T&gt;&nbsp;<a href="Spec.html" title="interface in org.gradle.api.specs">Spec</a>&lt;T&gt;</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#satisfyAll--">satisfyAll</a></span>()</code></th>
<td class="colLast">&nbsp;</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>static &lt;T&gt;&nbsp;<a href="Spec.html" title="interface in org.gradle.api.specs">Spec</a>&lt;T&gt;</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#satisfyNone--">satisfyNone</a></span>()</code></th>
<td class="colLast">&nbsp;</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>static &lt;T&gt;&nbsp;<a href="Spec.html" title="interface in org.gradle.api.specs">Spec</a>&lt;T&gt;</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#union-java.util.Collection-">union</a></span>&#8203;(java.util.Collection&lt;? extends <a href="Spec.html" title="interface in org.gradle.api.specs">Spec</a>&lt;? super T&gt;&gt;&nbsp;specs)</code></th>
<td class="colLast">
<div class="block">Returns a spec that selects the union of those items selected by the provided spec.</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>static &lt;T&gt;&nbsp;<a href="Spec.html" title="interface in org.gradle.api.specs">Spec</a>&lt;T&gt;</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#union-org.gradle.api.specs.Spec...-">union</a></span>&#8203;(<a href="Spec.html" title="interface in org.gradle.api.specs">Spec</a>&lt;? super T&gt;...&nbsp;specs)</code></th>
<td class="colLast">
<div class="block">Returns a spec that selects the union of those items selected by the provided spec.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>clone, equals, finalize, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ FIELD DETAIL =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.detail">
<!--   -->
</a>
<h3>Field Detail</h3>
<a name="SATISFIES_ALL">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>SATISFIES_ALL</h4>
<pre>public static final&nbsp;<a href="Spec.html" title="interface in org.gradle.api.specs">Spec</a>&lt;java.lang.Object&gt; SATISFIES_ALL</pre>
</li>
</ul>
<a name="SATISFIES_NONE">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>SATISFIES_NONE</h4>
<pre>public static final&nbsp;<a href="Spec.html" title="interface in org.gradle.api.specs">Spec</a>&lt;java.lang.Object&gt; SATISFIES_NONE</pre>
</li>
</ul>
</li>
</ul>
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a name="Specs--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>Specs</h4>
<pre>public&nbsp;Specs()</pre>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="satisfyAll--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>satisfyAll</h4>
<pre class="methodSignature">public static&nbsp;&lt;T&gt;&nbsp;<a href="Spec.html" title="interface in org.gradle.api.specs">Spec</a>&lt;T&gt;&nbsp;satisfyAll()</pre>
</li>
</ul>
<a name="satisfyNone--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>satisfyNone</h4>
<pre class="methodSignature">public static&nbsp;&lt;T&gt;&nbsp;<a href="Spec.html" title="interface in org.gradle.api.specs">Spec</a>&lt;T&gt;&nbsp;satisfyNone()</pre>
</li>
</ul>
<a name="convertClosureToSpec-groovy.lang.Closure-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>convertClosureToSpec</h4>
<pre class="methodSignature">public static&nbsp;&lt;T&gt;&nbsp;<a href="Spec.html" title="interface in org.gradle.api.specs">Spec</a>&lt;T&gt;&nbsp;convertClosureToSpec&#8203;(<a href="https://docs.groovy-lang.org/docs/groovy-3.0.17/html/gapi/groovy/lang/Closure.html?is-external=true" title="class or interface in groovy.lang" class="externalLink">Closure</a>&lt;?&gt;&nbsp;closure)</pre>
</li>
</ul>
<a name="intersect-org.gradle.api.specs.Spec...-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>intersect</h4>
<pre class="methodSignature">@SafeVarargs
public static&nbsp;&lt;T&gt;&nbsp;<a href="Spec.html" title="interface in org.gradle.api.specs">Spec</a>&lt;T&gt;&nbsp;intersect&#8203;(<a href="Spec.html" title="interface in org.gradle.api.specs">Spec</a>&lt;? super T&gt;...&nbsp;specs)</pre>
<div class="block">Returns a spec that selects the intersection of those items selected by the given specs. Returns a spec that selects everything when no specs provided.</div>
</li>
</ul>
<a name="intersect-java.util.Collection-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>intersect</h4>
<pre class="methodSignature">public static&nbsp;&lt;T&gt;&nbsp;<a href="Spec.html" title="interface in org.gradle.api.specs">Spec</a>&lt;T&gt;&nbsp;intersect&#8203;(java.util.Collection&lt;? extends <a href="Spec.html" title="interface in org.gradle.api.specs">Spec</a>&lt;? super T&gt;&gt;&nbsp;specs)</pre>
<div class="block">Returns a spec that selects the intersection of those items selected by the given specs. Returns a spec that selects everything when no specs provided.</div>
</li>
</ul>
<a name="union-org.gradle.api.specs.Spec...-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>union</h4>
<pre class="methodSignature">@SafeVarargs
public static&nbsp;&lt;T&gt;&nbsp;<a href="Spec.html" title="interface in org.gradle.api.specs">Spec</a>&lt;T&gt;&nbsp;union&#8203;(<a href="Spec.html" title="interface in org.gradle.api.specs">Spec</a>&lt;? super T&gt;...&nbsp;specs)</pre>
<div class="block">Returns a spec that selects the union of those items selected by the provided spec. Selects everything when no specs provided.</div>
</li>
</ul>
<a name="union-java.util.Collection-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>union</h4>
<pre class="methodSignature">public static&nbsp;&lt;T&gt;&nbsp;<a href="Spec.html" title="interface in org.gradle.api.specs">Spec</a>&lt;T&gt;&nbsp;union&#8203;(java.util.Collection&lt;? extends <a href="Spec.html" title="interface in org.gradle.api.specs">Spec</a>&lt;? super T&gt;&gt;&nbsp;specs)</pre>
<div class="block">Returns a spec that selects the union of those items selected by the provided spec. Selects everything when no specs provided.</div>
</li>
</ul>
<a name="negate-org.gradle.api.specs.Spec-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>negate</h4>
<pre class="methodSignature">public static&nbsp;&lt;T&gt;&nbsp;<a href="Spec.html" title="interface in org.gradle.api.specs">Spec</a>&lt;T&gt;&nbsp;negate&#8203;(<a href="Spec.html" title="interface in org.gradle.api.specs">Spec</a>&lt;? super T&gt;&nbsp;spec)</pre>
<div class="block">Returns a spec that selects everything that is not selected by the given spec.</div>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field.summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#field.detail">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
