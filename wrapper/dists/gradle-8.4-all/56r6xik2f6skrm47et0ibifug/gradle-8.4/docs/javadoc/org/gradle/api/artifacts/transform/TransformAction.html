<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc -->
<title>TransformAction (Gradle API 8.4)</title>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<link rel="stylesheet" type="text/css" href="../../../../../javadoc.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../jquery/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
<script type="text/javascript" src="../../../../../jquery/jszip/dist/jszip.min.js"></script>
<script type="text/javascript" src="../../../../../jquery/jszip-utils/dist/jszip-utils.min.js"></script>
<!--[if IE]>
<script type="text/javascript" src="../../../../../jquery/jszip-utils/dist/jszip-utils-ie.min.js"></script>
<![endif]-->
<script type="text/javascript" src="../../../../../jquery/jquery-3.6.0.min.js"></script>
<script type="text/javascript" src="../../../../../jquery/jquery-ui.min.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="TransformAction (Gradle API 8.4)";
        }
    }
    catch(err) {
    }
//-->
var data = {"i0":6,"i1":6};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],4:["t3","Abstract Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
var pathtoroot = "../../../../../";
var useModuleDirectories = false;
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="fixedNav">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../../allclasses.html">All&nbsp;Classes</a></li>
</ul>
<ul class="navListSearch">
<li><label for="search">SEARCH:</label>
<input type="text" id="search" value="search" disabled="disabled">
<input type="reset" id="reset" value="reset" disabled="disabled">
</li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
</div>
<div class="navPadding">&nbsp;</div>
<script type="text/javascript"><!--
$('.navPadding').css('padding-top', $('.fixedNav').css("height"));
//-->
</script>
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle"><span class="packageLabelInType">Package</span>&nbsp;<a href="package-summary.html">org.gradle.api.artifacts.transform</a></div>
<h2 title="Interface TransformAction" class="title">Interface TransformAction&lt;T extends <a href="TransformParameters.html" title="interface in org.gradle.api.artifacts.transform">TransformParameters</a>&gt;</h2>
</div>
<div class="contentContainer">
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt><span class="paramLabel">Type Parameters:</span></dt>
<dd><code>T</code> - Parameter type for the transform action. Should be <a href="TransformParameters.None.html" title="class in org.gradle.api.artifacts.transform"><code>TransformParameters.None</code></a> if the action does not have parameters.</dd>
</dl>
<hr>
<pre>public interface <span class="typeNameLabel">TransformAction&lt;T extends <a href="TransformParameters.html" title="interface in org.gradle.api.artifacts.transform">TransformParameters</a>&gt;</span></pre>
<div class="block">Interface for artifact transform actions.

 <p>
     A transform action implementation is an abstract class implementing the <a href="#transform-org.gradle.api.artifacts.transform.TransformOutputs-"><code>transform(TransformOutputs)</code></a> method.
     A minimal implementation may look like this:
 </p>

 <pre class='autoTested'>
 import org.gradle.api.artifacts.transform.TransformParameters;

 public abstract class MyTransform implements TransformAction&lt;TransformParameters.None&gt; {
     @InputArtifact
     public abstract Provider&lt;FileSystemLocation&gt; getInputArtifact();

     @Override
     public void transform(TransformOutputs outputs) {
         File input = getInputArtifact().get().getAsFile();
         File output = outputs.file(input.getName() + ".transformed");
         // Do something to generate output from input
     }
 }
 </pre>

 Implementations of TransformAction are subject to the following constraints:
 <ul>
     <li>Do not implement <a href="#getParameters--"><code>getParameters()</code></a> in your class, the method will be implemented by Gradle.</li>
     <li>Implementations may only have a default constructor.</li>
 </ul>

  Implementations can receive parameters by using annotated abstract getter methods.
  <ul>
      <li>An abstract getter annotated with <a href="InputArtifact.html" title="annotation in org.gradle.api.artifacts.transform"><code>InputArtifact</code></a> will receive the <em>input artifact</em> location, which is the file or directory that the transform should be applied to.</li>
      <li>An abstract getter with <a href="InputArtifactDependencies.html" title="annotation in org.gradle.api.artifacts.transform"><code>InputArtifactDependencies</code></a> will receive the <em>dependencies</em> of its input artifact.</li>
  </ul></div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>5.3</dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t3" class="tableTab"><span><a href="javascript:show(4);">Abstract Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colSecond" scope="col">Method</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code><a href="TransformAction.html" title="type parameter in TransformAction">T</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getParameters--">getParameters</a></span>()</code></th>
<td class="colLast">
<div class="block">The object provided by <a href="TransformSpec.html#getParameters--"><code>TransformSpec.getParameters()</code></a> when registering the artifact transform.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#transform-org.gradle.api.artifacts.transform.TransformOutputs-">transform</a></span>&#8203;(<a href="TransformOutputs.html" title="interface in org.gradle.api.artifacts.transform">TransformOutputs</a>&nbsp;outputs)</code></th>
<td class="colLast">
<div class="block">Executes the transform.</div>
</td>
</tr>
</table>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="getParameters--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getParameters</h4>
<pre class="methodSignature">@Inject
<a href="TransformAction.html" title="type parameter in TransformAction">T</a>&nbsp;getParameters()</pre>
<div class="block">The object provided by <a href="TransformSpec.html#getParameters--"><code>TransformSpec.getParameters()</code></a> when registering the artifact transform.

 <p>
     Do not implement this method in your subclass.
     Gradle provides the implementation when registering the transform action via <a href="../dsl/DependencyHandler.html#registerTransform-java.lang.Class-org.gradle.api.Action-"><code>DependencyHandler.registerTransform(Class, Action)</code></a>.
 </p></div>
</li>
</ul>
<a name="transform-org.gradle.api.artifacts.transform.TransformOutputs-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>transform</h4>
<pre class="methodSignature">void&nbsp;transform&#8203;(<a href="TransformOutputs.html" title="interface in org.gradle.api.artifacts.transform">TransformOutputs</a>&nbsp;outputs)</pre>
<div class="block">Executes the transform.

 <p>This method must be implemented in the subclass.</p></div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>outputs</code> - Receives the outputs of the transform.</dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../../allclasses.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
