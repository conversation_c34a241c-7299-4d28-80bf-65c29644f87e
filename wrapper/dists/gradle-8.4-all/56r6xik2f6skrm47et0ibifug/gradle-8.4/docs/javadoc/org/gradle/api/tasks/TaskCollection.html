<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc -->
<title>TaskCollection (Gradle API 8.4)</title>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<link rel="stylesheet" type="text/css" href="../../../../javadoc.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../jquery/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
<script type="text/javascript" src="../../../../jquery/jszip/dist/jszip.min.js"></script>
<script type="text/javascript" src="../../../../jquery/jszip-utils/dist/jszip-utils.min.js"></script>
<!--[if IE]>
<script type="text/javascript" src="../../../../jquery/jszip-utils/dist/jszip-utils-ie.min.js"></script>
<![endif]-->
<script type="text/javascript" src="../../../../jquery/jquery-3.6.0.min.js"></script>
<script type="text/javascript" src="../../../../jquery/jquery-ui.min.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="TaskCollection (Gradle API 8.4)";
        }
    }
    catch(err) {
    }
//-->
var data = {"i0":6,"i1":6,"i2":6,"i3":6,"i4":6,"i5":6,"i6":6,"i7":6,"i8":6,"i9":6,"i10":6,"i11":6};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],4:["t3","Abstract Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
var pathtoroot = "../../../../";
var useModuleDirectories = false;
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="fixedNav">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses.html">All&nbsp;Classes</a></li>
</ul>
<ul class="navListSearch">
<li><label for="search">SEARCH:</label>
<input type="text" id="search" value="search" disabled="disabled">
<input type="reset" id="reset" value="reset" disabled="disabled">
</li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
</div>
<div class="navPadding">&nbsp;</div>
<script type="text/javascript"><!--
$('.navPadding').css('padding-top', $('.fixedNav').css("height"));
//-->
</script>
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle"><span class="packageLabelInType">Package</span>&nbsp;<a href="package-summary.html">org.gradle.api.tasks</a></div>
<h2 title="Interface TaskCollection" class="title">Interface TaskCollection&lt;T extends <a href="../Task.html" title="interface in org.gradle.api">Task</a>&gt;</h2>
</div>
<div class="contentContainer">
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt><span class="paramLabel">Type Parameters:</span></dt>
<dd><code>T</code> - The type of tasks which this collection contains.</dd>
</dl>
<dl>
<dt>All Superinterfaces:</dt>
<dd><code>java.util.Collection&lt;T&gt;</code>, <code><a href="../DomainObjectCollection.html" title="interface in org.gradle.api">DomainObjectCollection</a>&lt;T&gt;</code>, <code><a href="../DomainObjectSet.html" title="interface in org.gradle.api">DomainObjectSet</a>&lt;T&gt;</code>, <code>java.lang.Iterable&lt;T&gt;</code>, <code><a href="../NamedDomainObjectCollection.html" title="interface in org.gradle.api">NamedDomainObjectCollection</a>&lt;T&gt;</code>, <code><a href="../NamedDomainObjectSet.html" title="interface in org.gradle.api">NamedDomainObjectSet</a>&lt;T&gt;</code>, <code>java.util.Set&lt;T&gt;</code></dd>
</dl>
<dl>
<dt>All Known Subinterfaces:</dt>
<dd><code><a href="TaskContainer.html" title="interface in org.gradle.api.tasks">TaskContainer</a></code></dd>
</dl>
<hr>
<pre>public interface <span class="typeNameLabel">TaskCollection&lt;T extends <a href="../Task.html" title="interface in org.gradle.api">Task</a>&gt;</span>
extends <a href="../NamedDomainObjectSet.html" title="interface in org.gradle.api">NamedDomainObjectSet</a>&lt;T&gt;</pre>
<div class="block">A <code>TaskCollection</code> contains a set of <a href="../Task.html" title="interface in org.gradle.api"><code>Task</code></a> instances, and provides a number of query methods.</div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t3" class="tableTab"><span><a href="javascript:show(4);">Abstract Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colSecond" scope="col">Method</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code><a href="TaskCollection.html" title="type parameter in TaskCollection">T</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getAt-java.lang.String-">getAt</a></span>&#8203;(java.lang.String&nbsp;name)</code></th>
<td class="colLast">
<div class="block">Locates an object by name, failing if there is no such object.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code><a href="TaskCollection.html" title="type parameter in TaskCollection">T</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getByName-java.lang.String-">getByName</a></span>&#8203;(java.lang.String&nbsp;name)</code></th>
<td class="colLast">
<div class="block">Locates an object by name, failing if there is no such object.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code><a href="TaskCollection.html" title="type parameter in TaskCollection">T</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getByName-java.lang.String-groovy.lang.Closure-">getByName</a></span>&#8203;(java.lang.String&nbsp;name,
         <a href="https://docs.groovy-lang.org/docs/groovy-3.0.17/html/gapi/groovy/lang/Closure.html?is-external=true" title="class or interface in groovy.lang" class="externalLink">Closure</a>&nbsp;configureClosure)</code></th>
<td class="colLast">
<div class="block">Locates an object by name, failing if there is no such object.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code><a href="TaskCollection.html" title="interface in org.gradle.api.tasks">TaskCollection</a>&lt;<a href="TaskCollection.html" title="type parameter in TaskCollection">T</a>&gt;</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#matching-groovy.lang.Closure-">matching</a></span>&#8203;(<a href="https://docs.groovy-lang.org/docs/groovy-3.0.17/html/gapi/groovy/lang/Closure.html?is-external=true" title="class or interface in groovy.lang" class="externalLink">Closure</a>&nbsp;closure)</code></th>
<td class="colLast">
<div class="block">Returns a collection which contains the objects in this collection which meet the given closure specification.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code><a href="TaskCollection.html" title="interface in org.gradle.api.tasks">TaskCollection</a>&lt;<a href="TaskCollection.html" title="type parameter in TaskCollection">T</a>&gt;</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#matching-org.gradle.api.specs.Spec-">matching</a></span>&#8203;(<a href="../specs/Spec.html" title="interface in org.gradle.api.specs">Spec</a>&lt;? super <a href="TaskCollection.html" title="type parameter in TaskCollection">T</a>&gt;&nbsp;spec)</code></th>
<td class="colLast">
<div class="block">Returns a collection which contains the objects in this collection which meet the given specification.</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code><a href="TaskProvider.html" title="interface in org.gradle.api.tasks">TaskProvider</a>&lt;<a href="TaskCollection.html" title="type parameter in TaskCollection">T</a>&gt;</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#named-java.lang.String-">named</a></span>&#8203;(java.lang.String&nbsp;name)</code></th>
<td class="colLast">
<div class="block">Locates a task by name, without triggering its creation or configuration, failing if there is no such object.</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>&lt;S extends <a href="TaskCollection.html" title="type parameter in TaskCollection">T</a>&gt;<br><a href="TaskProvider.html" title="interface in org.gradle.api.tasks">TaskProvider</a>&lt;S&gt;</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#named-java.lang.String-java.lang.Class-">named</a></span>&#8203;(java.lang.String&nbsp;name,
     java.lang.Class&lt;S&gt;&nbsp;type)</code></th>
<td class="colLast">
<div class="block">Locates a object by name and type, without triggering its creation or configuration, failing if there is no such object.</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>&lt;S extends <a href="TaskCollection.html" title="type parameter in TaskCollection">T</a>&gt;<br><a href="TaskProvider.html" title="interface in org.gradle.api.tasks">TaskProvider</a>&lt;S&gt;</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#named-java.lang.String-java.lang.Class-org.gradle.api.Action-">named</a></span>&#8203;(java.lang.String&nbsp;name,
     java.lang.Class&lt;S&gt;&nbsp;type,
     <a href="../Action.html" title="interface in org.gradle.api">Action</a>&lt;? super S&gt;&nbsp;configurationAction)</code></th>
<td class="colLast">
<div class="block">Locates a object by name and type, without triggering its creation or configuration, failing if there is no such object.</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code><a href="TaskProvider.html" title="interface in org.gradle.api.tasks">TaskProvider</a>&lt;<a href="TaskCollection.html" title="type parameter in TaskCollection">T</a>&gt;</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#named-java.lang.String-org.gradle.api.Action-">named</a></span>&#8203;(java.lang.String&nbsp;name,
     <a href="../Action.html" title="interface in org.gradle.api">Action</a>&lt;? super <a href="TaskCollection.html" title="type parameter in TaskCollection">T</a>&gt;&nbsp;configurationAction)</code></th>
<td class="colLast">
<div class="block">Locates a object by name, without triggering its creation or configuration, failing if there is no such object.</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#whenTaskAdded-groovy.lang.Closure-">whenTaskAdded</a></span>&#8203;(<a href="https://docs.groovy-lang.org/docs/groovy-3.0.17/html/gapi/groovy/lang/Closure.html?is-external=true" title="class or interface in groovy.lang" class="externalLink">Closure</a>&nbsp;closure)</code></th>
<td class="colLast">
<div class="block">Adds a closure to be called when a task is added to this collection.</div>
</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code><a href="../Action.html" title="interface in org.gradle.api">Action</a>&lt;? super <a href="TaskCollection.html" title="type parameter in TaskCollection">T</a>&gt;</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#whenTaskAdded-org.gradle.api.Action-">whenTaskAdded</a></span>&#8203;(<a href="../Action.html" title="interface in org.gradle.api">Action</a>&lt;? super <a href="TaskCollection.html" title="type parameter in TaskCollection">T</a>&gt;&nbsp;action)</code></th>
<td class="colLast">
<div class="block">Adds an <code>Action</code> to be executed when a task is added to this collection.</div>
</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code>&lt;S extends <a href="TaskCollection.html" title="type parameter in TaskCollection">T</a>&gt;<br><a href="TaskCollection.html" title="interface in org.gradle.api.tasks">TaskCollection</a>&lt;S&gt;</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#withType-java.lang.Class-">withType</a></span>&#8203;(java.lang.Class&lt;S&gt;&nbsp;type)</code></th>
<td class="colLast">
<div class="block">Returns a collection containing the objects in this collection of the given type.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.util.Collection">
<!--   -->
</a>
<h3>Methods inherited from interface&nbsp;java.util.Collection</h3>
<code>parallelStream, removeIf, stream, toArray</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.org.gradle.api.DomainObjectCollection">
<!--   -->
</a>
<h3>Methods inherited from interface&nbsp;org.gradle.api.<a href="../DomainObjectCollection.html" title="interface in org.gradle.api">DomainObjectCollection</a></h3>
<code><a href="../DomainObjectCollection.html#addAllLater-org.gradle.api.provider.Provider-">addAllLater</a>, <a href="../DomainObjectCollection.html#addLater-org.gradle.api.provider.Provider-">addLater</a>, <a href="../DomainObjectCollection.html#all-groovy.lang.Closure-">all</a>, <a href="../DomainObjectCollection.html#all-org.gradle.api.Action-">all</a>, <a href="../DomainObjectCollection.html#configureEach-org.gradle.api.Action-">configureEach</a>, <a href="../DomainObjectCollection.html#whenObjectAdded-groovy.lang.Closure-">whenObjectAdded</a>, <a href="../DomainObjectCollection.html#whenObjectAdded-org.gradle.api.Action-">whenObjectAdded</a>, <a href="../DomainObjectCollection.html#whenObjectRemoved-groovy.lang.Closure-">whenObjectRemoved</a>, <a href="../DomainObjectCollection.html#whenObjectRemoved-org.gradle.api.Action-">whenObjectRemoved</a>, <a href="../DomainObjectCollection.html#withType-java.lang.Class-groovy.lang.Closure-">withType</a>, <a href="../DomainObjectCollection.html#withType-java.lang.Class-org.gradle.api.Action-">withType</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Iterable">
<!--   -->
</a>
<h3>Methods inherited from interface&nbsp;java.lang.Iterable</h3>
<code>forEach</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.org.gradle.api.NamedDomainObjectCollection">
<!--   -->
</a>
<h3>Methods inherited from interface&nbsp;org.gradle.api.<a href="../NamedDomainObjectCollection.html" title="interface in org.gradle.api">NamedDomainObjectCollection</a></h3>
<code><a href="../NamedDomainObjectCollection.html#add-T-">add</a>, <a href="../NamedDomainObjectCollection.html#addAll-java.util.Collection-">addAll</a>, <a href="../NamedDomainObjectCollection.html#addRule-java.lang.String-groovy.lang.Closure-">addRule</a>, <a href="../NamedDomainObjectCollection.html#addRule-java.lang.String-org.gradle.api.Action-">addRule</a>, <a href="../NamedDomainObjectCollection.html#addRule-org.gradle.api.Rule-">addRule</a>, <a href="../NamedDomainObjectCollection.html#findByName-java.lang.String-">findByName</a>, <a href="../NamedDomainObjectCollection.html#getAsMap--">getAsMap</a>, <a href="../NamedDomainObjectCollection.html#getByName-java.lang.String-org.gradle.api.Action-">getByName</a>, <a href="../NamedDomainObjectCollection.html#getCollectionSchema--">getCollectionSchema</a>, <a href="../NamedDomainObjectCollection.html#getNamer--">getNamer</a>, <a href="../NamedDomainObjectCollection.html#getNames--">getNames</a>, <a href="../NamedDomainObjectCollection.html#getRules--">getRules</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.org.gradle.api.NamedDomainObjectSet">
<!--   -->
</a>
<h3>Methods inherited from interface&nbsp;org.gradle.api.<a href="../NamedDomainObjectSet.html" title="interface in org.gradle.api">NamedDomainObjectSet</a></h3>
<code><a href="../NamedDomainObjectSet.html#findAll-groovy.lang.Closure-">findAll</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.util.Set">
<!--   -->
</a>
<h3>Methods inherited from interface&nbsp;java.util.Set</h3>
<code>add, addAll, clear, contains, containsAll, equals, hashCode, isEmpty, iterator, remove, removeAll, retainAll, size, spliterator, toArray, toArray</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="matching-org.gradle.api.specs.Spec-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>matching</h4>
<pre class="methodSignature"><a href="TaskCollection.html" title="interface in org.gradle.api.tasks">TaskCollection</a>&lt;<a href="TaskCollection.html" title="type parameter in TaskCollection">T</a>&gt;&nbsp;matching&#8203;(<a href="../specs/Spec.html" title="interface in org.gradle.api.specs">Spec</a>&lt;? super <a href="TaskCollection.html" title="type parameter in TaskCollection">T</a>&gt;&nbsp;spec)</pre>
<div class="block">Returns a collection which contains the objects in this collection which meet the given specification. The
 returned collection is live, so that when matching objects are added to this collection, they are also visible in
 the filtered collection.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../DomainObjectCollection.html#matching-org.gradle.api.specs.Spec-">matching</a></code>&nbsp;in interface&nbsp;<code><a href="../DomainObjectCollection.html" title="interface in org.gradle.api">DomainObjectCollection</a>&lt;<a href="TaskCollection.html" title="type parameter in TaskCollection">T</a> extends <a href="../Task.html" title="interface in org.gradle.api">Task</a>&gt;</code></dd>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../DomainObjectSet.html#matching-org.gradle.api.specs.Spec-">matching</a></code>&nbsp;in interface&nbsp;<code><a href="../DomainObjectSet.html" title="interface in org.gradle.api">DomainObjectSet</a>&lt;<a href="TaskCollection.html" title="type parameter in TaskCollection">T</a> extends <a href="../Task.html" title="interface in org.gradle.api">Task</a>&gt;</code></dd>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../NamedDomainObjectCollection.html#matching-org.gradle.api.specs.Spec-">matching</a></code>&nbsp;in interface&nbsp;<code><a href="../NamedDomainObjectCollection.html" title="interface in org.gradle.api">NamedDomainObjectCollection</a>&lt;<a href="TaskCollection.html" title="type parameter in TaskCollection">T</a> extends <a href="../Task.html" title="interface in org.gradle.api">Task</a>&gt;</code></dd>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../NamedDomainObjectSet.html#matching-org.gradle.api.specs.Spec-">matching</a></code>&nbsp;in interface&nbsp;<code><a href="../NamedDomainObjectSet.html" title="interface in org.gradle.api">NamedDomainObjectSet</a>&lt;<a href="TaskCollection.html" title="type parameter in TaskCollection">T</a> extends <a href="../Task.html" title="interface in org.gradle.api">Task</a>&gt;</code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>spec</code> - The specification to use.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The collection of matching objects. Returns an empty collection if there are no such objects in this
         collection.</dd>
</dl>
</li>
</ul>
<a name="matching-groovy.lang.Closure-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>matching</h4>
<pre class="methodSignature"><a href="TaskCollection.html" title="interface in org.gradle.api.tasks">TaskCollection</a>&lt;<a href="TaskCollection.html" title="type parameter in TaskCollection">T</a>&gt;&nbsp;matching&#8203;(<a href="https://docs.groovy-lang.org/docs/groovy-3.0.17/html/gapi/groovy/lang/Closure.html?is-external=true" title="class or interface in groovy.lang" class="externalLink">Closure</a>&nbsp;closure)</pre>
<div class="block">Returns a collection which contains the objects in this collection which meet the given closure specification. The
 returned collection is live, so that when matching objects are added to this collection, they are also visible in
 the filtered collection.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../DomainObjectCollection.html#matching-groovy.lang.Closure-">matching</a></code>&nbsp;in interface&nbsp;<code><a href="../DomainObjectCollection.html" title="interface in org.gradle.api">DomainObjectCollection</a>&lt;<a href="TaskCollection.html" title="type parameter in TaskCollection">T</a> extends <a href="../Task.html" title="interface in org.gradle.api">Task</a>&gt;</code></dd>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../DomainObjectSet.html#matching-groovy.lang.Closure-">matching</a></code>&nbsp;in interface&nbsp;<code><a href="../DomainObjectSet.html" title="interface in org.gradle.api">DomainObjectSet</a>&lt;<a href="TaskCollection.html" title="type parameter in TaskCollection">T</a> extends <a href="../Task.html" title="interface in org.gradle.api">Task</a>&gt;</code></dd>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../NamedDomainObjectCollection.html#matching-groovy.lang.Closure-">matching</a></code>&nbsp;in interface&nbsp;<code><a href="../NamedDomainObjectCollection.html" title="interface in org.gradle.api">NamedDomainObjectCollection</a>&lt;<a href="TaskCollection.html" title="type parameter in TaskCollection">T</a> extends <a href="../Task.html" title="interface in org.gradle.api">Task</a>&gt;</code></dd>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../NamedDomainObjectSet.html#matching-groovy.lang.Closure-">matching</a></code>&nbsp;in interface&nbsp;<code><a href="../NamedDomainObjectSet.html" title="interface in org.gradle.api">NamedDomainObjectSet</a>&lt;<a href="TaskCollection.html" title="type parameter in TaskCollection">T</a> extends <a href="../Task.html" title="interface in org.gradle.api">Task</a>&gt;</code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>closure</code> - The specification to use. The closure gets a collection element as an argument.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The collection of matching objects. Returns an empty collection if there are no such objects in this
         collection.</dd>
</dl>
</li>
</ul>
<a name="getByName-java.lang.String-groovy.lang.Closure-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getByName</h4>
<pre class="methodSignature"><a href="TaskCollection.html" title="type parameter in TaskCollection">T</a>&nbsp;getByName&#8203;(java.lang.String&nbsp;name,
            <a href="https://docs.groovy-lang.org/docs/groovy-3.0.17/html/gapi/groovy/lang/Closure.html?is-external=true" title="class or interface in groovy.lang" class="externalLink">Closure</a>&nbsp;configureClosure)
     throws <a href="../UnknownTaskException.html" title="class in org.gradle.api">UnknownTaskException</a></pre>
<div class="block">Locates an object by name, failing if there is no such object. The given configure closure is executed against
 the object before it is returned from this method. The object is passed to the closure as its delegate.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../NamedDomainObjectCollection.html#getByName-java.lang.String-groovy.lang.Closure-">getByName</a></code>&nbsp;in interface&nbsp;<code><a href="../NamedDomainObjectCollection.html" title="interface in org.gradle.api">NamedDomainObjectCollection</a>&lt;<a href="TaskCollection.html" title="type parameter in TaskCollection">T</a> extends <a href="../Task.html" title="interface in org.gradle.api">Task</a>&gt;</code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>name</code> - The object name</dd>
<dd><code>configureClosure</code> - The closure to use to configure the object.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The object with the given name, after the configure closure has been applied to it. Never returns null.</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code><a href="../UnknownTaskException.html" title="class in org.gradle.api">UnknownTaskException</a></code></dd>
</dl>
</li>
</ul>
<a name="getByName-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getByName</h4>
<pre class="methodSignature"><a href="TaskCollection.html" title="type parameter in TaskCollection">T</a>&nbsp;getByName&#8203;(java.lang.String&nbsp;name)
     throws <a href="../UnknownTaskException.html" title="class in org.gradle.api">UnknownTaskException</a></pre>
<div class="block">Locates an object by name, failing if there is no such object.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../NamedDomainObjectCollection.html#getByName-java.lang.String-">getByName</a></code>&nbsp;in interface&nbsp;<code><a href="../NamedDomainObjectCollection.html" title="interface in org.gradle.api">NamedDomainObjectCollection</a>&lt;<a href="TaskCollection.html" title="type parameter in TaskCollection">T</a> extends <a href="../Task.html" title="interface in org.gradle.api">Task</a>&gt;</code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>name</code> - The object name</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The object with the given name. Never returns null.</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code><a href="../UnknownTaskException.html" title="class in org.gradle.api">UnknownTaskException</a></code></dd>
</dl>
</li>
</ul>
<a name="withType-java.lang.Class-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>withType</h4>
<pre class="methodSignature">&lt;S extends <a href="TaskCollection.html" title="type parameter in TaskCollection">T</a>&gt;&nbsp;<a href="TaskCollection.html" title="interface in org.gradle.api.tasks">TaskCollection</a>&lt;S&gt;&nbsp;withType&#8203;(java.lang.Class&lt;S&gt;&nbsp;type)</pre>
<div class="block">Returns a collection containing the objects in this collection of the given type.  The returned collection is
 live, so that when matching objects are later added to this collection, they are also visible in the filtered
 collection.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../DomainObjectCollection.html#withType-java.lang.Class-">withType</a></code>&nbsp;in interface&nbsp;<code><a href="../DomainObjectCollection.html" title="interface in org.gradle.api">DomainObjectCollection</a>&lt;<a href="TaskCollection.html" title="type parameter in TaskCollection">T</a> extends <a href="../Task.html" title="interface in org.gradle.api">Task</a>&gt;</code></dd>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../DomainObjectSet.html#withType-java.lang.Class-">withType</a></code>&nbsp;in interface&nbsp;<code><a href="../DomainObjectSet.html" title="interface in org.gradle.api">DomainObjectSet</a>&lt;<a href="TaskCollection.html" title="type parameter in TaskCollection">T</a> extends <a href="../Task.html" title="interface in org.gradle.api">Task</a>&gt;</code></dd>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../NamedDomainObjectCollection.html#withType-java.lang.Class-">withType</a></code>&nbsp;in interface&nbsp;<code><a href="../NamedDomainObjectCollection.html" title="interface in org.gradle.api">NamedDomainObjectCollection</a>&lt;<a href="TaskCollection.html" title="type parameter in TaskCollection">T</a> extends <a href="../Task.html" title="interface in org.gradle.api">Task</a>&gt;</code></dd>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../NamedDomainObjectSet.html#withType-java.lang.Class-">withType</a></code>&nbsp;in interface&nbsp;<code><a href="../NamedDomainObjectSet.html" title="interface in org.gradle.api">NamedDomainObjectSet</a>&lt;<a href="TaskCollection.html" title="type parameter in TaskCollection">T</a> extends <a href="../Task.html" title="interface in org.gradle.api">Task</a>&gt;</code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>type</code> - The type of objects to find.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The matching objects. Returns an empty collection if there are no such objects in this collection.</dd>
</dl>
</li>
</ul>
<a name="whenTaskAdded-org.gradle.api.Action-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>whenTaskAdded</h4>
<pre class="methodSignature"><a href="../Action.html" title="interface in org.gradle.api">Action</a>&lt;? super <a href="TaskCollection.html" title="type parameter in TaskCollection">T</a>&gt;&nbsp;whenTaskAdded&#8203;(<a href="../Action.html" title="interface in org.gradle.api">Action</a>&lt;? super <a href="TaskCollection.html" title="type parameter in TaskCollection">T</a>&gt;&nbsp;action)</pre>
<div class="block">Adds an <code>Action</code> to be executed when a task is added to this collection.
 <p>
 Like <a href="../DomainObjectCollection.html#all-org.gradle.api.Action-"><code>DomainObjectCollection.all(Action)</code></a>, this method will cause all tasks in this container to be realized.
 </p></div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>action</code> - The action to be executed</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the supplied action</dd>
</dl>
</li>
</ul>
<a name="whenTaskAdded-groovy.lang.Closure-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>whenTaskAdded</h4>
<pre class="methodSignature">void&nbsp;whenTaskAdded&#8203;(<a href="https://docs.groovy-lang.org/docs/groovy-3.0.17/html/gapi/groovy/lang/Closure.html?is-external=true" title="class or interface in groovy.lang" class="externalLink">Closure</a>&nbsp;closure)</pre>
<div class="block">Adds a closure to be called when a task is added to this collection. The task is passed to the closure as the
 parameter.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>closure</code> - The closure to be called</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="#whenTaskAdded-org.gradle.api.Action-"><code>whenTaskAdded(Action)</code></a></dd>
</dl>
</li>
</ul>
<a name="getAt-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getAt</h4>
<pre class="methodSignature"><a href="TaskCollection.html" title="type parameter in TaskCollection">T</a>&nbsp;getAt&#8203;(java.lang.String&nbsp;name)
 throws <a href="../UnknownTaskException.html" title="class in org.gradle.api">UnknownTaskException</a></pre>
<div class="block">Locates an object by name, failing if there is no such object. This method is identical to <a href="../NamedDomainObjectCollection.html#getByName-java.lang.String-"><code>NamedDomainObjectCollection.getByName(String)</code></a>. You can call this method in your build script by using the groovy <code>[]</code> operator.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../NamedDomainObjectCollection.html#getAt-java.lang.String-">getAt</a></code>&nbsp;in interface&nbsp;<code><a href="../NamedDomainObjectCollection.html" title="interface in org.gradle.api">NamedDomainObjectCollection</a>&lt;<a href="TaskCollection.html" title="type parameter in TaskCollection">T</a> extends <a href="../Task.html" title="interface in org.gradle.api">Task</a>&gt;</code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>name</code> - The object name</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The object with the given name. Never returns null.</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code><a href="../UnknownTaskException.html" title="class in org.gradle.api">UnknownTaskException</a></code></dd>
</dl>
</li>
</ul>
<a name="named-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>named</h4>
<pre class="methodSignature"><a href="TaskProvider.html" title="interface in org.gradle.api.tasks">TaskProvider</a>&lt;<a href="TaskCollection.html" title="type parameter in TaskCollection">T</a>&gt;&nbsp;named&#8203;(java.lang.String&nbsp;name)
               throws <a href="../UnknownTaskException.html" title="class in org.gradle.api">UnknownTaskException</a></pre>
<div class="block">Locates a task by name, without triggering its creation or configuration, failing if there is no such object.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../NamedDomainObjectCollection.html#named-java.lang.String-">named</a></code>&nbsp;in interface&nbsp;<code><a href="../NamedDomainObjectCollection.html" title="interface in org.gradle.api">NamedDomainObjectCollection</a>&lt;<a href="TaskCollection.html" title="type parameter in TaskCollection">T</a> extends <a href="../Task.html" title="interface in org.gradle.api">Task</a>&gt;</code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>name</code> - The task name</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>A <a href="../provider/Provider.html" title="interface in org.gradle.api.provider"><code>Provider</code></a> that will return the task when queried. The task may be created and configured at this point, if not already.</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code><a href="../UnknownTaskException.html" title="class in org.gradle.api">UnknownTaskException</a></code> - If a task with the given name is not defined.</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>4.9</dd>
</dl>
</li>
</ul>
<a name="named-java.lang.String-org.gradle.api.Action-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>named</h4>
<pre class="methodSignature"><a href="TaskProvider.html" title="interface in org.gradle.api.tasks">TaskProvider</a>&lt;<a href="TaskCollection.html" title="type parameter in TaskCollection">T</a>&gt;&nbsp;named&#8203;(java.lang.String&nbsp;name,
                      <a href="../Action.html" title="interface in org.gradle.api">Action</a>&lt;? super <a href="TaskCollection.html" title="type parameter in TaskCollection">T</a>&gt;&nbsp;configurationAction)
               throws <a href="../UnknownTaskException.html" title="class in org.gradle.api">UnknownTaskException</a></pre>
<div class="block">Locates a object by name, without triggering its creation or configuration, failing if there is no such object.
 The given configure action is executed against the object before it is returned from the provider.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../NamedDomainObjectCollection.html#named-java.lang.String-org.gradle.api.Action-">named</a></code>&nbsp;in interface&nbsp;<code><a href="../NamedDomainObjectCollection.html" title="interface in org.gradle.api">NamedDomainObjectCollection</a>&lt;<a href="TaskCollection.html" title="type parameter in TaskCollection">T</a> extends <a href="../Task.html" title="interface in org.gradle.api">Task</a>&gt;</code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>name</code> - The object's name</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>A <a href="../provider/Provider.html" title="interface in org.gradle.api.provider"><code>Provider</code></a> that will return the object when queried. The object may be created and configured at this point, if not already.</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code><a href="../UnknownTaskException.html" title="class in org.gradle.api">UnknownTaskException</a></code></dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>5.0</dd>
</dl>
</li>
</ul>
<a name="named-java.lang.String-java.lang.Class-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>named</h4>
<pre class="methodSignature">&lt;S extends <a href="TaskCollection.html" title="type parameter in TaskCollection">T</a>&gt;&nbsp;<a href="TaskProvider.html" title="interface in org.gradle.api.tasks">TaskProvider</a>&lt;S&gt;&nbsp;named&#8203;(java.lang.String&nbsp;name,
                                    java.lang.Class&lt;S&gt;&nbsp;type)
                             throws <a href="../UnknownTaskException.html" title="class in org.gradle.api">UnknownTaskException</a></pre>
<div class="block">Locates a object by name and type, without triggering its creation or configuration, failing if there is no such object.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../NamedDomainObjectCollection.html#named-java.lang.String-java.lang.Class-">named</a></code>&nbsp;in interface&nbsp;<code><a href="../NamedDomainObjectCollection.html" title="interface in org.gradle.api">NamedDomainObjectCollection</a>&lt;<a href="TaskCollection.html" title="type parameter in TaskCollection">T</a> extends <a href="../Task.html" title="interface in org.gradle.api">Task</a>&gt;</code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>name</code> - The object's name</dd>
<dd><code>type</code> - The object's type</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>A <a href="../provider/Provider.html" title="interface in org.gradle.api.provider"><code>Provider</code></a> that will return the object when queried. The object may be created and configured at this point, if not already.</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code><a href="../UnknownTaskException.html" title="class in org.gradle.api">UnknownTaskException</a></code></dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>5.0</dd>
</dl>
</li>
</ul>
<a name="named-java.lang.String-java.lang.Class-org.gradle.api.Action-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>named</h4>
<pre class="methodSignature">&lt;S extends <a href="TaskCollection.html" title="type parameter in TaskCollection">T</a>&gt;&nbsp;<a href="TaskProvider.html" title="interface in org.gradle.api.tasks">TaskProvider</a>&lt;S&gt;&nbsp;named&#8203;(java.lang.String&nbsp;name,
                                    java.lang.Class&lt;S&gt;&nbsp;type,
                                    <a href="../Action.html" title="interface in org.gradle.api">Action</a>&lt;? super S&gt;&nbsp;configurationAction)
                             throws <a href="../UnknownTaskException.html" title="class in org.gradle.api">UnknownTaskException</a></pre>
<div class="block">Locates a object by name and type, without triggering its creation or configuration, failing if there is no such object.
 The given configure action is executed against the object before it is returned from the provider.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../NamedDomainObjectCollection.html#named-java.lang.String-java.lang.Class-org.gradle.api.Action-">named</a></code>&nbsp;in interface&nbsp;<code><a href="../NamedDomainObjectCollection.html" title="interface in org.gradle.api">NamedDomainObjectCollection</a>&lt;<a href="TaskCollection.html" title="type parameter in TaskCollection">T</a> extends <a href="../Task.html" title="interface in org.gradle.api">Task</a>&gt;</code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>name</code> - The object's name</dd>
<dd><code>type</code> - The object's type</dd>
<dd><code>configurationAction</code> - The action to use to configure the object.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>A <a href="../provider/Provider.html" title="interface in org.gradle.api.provider"><code>Provider</code></a> that will return the object when queried. The object may be created and configured at this point, if not already.</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code><a href="../UnknownTaskException.html" title="class in org.gradle.api">UnknownTaskException</a></code></dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>5.0</dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
