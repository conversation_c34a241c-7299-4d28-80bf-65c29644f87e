<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc -->
<title>TestFilter (Gradle API 8.4)</title>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<link rel="stylesheet" type="text/css" href="../../../../../javadoc.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../jquery/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
<script type="text/javascript" src="../../../../../jquery/jszip/dist/jszip.min.js"></script>
<script type="text/javascript" src="../../../../../jquery/jszip-utils/dist/jszip-utils.min.js"></script>
<!--[if IE]>
<script type="text/javascript" src="../../../../../jquery/jszip-utils/dist/jszip-utils-ie.min.js"></script>
<![endif]-->
<script type="text/javascript" src="../../../../../jquery/jquery-3.6.0.min.js"></script>
<script type="text/javascript" src="../../../../../jquery/jquery-ui.min.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="TestFilter (Gradle API 8.4)";
        }
    }
    catch(err) {
    }
//-->
var data = {"i0":6,"i1":6,"i2":6,"i3":6,"i4":6,"i5":6,"i6":6,"i7":6,"i8":6,"i9":6};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],4:["t3","Abstract Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
var pathtoroot = "../../../../../";
var useModuleDirectories = false;
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="fixedNav">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../../allclasses.html">All&nbsp;Classes</a></li>
</ul>
<ul class="navListSearch">
<li><label for="search">SEARCH:</label>
<input type="text" id="search" value="search" disabled="disabled">
<input type="reset" id="reset" value="reset" disabled="disabled">
</li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
</div>
<div class="navPadding">&nbsp;</div>
<script type="text/javascript"><!--
$('.navPadding').css('padding-top', $('.fixedNav').css("height"));
//-->
</script>
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle"><span class="packageLabelInType">Package</span>&nbsp;<a href="package-summary.html">org.gradle.api.tasks.testing</a></div>
<h2 title="Interface TestFilter" class="title">Interface TestFilter</h2>
</div>
<div class="contentContainer">
<div class="description">
<ul class="blockList">
<li class="blockList">
<hr>
<pre>public interface <span class="typeNameLabel">TestFilter</span></pre>
<div class="block">Allows filtering tests for execution. Some examples:

 <pre class='autoTested'>
   apply plugin: 'java'

   test {
       filter {
          //specific test class, this can match 'SomeTest' class and corresponding method under any package
          includeTestsMatching "SomeTest"
          includeTestsMatching "SomeTest.someTestMethod*"

          //specific test class
          includeTestsMatching "org.gradle.SomeTest"

          //specific test class and method
          includeTestsMatching "org.gradle.SomeTest.someSpecificFeature"
          includeTest "org.gradle.SomeTest", "someTestMethod"

          //specific test method, use wildcard
          includeTestsMatching "*SomeTest.someSpecificFeature"

          //specific test class, wildcard for packages
          includeTestsMatching "*.SomeTest"

          //all classes in package, recursively
          includeTestsMatching "com.gradle.tooling.*"

          //all integration tests, by naming convention
          includeTestsMatching "*IntegTest"

          //only ui tests from integration tests, by some naming convention
          includeTestsMatching "*IntegTest*ui"

          //exclude a specific test by its name
          excludeTestsMatching "*canDoSomethingSpecific"
          //excluding tests by name also works for test names which have spaces
          excludeTestsMatching "*can do something specific"
       }
   }

 </pre></div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>1.10</dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t3" class="tableTab"><span><a href="javascript:show(4);">Abstract Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colSecond" scope="col">Method</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code><a href="TestFilter.html" title="interface in org.gradle.api.tasks.testing">TestFilter</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#excludeTest-java.lang.String-java.lang.String-">excludeTest</a></span>&#8203;(java.lang.String&nbsp;className,
           java.lang.String&nbsp;methodName)</code></th>
<td class="colLast">
<div class="block">Excludes a test method specified by test class name and method name.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code><a href="TestFilter.html" title="interface in org.gradle.api.tasks.testing">TestFilter</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#excludeTestsMatching-java.lang.String-">excludeTestsMatching</a></span>&#8203;(java.lang.String&nbsp;testNamePattern)</code></th>
<td class="colLast">
<div class="block">Appends a test name pattern to the exclusion filter.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>java.util.Set&lt;java.lang.String&gt;</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getExcludePatterns--">getExcludePatterns</a></span>()</code></th>
<td class="colLast">
<div class="block">Returns the excluded test name patterns.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>java.util.Set&lt;java.lang.String&gt;</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getIncludePatterns--">getIncludePatterns</a></span>()</code></th>
<td class="colLast">
<div class="block">Returns the included test name patterns.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code><a href="TestFilter.html" title="interface in org.gradle.api.tasks.testing">TestFilter</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#includeTest-java.lang.String-java.lang.String-">includeTest</a></span>&#8203;(java.lang.String&nbsp;className,
           java.lang.String&nbsp;methodName)</code></th>
<td class="colLast">
<div class="block">Add a test method specified by test class name and method name.</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code><a href="TestFilter.html" title="interface in org.gradle.api.tasks.testing">TestFilter</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#includeTestsMatching-java.lang.String-">includeTestsMatching</a></span>&#8203;(java.lang.String&nbsp;testNamePattern)</code></th>
<td class="colLast">
<div class="block">Appends a test name pattern to the inclusion filter.</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#isFailOnNoMatchingTests--">isFailOnNoMatchingTests</a></span>()</code></th>
<td class="colLast">
<div class="block">Returns whether the task should fail if no matching tests where found.</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code><a href="TestFilter.html" title="interface in org.gradle.api.tasks.testing">TestFilter</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setExcludePatterns-java.lang.String...-">setExcludePatterns</a></span>&#8203;(java.lang.String...&nbsp;testNamePatterns)</code></th>
<td class="colLast">
<div class="block">Sets the test name patterns to be excluded in the filter.</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setFailOnNoMatchingTests-boolean-">setFailOnNoMatchingTests</a></span>&#8203;(boolean&nbsp;failOnNoMatchingTests)</code></th>
<td class="colLast">
<div class="block">Let the test task fail if a filter configuration was provided but no test matched the given configuration.</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code><a href="TestFilter.html" title="interface in org.gradle.api.tasks.testing">TestFilter</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setIncludePatterns-java.lang.String...-">setIncludePatterns</a></span>&#8203;(java.lang.String...&nbsp;testNamePatterns)</code></th>
<td class="colLast">
<div class="block">Sets the test name patterns to be included in the filter.</div>
</td>
</tr>
</table>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="includeTestsMatching-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>includeTestsMatching</h4>
<pre class="methodSignature"><a href="TestFilter.html" title="interface in org.gradle.api.tasks.testing">TestFilter</a>&nbsp;includeTestsMatching&#8203;(java.lang.String&nbsp;testNamePattern)</pre>
<div class="block">Appends a test name pattern to the inclusion filter. Wildcard '*' is supported, either test method name or class name is supported. Examples of test names: "com.foo.FooTest.someMethod",
 "com.foo.FooTest", "*FooTest*", "com.foo*". See examples in the docs for <a href="TestFilter.html" title="interface in org.gradle.api.tasks.testing"><code>TestFilter</code></a>.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>testNamePattern</code> - test name pattern to include, can be class or method name, can contain wildcard '*'</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>this filter object</dd>
</dl>
</li>
</ul>
<a name="excludeTestsMatching-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>excludeTestsMatching</h4>
<pre class="methodSignature"><a href="TestFilter.html" title="interface in org.gradle.api.tasks.testing">TestFilter</a>&nbsp;excludeTestsMatching&#8203;(java.lang.String&nbsp;testNamePattern)</pre>
<div class="block">Appends a test name pattern to the exclusion filter. Wildcard '*' is supported, either test
 method name or class name is supported. Examples of test names: "com.foo.FooTest.someMethod",
 "com.foo.FooTest", "*FooTest*", "com.foo*", "*someTestMethod". See examples in the docs for <a href="TestFilter.html" title="interface in org.gradle.api.tasks.testing"><code>TestFilter</code></a>.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>testNamePattern</code> - test name pattern to exclude, can be class or method name, can contain wildcard '*'</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>this filter object</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>5.0</dd>
</dl>
</li>
</ul>
<a name="getIncludePatterns--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getIncludePatterns</h4>
<pre class="methodSignature"><a href="../Input.html" title="annotation in org.gradle.api.tasks">@Input</a>
java.util.Set&lt;java.lang.String&gt;&nbsp;getIncludePatterns()</pre>
<div class="block">Returns the included test name patterns. They can be class or method names and may contain wildcard '*'. Test name patterns can be appended via <a href="#includeTestsMatching-java.lang.String-"><code>includeTestsMatching(String)</code></a> or set via
 <a href="#setIncludePatterns-java.lang.String...-"><code>setIncludePatterns(String...)</code></a>.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>included test name patterns</dd>
</dl>
</li>
</ul>
<a name="getExcludePatterns--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getExcludePatterns</h4>
<pre class="methodSignature"><a href="../Input.html" title="annotation in org.gradle.api.tasks">@Input</a>
java.util.Set&lt;java.lang.String&gt;&nbsp;getExcludePatterns()</pre>
<div class="block">Returns the excluded test name patterns. They can be class or method names and may contain wildcard '*'.
 Test name patterns can be appended via <a href="#excludeTestsMatching-java.lang.String-"><code>excludeTestsMatching(String)</code></a> or set via
 <a href="#setExcludePatterns-java.lang.String...-"><code>setExcludePatterns(String...)</code></a>.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>included test name patterns</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>5.0</dd>
</dl>
</li>
</ul>
<a name="setIncludePatterns-java.lang.String...-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setIncludePatterns</h4>
<pre class="methodSignature"><a href="TestFilter.html" title="interface in org.gradle.api.tasks.testing">TestFilter</a>&nbsp;setIncludePatterns&#8203;(java.lang.String...&nbsp;testNamePatterns)</pre>
<div class="block">Sets the test name patterns to be included in the filter. Wildcard '*' is supported. Replaces any existing test name patterns.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>testNamePatterns</code> - class or method name patterns to set, may contain wildcard '*'</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>this filter object</dd>
</dl>
</li>
</ul>
<a name="setExcludePatterns-java.lang.String...-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setExcludePatterns</h4>
<pre class="methodSignature"><a href="TestFilter.html" title="interface in org.gradle.api.tasks.testing">TestFilter</a>&nbsp;setExcludePatterns&#8203;(java.lang.String...&nbsp;testNamePatterns)</pre>
<div class="block">Sets the test name patterns to be excluded in the filter. Wildcard '*' is supported. Replaces any existing test name patterns.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>testNamePatterns</code> - class or method name patterns to set, may contain wildcard '*'</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>this filter object</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>5.0</dd>
</dl>
</li>
</ul>
<a name="includeTest-java.lang.String-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>includeTest</h4>
<pre class="methodSignature"><a href="TestFilter.html" title="interface in org.gradle.api.tasks.testing">TestFilter</a>&nbsp;includeTest&#8203;(java.lang.String&nbsp;className,
                       java.lang.String&nbsp;methodName)</pre>
<div class="block">Add a test method specified by test class name and method name.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>className</code> - the class name of the test to execute</dd>
<dd><code>methodName</code> - the method name of the test to execute. Can be null.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>this filter object</dd>
</dl>
</li>
</ul>
<a name="excludeTest-java.lang.String-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>excludeTest</h4>
<pre class="methodSignature"><a href="TestFilter.html" title="interface in org.gradle.api.tasks.testing">TestFilter</a>&nbsp;excludeTest&#8203;(java.lang.String&nbsp;className,
                       java.lang.String&nbsp;methodName)</pre>
<div class="block">Excludes a test method specified by test class name and method name.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>className</code> - the class name of the test to exclude</dd>
<dd><code>methodName</code> - the method name of the test to exclude. Can be null.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>this filter object</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>5.0</dd>
</dl>
</li>
</ul>
<a name="setFailOnNoMatchingTests-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setFailOnNoMatchingTests</h4>
<pre class="methodSignature">void&nbsp;setFailOnNoMatchingTests&#8203;(boolean&nbsp;failOnNoMatchingTests)</pre>
<div class="block">Let the test task fail if a filter configuration was provided but no test matched the given configuration.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>failOnNoMatchingTests</code> - whether a test task should fail if no test is matching the filter configuration.</dd>
</dl>
</li>
</ul>
<a name="isFailOnNoMatchingTests--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>isFailOnNoMatchingTests</h4>
<pre class="methodSignature"><a href="../Input.html" title="annotation in org.gradle.api.tasks">@Input</a>
boolean&nbsp;isFailOnNoMatchingTests()</pre>
<div class="block">Returns whether the task should fail if no matching tests where found.
 The default is true.</div>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../../allclasses.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
