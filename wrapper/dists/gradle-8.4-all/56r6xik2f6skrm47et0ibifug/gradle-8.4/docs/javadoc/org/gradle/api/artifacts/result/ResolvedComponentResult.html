<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc -->
<title>ResolvedComponentResult (Gradle API 8.4)</title>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<link rel="stylesheet" type="text/css" href="../../../../../javadoc.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../jquery/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
<script type="text/javascript" src="../../../../../jquery/jszip/dist/jszip.min.js"></script>
<script type="text/javascript" src="../../../../../jquery/jszip-utils/dist/jszip-utils.min.js"></script>
<!--[if IE]>
<script type="text/javascript" src="../../../../../jquery/jszip-utils/dist/jszip-utils-ie.min.js"></script>
<![endif]-->
<script type="text/javascript" src="../../../../../jquery/jquery-3.6.0.min.js"></script>
<script type="text/javascript" src="../../../../../jquery/jquery-ui.min.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="ResolvedComponentResult (Gradle API 8.4)";
        }
    }
    catch(err) {
    }
//-->
var data = {"i0":6,"i1":6,"i2":6,"i3":6,"i4":6,"i5":6};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],4:["t3","Abstract Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
var pathtoroot = "../../../../../";
var useModuleDirectories = false;
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="fixedNav">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../../allclasses.html">All&nbsp;Classes</a></li>
</ul>
<ul class="navListSearch">
<li><label for="search">SEARCH:</label>
<input type="text" id="search" value="search" disabled="disabled">
<input type="reset" id="reset" value="reset" disabled="disabled">
</li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
</div>
<div class="navPadding">&nbsp;</div>
<script type="text/javascript"><!--
$('.navPadding').css('padding-top', $('.fixedNav').css("height"));
//-->
</script>
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle"><span class="packageLabelInType">Package</span>&nbsp;<a href="package-summary.html">org.gradle.api.artifacts.result</a></div>
<h2 title="Interface ResolvedComponentResult" class="title">Interface ResolvedComponentResult</h2>
</div>
<div class="contentContainer">
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Superinterfaces:</dt>
<dd><code><a href="ComponentResult.html" title="interface in org.gradle.api.artifacts.result">ComponentResult</a></code></dd>
</dl>
<hr>
<pre>public interface <span class="typeNameLabel">ResolvedComponentResult</span>
extends <a href="ComponentResult.html" title="interface in org.gradle.api.artifacts.result">ComponentResult</a></pre>
<div class="block">Represents a component instance in the resolved dependency graph. Provides some basic identity and dependency information about the component.</div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t3" class="tableTab"><span><a href="javascript:show(4);">Abstract Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colSecond" scope="col">Method</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>java.util.Set&lt;? extends <a href="DependencyResult.html" title="interface in org.gradle.api.artifacts.result">DependencyResult</a>&gt;</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getDependencies--">getDependencies</a></span>()</code></th>
<td class="colLast">
<div class="block">Returns the dependencies of this component.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>java.util.List&lt;<a href="DependencyResult.html" title="interface in org.gradle.api.artifacts.result">DependencyResult</a>&gt;</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getDependenciesForVariant-org.gradle.api.artifacts.result.ResolvedVariantResult-">getDependenciesForVariant</a></span>&#8203;(<a href="ResolvedVariantResult.html" title="interface in org.gradle.api.artifacts.result">ResolvedVariantResult</a>&nbsp;variant)</code></th>
<td class="colLast">
<div class="block">Returns the dependencies of a specific variant.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>java.util.Set&lt;? extends <a href="ResolvedDependencyResult.html" title="interface in org.gradle.api.artifacts.result">ResolvedDependencyResult</a>&gt;</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getDependents--">getDependents</a></span>()</code></th>
<td class="colLast">
<div class="block">Returns the incoming dependencies of this component.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code><a href="../ModuleVersionIdentifier.html" title="interface in org.gradle.api.artifacts">ModuleVersionIdentifier</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getModuleVersion--">getModuleVersion</a></span>()</code></th>
<td class="colLast">
<div class="block">Returns the module version which this component belongs to, if any.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code><a href="ComponentSelectionReason.html" title="interface in org.gradle.api.artifacts.result">ComponentSelectionReason</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getSelectionReason--">getSelectionReason</a></span>()</code></th>
<td class="colLast">
<div class="block">Returns the reason why this particular component was selected in the result.</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>java.util.List&lt;<a href="ResolvedVariantResult.html" title="interface in org.gradle.api.artifacts.result">ResolvedVariantResult</a>&gt;</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getVariants--">getVariants</a></span>()</code></th>
<td class="colLast">
<div class="block">Returns the variants that were selected for this component.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.org.gradle.api.artifacts.result.ComponentResult">
<!--   -->
</a>
<h3>Methods inherited from interface&nbsp;org.gradle.api.artifacts.result.<a href="ComponentResult.html" title="interface in org.gradle.api.artifacts.result">ComponentResult</a></h3>
<code><a href="ComponentResult.html#getId--">getId</a></code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="getDependencies--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getDependencies</h4>
<pre class="methodSignature">java.util.Set&lt;? extends <a href="DependencyResult.html" title="interface in org.gradle.api.artifacts.result">DependencyResult</a>&gt;&nbsp;getDependencies()</pre>
<div class="block"><p>Returns the dependencies of this component. Includes resolved and unresolved dependencies (if any).

 <p>The elements of the returned collection are declared as <a href="DependencyResult.html" title="interface in org.gradle.api.artifacts.result"><code>DependencyResult</code></a>, however the dependency instances will also implement one of the
 following interfaces:</p>

 <ul>
     <li><a href="ResolvedDependencyResult.html" title="interface in org.gradle.api.artifacts.result"><code>ResolvedDependencyResult</code></a> for dependencies which were successfully resolved.</li>
     <li><a href="UnresolvedDependencyResult.html" title="interface in org.gradle.api.artifacts.result"><code>UnresolvedDependencyResult</code></a> for dependencies which could not be resolved for some reason.</li>
 </ul></div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the dependencies of this component</dd>
</dl>
</li>
</ul>
<a name="getDependents--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getDependents</h4>
<pre class="methodSignature">java.util.Set&lt;? extends <a href="ResolvedDependencyResult.html" title="interface in org.gradle.api.artifacts.result">ResolvedDependencyResult</a>&gt;&nbsp;getDependents()</pre>
<div class="block">Returns the incoming dependencies of this component.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the dependents of this component</dd>
</dl>
</li>
</ul>
<a name="getSelectionReason--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getSelectionReason</h4>
<pre class="methodSignature"><a href="ComponentSelectionReason.html" title="interface in org.gradle.api.artifacts.result">ComponentSelectionReason</a>&nbsp;getSelectionReason()</pre>
<div class="block">Returns the reason why this particular component was selected in the result.
 Useful if multiple candidate components were found during dependency resolution.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the reason for selecting the component</dd>
</dl>
</li>
</ul>
<a name="getModuleVersion--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getModuleVersion</h4>
<pre class="methodSignature">@Nullable
<a href="../ModuleVersionIdentifier.html" title="interface in org.gradle.api.artifacts">ModuleVersionIdentifier</a>&nbsp;getModuleVersion()</pre>
<div class="block">Returns the module version which this component belongs to, if any. A component will belong to a module version if it was found in some repository, or if the
 module version for the component has been declared, usually by declaring how the component should be published.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the module version of the component, or <code>null</code> if this component has no associated module version.</dd>
</dl>
</li>
</ul>
<a name="getVariants--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getVariants</h4>
<pre class="methodSignature">java.util.List&lt;<a href="ResolvedVariantResult.html" title="interface in org.gradle.api.artifacts.result">ResolvedVariantResult</a>&gt;&nbsp;getVariants()</pre>
<div class="block">Returns the variants that were selected for this component. When Gradle metadata is not used, this usually only refers to the target
 "configuration" (for an Ivy dependency) or "scope" (for a Maven dependency).</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the resolved variants for this component</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>5.2</dd>
</dl>
</li>
</ul>
<a name="getDependenciesForVariant-org.gradle.api.artifacts.result.ResolvedVariantResult-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>getDependenciesForVariant</h4>
<pre class="methodSignature">java.util.List&lt;<a href="DependencyResult.html" title="interface in org.gradle.api.artifacts.result">DependencyResult</a>&gt;&nbsp;getDependenciesForVariant&#8203;(<a href="ResolvedVariantResult.html" title="interface in org.gradle.api.artifacts.result">ResolvedVariantResult</a>&nbsp;variant)</pre>
<div class="block">Returns the dependencies of a specific variant. It is possible for a component to be selected multiple
 times with different variants (for example, the main component and its test fixtures). The dependencies
 of each variant are different, but the <a href="#getDependencies--"><code>method</code></a> doesn't give access to each
 variant individual dependencies.

 <p>
 The variant must be a <a href="#getVariants--">selected variant</a> of this component.
 </p></div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>variant</code> - the variant to find the dependencies for</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code>java.lang.IllegalStateException</code> - if the variant is not a selected variant of this component</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>5.6</dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../../allclasses.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
