<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc -->
<title>org.gradle.api.file (Gradle API 8.4)</title>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<link rel="stylesheet" type="text/css" href="../../../../javadoc.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../jquery/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
<script type="text/javascript" src="../../../../jquery/jszip/dist/jszip.min.js"></script>
<script type="text/javascript" src="../../../../jquery/jszip-utils/dist/jszip-utils.min.js"></script>
<!--[if IE]>
<script type="text/javascript" src="../../../../jquery/jszip-utils/dist/jszip-utils-ie.min.js"></script>
<![endif]-->
<script type="text/javascript" src="../../../../jquery/jquery-3.6.0.min.js"></script>
<script type="text/javascript" src="../../../../jquery/jquery-ui.min.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="org.gradle.api.file (Gradle API 8.4)";
        }
    }
    catch(err) {
    }
//-->
var pathtoroot = "../../../../";
var useModuleDirectories = false;
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="fixedNav">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../index.html">Overview</a></li>
<li class="navBarCell1Rev">Package</li>
<li>Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses.html">All&nbsp;Classes</a></li>
</ul>
<ul class="navListSearch">
<li><label for="search">SEARCH:</label>
<input type="text" id="search" value="search" disabled="disabled">
<input type="reset" id="reset" value="reset" disabled="disabled">
</li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
</div>
<div class="navPadding">&nbsp;</div>
<script type="text/javascript"><!--
$('.navPadding').css('padding-top', $('.fixedNav').css("height"));
//-->
</script>
<div class="header">
<p><a href="../NonNullApi.html" title="annotation in org.gradle.api">@NonNullApi</a>
</p>
<h1 title="Package" class="title">Package&nbsp;org.gradle.api.file</h1>
</div>
<div class="contentContainer"><a name="package.description">
<!--   -->
</a>
<div class="block">Classes for working with files.</div>
<ul class="blockList">
<li class="blockList">
<table class="typeSummary" summary="Interface Summary table, listing interfaces, and an explanation">
<caption><span>Interface Summary</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Interface</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tbody>
<tr class="altColor">
<th class="colFirst" scope="row"><a href="ArchiveOperations.html" title="interface in org.gradle.api.file">ArchiveOperations</a></th>
<td class="colLast">
<div class="block">Operations on archives such as ZIP or TAR files.</div>
</td>
</tr>
<tr class="rowColor">
<th class="colFirst" scope="row"><a href="ConfigurableFileCollection.html" title="interface in org.gradle.api.file">ConfigurableFileCollection</a></th>
<td class="colLast">
<div class="block">A <code>ConfigurableFileCollection</code> is a mutable <code>FileCollection</code>.</div>
</td>
</tr>
<tr class="altColor">
<th class="colFirst" scope="row"><a href="ConfigurableFilePermissions.html" title="interface in org.gradle.api.file">ConfigurableFilePermissions</a></th>
<td class="colLast">
<div class="block">Provides the means of specifying file and directory access permissions for all classes of system users.</div>
</td>
</tr>
<tr class="rowColor">
<th class="colFirst" scope="row"><a href="ConfigurableFileTree.html" title="interface in org.gradle.api.file">ConfigurableFileTree</a></th>
<td class="colLast">
<div class="block">A <a href="FileTree.html" title="interface in org.gradle.api.file"><code>FileTree</code></a> with a single base directory, which can be configured and modified.</div>
</td>
</tr>
<tr class="altColor">
<th class="colFirst" scope="row"><a href="ConfigurableUserClassFilePermissions.html" title="interface in org.gradle.api.file">ConfigurableUserClassFilePermissions</a></th>
<td class="colLast">
<div class="block">Provides the means of specifying file and directory access permissions for a certain class of users (see <a href="ConfigurableFilePermissions.html" title="interface in org.gradle.api.file"><code>ConfigurableFilePermissions</code></a>).</div>
</td>
</tr>
<tr class="rowColor">
<th class="colFirst" scope="row"><a href="ContentFilterable.html" title="interface in org.gradle.api.file">ContentFilterable</a></th>
<td class="colLast">
<div class="block">Represents some binary resource whose content can be filtered.</div>
</td>
</tr>
<tr class="altColor">
<th class="colFirst" scope="row"><a href="CopyProcessingSpec.html" title="interface in org.gradle.api.file">CopyProcessingSpec</a></th>
<td class="colLast">
<div class="block">Specifies the destination of a copy.</div>
</td>
</tr>
<tr class="rowColor">
<th class="colFirst" scope="row"><a href="CopySourceSpec.html" title="interface in org.gradle.api.file">CopySourceSpec</a></th>
<td class="colLast">
<div class="block">Specifies sources for a file copy.</div>
</td>
</tr>
<tr class="altColor">
<th class="colFirst" scope="row"><a href="CopySpec.html" title="interface in org.gradle.api.file">CopySpec</a></th>
<td class="colLast">
<div class="block">A set of specifications for copying files.</div>
</td>
</tr>
<tr class="rowColor">
<th class="colFirst" scope="row"><a href="DeleteSpec.html" title="interface in org.gradle.api.file">DeleteSpec</a></th>
<td class="colLast">
<div class="block">A specification for deleting files from the filesystem.</div>
</td>
</tr>
<tr class="altColor">
<th class="colFirst" scope="row"><a href="Directory.html" title="interface in org.gradle.api.file">Directory</a></th>
<td class="colLast">
<div class="block">Represents a directory at some fixed location on the file system.</div>
</td>
</tr>
<tr class="rowColor">
<th class="colFirst" scope="row"><a href="DirectoryProperty.html" title="interface in org.gradle.api.file">DirectoryProperty</a></th>
<td class="colLast">
<div class="block">Represents some configurable directory location, whose value is mutable.</div>
</td>
</tr>
<tr class="altColor">
<th class="colFirst" scope="row"><a href="DirectoryTree.html" title="interface in org.gradle.api.file">DirectoryTree</a></th>
<td class="colLast">
<div class="block">A directory with some associated include and exclude patterns.</div>
</td>
</tr>
<tr class="rowColor">
<th class="colFirst" scope="row"><a href="ExpandDetails.html" title="interface in org.gradle.api.file">ExpandDetails</a></th>
<td class="colLast">
<div class="block">Additional configuration parameters for <a href="ContentFilterable.html#expand-java.util.Map-org.gradle.api.Action-"><code>ContentFilterable.expand(Map, Action)</code></a> action.</div>
</td>
</tr>
<tr class="altColor">
<th class="colFirst" scope="row"><a href="FileCollection.html" title="interface in org.gradle.api.file">FileCollection</a></th>
<td class="colLast">
<div class="block">A <code>FileCollection</code> represents a collection of file system locations which you can query in certain ways.</div>
</td>
</tr>
<tr class="rowColor">
<th class="colFirst" scope="row"><a href="FileContents.html" title="interface in org.gradle.api.file">FileContents</a></th>
<td class="colLast">
<div class="block">Provides lazy access to the contents of a given file.</div>
</td>
</tr>
<tr class="altColor">
<th class="colFirst" scope="row"><a href="FileCopyDetails.html" title="interface in org.gradle.api.file">FileCopyDetails</a></th>
<td class="colLast">
<div class="block">Provides details about a file or directory about to be copied, and allows some aspects of the destination file to
 be modified.</div>
</td>
</tr>
<tr class="rowColor">
<th class="colFirst" scope="row"><a href="FilePermissions.html" title="interface in org.gradle.api.file">FilePermissions</a></th>
<td class="colLast">
<div class="block">Describes file and directory access permissions for all classes of system users.</div>
</td>
</tr>
<tr class="altColor">
<th class="colFirst" scope="row"><a href="FileSystemLocation.html" title="interface in org.gradle.api.file">FileSystemLocation</a></th>
<td class="colLast">
<div class="block">Represents some immutable location on the file system.</div>
</td>
</tr>
<tr class="rowColor">
<th class="colFirst" scope="row"><a href="FileSystemLocationProperty.html" title="interface in org.gradle.api.file">FileSystemLocationProperty</a>&lt;T extends <a href="FileSystemLocation.html" title="interface in org.gradle.api.file">FileSystemLocation</a>&gt;</th>
<td class="colLast">
<div class="block">Represents some element of the file system.</div>
</td>
</tr>
<tr class="altColor">
<th class="colFirst" scope="row"><a href="FileSystemOperations.html" title="interface in org.gradle.api.file">FileSystemOperations</a></th>
<td class="colLast">
<div class="block">Operations on the file system.</div>
</td>
</tr>
<tr class="rowColor">
<th class="colFirst" scope="row"><a href="FileTree.html" title="interface in org.gradle.api.file">FileTree</a></th>
<td class="colLast">
<div class="block">A <code>FileTree</code> represents a hierarchy of files.</div>
</td>
</tr>
<tr class="altColor">
<th class="colFirst" scope="row"><a href="FileTreeElement.html" title="interface in org.gradle.api.file">FileTreeElement</a></th>
<td class="colLast">
<div class="block">Information about a file in a directory/file tree.</div>
</td>
</tr>
<tr class="rowColor">
<th class="colFirst" scope="row"><a href="FileVisitDetails.html" title="interface in org.gradle.api.file">FileVisitDetails</a></th>
<td class="colLast">
<div class="block">Provides access to details about a file or directory being visited by a <a href="FileVisitor.html" title="interface in org.gradle.api.file"><code>FileVisitor</code></a>.</div>
</td>
</tr>
<tr class="altColor">
<th class="colFirst" scope="row"><a href="FileVisitor.html" title="interface in org.gradle.api.file">FileVisitor</a></th>
<td class="colLast">
<div class="block">A <code>FileVisitor</code> is used to visit each of the files in a <a href="FileTree.html" title="interface in org.gradle.api.file"><code>FileTree</code></a>.</div>
</td>
</tr>
<tr class="rowColor">
<th class="colFirst" scope="row"><a href="ProjectLayout.html" title="interface in org.gradle.api.file">ProjectLayout</a></th>
<td class="colLast">
<div class="block">Provides access to several important locations for a project.</div>
</td>
</tr>
<tr class="altColor">
<th class="colFirst" scope="row"><a href="RegularFile.html" title="interface in org.gradle.api.file">RegularFile</a></th>
<td class="colLast">
<div class="block">Represents a regular file at a fixed location on the file system.</div>
</td>
</tr>
<tr class="rowColor">
<th class="colFirst" scope="row"><a href="RegularFileProperty.html" title="interface in org.gradle.api.file">RegularFileProperty</a></th>
<td class="colLast">
<div class="block">Represents some configurable regular file location, whose value is mutable.</div>
</td>
</tr>
<tr class="altColor">
<th class="colFirst" scope="row"><a href="ReproducibleFileVisitor.html" title="interface in org.gradle.api.file">ReproducibleFileVisitor</a></th>
<td class="colLast">
<div class="block">Visitor which can request a reproducible file order.</div>
</td>
</tr>
<tr class="rowColor">
<th class="colFirst" scope="row"><a href="SourceDirectorySet.html" title="interface in org.gradle.api.file">SourceDirectorySet</a></th>
<td class="colLast">
<div class="block">A <code>SourceDirectorySet</code> represents a set of source files composed from a set of source directories, along
 with associated include and exclude patterns.</div>
</td>
</tr>
<tr class="altColor">
<th class="colFirst" scope="row"><a href="SyncSpec.html" title="interface in org.gradle.api.file">SyncSpec</a></th>
<td class="colLast">
<div class="block">Synchronizes the contents of a destination directory with some source directories and files.</div>
</td>
</tr>
<tr class="rowColor">
<th class="colFirst" scope="row"><a href="UserClassFilePermissions.html" title="interface in org.gradle.api.file">UserClassFilePermissions</a></th>
<td class="colLast">
<div class="block">Describes file and directory access permissions for a certain class of users (see <a href="FilePermissions.html" title="interface in org.gradle.api.file"><code>FilePermissions</code></a>).</div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList">
<table class="typeSummary" summary="Class Summary table, listing classes, and an explanation">
<caption><span>Class Summary</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Class</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tbody>
<tr class="altColor">
<th class="colFirst" scope="row"><a href="EmptyFileVisitor.html" title="class in org.gradle.api.file">EmptyFileVisitor</a></th>
<td class="colLast">
<div class="block">The EmptyFileVisitor can be extends by implementations that only require to implement one of the 2 visit methods
 (dir or file).</div>
</td>
</tr>
<tr class="rowColor">
<th class="colFirst" scope="row"><a href="RelativePath.html" title="class in org.gradle.api.file">RelativePath</a></th>
<td class="colLast">
<div class="block">Represents a relative path from some base directory to a file.</div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList">
<table class="typeSummary" summary="Enum Summary table, listing enums, and an explanation">
<caption><span>Enum Summary</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Enum</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tbody>
<tr class="altColor">
<th class="colFirst" scope="row"><a href="DuplicatesStrategy.html" title="enum in org.gradle.api.file">DuplicatesStrategy</a></th>
<td class="colLast">
<div class="block">Strategies for dealing with the potential creation of duplicate files or archive entries.</div>
</td>
</tr>
<tr class="rowColor">
<th class="colFirst" scope="row"><a href="FileCollection.AntType.html" title="enum in org.gradle.api.file">FileCollection.AntType</a></th>
<td class="colLast">
<div class="block">Ant types which a <code>FileCollection</code> can be mapped to.</div>
</td>
</tr>
<tr class="altColor">
<th class="colFirst" scope="row"><a href="FileType.html" title="enum in org.gradle.api.file">FileType</a></th>
<td class="colLast">
<div class="block">The type of a file.</div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList">
<table class="typeSummary" summary="Exception Summary table, listing exceptions, and an explanation">
<caption><span>Exception Summary</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Exception</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tbody>
<tr class="altColor">
<th class="colFirst" scope="row"><a href="DuplicateFileCopyingException.html" title="class in org.gradle.api.file">DuplicateFileCopyingException</a></th>
<td class="colLast">
<div class="block">Thrown when more than one file with the same relative path name is to be copied
 and the <a href="DuplicatesStrategy.html" title="enum in org.gradle.api.file"><code>DuplicatesStrategy</code></a> is set to DuplicatesStrategy.FAIL</div>
</td>
</tr>
</tbody>
</table>
</li>
</ul>
</div>
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../index.html">Overview</a></li>
<li class="navBarCell1Rev">Package</li>
<li>Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
