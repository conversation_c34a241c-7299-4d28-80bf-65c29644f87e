<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc -->
<title>TestLogging (Gradle API 8.4)</title>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<link rel="stylesheet" type="text/css" href="../../../../../../javadoc.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../../jquery/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../../jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="../../../../../../script.js"></script>
<script type="text/javascript" src="../../../../../../jquery/jszip/dist/jszip.min.js"></script>
<script type="text/javascript" src="../../../../../../jquery/jszip-utils/dist/jszip-utils.min.js"></script>
<!--[if IE]>
<script type="text/javascript" src="../../../../../../jquery/jszip-utils/dist/jszip-utils-ie.min.js"></script>
<![endif]-->
<script type="text/javascript" src="../../../../../../jquery/jquery-3.6.0.min.js"></script>
<script type="text/javascript" src="../../../../../../jquery/jquery-ui.min.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="TestLogging (Gradle API 8.4)";
        }
    }
    catch(err) {
    }
//-->
var data = {"i0":6,"i1":6,"i2":6,"i3":6,"i4":6,"i5":6,"i6":6,"i7":6,"i8":6,"i9":6,"i10":6,"i11":6,"i12":6,"i13":6,"i14":6,"i15":6,"i16":6,"i17":6,"i18":6,"i19":6,"i20":6,"i21":6,"i22":6,"i23":6,"i24":6};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],4:["t3","Abstract Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
var pathtoroot = "../../../../../../";
var useModuleDirectories = false;
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="fixedNav">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../../../allclasses.html">All&nbsp;Classes</a></li>
</ul>
<ul class="navListSearch">
<li><label for="search">SEARCH:</label>
<input type="text" id="search" value="search" disabled="disabled">
<input type="reset" id="reset" value="reset" disabled="disabled">
</li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
</div>
<div class="navPadding">&nbsp;</div>
<script type="text/javascript"><!--
$('.navPadding').css('padding-top', $('.fixedNav').css("height"));
//-->
</script>
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle"><span class="packageLabelInType">Package</span>&nbsp;<a href="package-summary.html">org.gradle.api.tasks.testing.logging</a></div>
<h2 title="Interface TestLogging" class="title">Interface TestLogging</h2>
</div>
<div class="contentContainer">
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Known Subinterfaces:</dt>
<dd><code><a href="TestLoggingContainer.html" title="interface in org.gradle.api.tasks.testing.logging">TestLoggingContainer</a></code></dd>
</dl>
<hr>
<pre>public interface <span class="typeNameLabel">TestLogging</span></pre>
<div class="block">Options that determine which test events get logged, and at which detail.</div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t3" class="tableTab"><span><a href="javascript:show(4);">Abstract Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colSecond" scope="col">Method</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#events-java.lang.Object...-">events</a></span>&#8203;(java.lang.Object...&nbsp;events)</code></th>
<td class="colLast">
<div class="block">Sets the events to be logged.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>int</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getDisplayGranularity--">getDisplayGranularity</a></span>()</code></th>
<td class="colLast">
<div class="block">Returns the display granularity of the events to be logged.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>java.util.Set&lt;<a href="TestLogEvent.html" title="enum in org.gradle.api.tasks.testing.logging">TestLogEvent</a>&gt;</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getEvents--">getEvents</a></span>()</code></th>
<td class="colLast">
<div class="block">Returns the events to be logged.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code><a href="TestExceptionFormat.html" title="enum in org.gradle.api.tasks.testing.logging">TestExceptionFormat</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getExceptionFormat--">getExceptionFormat</a></span>()</code></th>
<td class="colLast">
<div class="block">Returns the format to be used for logging test exceptions.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>int</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getMaxGranularity--">getMaxGranularity</a></span>()</code></th>
<td class="colLast">
<div class="block">Returns the maximum granularity of the events to be logged.</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>int</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getMinGranularity--">getMinGranularity</a></span>()</code></th>
<td class="colLast">
<div class="block">Returns the minimum granularity of the events to be logged.</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getShowCauses--">getShowCauses</a></span>()</code></th>
<td class="colLast">
<div class="block">Tells whether causes of exceptions that occur during test execution will be logged.</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getShowExceptions--">getShowExceptions</a></span>()</code></th>
<td class="colLast">
<div class="block">Tells whether exceptions that occur during test execution will be logged.</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getShowStackTraces--">getShowStackTraces</a></span>()</code></th>
<td class="colLast">
<div class="block">Tells whether stack traces of exceptions that occur during test execution will be logged.</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getShowStandardStreams--">getShowStandardStreams</a></span>()</code></th>
<td class="colLast">
<div class="block">Tells whether output on standard out and standard error will be logged.</div>
</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code>java.util.Set&lt;<a href="TestStackTraceFilter.html" title="enum in org.gradle.api.tasks.testing.logging">TestStackTraceFilter</a>&gt;</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getStackTraceFilters--">getStackTraceFilters</a></span>()</code></th>
<td class="colLast">
<div class="block">Returns the set of filters to be used for sanitizing test stack traces.</div>
</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setDisplayGranularity-int-">setDisplayGranularity</a></span>&#8203;(int&nbsp;granularity)</code></th>
<td class="colLast">
<div class="block">Sets the display granularity of the events to be logged.</div>
</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setEvents-java.lang.Iterable-">setEvents</a></span>&#8203;(java.lang.Iterable&lt;?&gt;&nbsp;events)</code></th>
<td class="colLast">
<div class="block">Sets the events to be logged.</div>
</td>
</tr>
<tr id="i13" class="rowColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setEvents-java.util.Set-">setEvents</a></span>&#8203;(java.util.Set&lt;<a href="TestLogEvent.html" title="enum in org.gradle.api.tasks.testing.logging">TestLogEvent</a>&gt;&nbsp;events)</code></th>
<td class="colLast">
<div class="block">Sets the events to be logged.</div>
</td>
</tr>
<tr id="i14" class="altColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setExceptionFormat-java.lang.Object-">setExceptionFormat</a></span>&#8203;(java.lang.Object&nbsp;exceptionFormat)</code></th>
<td class="colLast">
<div class="block">Sets the format to be used for logging test exceptions.</div>
</td>
</tr>
<tr id="i15" class="rowColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setExceptionFormat-org.gradle.api.tasks.testing.logging.TestExceptionFormat-">setExceptionFormat</a></span>&#8203;(<a href="TestExceptionFormat.html" title="enum in org.gradle.api.tasks.testing.logging">TestExceptionFormat</a>&nbsp;exceptionFormat)</code></th>
<td class="colLast">
<div class="block">Sets the format to be used for logging test exceptions.</div>
</td>
</tr>
<tr id="i16" class="altColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setMaxGranularity-int-">setMaxGranularity</a></span>&#8203;(int&nbsp;granularity)</code></th>
<td class="colLast">
<div class="block">Returns the maximum granularity of the events to be logged.</div>
</td>
</tr>
<tr id="i17" class="rowColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setMinGranularity-int-">setMinGranularity</a></span>&#8203;(int&nbsp;granularity)</code></th>
<td class="colLast">
<div class="block">Sets the minimum granularity of the events to be logged.</div>
</td>
</tr>
<tr id="i18" class="altColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setShowCauses-boolean-">setShowCauses</a></span>&#8203;(boolean&nbsp;flag)</code></th>
<td class="colLast">
<div class="block">Sets whether causes of exceptions that occur during test execution will be logged.</div>
</td>
</tr>
<tr id="i19" class="rowColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setShowExceptions-boolean-">setShowExceptions</a></span>&#8203;(boolean&nbsp;flag)</code></th>
<td class="colLast">
<div class="block">Sets whether exceptions that occur during test execution will be logged.</div>
</td>
</tr>
<tr id="i20" class="altColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setShowStackTraces-boolean-">setShowStackTraces</a></span>&#8203;(boolean&nbsp;flag)</code></th>
<td class="colLast">
<div class="block">Sets whether stack traces of exceptions that occur during test execution will be logged.</div>
</td>
</tr>
<tr id="i21" class="rowColor">
<td class="colFirst"><code><a href="TestLogging.html" title="interface in org.gradle.api.tasks.testing.logging">TestLogging</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setShowStandardStreams-boolean-">setShowStandardStreams</a></span>&#8203;(boolean&nbsp;flag)</code></th>
<td class="colLast">
<div class="block">Sets whether output on standard out and standard error will be logged.</div>
</td>
</tr>
<tr id="i22" class="altColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setStackTraceFilters-java.lang.Iterable-">setStackTraceFilters</a></span>&#8203;(java.lang.Iterable&lt;?&gt;&nbsp;stackTraces)</code></th>
<td class="colLast">
<div class="block">Sets the set of filters to be used for sanitizing test stack traces.</div>
</td>
</tr>
<tr id="i23" class="rowColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setStackTraceFilters-java.util.Set-">setStackTraceFilters</a></span>&#8203;(java.util.Set&lt;<a href="TestStackTraceFilter.html" title="enum in org.gradle.api.tasks.testing.logging">TestStackTraceFilter</a>&gt;&nbsp;stackTraces)</code></th>
<td class="colLast">
<div class="block">Sets the set of filters to be used for sanitizing test stack traces.</div>
</td>
</tr>
<tr id="i24" class="altColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#stackTraceFilters-java.lang.Object...-">stackTraceFilters</a></span>&#8203;(java.lang.Object...&nbsp;stackTraces)</code></th>
<td class="colLast">
<div class="block">Convenience method for <a href="#setStackTraceFilters-java.lang.Iterable-"><code>setStackTraceFilters(java.lang.Iterable)</code></a>.</div>
</td>
</tr>
</table>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="getEvents--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getEvents</h4>
<pre class="methodSignature">java.util.Set&lt;<a href="TestLogEvent.html" title="enum in org.gradle.api.tasks.testing.logging">TestLogEvent</a>&gt;&nbsp;getEvents()</pre>
<div class="block">Returns the events to be logged.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the events to be logged</dd>
</dl>
</li>
</ul>
<a name="setEvents-java.util.Set-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setEvents</h4>
<pre class="methodSignature">void&nbsp;setEvents&#8203;(java.util.Set&lt;<a href="TestLogEvent.html" title="enum in org.gradle.api.tasks.testing.logging">TestLogEvent</a>&gt;&nbsp;events)</pre>
<div class="block">Sets the events to be logged.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>events</code> - the events to be logged</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>4.0</dd>
</dl>
</li>
</ul>
<a name="setEvents-java.lang.Iterable-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setEvents</h4>
<pre class="methodSignature">void&nbsp;setEvents&#8203;(java.lang.Iterable&lt;?&gt;&nbsp;events)</pre>
<div class="block">Sets the events to be logged.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>events</code> - the events to be logged</dd>
</dl>
</li>
</ul>
<a name="events-java.lang.Object...-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>events</h4>
<pre class="methodSignature">void&nbsp;events&#8203;(java.lang.Object...&nbsp;events)</pre>
<div class="block">Sets the events to be logged. Events can be passed as enum values (e.g. <a href="TestLogEvent.html#FAILED"><code>TestLogEvent.FAILED</code></a>) or Strings (e.g. "failed").</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>events</code> - the events to be logged</dd>
</dl>
</li>
</ul>
<a name="getMinGranularity--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getMinGranularity</h4>
<pre class="methodSignature">int&nbsp;getMinGranularity()</pre>
<div class="block">Returns the minimum granularity of the events to be logged. Typically, 0 corresponds to events from the Gradle-generated test suite for the whole test run, 1 corresponds to the Gradle-generated test suite
 for a particular test JVM, 2 corresponds to a test class, and 3 corresponds to a test method. These values may extend higher if user-defined suites or parameterized test methods are executed.  Events
 from levels lower than the specified granularity will be ignored.
 <p>The default granularity is -1, which specifies that test events from only the most granular level should be logged.  In other words, if a test method is not parameterized, only events
 from the test method will be logged and events from the test class and lower will be ignored.  On the other hand, if a test method is parameterized, then events from the iterations of that test
 method will be logged and events from the test method and lower will be ignored.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the minimum granularity of the events to be logged</dd>
</dl>
</li>
</ul>
<a name="setMinGranularity-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setMinGranularity</h4>
<pre class="methodSignature">void&nbsp;setMinGranularity&#8203;(int&nbsp;granularity)</pre>
<div class="block">Sets the minimum granularity of the events to be logged. Typically, 0 corresponds to events from the Gradle-generated test suite for the whole test run, 1 corresponds to the Gradle-generated test suite
 for a particular test JVM, 2 corresponds to a test class, and 3 corresponds to a test method. These values may extend higher if user-defined suites or parameterized test methods are executed.  Events
 from levels lower than the specified granularity will be ignored.
 <p>The default granularity is -1, which specifies that test events from only the most granular level should be logged.  In other words, if a test method is not parameterized, only events
 from the test method will be logged and events from the test class and lower will be ignored.  On the other hand, if a test method is parameterized, then events from the iterations of that test
 method will be logged and events from the test method and lower will be ignored.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>granularity</code> - the minimum granularity of the events to be logged</dd>
</dl>
</li>
</ul>
<a name="getMaxGranularity--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getMaxGranularity</h4>
<pre class="methodSignature">int&nbsp;getMaxGranularity()</pre>
<div class="block">Returns the maximum granularity of the events to be logged. Typically, 0 corresponds to the Gradle-generated test suite for the whole test run, 1 corresponds to the Gradle-generated test suite
 for a particular test JVM, 2 corresponds to a test class, and 3 corresponds to a test method. These values may extend higher if user-defined suites or parameterized test methods are executed.  Events
 from levels higher than the specified granularity will be ignored.
 <p>The default granularity is -1, which specifies that test events from only the most granular level should be logged.  Setting this value to something lower will cause events
 from a higher level to be ignored.  For example, setting the value to 3 will cause only events from the test method level to be logged and any events from iterations of a parameterized test method
 will be ignored.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the maximum granularity of the events to be logged</dd>
</dl>
</li>
</ul>
<a name="setMaxGranularity-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setMaxGranularity</h4>
<pre class="methodSignature">void&nbsp;setMaxGranularity&#8203;(int&nbsp;granularity)</pre>
<div class="block">Returns the maximum granularity of the events to be logged. Typically, 0 corresponds to the Gradle-generated test suite for the whole test run, 1 corresponds to the Gradle-generated test suite
 for a particular test JVM, 2 corresponds to a test class, and 3 corresponds to a test method. These values may extend higher if user-defined suites or parameterized test methods are executed.  Events
 from levels higher than the specified granularity will be ignored.
 <p>The default granularity is -1, which specifies that test events from only the most granular level should be logged.  Setting this value to something lower will cause events
 from a higher level to be ignored.  For example, setting the value to 3 will cause only events from the test method level to be logged and any events from iterations of a parameterized test method
 will be ignored.
<p>Note that since the default value of <a href="#getMinGranularity--"><code>getMinGranularity()</code></a> is -1 (the highest level of granularity) it only makes sense to configure the maximum granularity while also setting the
 minimum granularity to a value greater than -1.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>granularity</code> - the maximum granularity of the events to be logged</dd>
</dl>
</li>
</ul>
<a name="getDisplayGranularity--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getDisplayGranularity</h4>
<pre class="methodSignature">int&nbsp;getDisplayGranularity()</pre>
<div class="block">Returns the display granularity of the events to be logged. For example, if set to 0, a method-level event will be displayed as "Test Run &gt; Test Worker x &gt; org.SomeClass &gt; org.someMethod". If
 set to 2, the same event will be displayed as "org.someClass &gt; org.someMethod". <p>-1 denotes the highest granularity and corresponds to an atomic test.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the display granularity of the events to be logged</dd>
</dl>
</li>
</ul>
<a name="setDisplayGranularity-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setDisplayGranularity</h4>
<pre class="methodSignature">void&nbsp;setDisplayGranularity&#8203;(int&nbsp;granularity)</pre>
<div class="block">Sets the display granularity of the events to be logged. For example, if set to 0, a method-level event will be displayed as "Test Run &gt; Test Worker x &gt; org.SomeClass &gt; org.someMethod". If set
 to 2, the same event will be displayed as "org.someClass &gt; org.someMethod". <p>-1 denotes the highest granularity and corresponds to an atomic test.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>granularity</code> - the display granularity of the events to be logged</dd>
</dl>
</li>
</ul>
<a name="getShowExceptions--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getShowExceptions</h4>
<pre class="methodSignature">boolean&nbsp;getShowExceptions()</pre>
<div class="block">Tells whether exceptions that occur during test execution will be logged. Typically these exceptions coincide with a "failed" event.  Defaults to true.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>whether exceptions that occur during test execution will be logged</dd>
</dl>
</li>
</ul>
<a name="setShowExceptions-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setShowExceptions</h4>
<pre class="methodSignature">void&nbsp;setShowExceptions&#8203;(boolean&nbsp;flag)</pre>
<div class="block">Sets whether exceptions that occur during test execution will be logged.  Defaults to true.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>flag</code> - whether exceptions that occur during test execution will be logged</dd>
</dl>
</li>
</ul>
<a name="getShowCauses--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getShowCauses</h4>
<pre class="methodSignature">boolean&nbsp;getShowCauses()</pre>
<div class="block">Tells whether causes of exceptions that occur during test execution will be logged. Only relevant if <code>showExceptions</code> is <code>true</code>.  Defaults to true.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>whether causes of exceptions that occur during test execution will be logged</dd>
</dl>
</li>
</ul>
<a name="setShowCauses-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setShowCauses</h4>
<pre class="methodSignature">void&nbsp;setShowCauses&#8203;(boolean&nbsp;flag)</pre>
<div class="block">Sets whether causes of exceptions that occur during test execution will be logged. Only relevant if <code>showExceptions</code> is <code>true</code>. Defaults to true.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>flag</code> - whether causes of exceptions that occur during test execution will be logged</dd>
</dl>
</li>
</ul>
<a name="getShowStackTraces--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getShowStackTraces</h4>
<pre class="methodSignature">boolean&nbsp;getShowStackTraces()</pre>
<div class="block">Tells whether stack traces of exceptions that occur during test execution will be logged.  Defaults to true.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>whether stack traces of exceptions that occur during test execution will be logged</dd>
</dl>
</li>
</ul>
<a name="setShowStackTraces-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setShowStackTraces</h4>
<pre class="methodSignature">void&nbsp;setShowStackTraces&#8203;(boolean&nbsp;flag)</pre>
<div class="block">Sets whether stack traces of exceptions that occur during test execution will be logged.  Defaults to true.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>flag</code> - whether stack traces of exceptions that occur during test execution will be logged</dd>
</dl>
</li>
</ul>
<a name="getExceptionFormat--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getExceptionFormat</h4>
<pre class="methodSignature"><a href="TestExceptionFormat.html" title="enum in org.gradle.api.tasks.testing.logging">TestExceptionFormat</a>&nbsp;getExceptionFormat()</pre>
<div class="block">Returns the format to be used for logging test exceptions. Only relevant if <code>showStackTraces</code> is <code>true</code>.  Defaults to <a href="TestExceptionFormat.html#FULL"><code>TestExceptionFormat.FULL</code></a> for
 the INFO and DEBUG log levels and <a href="TestExceptionFormat.html#SHORT"><code>TestExceptionFormat.SHORT</code></a> for the LIFECYCLE log level.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the format to be used for logging test exceptions</dd>
</dl>
</li>
</ul>
<a name="setExceptionFormat-org.gradle.api.tasks.testing.logging.TestExceptionFormat-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setExceptionFormat</h4>
<pre class="methodSignature">void&nbsp;setExceptionFormat&#8203;(<a href="TestExceptionFormat.html" title="enum in org.gradle.api.tasks.testing.logging">TestExceptionFormat</a>&nbsp;exceptionFormat)</pre>
<div class="block">Sets the format to be used for logging test exceptions. Only relevant if <code>showStackTraces</code> is <code>true</code>.  Defaults to <a href="TestExceptionFormat.html#FULL"><code>TestExceptionFormat.FULL</code></a> for
 the INFO and DEBUG log levels and <a href="TestExceptionFormat.html#SHORT"><code>TestExceptionFormat.SHORT</code></a> for the LIFECYCLE log level.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>exceptionFormat</code> - the format to be used for logging test exceptions</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>4.0</dd>
</dl>
</li>
</ul>
<a name="setExceptionFormat-java.lang.Object-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setExceptionFormat</h4>
<pre class="methodSignature">void&nbsp;setExceptionFormat&#8203;(java.lang.Object&nbsp;exceptionFormat)</pre>
<div class="block">Sets the format to be used for logging test exceptions. Only relevant if <code>showStackTraces</code> is <code>true</code>.  Defaults to <a href="TestExceptionFormat.html#FULL"><code>TestExceptionFormat.FULL</code></a> for
 the INFO and DEBUG log levels and <a href="TestExceptionFormat.html#SHORT"><code>TestExceptionFormat.SHORT</code></a> for the LIFECYCLE log level.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>exceptionFormat</code> - the format to be used for logging test exceptions</dd>
</dl>
</li>
</ul>
<a name="getStackTraceFilters--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getStackTraceFilters</h4>
<pre class="methodSignature">java.util.Set&lt;<a href="TestStackTraceFilter.html" title="enum in org.gradle.api.tasks.testing.logging">TestStackTraceFilter</a>&gt;&nbsp;getStackTraceFilters()</pre>
<div class="block">Returns the set of filters to be used for sanitizing test stack traces.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the set of filters to be used for sanitizing test stack traces</dd>
</dl>
</li>
</ul>
<a name="setStackTraceFilters-java.util.Set-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setStackTraceFilters</h4>
<pre class="methodSignature">void&nbsp;setStackTraceFilters&#8203;(java.util.Set&lt;<a href="TestStackTraceFilter.html" title="enum in org.gradle.api.tasks.testing.logging">TestStackTraceFilter</a>&gt;&nbsp;stackTraces)</pre>
<div class="block">Sets the set of filters to be used for sanitizing test stack traces.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>stackTraces</code> - the set of filters to be used for sanitizing test stack traces</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>4.0</dd>
</dl>
</li>
</ul>
<a name="setStackTraceFilters-java.lang.Iterable-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setStackTraceFilters</h4>
<pre class="methodSignature">void&nbsp;setStackTraceFilters&#8203;(java.lang.Iterable&lt;?&gt;&nbsp;stackTraces)</pre>
<div class="block">Sets the set of filters to be used for sanitizing test stack traces.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>stackTraces</code> - the set of filters to be used for sanitizing test stack traces</dd>
</dl>
</li>
</ul>
<a name="stackTraceFilters-java.lang.Object...-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>stackTraceFilters</h4>
<pre class="methodSignature">void&nbsp;stackTraceFilters&#8203;(java.lang.Object...&nbsp;stackTraces)</pre>
<div class="block">Convenience method for <a href="#setStackTraceFilters-java.lang.Iterable-"><code>setStackTraceFilters(java.lang.Iterable)</code></a>. Accepts both enum values and Strings.</div>
</li>
</ul>
<a name="getShowStandardStreams--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getShowStandardStreams</h4>
<pre class="methodSignature">boolean&nbsp;getShowStandardStreams()</pre>
<div class="block">Tells whether output on standard out and standard error will be logged. Equivalent to checking if both log events <code>TestLogEvent.STANDARD_OUT</code> and <code>TestLogEvent.STANDARD_ERROR</code> are
 set.</div>
</li>
</ul>
<a name="setShowStandardStreams-boolean-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>setShowStandardStreams</h4>
<pre class="methodSignature"><a href="TestLogging.html" title="interface in org.gradle.api.tasks.testing.logging">TestLogging</a>&nbsp;setShowStandardStreams&#8203;(boolean&nbsp;flag)</pre>
<div class="block">Sets whether output on standard out and standard error will be logged. Equivalent to setting log events <code>TestLogEvent.STANDARD_OUT</code> and <code>TestLogEvent.STANDARD_ERROR</code>.</div>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../../../allclasses.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
