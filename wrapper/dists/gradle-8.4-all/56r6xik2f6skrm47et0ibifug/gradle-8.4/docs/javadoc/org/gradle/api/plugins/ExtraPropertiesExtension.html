<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc -->
<title>ExtraPropertiesExtension (Gradle API 8.4)</title>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<link rel="stylesheet" type="text/css" href="../../../../javadoc.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../jquery/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
<script type="text/javascript" src="../../../../jquery/jszip/dist/jszip.min.js"></script>
<script type="text/javascript" src="../../../../jquery/jszip-utils/dist/jszip-utils.min.js"></script>
<!--[if IE]>
<script type="text/javascript" src="../../../../jquery/jszip-utils/dist/jszip-utils-ie.min.js"></script>
<![endif]-->
<script type="text/javascript" src="../../../../jquery/jquery-3.6.0.min.js"></script>
<script type="text/javascript" src="../../../../jquery/jquery-ui.min.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="ExtraPropertiesExtension (Gradle API 8.4)";
        }
    }
    catch(err) {
    }
//-->
var data = {"i0":6,"i1":6,"i2":6,"i3":6};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],4:["t3","Abstract Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
var pathtoroot = "../../../../";
var useModuleDirectories = false;
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="fixedNav">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses.html">All&nbsp;Classes</a></li>
</ul>
<ul class="navListSearch">
<li><label for="search">SEARCH:</label>
<input type="text" id="search" value="search" disabled="disabled">
<input type="reset" id="reset" value="reset" disabled="disabled">
</li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li><a href="#nested.class.summary">Nested</a>&nbsp;|&nbsp;</li>
<li><a href="#field.summary">Field</a>&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#field.detail">Field</a>&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
</div>
<div class="navPadding">&nbsp;</div>
<script type="text/javascript"><!--
$('.navPadding').css('padding-top', $('.fixedNav').css("height"));
//-->
</script>
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle"><span class="packageLabelInType">Package</span>&nbsp;<a href="package-summary.html">org.gradle.api.plugins</a></div>
<h2 title="Interface ExtraPropertiesExtension" class="title">Interface ExtraPropertiesExtension</h2>
</div>
<div class="contentContainer">
<div class="description">
<ul class="blockList">
<li class="blockList">
<hr>
<pre>public interface <span class="typeNameLabel">ExtraPropertiesExtension</span></pre>
<div class="block">Additional, ad-hoc, properties for Gradle domain objects.
 <p>
 Extra properties extensions allow new properties to be added to existing domain objects. They act like maps,
 allowing the storage of arbitrary key/value pairs. All <a href="ExtensionAware.html" title="interface in org.gradle.api.plugins"><code>ExtensionAware</code></a> Gradle domain objects intrinsically have an extension
 named “<a href="#EXTENSION_NAME">"ext"</a>” of this type.
 <p>
 An important feature of extra properties extensions is that all of its properties are exposed for reading and writing via the <a href="ExtensionAware.html" title="interface in org.gradle.api.plugins"><code>ExtensionAware</code></a>
 object that owns the extension.

 <pre class='autoTested'>
 project.ext.set("myProp", "myValue")
 assert project.myProp == "myValue"

 project.myProp = "anotherValue"
 assert project.myProp == "anotherValue"
 assert project.ext.get("myProp") == "anotherValue"
 </pre>

 Extra properties extension objects support Groovy property syntax. That is, a property can be read via <code>extension.«name»</code> and set via
 <code>extension.«name» = "value"</code>. <b>Wherever possible, the Groovy property syntax should be preferred over the
 <a href="#get-java.lang.String-"><code>get(String)</code></a> and <a href="#set-java.lang.String-java.lang.Object-"><code>set(String, Object)</code></a> methods.</b>

 <pre class='autoTested'>
 project.ext {
   myprop = "a"
 }
 assert project.myprop == "a"
 assert project.ext.myprop == "a"

 project.myprop = "b"
 assert project.myprop == "b"
 assert project.ext.myprop == "b"
 </pre>

 You can also use the Groovy accessor syntax to get and set properties on an extra properties extension.

 <pre class='autoTested'>
 project.ext["otherProp"] = "a"
 assert project.otherProp == "a"
 assert project.ext["otherProp"] == "a"
 </pre>

 The exception that is thrown when an attempt is made to get the value of a property that does not exist is different depending on whether the
 Groovy syntax is used or not. If Groovy property syntax is used, the Groovy <a href="https://docs.groovy-lang.org/docs/groovy-3.0.17/html/gapi/groovy/lang/MissingPropertyException.html?is-external=true" title="class or interface in groovy.lang" class="externalLink"><code>MissingPropertyException</code></a> will be thrown.
 When the <a href="#get-java.lang.String-"><code>get(String)</code></a> method is used, an <a href="ExtraPropertiesExtension.UnknownPropertyException.html" title="class in org.gradle.api.plugins"><code>ExtraPropertiesExtension.UnknownPropertyException</code></a> will be thrown.</div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== NESTED CLASS SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="nested.class.summary">
<!--   -->
</a>
<h3>Nested Class Summary</h3>
<table class="memberSummary" summary="Nested Class Summary table, listing nested classes, and an explanation">
<caption><span>Nested Classes</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colSecond" scope="col">Interface</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static class&nbsp;</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="ExtraPropertiesExtension.UnknownPropertyException.html" title="class in org.gradle.api.plugins">ExtraPropertiesExtension.UnknownPropertyException</a></span></code></th>
<td class="colLast">
<div class="block">The exception that will be thrown when an attempt is made to read a property that is not set.</div>
</td>
</tr>
</table>
</li>
</ul>
<!-- =========== FIELD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.summary">
<!--   -->
</a>
<h3>Field Summary</h3>
<table class="memberSummary" summary="Field Summary table, listing fields, and an explanation">
<caption><span>Fields</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colSecond" scope="col">Field</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#EXTENSION_NAME">EXTENSION_NAME</a></span></code></th>
<td class="colLast">
<div class="block">The name of this extension in all <a href="ExtensionContainer.html" title="interface in org.gradle.api.plugins"><code>ExtensionContainers</code></a>, "ext".</div>
</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t3" class="tableTab"><span><a href="javascript:show(4);">Abstract Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colSecond" scope="col">Method</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>java.lang.Object</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#get-java.lang.String-">get</a></span>&#8203;(java.lang.String&nbsp;name)</code></th>
<td class="colLast">
<div class="block">Returns the value for the registered property with the given name.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>java.util.Map&lt;java.lang.String,&#8203;java.lang.Object&gt;</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getProperties--">getProperties</a></span>()</code></th>
<td class="colLast">
<div class="block">Returns all of the registered properties and their current values as a map.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#has-java.lang.String-">has</a></span>&#8203;(java.lang.String&nbsp;name)</code></th>
<td class="colLast">
<div class="block">Returns whether or not the extension has a property registered via the given name.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#set-java.lang.String-java.lang.Object-">set</a></span>&#8203;(java.lang.String&nbsp;name,
   java.lang.Object&nbsp;value)</code></th>
<td class="colLast">
<div class="block">Updates the value for, or creates, the registered property with the given name to the given value.</div>
</td>
</tr>
</table>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ FIELD DETAIL =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.detail">
<!--   -->
</a>
<h3>Field Detail</h3>
<a name="EXTENSION_NAME">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>EXTENSION_NAME</h4>
<pre>static final&nbsp;java.lang.String EXTENSION_NAME</pre>
<div class="block">The name of this extension in all <a href="ExtensionContainer.html" title="interface in org.gradle.api.plugins"><code>ExtensionContainers</code></a>, "ext".</div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../../constant-values.html#org.gradle.api.plugins.ExtraPropertiesExtension.EXTENSION_NAME">Constant Field Values</a></dd>
</dl>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="has-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>has</h4>
<pre class="methodSignature">boolean&nbsp;has&#8203;(java.lang.String&nbsp;name)</pre>
<div class="block">Returns whether or not the extension has a property registered via the given name.

 <pre class='autoTested'>
 assert project.ext.has("foo") == false
 assert project.hasProperty("foo") == false

 project.ext.foo = "bar"

 assert project.ext.has("foo")
 assert project.hasProperty("foo")
 </pre></div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>name</code> - The name of the property to check for</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd><code>true</code> if a property has been registered with this name, otherwise <code>false</code>.</dd>
</dl>
</li>
</ul>
<a name="get-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>get</h4>
<pre class="methodSignature">@Nullable
java.lang.Object&nbsp;get&#8203;(java.lang.String&nbsp;name)
              throws <a href="ExtraPropertiesExtension.UnknownPropertyException.html" title="class in org.gradle.api.plugins">ExtraPropertiesExtension.UnknownPropertyException</a></pre>
<div class="block">Returns the value for the registered property with the given name.

 When using an extra properties extension from Groovy, you can also get properties via Groovy's property syntax.
 All of the following lines of code are equivalent.

 <pre class='autoTested'>
 project.ext { foo = "bar" }

 assert project.ext.get("foo") == "bar"
 assert project.ext.foo == "bar"
 assert project.ext["foo"] == "bar"

 assert project.foo == "bar"
 assert project["foo"] == "bar"
 </pre>

 When using the first form, an <a href="ExtraPropertiesExtension.UnknownPropertyException.html" title="class in org.gradle.api.plugins"><code>ExtraPropertiesExtension.UnknownPropertyException</code></a> exception will be thrown if the
 extension does not have a property called “<code>foo</code>”. When using the second forms (i.e. Groovy notation),
 Groovy's <a href="https://docs.groovy-lang.org/docs/groovy-3.0.17/html/gapi/groovy/lang/MissingPropertyException.html?is-external=true" title="class or interface in groovy.lang" class="externalLink"><code>MissingPropertyException</code></a> will be thrown instead.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>name</code> - The name of the property to get the value of</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The value for the property with the given name.</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code><a href="ExtraPropertiesExtension.UnknownPropertyException.html" title="class in org.gradle.api.plugins">ExtraPropertiesExtension.UnknownPropertyException</a></code> - if there is no property registered with the given name</dd>
</dl>
</li>
</ul>
<a name="set-java.lang.String-java.lang.Object-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>set</h4>
<pre class="methodSignature">void&nbsp;set&#8203;(java.lang.String&nbsp;name,
         @Nullable
         java.lang.Object&nbsp;value)</pre>
<div class="block">Updates the value for, or creates, the registered property with the given name to the given value.

 When using an extra properties extension from Groovy, you can also set properties via Groovy's property syntax.
 All of the following lines of code are equivalent.

 <pre class='autoTested'>
 project.ext.set("foo", "bar")
 project.ext.foo = "bar"
 project.ext["foo"] = "bar"

 // Once the property has been created via the extension, it can be changed by the owner.
 project.foo = "bar"
 project["foo"] = "bar"
 </pre></div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>name</code> - The name of the property to update the value of or create</dd>
<dd><code>value</code> - The value to set for the property</dd>
</dl>
</li>
</ul>
<a name="getProperties--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>getProperties</h4>
<pre class="methodSignature">java.util.Map&lt;java.lang.String,&#8203;java.lang.Object&gt;&nbsp;getProperties()</pre>
<div class="block">Returns all of the registered properties and their current values as a map.

 The returned map is detached from the extension. That is, any changes made to the map do not
 change the extension from which it originated.

 <pre class='autoTested'>
 project.version = "1.0"

 assert project.hasProperty("version")
 assert project.ext.properties.containsKey("version") == false

 project.ext.foo = "bar"

 assert project.ext.properties.containsKey("foo")
 assert project.ext.properties.foo == project.ext.foo

 assert project.ext.properties.every { key, value -&gt; project.properties[key] == value }
 </pre></div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>All of the registered properties and their current values as a map.</dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li><a href="#nested.class.summary">Nested</a>&nbsp;|&nbsp;</li>
<li><a href="#field.summary">Field</a>&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#field.detail">Field</a>&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
