<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc -->
<title>JavaBasePlugin (Gradle API 8.4)</title>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<link rel="stylesheet" type="text/css" href="../../../../javadoc.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../jquery/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
<script type="text/javascript" src="../../../../jquery/jszip/dist/jszip.min.js"></script>
<script type="text/javascript" src="../../../../jquery/jszip-utils/dist/jszip-utils.min.js"></script>
<!--[if IE]>
<script type="text/javascript" src="../../../../jquery/jszip-utils/dist/jszip-utils-ie.min.js"></script>
<![endif]-->
<script type="text/javascript" src="../../../../jquery/jquery-3.6.0.min.js"></script>
<script type="text/javascript" src="../../../../jquery/jquery-ui.min.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="JavaBasePlugin (Gradle API 8.4)";
        }
    }
    catch(err) {
    }
//-->
var data = {"i0":10,"i1":6};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],4:["t3","Abstract Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
var pathtoroot = "../../../../";
var useModuleDirectories = false;
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="fixedNav">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses.html">All&nbsp;Classes</a></li>
</ul>
<ul class="navListSearch">
<li><label for="search">SEARCH:</label>
<input type="text" id="search" value="search" disabled="disabled">
<input type="reset" id="reset" value="reset" disabled="disabled">
</li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field.summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#field.detail">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
</div>
<div class="navPadding">&nbsp;</div>
<script type="text/javascript"><!--
$('.navPadding').css('padding-top', $('.fixedNav').css("height"));
//-->
</script>
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle"><span class="packageLabelInType">Package</span>&nbsp;<a href="package-summary.html">org.gradle.api.plugins</a></div>
<h2 title="Class JavaBasePlugin" class="title">Class JavaBasePlugin</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>org.gradle.api.plugins.JavaBasePlugin</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Implemented Interfaces:</dt>
<dd><code><a href="../Plugin.html" title="interface in org.gradle.api">Plugin</a>&lt;<a href="../Project.html" title="interface in org.gradle.api">Project</a>&gt;</code></dd>
</dl>
<hr>
<pre>public abstract class <span class="typeNameLabel">JavaBasePlugin</span>
extends java.lang.Object
implements <a href="../Plugin.html" title="interface in org.gradle.api">Plugin</a>&lt;<a href="../Project.html" title="interface in org.gradle.api">Project</a>&gt;</pre>
<div class="block"><p>A <a href="../Plugin.html" title="interface in org.gradle.api"><code>Plugin</code></a> which compiles and tests Java source, and assembles it into a JAR file.</p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="https://docs.gradle.org/current/userguide/java_plugin.html">Java plugin reference</a></dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- =========== FIELD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.summary">
<!--   -->
</a>
<h3>Field Summary</h3>
<table class="memberSummary" summary="Field Summary table, listing fields, and an explanation">
<caption><span>Fields</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colSecond" scope="col">Field</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#BUILD_DEPENDENTS_TASK_NAME">BUILD_DEPENDENTS_TASK_NAME</a></span></code></th>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#BUILD_NEEDED_TASK_NAME">BUILD_NEEDED_TASK_NAME</a></span></code></th>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#BUILD_TASK_NAME">BUILD_TASK_NAME</a></span></code></th>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#CHECK_TASK_NAME">CHECK_TASK_NAME</a></span></code></th>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#COMPILE_CLASSPATH_PACKAGING_SYSTEM_PROPERTY">COMPILE_CLASSPATH_PACKAGING_SYSTEM_PROPERTY</a></span></code></th>
<td class="colLast">
<div class="block">Set this property to use JARs build from subprojects, instead of the classes folder from these project, on the compile classpath.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#DOCUMENTATION_GROUP">DOCUMENTATION_GROUP</a></span></code></th>
<td class="colLast">
<div class="block">Task group name for documentation-related tasks.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static java.util.Set&lt;java.lang.String&gt;</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#UNPUBLISHABLE_VARIANT_ARTIFACTS">UNPUBLISHABLE_VARIANT_ARTIFACTS</a></span></code></th>
<td class="colLast">
<div class="block">A list of known artifact types which are known to prevent from
 publication.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#VERIFICATION_GROUP">VERIFICATION_GROUP</a></span></code></th>
<td class="colLast">&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="memberSummary" summary="Constructor Summary table, listing constructors, and an explanation">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Constructor</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tr class="altColor">
<th class="colConstructorName" scope="row"><code><span class="memberNameLink"><a href="#JavaBasePlugin-org.gradle.api.model.ObjectFactory-org.gradle.api.plugins.jvm.internal.JvmPluginServices-">JavaBasePlugin</a></span>&#8203;(<a href="../model/ObjectFactory.html" title="interface in org.gradle.api.model">ObjectFactory</a>&nbsp;objectFactory,
              org.gradle.api.plugins.jvm.internal.JvmPluginServices&nbsp;jvmPluginServices)</code></th>
<td class="colLast">&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t3" class="tableTab"><span><a href="javascript:show(4);">Abstract Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colSecond" scope="col">Method</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#apply-org.gradle.api.Project-">apply</a></span>&#8203;(<a href="../Project.html" title="interface in org.gradle.api">Project</a>&nbsp;project)</code></th>
<td class="colLast">
<div class="block">Apply this plugin to the given target object.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>protected abstract org.gradle.api.plugins.jvm.internal.JvmLanguageUtilities</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getJvmLanguageUtils--">getJvmLanguageUtils</a></span>()</code></th>
<td class="colLast">&nbsp;</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>clone, equals, finalize, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ FIELD DETAIL =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.detail">
<!--   -->
</a>
<h3>Field Detail</h3>
<a name="CHECK_TASK_NAME">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CHECK_TASK_NAME</h4>
<pre>public static final&nbsp;java.lang.String CHECK_TASK_NAME</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../../constant-values.html#org.gradle.api.plugins.JavaBasePlugin.CHECK_TASK_NAME">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="VERIFICATION_GROUP">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>VERIFICATION_GROUP</h4>
<pre>public static final&nbsp;java.lang.String VERIFICATION_GROUP</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../../constant-values.html#org.gradle.api.plugins.JavaBasePlugin.VERIFICATION_GROUP">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="BUILD_TASK_NAME">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BUILD_TASK_NAME</h4>
<pre>public static final&nbsp;java.lang.String BUILD_TASK_NAME</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../../constant-values.html#org.gradle.api.plugins.JavaBasePlugin.BUILD_TASK_NAME">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="BUILD_DEPENDENTS_TASK_NAME">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BUILD_DEPENDENTS_TASK_NAME</h4>
<pre>public static final&nbsp;java.lang.String BUILD_DEPENDENTS_TASK_NAME</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../../constant-values.html#org.gradle.api.plugins.JavaBasePlugin.BUILD_DEPENDENTS_TASK_NAME">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="BUILD_NEEDED_TASK_NAME">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BUILD_NEEDED_TASK_NAME</h4>
<pre>public static final&nbsp;java.lang.String BUILD_NEEDED_TASK_NAME</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../../constant-values.html#org.gradle.api.plugins.JavaBasePlugin.BUILD_NEEDED_TASK_NAME">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="DOCUMENTATION_GROUP">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DOCUMENTATION_GROUP</h4>
<pre>public static final&nbsp;java.lang.String DOCUMENTATION_GROUP</pre>
<div class="block">Task group name for documentation-related tasks.</div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../../constant-values.html#org.gradle.api.plugins.JavaBasePlugin.DOCUMENTATION_GROUP">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="COMPILE_CLASSPATH_PACKAGING_SYSTEM_PROPERTY">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>COMPILE_CLASSPATH_PACKAGING_SYSTEM_PROPERTY</h4>
<pre>public static final&nbsp;java.lang.String COMPILE_CLASSPATH_PACKAGING_SYSTEM_PROPERTY</pre>
<div class="block">Set this property to use JARs build from subprojects, instead of the classes folder from these project, on the compile classpath.
 The main use case for this is to mitigate performance issues on very large multi-projects building on Windows.
 Setting this property will cause the 'jar' task of all subprojects in the dependency tree to always run during compilation.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>5.6</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../../constant-values.html#org.gradle.api.plugins.JavaBasePlugin.COMPILE_CLASSPATH_PACKAGING_SYSTEM_PROPERTY">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="UNPUBLISHABLE_VARIANT_ARTIFACTS">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>UNPUBLISHABLE_VARIANT_ARTIFACTS</h4>
<pre>public static final&nbsp;java.util.Set&lt;java.lang.String&gt; UNPUBLISHABLE_VARIANT_ARTIFACTS</pre>
<div class="block">A list of known artifact types which are known to prevent from
 publication.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>5.3</dd>
</dl>
</li>
</ul>
</li>
</ul>
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a name="JavaBasePlugin-org.gradle.api.model.ObjectFactory-org.gradle.api.plugins.jvm.internal.JvmPluginServices-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>JavaBasePlugin</h4>
<pre>@Inject
public&nbsp;JavaBasePlugin&#8203;(<a href="../model/ObjectFactory.html" title="interface in org.gradle.api.model">ObjectFactory</a>&nbsp;objectFactory,
                      org.gradle.api.plugins.jvm.internal.JvmPluginServices&nbsp;jvmPluginServices)</pre>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="getJvmLanguageUtils--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getJvmLanguageUtils</h4>
<pre class="methodSignature">@Inject
protected abstract&nbsp;org.gradle.api.plugins.jvm.internal.JvmLanguageUtilities&nbsp;getJvmLanguageUtils()</pre>
</li>
</ul>
<a name="apply-org.gradle.api.Project-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>apply</h4>
<pre class="methodSignature">public&nbsp;void&nbsp;apply&#8203;(<a href="../Project.html" title="interface in org.gradle.api">Project</a>&nbsp;project)</pre>
<div class="block"><span class="descfrmTypeLabel">Description copied from interface:&nbsp;<code><a href="../Plugin.html#apply-T-">Plugin</a></code></span></div>
<div class="block">Apply this plugin to the given target object.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../Plugin.html#apply-T-">apply</a></code>&nbsp;in interface&nbsp;<code><a href="../Plugin.html" title="interface in org.gradle.api">Plugin</a>&lt;<a href="../Project.html" title="interface in org.gradle.api">Project</a>&gt;</code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>project</code> - The target object</dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field.summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#field.detail">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
