<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc -->
<title>ExclusiveContentRepository (Gradle API 8.4)</title>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<link rel="stylesheet" type="text/css" href="../../../../../javadoc.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../jquery/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
<script type="text/javascript" src="../../../../../jquery/jszip/dist/jszip.min.js"></script>
<script type="text/javascript" src="../../../../../jquery/jszip-utils/dist/jszip-utils.min.js"></script>
<!--[if IE]>
<script type="text/javascript" src="../../../../../jquery/jszip-utils/dist/jszip-utils-ie.min.js"></script>
<![endif]-->
<script type="text/javascript" src="../../../../../jquery/jquery-3.6.0.min.js"></script>
<script type="text/javascript" src="../../../../../jquery/jquery-ui.min.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="ExclusiveContentRepository (Gradle API 8.4)";
        }
    }
    catch(err) {
    }
//-->
var data = {"i0":6,"i1":6,"i2":6};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],4:["t3","Abstract Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
var pathtoroot = "../../../../../";
var useModuleDirectories = false;
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="fixedNav">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../../allclasses.html">All&nbsp;Classes</a></li>
</ul>
<ul class="navListSearch">
<li><label for="search">SEARCH:</label>
<input type="text" id="search" value="search" disabled="disabled">
<input type="reset" id="reset" value="reset" disabled="disabled">
</li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
</div>
<div class="navPadding">&nbsp;</div>
<script type="text/javascript"><!--
$('.navPadding').css('padding-top', $('.fixedNav').css("height"));
//-->
</script>
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle"><span class="packageLabelInType">Package</span>&nbsp;<a href="package-summary.html">org.gradle.api.artifacts.repositories</a></div>
<h2 title="Interface ExclusiveContentRepository" class="title">Interface ExclusiveContentRepository</h2>
</div>
<div class="contentContainer">
<div class="description">
<ul class="blockList">
<li class="blockList">
<hr>
<pre>public interface <span class="typeNameLabel">ExclusiveContentRepository</span></pre>
<div class="block">Describes one or more repositories which together constitute the only possible
 source for an artifact, independently of the others.

 This means that if a repository declares an include, other repositories will
 automatically exclude it.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>6.2</dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t3" class="tableTab"><span><a href="javascript:show(4);">Abstract Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colSecond" scope="col">Method</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code><a href="ExclusiveContentRepository.html" title="interface in org.gradle.api.artifacts.repositories">ExclusiveContentRepository</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#filter-org.gradle.api.Action-">filter</a></span>&#8203;(<a href="../../Action.html" title="interface in org.gradle.api">Action</a>&lt;? super <a href="InclusiveRepositoryContentDescriptor.html" title="interface in org.gradle.api.artifacts.repositories">InclusiveRepositoryContentDescriptor</a>&gt;&nbsp;config)</code></th>
<td class="colLast">
<div class="block">Defines the content filter for this repository</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code><a href="ExclusiveContentRepository.html" title="interface in org.gradle.api.artifacts.repositories">ExclusiveContentRepository</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#forRepositories-org.gradle.api.artifacts.repositories.ArtifactRepository...-">forRepositories</a></span>&#8203;(<a href="ArtifactRepository.html" title="interface in org.gradle.api.artifacts.repositories">ArtifactRepository</a>...&nbsp;repositories)</code></th>
<td class="colLast">
<div class="block">Declares the repository</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code><a href="ExclusiveContentRepository.html" title="interface in org.gradle.api.artifacts.repositories">ExclusiveContentRepository</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#forRepository-org.gradle.internal.Factory-">forRepository</a></span>&#8203;(org.gradle.internal.Factory&lt;? extends <a href="ArtifactRepository.html" title="interface in org.gradle.api.artifacts.repositories">ArtifactRepository</a>&gt;&nbsp;repository)</code></th>
<td class="colLast">
<div class="block">Declares the repository</div>
</td>
</tr>
</table>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="forRepository-org.gradle.internal.Factory-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>forRepository</h4>
<pre class="methodSignature"><a href="ExclusiveContentRepository.html" title="interface in org.gradle.api.artifacts.repositories">ExclusiveContentRepository</a>&nbsp;forRepository&#8203;(org.gradle.internal.Factory&lt;? extends <a href="ArtifactRepository.html" title="interface in org.gradle.api.artifacts.repositories">ArtifactRepository</a>&gt;&nbsp;repository)</pre>
<div class="block">Declares the repository</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>repository</code> - the repository for which we declare exclusive content</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>this repository descriptor</dd>
</dl>
</li>
</ul>
<a name="forRepositories-org.gradle.api.artifacts.repositories.ArtifactRepository...-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>forRepositories</h4>
<pre class="methodSignature"><a href="ExclusiveContentRepository.html" title="interface in org.gradle.api.artifacts.repositories">ExclusiveContentRepository</a>&nbsp;forRepositories&#8203;(<a href="ArtifactRepository.html" title="interface in org.gradle.api.artifacts.repositories">ArtifactRepository</a>...&nbsp;repositories)</pre>
<div class="block">Declares the repository</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>repositories</code> - the repositories for which we declare exclusive content</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>this repository descriptor</dd>
</dl>
</li>
</ul>
<a name="filter-org.gradle.api.Action-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>filter</h4>
<pre class="methodSignature"><a href="ExclusiveContentRepository.html" title="interface in org.gradle.api.artifacts.repositories">ExclusiveContentRepository</a>&nbsp;filter&#8203;(<a href="../../Action.html" title="interface in org.gradle.api">Action</a>&lt;? super <a href="InclusiveRepositoryContentDescriptor.html" title="interface in org.gradle.api.artifacts.repositories">InclusiveRepositoryContentDescriptor</a>&gt;&nbsp;config)</pre>
<div class="block">Defines the content filter for this repository</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>config</code> - the configuration of the filter</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>this repository descriptor</dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../../allclasses.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
