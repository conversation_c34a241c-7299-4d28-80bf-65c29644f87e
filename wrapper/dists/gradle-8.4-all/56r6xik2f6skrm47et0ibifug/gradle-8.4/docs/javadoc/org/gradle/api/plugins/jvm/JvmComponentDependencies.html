<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc -->
<title>JvmComponentDependencies (Gradle API 8.4)</title>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<link rel="stylesheet" type="text/css" href="../../../../../javadoc.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../jquery/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
<script type="text/javascript" src="../../../../../jquery/jszip/dist/jszip.min.js"></script>
<script type="text/javascript" src="../../../../../jquery/jszip-utils/dist/jszip-utils.min.js"></script>
<!--[if IE]>
<script type="text/javascript" src="../../../../../jquery/jszip-utils/dist/jszip-utils-ie.min.js"></script>
<![endif]-->
<script type="text/javascript" src="../../../../../jquery/jquery-3.6.0.min.js"></script>
<script type="text/javascript" src="../../../../../jquery/jquery-ui.min.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="JvmComponentDependencies (Gradle API 8.4)";
        }
    }
    catch(err) {
    }
//-->
var data = {"i0":6,"i1":6,"i2":6,"i3":6};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],4:["t3","Abstract Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
var pathtoroot = "../../../../../";
var useModuleDirectories = false;
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="fixedNav">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../../allclasses.html">All&nbsp;Classes</a></li>
</ul>
<ul class="navListSearch">
<li><label for="search">SEARCH:</label>
<input type="text" id="search" value="search" disabled="disabled">
<input type="reset" id="reset" value="reset" disabled="disabled">
</li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li><a href="#nested.class.summary">Nested</a>&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
</div>
<div class="navPadding">&nbsp;</div>
<script type="text/javascript"><!--
$('.navPadding').css('padding-top', $('.fixedNav').css("height"));
//-->
</script>
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle"><span class="packageLabelInType">Package</span>&nbsp;<a href="package-summary.html">org.gradle.api.plugins.jvm</a></div>
<h2 title="Interface JvmComponentDependencies" class="title">Interface JvmComponentDependencies</h2>
</div>
<div class="contentContainer">
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Superinterfaces:</dt>
<dd><code><a href="../../artifacts/dsl/Dependencies.html" title="interface in org.gradle.api.artifacts.dsl">Dependencies</a></code>, <code><a href="../../artifacts/dsl/GradleDependencies.html" title="interface in org.gradle.api.artifacts.dsl">GradleDependencies</a></code>, <code><a href="PlatformDependencyModifiers.html" title="interface in org.gradle.api.plugins.jvm">PlatformDependencyModifiers</a></code>, <code><a href="TestFixturesDependencyModifiers.html" title="interface in org.gradle.api.plugins.jvm">TestFixturesDependencyModifiers</a></code></dd>
</dl>
<hr>
<pre><a href="../../Incubating.html" title="annotation in org.gradle.api">@Incubating</a>
public interface <span class="typeNameLabel">JvmComponentDependencies</span>
extends <a href="PlatformDependencyModifiers.html" title="interface in org.gradle.api.plugins.jvm">PlatformDependencyModifiers</a>, <a href="TestFixturesDependencyModifiers.html" title="interface in org.gradle.api.plugins.jvm">TestFixturesDependencyModifiers</a>, <a href="../../artifacts/dsl/GradleDependencies.html" title="interface in org.gradle.api.artifacts.dsl">GradleDependencies</a></pre>
<div class="block">This DSL element is used to add dependencies to a component, like <a href="JvmTestSuite.html" title="interface in org.gradle.api.plugins.jvm"><code>JvmTestSuite</code></a>.

 <ul>
     <li><code>implementation</code> dependencies are used at compilation and runtime.</li>
     <li><code>compileOnly</code> dependencies are used only at compilation and are not available at runtime.</li>
     <li><code>runtimeOnly</code> dependencies are not available at compilation and are used only at runtime.</li>
     <li><code>annotationProcessor</code> dependencies are used only at compilation for the annotation processor classpath</li>
 </ul></div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>7.3</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../artifacts/dsl/DependencyHandler.html" title="interface in org.gradle.api.artifacts.dsl"><code>For more information.</code></a></dd>
<dt><span class="simpleTagLabel">API Note:</span></dt>
<dd>This interface combines various <a href="../../artifacts/dsl/Dependencies.html" title="interface in org.gradle.api.artifacts.dsl"><code>Dependencies</code></a> APIs into a DSL type that can be used to add dependencies for JVM components.</dd>
<dt><span class="simpleTagLabel">Implementation Requirements:</span></dt>
<dd>The default implementation of all methods should not be overridden.</dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== NESTED CLASS SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="nested.class.summary">
<!--   -->
</a>
<h3>Nested Class Summary</h3>
<ul class="blockList">
<li class="blockList"><a name="nested.classes.inherited.from.class.org.gradle.api.plugins.jvm.PlatformDependencyModifiers">
<!--   -->
</a>
<h3>Nested classes/interfaces inherited from interface&nbsp;org.gradle.api.plugins.jvm.<a href="PlatformDependencyModifiers.html" title="interface in org.gradle.api.plugins.jvm">PlatformDependencyModifiers</a></h3>
<code><a href="PlatformDependencyModifiers.EnforcedPlatformDependencyModifier.html" title="class in org.gradle.api.plugins.jvm">PlatformDependencyModifiers.EnforcedPlatformDependencyModifier</a>, <a href="PlatformDependencyModifiers.PlatformDependencyModifier.html" title="class in org.gradle.api.plugins.jvm">PlatformDependencyModifiers.PlatformDependencyModifier</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="nested.classes.inherited.from.class.org.gradle.api.plugins.jvm.TestFixturesDependencyModifiers">
<!--   -->
</a>
<h3>Nested classes/interfaces inherited from interface&nbsp;org.gradle.api.plugins.jvm.<a href="TestFixturesDependencyModifiers.html" title="interface in org.gradle.api.plugins.jvm">TestFixturesDependencyModifiers</a></h3>
<code><a href="TestFixturesDependencyModifiers.TestFixturesDependencyModifier.html" title="class in org.gradle.api.plugins.jvm">TestFixturesDependencyModifiers.TestFixturesDependencyModifier</a></code></li>
</ul>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t3" class="tableTab"><span><a href="javascript:show(4);">Abstract Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colSecond" scope="col">Method</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code><a href="../../artifacts/dsl/DependencyAdder.html" title="interface in org.gradle.api.artifacts.dsl">DependencyAdder</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getAnnotationProcessor--">getAnnotationProcessor</a></span>()</code></th>
<td class="colLast">
<div class="block">Returns a <a href="../../artifacts/dsl/DependencyAdder.html" title="interface in org.gradle.api.artifacts.dsl"><code>DependencyAdder</code></a> to add to the set of annotation processor dependencies.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code><a href="../../artifacts/dsl/DependencyAdder.html" title="interface in org.gradle.api.artifacts.dsl">DependencyAdder</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getCompileOnly--">getCompileOnly</a></span>()</code></th>
<td class="colLast">
<div class="block">Returns a <a href="../../artifacts/dsl/DependencyAdder.html" title="interface in org.gradle.api.artifacts.dsl"><code>DependencyAdder</code></a> to add to the set of compile-only dependencies.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code><a href="../../artifacts/dsl/DependencyAdder.html" title="interface in org.gradle.api.artifacts.dsl">DependencyAdder</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getImplementation--">getImplementation</a></span>()</code></th>
<td class="colLast">
<div class="block">Returns a <a href="../../artifacts/dsl/DependencyAdder.html" title="interface in org.gradle.api.artifacts.dsl"><code>DependencyAdder</code></a> to add to the set of implementation dependencies.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code><a href="../../artifacts/dsl/DependencyAdder.html" title="interface in org.gradle.api.artifacts.dsl">DependencyAdder</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getRuntimeOnly--">getRuntimeOnly</a></span>()</code></th>
<td class="colLast">
<div class="block">Returns a <a href="../../artifacts/dsl/DependencyAdder.html" title="interface in org.gradle.api.artifacts.dsl"><code>DependencyAdder</code></a> to add to the set of runtime-only dependencies.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.org.gradle.api.artifacts.dsl.Dependencies">
<!--   -->
</a>
<h3>Methods inherited from interface&nbsp;org.gradle.api.artifacts.dsl.<a href="../../artifacts/dsl/Dependencies.html" title="interface in org.gradle.api.artifacts.dsl">Dependencies</a></h3>
<code><a href="../../artifacts/dsl/Dependencies.html#getDependencyFactory--">getDependencyFactory</a>, <a href="../../artifacts/dsl/Dependencies.html#getObjectFactory--">getObjectFactory</a>, <a href="../../artifacts/dsl/Dependencies.html#getProject--">getProject</a>, <a href="../../artifacts/dsl/Dependencies.html#module-java.lang.CharSequence-">module</a>, <a href="../../artifacts/dsl/Dependencies.html#module-java.lang.String-java.lang.String-java.lang.String-">module</a>, <a href="../../artifacts/dsl/Dependencies.html#project--">project</a>, <a href="../../artifacts/dsl/Dependencies.html#project-java.lang.String-">project</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.org.gradle.api.artifacts.dsl.GradleDependencies">
<!--   -->
</a>
<h3>Methods inherited from interface&nbsp;org.gradle.api.artifacts.dsl.<a href="../../artifacts/dsl/GradleDependencies.html" title="interface in org.gradle.api.artifacts.dsl">GradleDependencies</a></h3>
<code><a href="../../artifacts/dsl/GradleDependencies.html#gradleApi--">gradleApi</a>, <a href="../../artifacts/dsl/GradleDependencies.html#gradleTestKit--">gradleTestKit</a>, <a href="../../artifacts/dsl/GradleDependencies.html#localGroovy--">localGroovy</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.org.gradle.api.plugins.jvm.PlatformDependencyModifiers">
<!--   -->
</a>
<h3>Methods inherited from interface&nbsp;org.gradle.api.plugins.jvm.<a href="PlatformDependencyModifiers.html" title="interface in org.gradle.api.plugins.jvm">PlatformDependencyModifiers</a></h3>
<code><a href="PlatformDependencyModifiers.html#getEnforcedPlatform--">getEnforcedPlatform</a>, <a href="PlatformDependencyModifiers.html#getPlatform--">getPlatform</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.org.gradle.api.plugins.jvm.TestFixturesDependencyModifiers">
<!--   -->
</a>
<h3>Methods inherited from interface&nbsp;org.gradle.api.plugins.jvm.<a href="TestFixturesDependencyModifiers.html" title="interface in org.gradle.api.plugins.jvm">TestFixturesDependencyModifiers</a></h3>
<code><a href="TestFixturesDependencyModifiers.html#getTestFixtures--">getTestFixtures</a></code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="getImplementation--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getImplementation</h4>
<pre class="methodSignature"><a href="../../artifacts/dsl/DependencyAdder.html" title="interface in org.gradle.api.artifacts.dsl">DependencyAdder</a>&nbsp;getImplementation()</pre>
<div class="block">Returns a <a href="../../artifacts/dsl/DependencyAdder.html" title="interface in org.gradle.api.artifacts.dsl"><code>DependencyAdder</code></a> to add to the set of implementation dependencies.
 <p>
 <code>implementation</code> dependencies are used at compilation and runtime.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>a <a href="../../artifacts/dsl/DependencyAdder.html" title="interface in org.gradle.api.artifacts.dsl"><code>DependencyAdder</code></a> to add to the set of implementation dependencies</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>7.6</dd>
</dl>
</li>
</ul>
<a name="getCompileOnly--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCompileOnly</h4>
<pre class="methodSignature"><a href="../../artifacts/dsl/DependencyAdder.html" title="interface in org.gradle.api.artifacts.dsl">DependencyAdder</a>&nbsp;getCompileOnly()</pre>
<div class="block">Returns a <a href="../../artifacts/dsl/DependencyAdder.html" title="interface in org.gradle.api.artifacts.dsl"><code>DependencyAdder</code></a> to add to the set of compile-only dependencies.
 <p>
 <code>compileOnly</code> dependencies are used only at compilation and are not available at runtime.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>a <a href="../../artifacts/dsl/DependencyAdder.html" title="interface in org.gradle.api.artifacts.dsl"><code>DependencyAdder</code></a> to add to the set of compile-only dependencies</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>7.6</dd>
</dl>
</li>
</ul>
<a name="getRuntimeOnly--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getRuntimeOnly</h4>
<pre class="methodSignature"><a href="../../artifacts/dsl/DependencyAdder.html" title="interface in org.gradle.api.artifacts.dsl">DependencyAdder</a>&nbsp;getRuntimeOnly()</pre>
<div class="block">Returns a <a href="../../artifacts/dsl/DependencyAdder.html" title="interface in org.gradle.api.artifacts.dsl"><code>DependencyAdder</code></a> to add to the set of runtime-only dependencies.
 <p>
 <code>runtimeOnly</code> dependencies are not available at compilation and are used only at runtime.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>a <a href="../../artifacts/dsl/DependencyAdder.html" title="interface in org.gradle.api.artifacts.dsl"><code>DependencyAdder</code></a> to add to the set of runtime-only dependencies</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>7.6</dd>
</dl>
</li>
</ul>
<a name="getAnnotationProcessor--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>getAnnotationProcessor</h4>
<pre class="methodSignature"><a href="../../artifacts/dsl/DependencyAdder.html" title="interface in org.gradle.api.artifacts.dsl">DependencyAdder</a>&nbsp;getAnnotationProcessor()</pre>
<div class="block">Returns a <a href="../../artifacts/dsl/DependencyAdder.html" title="interface in org.gradle.api.artifacts.dsl"><code>DependencyAdder</code></a> to add to the set of annotation processor dependencies.
 <p>
 <code>annotationProcessor</code> dependencies are used only at compilation, and are added to the annotation processor classpath.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>a <a href="../../artifacts/dsl/DependencyAdder.html" title="interface in org.gradle.api.artifacts.dsl"><code>DependencyAdder</code></a> to add to the set of annotation processor dependencies</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>7.6</dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../../allclasses.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li><a href="#nested.class.summary">Nested</a>&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
