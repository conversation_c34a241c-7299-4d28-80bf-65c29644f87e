<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc -->
<title>SourceSet (Gradle API 8.4)</title>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<link rel="stylesheet" type="text/css" href="../../../../javadoc.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../jquery/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
<script type="text/javascript" src="../../../../jquery/jszip/dist/jszip.min.js"></script>
<script type="text/javascript" src="../../../../jquery/jszip-utils/dist/jszip-utils.min.js"></script>
<!--[if IE]>
<script type="text/javascript" src="../../../../jquery/jszip-utils/dist/jszip-utils-ie.min.js"></script>
<![endif]-->
<script type="text/javascript" src="../../../../jquery/jquery-3.6.0.min.js"></script>
<script type="text/javascript" src="../../../../jquery/jquery-ui.min.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="SourceSet (Gradle API 8.4)";
        }
    }
    catch(err) {
    }
//-->
var data = {"i0":6,"i1":6,"i2":6,"i3":6,"i4":6,"i5":6,"i6":6,"i7":6,"i8":6,"i9":6,"i10":6,"i11":6,"i12":6,"i13":6,"i14":6,"i15":6,"i16":6,"i17":6,"i18":6,"i19":6,"i20":6,"i21":6,"i22":6,"i23":6,"i24":6,"i25":6,"i26":6,"i27":6,"i28":6,"i29":6,"i30":6,"i31":1,"i32":6,"i33":6,"i34":6,"i35":6,"i36":6,"i37":6,"i38":6};
var tabs = {65535:["t0","All Methods"],1:["t1","Static Methods"],2:["t2","Instance Methods"],4:["t3","Abstract Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
var pathtoroot = "../../../../";
var useModuleDirectories = false;
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="fixedNav">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses.html">All&nbsp;Classes</a></li>
</ul>
<ul class="navListSearch">
<li><label for="search">SEARCH:</label>
<input type="text" id="search" value="search" disabled="disabled">
<input type="reset" id="reset" value="reset" disabled="disabled">
</li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field.summary">Field</a>&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#field.detail">Field</a>&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
</div>
<div class="navPadding">&nbsp;</div>
<script type="text/javascript"><!--
$('.navPadding').css('padding-top', $('.fixedNav').css("height"));
//-->
</script>
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle"><span class="packageLabelInType">Package</span>&nbsp;<a href="package-summary.html">org.gradle.api.tasks</a></div>
<h2 title="Interface SourceSet" class="title">Interface SourceSet</h2>
</div>
<div class="contentContainer">
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Superinterfaces:</dt>
<dd><code><a href="../plugins/ExtensionAware.html" title="interface in org.gradle.api.plugins">ExtensionAware</a></code></dd>
</dl>
<hr>
<pre>public interface <span class="typeNameLabel">SourceSet</span>
extends <a href="../plugins/ExtensionAware.html" title="interface in org.gradle.api.plugins">ExtensionAware</a></pre>
<div class="block">A <code>SourceSet</code> represents a logical group of Java source and resource files. They
 are covered in more detail in the
 <a href="https://docs.gradle.org/current/userguide/building_java_projects.html#sec:java_source_sets">user manual</a>.
 <p>
 The following example shows how you can configure the 'main' source set, which in this
 case involves excluding classes whose package begins 'some.unwanted.package' from
 compilation of the source files in the 'java' <a href="../file/SourceDirectorySet.html" title="interface in org.gradle.api.file"><code>SourceDirectorySet</code></a>:

 <pre class='autoTested'>
 plugins {
     id 'java'
 }

 sourceSets {
   main {
     java {
       exclude 'some/unwanted/package/**'
     }
   }
 }
 </pre></div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- =========== FIELD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.summary">
<!--   -->
</a>
<h3>Field Summary</h3>
<table class="memberSummary" summary="Field Summary table, listing fields, and an explanation">
<caption><span>Fields</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colSecond" scope="col">Field</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#MAIN_SOURCE_SET_NAME">MAIN_SOURCE_SET_NAME</a></span></code></th>
<td class="colLast">
<div class="block">The name of the main source set.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#TEST_SOURCE_SET_NAME">TEST_SOURCE_SET_NAME</a></span></code></th>
<td class="colLast">
<div class="block">The name of the test source set.</div>
</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t1" class="tableTab"><span><a href="javascript:show(1);">Static Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t3" class="tableTab"><span><a href="javascript:show(4);">Abstract Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colSecond" scope="col">Method</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code><a href="SourceSet.html" title="interface in org.gradle.api.tasks">SourceSet</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#compiledBy-java.lang.Object...-">compiledBy</a></span>&#8203;(java.lang.Object...&nbsp;taskPaths)</code></th>
<td class="colLast">
<div class="block">Registers a set of tasks which are responsible for compiling this source set into the classes directory.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code><a href="../file/SourceDirectorySet.html" title="interface in org.gradle.api.file">SourceDirectorySet</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getAllJava--">getAllJava</a></span>()</code></th>
<td class="colLast">
<div class="block">All Java source files for this source set.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code><a href="../file/SourceDirectorySet.html" title="interface in org.gradle.api.file">SourceDirectorySet</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getAllSource--">getAllSource</a></span>()</code></th>
<td class="colLast">
<div class="block">All source files for this source set.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>java.lang.String</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getAnnotationProcessorConfigurationName--">getAnnotationProcessorConfigurationName</a></span>()</code></th>
<td class="colLast">
<div class="block">Returns the name of the configuration containing annotation processors and their
 dependencies needed to compile this source set.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code><a href="../file/FileCollection.html" title="interface in org.gradle.api.file">FileCollection</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getAnnotationProcessorPath--">getAnnotationProcessorPath</a></span>()</code></th>
<td class="colLast">
<div class="block">Returns the classpath used to load annotation processors when compiling this source set.</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>java.lang.String</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getApiConfigurationName--">getApiConfigurationName</a></span>()</code></th>
<td class="colLast">
<div class="block">Returns the name of the API configuration for this source set.</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getApiElementsConfigurationName--">getApiElementsConfigurationName</a></span>()</code></th>
<td class="colLast">
<div class="block">Returns the name of the configuration that should be used when compiling against the API
 of this component.</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>java.lang.String</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getClassesTaskName--">getClassesTaskName</a></span>()</code></th>
<td class="colLast">
<div class="block">Returns the name of the classes task for this source set.</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code><a href="../file/FileCollection.html" title="interface in org.gradle.api.file">FileCollection</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getCompileClasspath--">getCompileClasspath</a></span>()</code></th>
<td class="colLast">
<div class="block">Returns the classpath used to compile this source.</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>java.lang.String</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getCompileClasspathConfigurationName--">getCompileClasspathConfigurationName</a></span>()</code></th>
<td class="colLast">
<div class="block">Returns the name of the compile classpath configuration for this source set.</div>
</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getCompileJavaTaskName--">getCompileJavaTaskName</a></span>()</code></th>
<td class="colLast">
<div class="block">Returns the name of the compile Java task for this source set.</div>
</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code>java.lang.String</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getCompileOnlyApiConfigurationName--">getCompileOnlyApiConfigurationName</a></span>()</code></th>
<td class="colLast">
<div class="block">Returns the name of the 'compile only api' configuration for this source set.</div>
</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getCompileOnlyConfigurationName--">getCompileOnlyConfigurationName</a></span>()</code></th>
<td class="colLast">
<div class="block">Returns the name of the compile only configuration for this source set.</div>
</td>
</tr>
<tr id="i13" class="rowColor">
<td class="colFirst"><code>java.lang.String</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getCompileTaskName-java.lang.String-">getCompileTaskName</a></span>&#8203;(java.lang.String&nbsp;language)</code></th>
<td class="colLast">
<div class="block">Returns the name of a compile task for this source set.</div>
</td>
</tr>
<tr id="i14" class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getImplementationConfigurationName--">getImplementationConfigurationName</a></span>()</code></th>
<td class="colLast">
<div class="block">Returns the name of the implementation configuration for this source set.</div>
</td>
</tr>
<tr id="i15" class="rowColor">
<td class="colFirst"><code>java.lang.String</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getJarTaskName--">getJarTaskName</a></span>()</code></th>
<td class="colLast">
<div class="block">Returns the name of the Jar task for this source set.</div>
</td>
</tr>
<tr id="i16" class="altColor">
<td class="colFirst"><code><a href="../file/SourceDirectorySet.html" title="interface in org.gradle.api.file">SourceDirectorySet</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getJava--">getJava</a></span>()</code></th>
<td class="colLast">
<div class="block">Returns the Java source which is to be compiled by the Java compiler into the class output directory.</div>
</td>
</tr>
<tr id="i17" class="rowColor">
<td class="colFirst"><code>java.lang.String</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getJavadocElementsConfigurationName--">getJavadocElementsConfigurationName</a></span>()</code></th>
<td class="colLast">
<div class="block">Returns the name of the configuration that represents the variant that carries the
 Javadoc for this source set in packaged form.</div>
</td>
</tr>
<tr id="i18" class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getJavadocJarTaskName--">getJavadocJarTaskName</a></span>()</code></th>
<td class="colLast">
<div class="block">Returns the name of the Javadoc Jar task for this source set.</div>
</td>
</tr>
<tr id="i19" class="rowColor">
<td class="colFirst"><code>java.lang.String</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getJavadocTaskName--">getJavadocTaskName</a></span>()</code></th>
<td class="colLast">
<div class="block">Returns the name of the Javadoc task for this source set.</div>
</td>
</tr>
<tr id="i20" class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getName--">getName</a></span>()</code></th>
<td class="colLast">
<div class="block">Returns the name of this source set.</div>
</td>
</tr>
<tr id="i21" class="rowColor">
<td class="colFirst"><code><a href="SourceSetOutput.html" title="interface in org.gradle.api.tasks">SourceSetOutput</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getOutput--">getOutput</a></span>()</code></th>
<td class="colLast">
<div class="block"><a href="SourceSetOutput.html" title="interface in org.gradle.api.tasks"><code>SourceSetOutput</code></a> is a <a href="../file/FileCollection.html" title="interface in org.gradle.api.file"><code>FileCollection</code></a> of all output directories (compiled classes, processed resources, etc.)
 and it provides means to configure the default output dirs and register additional output dirs.</div>
</td>
</tr>
<tr id="i22" class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getProcessResourcesTaskName--">getProcessResourcesTaskName</a></span>()</code></th>
<td class="colLast">
<div class="block">Returns the name of the resource process task for this source set.</div>
</td>
</tr>
<tr id="i23" class="rowColor">
<td class="colFirst"><code><a href="../file/SourceDirectorySet.html" title="interface in org.gradle.api.file">SourceDirectorySet</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getResources--">getResources</a></span>()</code></th>
<td class="colLast">
<div class="block">Returns the non-Java resources which are to be copied into the resources output directory.</div>
</td>
</tr>
<tr id="i24" class="altColor">
<td class="colFirst"><code><a href="../file/FileCollection.html" title="interface in org.gradle.api.file">FileCollection</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getRuntimeClasspath--">getRuntimeClasspath</a></span>()</code></th>
<td class="colLast">
<div class="block">Returns the classpath used to execute this source.</div>
</td>
</tr>
<tr id="i25" class="rowColor">
<td class="colFirst"><code>java.lang.String</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getRuntimeClasspathConfigurationName--">getRuntimeClasspathConfigurationName</a></span>()</code></th>
<td class="colLast">
<div class="block">Returns the name of the runtime classpath configuration of this component: the runtime
 classpath contains elements of the implementation, as well as runtime only elements.</div>
</td>
</tr>
<tr id="i26" class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getRuntimeElementsConfigurationName--">getRuntimeElementsConfigurationName</a></span>()</code></th>
<td class="colLast">
<div class="block">Returns the name of the configuration containing elements that are strictly required
 at runtime.</div>
</td>
</tr>
<tr id="i27" class="rowColor">
<td class="colFirst"><code>java.lang.String</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getRuntimeOnlyConfigurationName--">getRuntimeOnlyConfigurationName</a></span>()</code></th>
<td class="colLast">
<div class="block">Returns the name of the configuration that contains dependencies that are only required
 at runtime of the component.</div>
</td>
</tr>
<tr id="i28" class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getSourcesElementsConfigurationName--">getSourcesElementsConfigurationName</a></span>()</code></th>
<td class="colLast">
<div class="block">Returns the name of the configuration that represents the variant that carries the
 original source code in packaged form.</div>
</td>
</tr>
<tr id="i29" class="rowColor">
<td class="colFirst"><code>java.lang.String</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getSourcesJarTaskName--">getSourcesJarTaskName</a></span>()</code></th>
<td class="colLast">
<div class="block">Returns the name of the Source Jar task for this source set.</div>
</td>
</tr>
<tr id="i30" class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getTaskName-java.lang.String-java.lang.String-">getTaskName</a></span>&#8203;(java.lang.String&nbsp;verb,
           java.lang.String&nbsp;target)</code></th>
<td class="colLast">
<div class="block">Returns the name of a task for this source set.</div>
</td>
</tr>
<tr id="i31" class="rowColor">
<td class="colFirst"><code>static boolean</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#isMain-org.gradle.api.tasks.SourceSet-">isMain</a></span>&#8203;(<a href="SourceSet.html" title="interface in org.gradle.api.tasks">SourceSet</a>&nbsp;sourceSet)</code></th>
<td class="colLast">
<div class="block">Determines if this source set is the main source set</div>
</td>
</tr>
<tr id="i32" class="altColor">
<td class="colFirst"><code><a href="SourceSet.html" title="interface in org.gradle.api.tasks">SourceSet</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#java-groovy.lang.Closure-">java</a></span>&#8203;(<a href="https://docs.groovy-lang.org/docs/groovy-3.0.17/html/gapi/groovy/lang/Closure.html?is-external=true" title="class or interface in groovy.lang" class="externalLink">Closure</a>&nbsp;configureClosure)</code></th>
<td class="colLast">
<div class="block">Configures the Java source for this set.</div>
</td>
</tr>
<tr id="i33" class="rowColor">
<td class="colFirst"><code><a href="SourceSet.html" title="interface in org.gradle.api.tasks">SourceSet</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#java-org.gradle.api.Action-">java</a></span>&#8203;(<a href="../Action.html" title="interface in org.gradle.api">Action</a>&lt;? super <a href="../file/SourceDirectorySet.html" title="interface in org.gradle.api.file">SourceDirectorySet</a>&gt;&nbsp;configureAction)</code></th>
<td class="colLast">
<div class="block">Configures the Java source for this set.</div>
</td>
</tr>
<tr id="i34" class="altColor">
<td class="colFirst"><code><a href="SourceSet.html" title="interface in org.gradle.api.tasks">SourceSet</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#resources-groovy.lang.Closure-">resources</a></span>&#8203;(<a href="https://docs.groovy-lang.org/docs/groovy-3.0.17/html/gapi/groovy/lang/Closure.html?is-external=true" title="class or interface in groovy.lang" class="externalLink">Closure</a>&nbsp;configureClosure)</code></th>
<td class="colLast">
<div class="block">Configures the non-Java resources for this set.</div>
</td>
</tr>
<tr id="i35" class="rowColor">
<td class="colFirst"><code><a href="SourceSet.html" title="interface in org.gradle.api.tasks">SourceSet</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#resources-org.gradle.api.Action-">resources</a></span>&#8203;(<a href="../Action.html" title="interface in org.gradle.api">Action</a>&lt;? super <a href="../file/SourceDirectorySet.html" title="interface in org.gradle.api.file">SourceDirectorySet</a>&gt;&nbsp;configureAction)</code></th>
<td class="colLast">
<div class="block">Configures the non-Java resources for this set.</div>
</td>
</tr>
<tr id="i36" class="altColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setAnnotationProcessorPath-org.gradle.api.file.FileCollection-">setAnnotationProcessorPath</a></span>&#8203;(<a href="../file/FileCollection.html" title="interface in org.gradle.api.file">FileCollection</a>&nbsp;annotationProcessorPath)</code></th>
<td class="colLast">
<div class="block">Set the classpath to use to load annotation processors when compiling this source set.</div>
</td>
</tr>
<tr id="i37" class="rowColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setCompileClasspath-org.gradle.api.file.FileCollection-">setCompileClasspath</a></span>&#8203;(<a href="../file/FileCollection.html" title="interface in org.gradle.api.file">FileCollection</a>&nbsp;classpath)</code></th>
<td class="colLast">
<div class="block">Sets the classpath used to compile this source.</div>
</td>
</tr>
<tr id="i38" class="altColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setRuntimeClasspath-org.gradle.api.file.FileCollection-">setRuntimeClasspath</a></span>&#8203;(<a href="../file/FileCollection.html" title="interface in org.gradle.api.file">FileCollection</a>&nbsp;classpath)</code></th>
<td class="colLast">
<div class="block">Sets the classpath used to execute this source.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.org.gradle.api.plugins.ExtensionAware">
<!--   -->
</a>
<h3>Methods inherited from interface&nbsp;org.gradle.api.plugins.<a href="../plugins/ExtensionAware.html" title="interface in org.gradle.api.plugins">ExtensionAware</a></h3>
<code><a href="../plugins/ExtensionAware.html#getExtensions--">getExtensions</a></code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ FIELD DETAIL =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.detail">
<!--   -->
</a>
<h3>Field Detail</h3>
<a name="MAIN_SOURCE_SET_NAME">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>MAIN_SOURCE_SET_NAME</h4>
<pre>static final&nbsp;java.lang.String MAIN_SOURCE_SET_NAME</pre>
<div class="block">The name of the main source set.</div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../../constant-values.html#org.gradle.api.tasks.SourceSet.MAIN_SOURCE_SET_NAME">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="TEST_SOURCE_SET_NAME">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>TEST_SOURCE_SET_NAME</h4>
<pre>static final&nbsp;java.lang.String TEST_SOURCE_SET_NAME</pre>
<div class="block">The name of the test source set.</div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../../constant-values.html#org.gradle.api.tasks.SourceSet.TEST_SOURCE_SET_NAME">Constant Field Values</a></dd>
</dl>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="getName--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getName</h4>
<pre class="methodSignature">java.lang.String&nbsp;getName()</pre>
<div class="block">Returns the name of this source set.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The name. Never returns null.</dd>
</dl>
</li>
</ul>
<a name="getCompileClasspath--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCompileClasspath</h4>
<pre class="methodSignature"><a href="../file/FileCollection.html" title="interface in org.gradle.api.file">FileCollection</a>&nbsp;getCompileClasspath()</pre>
<div class="block">Returns the classpath used to compile this source.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The classpath. Never returns null.</dd>
</dl>
</li>
</ul>
<a name="setCompileClasspath-org.gradle.api.file.FileCollection-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCompileClasspath</h4>
<pre class="methodSignature">void&nbsp;setCompileClasspath&#8203;(<a href="../file/FileCollection.html" title="interface in org.gradle.api.file">FileCollection</a>&nbsp;classpath)</pre>
<div class="block">Sets the classpath used to compile this source.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>classpath</code> - The classpath. Should not be null.</dd>
</dl>
</li>
</ul>
<a name="getAnnotationProcessorPath--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getAnnotationProcessorPath</h4>
<pre class="methodSignature"><a href="../file/FileCollection.html" title="interface in org.gradle.api.file">FileCollection</a>&nbsp;getAnnotationProcessorPath()</pre>
<div class="block">Returns the classpath used to load annotation processors when compiling this source set.
 This path is also used for annotation processor discovery. The classpath can be empty,
 which means use the compile classpath; if you want to disable annotation processing,
 then use <code>-proc:none</code> as a compiler argument.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The annotation processor path. Never returns null.</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>4.6</dd>
</dl>
</li>
</ul>
<a name="setAnnotationProcessorPath-org.gradle.api.file.FileCollection-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setAnnotationProcessorPath</h4>
<pre class="methodSignature">void&nbsp;setAnnotationProcessorPath&#8203;(<a href="../file/FileCollection.html" title="interface in org.gradle.api.file">FileCollection</a>&nbsp;annotationProcessorPath)</pre>
<div class="block">Set the classpath to use to load annotation processors when compiling this source set.
 This path is also used for annotation processor discovery. The classpath can be empty,
 which means use the compile classpath; if you want to disable annotation processing,
 then use <code>-proc:none</code> as a compiler argument.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>annotationProcessorPath</code> - The annotation processor path. Should not be null.</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>4.6</dd>
</dl>
</li>
</ul>
<a name="getRuntimeClasspath--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getRuntimeClasspath</h4>
<pre class="methodSignature"><a href="../file/FileCollection.html" title="interface in org.gradle.api.file">FileCollection</a>&nbsp;getRuntimeClasspath()</pre>
<div class="block">Returns the classpath used to execute this source.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The classpath. Never returns null.</dd>
</dl>
</li>
</ul>
<a name="setRuntimeClasspath-org.gradle.api.file.FileCollection-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setRuntimeClasspath</h4>
<pre class="methodSignature">void&nbsp;setRuntimeClasspath&#8203;(<a href="../file/FileCollection.html" title="interface in org.gradle.api.file">FileCollection</a>&nbsp;classpath)</pre>
<div class="block">Sets the classpath used to execute this source.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>classpath</code> - The classpath. Should not be null.</dd>
</dl>
</li>
</ul>
<a name="getOutput--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getOutput</h4>
<pre class="methodSignature"><a href="SourceSetOutput.html" title="interface in org.gradle.api.tasks">SourceSetOutput</a>&nbsp;getOutput()</pre>
<div class="block"><a href="SourceSetOutput.html" title="interface in org.gradle.api.tasks"><code>SourceSetOutput</code></a> is a <a href="../file/FileCollection.html" title="interface in org.gradle.api.file"><code>FileCollection</code></a> of all output directories (compiled classes, processed resources, etc.)
 and it provides means to configure the default output dirs and register additional output dirs. See examples in <a href="SourceSetOutput.html" title="interface in org.gradle.api.tasks"><code>SourceSetOutput</code></a></div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The output dirs, as a <a href="SourceSetOutput.html" title="interface in org.gradle.api.tasks"><code>SourceSetOutput</code></a>.</dd>
</dl>
</li>
</ul>
<a name="compiledBy-java.lang.Object...-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>compiledBy</h4>
<pre class="methodSignature"><a href="SourceSet.html" title="interface in org.gradle.api.tasks">SourceSet</a>&nbsp;compiledBy&#8203;(java.lang.Object...&nbsp;taskPaths)</pre>
<div class="block">Registers a set of tasks which are responsible for compiling this source set into the classes directory. The
 paths are evaluated as per <a href="../Task.html#dependsOn-java.lang.Object...-"><code>Task.dependsOn(Object...)</code></a>.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>taskPaths</code> - The tasks which compile this source set.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>this</dd>
</dl>
</li>
</ul>
<a name="getResources--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getResources</h4>
<pre class="methodSignature"><a href="../file/SourceDirectorySet.html" title="interface in org.gradle.api.file">SourceDirectorySet</a>&nbsp;getResources()</pre>
<div class="block">Returns the non-Java resources which are to be copied into the resources output directory.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the resources. Never returns null.</dd>
</dl>
</li>
</ul>
<a name="resources-groovy.lang.Closure-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>resources</h4>
<pre class="methodSignature"><a href="SourceSet.html" title="interface in org.gradle.api.tasks">SourceSet</a>&nbsp;resources&#8203;(@Nullable <a href="https://docs.groovy-lang.org/docs/groovy-3.0.17/html/gapi/groovy/lang/DelegatesTo.html?is-external=true" title="class or interface in groovy.lang" class="externalLink">@DelegatesTo</a>(<a href="../file/SourceDirectorySet.html" title="interface in org.gradle.api.file">SourceDirectorySet.class</a>)
                    <a href="https://docs.groovy-lang.org/docs/groovy-3.0.17/html/gapi/groovy/lang/Closure.html?is-external=true" title="class or interface in groovy.lang" class="externalLink">Closure</a>&nbsp;configureClosure)</pre>
<div class="block">Configures the non-Java resources for this set.

 <p>The given closure is used to configure the <a href="../file/SourceDirectorySet.html" title="interface in org.gradle.api.file"><code>SourceDirectorySet</code></a> which contains the resources.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>configureClosure</code> - The closure to use to configure the resources.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>this</dd>
</dl>
</li>
</ul>
<a name="resources-org.gradle.api.Action-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>resources</h4>
<pre class="methodSignature"><a href="SourceSet.html" title="interface in org.gradle.api.tasks">SourceSet</a>&nbsp;resources&#8203;(<a href="../Action.html" title="interface in org.gradle.api">Action</a>&lt;? super <a href="../file/SourceDirectorySet.html" title="interface in org.gradle.api.file">SourceDirectorySet</a>&gt;&nbsp;configureAction)</pre>
<div class="block">Configures the non-Java resources for this set.

 <p>The given action is used to configure the <a href="../file/SourceDirectorySet.html" title="interface in org.gradle.api.file"><code>SourceDirectorySet</code></a> which contains the resources.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>configureAction</code> - The action to use to configure the resources.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>this</dd>
</dl>
</li>
</ul>
<a name="getJava--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getJava</h4>
<pre class="methodSignature"><a href="../file/SourceDirectorySet.html" title="interface in org.gradle.api.file">SourceDirectorySet</a>&nbsp;getJava()</pre>
<div class="block">Returns the Java source which is to be compiled by the Java compiler into the class output directory.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the Java source. Never returns null.</dd>
</dl>
</li>
</ul>
<a name="java-groovy.lang.Closure-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>java</h4>
<pre class="methodSignature"><a href="SourceSet.html" title="interface in org.gradle.api.tasks">SourceSet</a>&nbsp;java&#8203;(@Nullable <a href="https://docs.groovy-lang.org/docs/groovy-3.0.17/html/gapi/groovy/lang/DelegatesTo.html?is-external=true" title="class or interface in groovy.lang" class="externalLink">@DelegatesTo</a>(<a href="../file/SourceDirectorySet.html" title="interface in org.gradle.api.file">SourceDirectorySet.class</a>)
               <a href="https://docs.groovy-lang.org/docs/groovy-3.0.17/html/gapi/groovy/lang/Closure.html?is-external=true" title="class or interface in groovy.lang" class="externalLink">Closure</a>&nbsp;configureClosure)</pre>
<div class="block">Configures the Java source for this set.

 <p>The given closure is used to configure the <a href="../file/SourceDirectorySet.html" title="interface in org.gradle.api.file"><code>SourceDirectorySet</code></a> which contains the Java source.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>configureClosure</code> - The closure to use to configure the Java source.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>this</dd>
</dl>
</li>
</ul>
<a name="java-org.gradle.api.Action-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>java</h4>
<pre class="methodSignature"><a href="SourceSet.html" title="interface in org.gradle.api.tasks">SourceSet</a>&nbsp;java&#8203;(<a href="../Action.html" title="interface in org.gradle.api">Action</a>&lt;? super <a href="../file/SourceDirectorySet.html" title="interface in org.gradle.api.file">SourceDirectorySet</a>&gt;&nbsp;configureAction)</pre>
<div class="block">Configures the Java source for this set.

 <p>The given action is used to configure the <a href="../file/SourceDirectorySet.html" title="interface in org.gradle.api.file"><code>SourceDirectorySet</code></a> which contains the Java source.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>configureAction</code> - The action to use to configure the Java source.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>this</dd>
</dl>
</li>
</ul>
<a name="getAllJava--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getAllJava</h4>
<pre class="methodSignature"><a href="../file/SourceDirectorySet.html" title="interface in org.gradle.api.file">SourceDirectorySet</a>&nbsp;getAllJava()</pre>
<div class="block">All Java source files for this source set. This includes, for example, source which is directly compiled, and
 source which is indirectly compiled through joint compilation.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the Java source. Never returns null.</dd>
</dl>
</li>
</ul>
<a name="getAllSource--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getAllSource</h4>
<pre class="methodSignature"><a href="../file/SourceDirectorySet.html" title="interface in org.gradle.api.file">SourceDirectorySet</a>&nbsp;getAllSource()</pre>
<div class="block">All source files for this source set.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the source. Never returns null.</dd>
</dl>
</li>
</ul>
<a name="getClassesTaskName--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getClassesTaskName</h4>
<pre class="methodSignature">java.lang.String&nbsp;getClassesTaskName()</pre>
<div class="block">Returns the name of the classes task for this source set.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The task name. Never returns null.</dd>
</dl>
</li>
</ul>
<a name="getProcessResourcesTaskName--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getProcessResourcesTaskName</h4>
<pre class="methodSignature">java.lang.String&nbsp;getProcessResourcesTaskName()</pre>
<div class="block">Returns the name of the resource process task for this source set.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The task name. Never returns null.</dd>
</dl>
</li>
</ul>
<a name="getCompileJavaTaskName--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCompileJavaTaskName</h4>
<pre class="methodSignature">java.lang.String&nbsp;getCompileJavaTaskName()</pre>
<div class="block">Returns the name of the compile Java task for this source set.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The task name. Never returns null.</dd>
</dl>
</li>
</ul>
<a name="getCompileTaskName-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCompileTaskName</h4>
<pre class="methodSignature">java.lang.String&nbsp;getCompileTaskName&#8203;(java.lang.String&nbsp;language)</pre>
<div class="block">Returns the name of a compile task for this source set.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>language</code> - The language to be compiled.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The task name. Never returns null.</dd>
</dl>
</li>
</ul>
<a name="getJavadocTaskName--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getJavadocTaskName</h4>
<pre class="methodSignature">java.lang.String&nbsp;getJavadocTaskName()</pre>
<div class="block">Returns the name of the Javadoc task for this source set.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The task name. Never returns null.</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>6.0</dd>
</dl>
</li>
</ul>
<a name="getJarTaskName--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getJarTaskName</h4>
<pre class="methodSignature">java.lang.String&nbsp;getJarTaskName()</pre>
<div class="block">Returns the name of the Jar task for this source set.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The task name. Never returns null.</dd>
</dl>
</li>
</ul>
<a name="getJavadocJarTaskName--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getJavadocJarTaskName</h4>
<pre class="methodSignature">java.lang.String&nbsp;getJavadocJarTaskName()</pre>
<div class="block">Returns the name of the Javadoc Jar task for this source set.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The task name. Never returns null.</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>6.0</dd>
</dl>
</li>
</ul>
<a name="getSourcesJarTaskName--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getSourcesJarTaskName</h4>
<pre class="methodSignature">java.lang.String&nbsp;getSourcesJarTaskName()</pre>
<div class="block">Returns the name of the Source Jar task for this source set.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The task name. Never returns null.</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>6.0</dd>
</dl>
</li>
</ul>
<a name="getTaskName-java.lang.String-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getTaskName</h4>
<pre class="methodSignature">java.lang.String&nbsp;getTaskName&#8203;(@Nullable
                             java.lang.String&nbsp;verb,
                             @Nullable
                             java.lang.String&nbsp;target)</pre>
<div class="block">Returns the name of a task for this source set.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>verb</code> - The action, may be null.</dd>
<dd><code>target</code> - The target, may be null</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The task name, generally of the form ${verb}${name}${noun}</dd>
</dl>
</li>
</ul>
<a name="getCompileOnlyConfigurationName--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCompileOnlyConfigurationName</h4>
<pre class="methodSignature">java.lang.String&nbsp;getCompileOnlyConfigurationName()</pre>
<div class="block">Returns the name of the compile only configuration for this source set.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The compile only configuration name</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>2.12</dd>
</dl>
</li>
</ul>
<a name="getCompileOnlyApiConfigurationName--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCompileOnlyApiConfigurationName</h4>
<pre class="methodSignature">java.lang.String&nbsp;getCompileOnlyApiConfigurationName()</pre>
<div class="block">Returns the name of the 'compile only api' configuration for this source set.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The 'compile only api' configuration name</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>6.7</dd>
</dl>
</li>
</ul>
<a name="getCompileClasspathConfigurationName--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCompileClasspathConfigurationName</h4>
<pre class="methodSignature">java.lang.String&nbsp;getCompileClasspathConfigurationName()</pre>
<div class="block">Returns the name of the compile classpath configuration for this source set.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The compile classpath configuration</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>2.12</dd>
</dl>
</li>
</ul>
<a name="getAnnotationProcessorConfigurationName--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getAnnotationProcessorConfigurationName</h4>
<pre class="methodSignature">java.lang.String&nbsp;getAnnotationProcessorConfigurationName()</pre>
<div class="block">Returns the name of the configuration containing annotation processors and their
 dependencies needed to compile this source set.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the name of the annotation processor configuration.</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>4.6</dd>
</dl>
</li>
</ul>
<a name="getApiConfigurationName--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getApiConfigurationName</h4>
<pre class="methodSignature">java.lang.String&nbsp;getApiConfigurationName()</pre>
<div class="block">Returns the name of the API configuration for this source set. The API configuration
 contains dependencies which are exported by this source set, and is not transitive
 by default. This configuration is not meant to be resolved and should only contain
 dependencies that are required when compiling against this component.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The API configuration name</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>3.3</dd>
</dl>
</li>
</ul>
<a name="getImplementationConfigurationName--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getImplementationConfigurationName</h4>
<pre class="methodSignature">java.lang.String&nbsp;getImplementationConfigurationName()</pre>
<div class="block">Returns the name of the implementation configuration for this source set. The implementation
 configuration should contain dependencies which are specific to the implementation of the component
 (internal APIs).</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The configuration name</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>3.4</dd>
</dl>
</li>
</ul>
<a name="getApiElementsConfigurationName--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getApiElementsConfigurationName</h4>
<pre class="methodSignature">java.lang.String&nbsp;getApiElementsConfigurationName()</pre>
<div class="block">Returns the name of the configuration that should be used when compiling against the API
 of this component. This configuration is meant to be consumed by other components when
 they need to compile against it.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The API compile configuration name</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>3.3</dd>
</dl>
</li>
</ul>
<a name="getRuntimeOnlyConfigurationName--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getRuntimeOnlyConfigurationName</h4>
<pre class="methodSignature">java.lang.String&nbsp;getRuntimeOnlyConfigurationName()</pre>
<div class="block">Returns the name of the configuration that contains dependencies that are only required
 at runtime of the component. Dependencies found in this configuration are visible to
 the runtime classpath of the component, but not to consumers.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the runtime only configuration name</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>3.4</dd>
</dl>
</li>
</ul>
<a name="getRuntimeClasspathConfigurationName--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getRuntimeClasspathConfigurationName</h4>
<pre class="methodSignature">java.lang.String&nbsp;getRuntimeClasspathConfigurationName()</pre>
<div class="block">Returns the name of the runtime classpath configuration of this component: the runtime
 classpath contains elements of the implementation, as well as runtime only elements.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the name of the runtime classpath configuration</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>3.4</dd>
</dl>
</li>
</ul>
<a name="getRuntimeElementsConfigurationName--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getRuntimeElementsConfigurationName</h4>
<pre class="methodSignature">java.lang.String&nbsp;getRuntimeElementsConfigurationName()</pre>
<div class="block">Returns the name of the configuration containing elements that are strictly required
 at runtime. Consumers of this configuration will get all the mandatory elements for
 this component to execute at runtime.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the name of the runtime elements configuration.</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>3.4</dd>
</dl>
</li>
</ul>
<a name="getJavadocElementsConfigurationName--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getJavadocElementsConfigurationName</h4>
<pre class="methodSignature">java.lang.String&nbsp;getJavadocElementsConfigurationName()</pre>
<div class="block">Returns the name of the configuration that represents the variant that carries the
 Javadoc for this source set in packaged form. Used to publish a variant with a '-javadoc' zip.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the name of the javadoc elements configuration.</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>6.0</dd>
</dl>
</li>
</ul>
<a name="getSourcesElementsConfigurationName--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getSourcesElementsConfigurationName</h4>
<pre class="methodSignature">java.lang.String&nbsp;getSourcesElementsConfigurationName()</pre>
<div class="block">Returns the name of the configuration that represents the variant that carries the
 original source code in packaged form. Used to publish a variant with a '-sources' zip.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the name of the sources elements configuration.</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>6.0</dd>
</dl>
</li>
</ul>
<a name="isMain-org.gradle.api.tasks.SourceSet-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>isMain</h4>
<pre class="methodSignature">static&nbsp;boolean&nbsp;isMain&#8203;(<a href="SourceSet.html" title="interface in org.gradle.api.tasks">SourceSet</a>&nbsp;sourceSet)</pre>
<div class="block">Determines if this source set is the main source set</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>6.7</dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field.summary">Field</a>&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#field.detail">Field</a>&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
