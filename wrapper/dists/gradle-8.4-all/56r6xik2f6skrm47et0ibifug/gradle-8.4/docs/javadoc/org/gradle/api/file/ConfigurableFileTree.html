<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc -->
<title>ConfigurableFileTree (Gradle API 8.4)</title>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<link rel="stylesheet" type="text/css" href="../../../../javadoc.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../jquery/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
<script type="text/javascript" src="../../../../jquery/jszip/dist/jszip.min.js"></script>
<script type="text/javascript" src="../../../../jquery/jszip-utils/dist/jszip-utils.min.js"></script>
<!--[if IE]>
<script type="text/javascript" src="../../../../jquery/jszip-utils/dist/jszip-utils-ie.min.js"></script>
<![endif]-->
<script type="text/javascript" src="../../../../jquery/jquery-3.6.0.min.js"></script>
<script type="text/javascript" src="../../../../jquery/jquery-ui.min.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="ConfigurableFileTree (Gradle API 8.4)";
        }
    }
    catch(err) {
    }
//-->
var data = {"i0":6,"i1":6,"i2":6,"i3":6,"i4":6,"i5":6};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],4:["t3","Abstract Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
var pathtoroot = "../../../../";
var useModuleDirectories = false;
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="fixedNav">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses.html">All&nbsp;Classes</a></li>
</ul>
<ul class="navListSearch">
<li><label for="search">SEARCH:</label>
<input type="text" id="search" value="search" disabled="disabled">
<input type="reset" id="reset" value="reset" disabled="disabled">
</li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li><a href="#nested.class.summary">Nested</a>&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
</div>
<div class="navPadding">&nbsp;</div>
<script type="text/javascript"><!--
$('.navPadding').css('padding-top', $('.fixedNav').css("height"));
//-->
</script>
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle"><span class="packageLabelInType">Package</span>&nbsp;<a href="package-summary.html">org.gradle.api.file</a></div>
<h2 title="Interface ConfigurableFileTree" class="title">Interface ConfigurableFileTree</h2>
</div>
<div class="contentContainer">
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Superinterfaces:</dt>
<dd><code><a href="../tasks/AntBuilderAware.html" title="interface in org.gradle.api.tasks">AntBuilderAware</a></code>, <code><a href="../Buildable.html" title="interface in org.gradle.api">Buildable</a></code>, <code><a href="DirectoryTree.html" title="interface in org.gradle.api.file">DirectoryTree</a></code>, <code><a href="FileCollection.html" title="interface in org.gradle.api.file">FileCollection</a></code>, <code><a href="FileTree.html" title="interface in org.gradle.api.file">FileTree</a></code>, <code>java.lang.Iterable&lt;java.io.File&gt;</code>, <code><a href="../tasks/util/PatternFilterable.html" title="interface in org.gradle.api.tasks.util">PatternFilterable</a></code></dd>
</dl>
<hr>
<pre>public interface <span class="typeNameLabel">ConfigurableFileTree</span>
extends <a href="FileTree.html" title="interface in org.gradle.api.file">FileTree</a>, <a href="DirectoryTree.html" title="interface in org.gradle.api.file">DirectoryTree</a>, <a href="../tasks/util/PatternFilterable.html" title="interface in org.gradle.api.tasks.util">PatternFilterable</a>, <a href="../Buildable.html" title="interface in org.gradle.api">Buildable</a></pre>
<div class="block"><p>A <a href="FileTree.html" title="interface in org.gradle.api.file"><code>FileTree</code></a> with a single base directory, which can be configured and modified.</p>

 <p>You can obtain a <code>ConfigurableFileTree</code> instance by calling <a href="../Project.html#fileTree-java.util.Map-"><code>Project.fileTree(java.util.Map)</code></a>.</p></div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== NESTED CLASS SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="nested.class.summary">
<!--   -->
</a>
<h3>Nested Class Summary</h3>
<ul class="blockList">
<li class="blockList"><a name="nested.classes.inherited.from.class.org.gradle.api.file.FileCollection">
<!--   -->
</a>
<h3>Nested classes/interfaces inherited from interface&nbsp;org.gradle.api.file.<a href="FileCollection.html" title="interface in org.gradle.api.file">FileCollection</a></h3>
<code><a href="FileCollection.AntType.html" title="enum in org.gradle.api.file">FileCollection.AntType</a></code></li>
</ul>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t3" class="tableTab"><span><a href="javascript:show(4);">Abstract Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colSecond" scope="col">Method</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code><a href="ConfigurableFileTree.html" title="interface in org.gradle.api.file">ConfigurableFileTree</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#builtBy-java.lang.Object...-">builtBy</a></span>&#8203;(java.lang.Object...&nbsp;tasks)</code></th>
<td class="colLast">
<div class="block">Registers some tasks which build the files of this collection.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code><a href="ConfigurableFileTree.html" title="interface in org.gradle.api.file">ConfigurableFileTree</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#from-java.lang.Object-">from</a></span>&#8203;(java.lang.Object&nbsp;dir)</code></th>
<td class="colLast">
<div class="block">Specifies base directory for this file tree using the given path.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>java.util.Set&lt;java.lang.Object&gt;</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getBuiltBy--">getBuiltBy</a></span>()</code></th>
<td class="colLast">
<div class="block">Returns the set of tasks which build the files of this collection.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>java.io.File</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getDir--">getDir</a></span>()</code></th>
<td class="colLast">
<div class="block">Returns the base directory of this file tree.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code><a href="ConfigurableFileTree.html" title="interface in org.gradle.api.file">ConfigurableFileTree</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setBuiltBy-java.lang.Iterable-">setBuiltBy</a></span>&#8203;(java.lang.Iterable&lt;?&gt;&nbsp;tasks)</code></th>
<td class="colLast">
<div class="block">Sets the tasks which build the files of this collection.</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code><a href="ConfigurableFileTree.html" title="interface in org.gradle.api.file">ConfigurableFileTree</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setDir-java.lang.Object-">setDir</a></span>&#8203;(java.lang.Object&nbsp;dir)</code></th>
<td class="colLast">
<div class="block">Specifies base directory for this file tree using the given path.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.org.gradle.api.Buildable">
<!--   -->
</a>
<h3>Methods inherited from interface&nbsp;org.gradle.api.<a href="../Buildable.html" title="interface in org.gradle.api">Buildable</a></h3>
<code><a href="../Buildable.html#getBuildDependencies--">getBuildDependencies</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.org.gradle.api.file.DirectoryTree">
<!--   -->
</a>
<h3>Methods inherited from interface&nbsp;org.gradle.api.file.<a href="DirectoryTree.html" title="interface in org.gradle.api.file">DirectoryTree</a></h3>
<code><a href="DirectoryTree.html#getPatterns--">getPatterns</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.org.gradle.api.file.FileCollection">
<!--   -->
</a>
<h3>Methods inherited from interface&nbsp;org.gradle.api.file.<a href="FileCollection.html" title="interface in org.gradle.api.file">FileCollection</a></h3>
<code><a href="FileCollection.html#addToAntBuilder-java.lang.Object-java.lang.String-">addToAntBuilder</a>, <a href="FileCollection.html#addToAntBuilder-java.lang.Object-java.lang.String-org.gradle.api.file.FileCollection.AntType-">addToAntBuilder</a>, <a href="FileCollection.html#contains-java.io.File-">contains</a>, <a href="FileCollection.html#filter-groovy.lang.Closure-">filter</a>, <a href="FileCollection.html#filter-org.gradle.api.specs.Spec-">filter</a>, <a href="FileCollection.html#getAsPath--">getAsPath</a>, <a href="FileCollection.html#getElements--">getElements</a>, <a href="FileCollection.html#getSingleFile--">getSingleFile</a>, <a href="FileCollection.html#isEmpty--">isEmpty</a>, <a href="FileCollection.html#minus-org.gradle.api.file.FileCollection-">minus</a>, <a href="FileCollection.html#plus-org.gradle.api.file.FileCollection-">plus</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.org.gradle.api.file.FileTree">
<!--   -->
</a>
<h3>Methods inherited from interface&nbsp;org.gradle.api.file.<a href="FileTree.html" title="interface in org.gradle.api.file">FileTree</a></h3>
<code><a href="FileTree.html#getAsFileTree--">getAsFileTree</a>, <a href="FileTree.html#getFiles--">getFiles</a>, <a href="FileTree.html#matching-groovy.lang.Closure-">matching</a>, <a href="FileTree.html#matching-org.gradle.api.Action-">matching</a>, <a href="FileTree.html#matching-org.gradle.api.tasks.util.PatternFilterable-">matching</a>, <a href="FileTree.html#plus-org.gradle.api.file.FileTree-">plus</a>, <a href="FileTree.html#visit-groovy.lang.Closure-">visit</a>, <a href="FileTree.html#visit-org.gradle.api.Action-">visit</a>, <a href="FileTree.html#visit-org.gradle.api.file.FileVisitor-">visit</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Iterable">
<!--   -->
</a>
<h3>Methods inherited from interface&nbsp;java.lang.Iterable</h3>
<code>forEach, iterator, spliterator</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.org.gradle.api.tasks.util.PatternFilterable">
<!--   -->
</a>
<h3>Methods inherited from interface&nbsp;org.gradle.api.tasks.util.<a href="../tasks/util/PatternFilterable.html" title="interface in org.gradle.api.tasks.util">PatternFilterable</a></h3>
<code><a href="../tasks/util/PatternFilterable.html#exclude-groovy.lang.Closure-">exclude</a>, <a href="../tasks/util/PatternFilterable.html#exclude-java.lang.Iterable-">exclude</a>, <a href="../tasks/util/PatternFilterable.html#exclude-java.lang.String...-">exclude</a>, <a href="../tasks/util/PatternFilterable.html#exclude-org.gradle.api.specs.Spec-">exclude</a>, <a href="../tasks/util/PatternFilterable.html#getExcludes--">getExcludes</a>, <a href="../tasks/util/PatternFilterable.html#getIncludes--">getIncludes</a>, <a href="../tasks/util/PatternFilterable.html#include-groovy.lang.Closure-">include</a>, <a href="../tasks/util/PatternFilterable.html#include-java.lang.Iterable-">include</a>, <a href="../tasks/util/PatternFilterable.html#include-java.lang.String...-">include</a>, <a href="../tasks/util/PatternFilterable.html#include-org.gradle.api.specs.Spec-">include</a>, <a href="../tasks/util/PatternFilterable.html#setExcludes-java.lang.Iterable-">setExcludes</a>, <a href="../tasks/util/PatternFilterable.html#setIncludes-java.lang.Iterable-">setIncludes</a></code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="from-java.lang.Object-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>from</h4>
<pre class="methodSignature"><a href="ConfigurableFileTree.html" title="interface in org.gradle.api.file">ConfigurableFileTree</a>&nbsp;from&#8203;(java.lang.Object&nbsp;dir)</pre>
<div class="block">Specifies base directory for this file tree using the given path. The path is evaluated as per <a href="../Project.html#file-java.lang.Object-"><code>Project.file(Object)</code></a>.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>dir</code> - The base directory.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>this</dd>
</dl>
</li>
</ul>
<a name="getDir--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getDir</h4>
<pre class="methodSignature">java.io.File&nbsp;getDir()</pre>
<div class="block">Returns the base directory of this file tree.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="DirectoryTree.html#getDir--">getDir</a></code>&nbsp;in interface&nbsp;<code><a href="DirectoryTree.html" title="interface in org.gradle.api.file">DirectoryTree</a></code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The base directory. Never returns null.</dd>
</dl>
</li>
</ul>
<a name="setDir-java.lang.Object-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setDir</h4>
<pre class="methodSignature"><a href="ConfigurableFileTree.html" title="interface in org.gradle.api.file">ConfigurableFileTree</a>&nbsp;setDir&#8203;(java.lang.Object&nbsp;dir)</pre>
<div class="block">Specifies base directory for this file tree using the given path. The path is evaluated as per <a href="../Project.html#file-java.lang.Object-"><code>Project.file(Object)</code></a>.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>dir</code> - The base directory.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>this</dd>
</dl>
</li>
</ul>
<a name="getBuiltBy--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getBuiltBy</h4>
<pre class="methodSignature">java.util.Set&lt;java.lang.Object&gt;&nbsp;getBuiltBy()</pre>
<div class="block">Returns the set of tasks which build the files of this collection.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The set. Returns an empty set when there are no such tasks.</dd>
</dl>
</li>
</ul>
<a name="setBuiltBy-java.lang.Iterable-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setBuiltBy</h4>
<pre class="methodSignature"><a href="ConfigurableFileTree.html" title="interface in org.gradle.api.file">ConfigurableFileTree</a>&nbsp;setBuiltBy&#8203;(java.lang.Iterable&lt;?&gt;&nbsp;tasks)</pre>
<div class="block">Sets the tasks which build the files of this collection.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>tasks</code> - The tasks. These are evaluated as per <a href="../Task.html#dependsOn-java.lang.Object...-"><code>Task.dependsOn(Object...)</code></a>.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>this</dd>
</dl>
</li>
</ul>
<a name="builtBy-java.lang.Object...-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>builtBy</h4>
<pre class="methodSignature"><a href="ConfigurableFileTree.html" title="interface in org.gradle.api.file">ConfigurableFileTree</a>&nbsp;builtBy&#8203;(java.lang.Object...&nbsp;tasks)</pre>
<div class="block">Registers some tasks which build the files of this collection.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>tasks</code> - The tasks. These are evaluated as per <a href="../Task.html#dependsOn-java.lang.Object...-"><code>Task.dependsOn(Object...)</code></a>.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>this</dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li><a href="#nested.class.summary">Nested</a>&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
