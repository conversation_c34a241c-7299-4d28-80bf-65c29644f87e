<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc -->
<title>org.gradle.api.plugins Class Hierarchy (Gradle API 8.4)</title>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<link rel="stylesheet" type="text/css" href="../../../../javadoc.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../jquery/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
<script type="text/javascript" src="../../../../jquery/jszip/dist/jszip.min.js"></script>
<script type="text/javascript" src="../../../../jquery/jszip-utils/dist/jszip-utils.min.js"></script>
<!--[if IE]>
<script type="text/javascript" src="../../../../jquery/jszip-utils/dist/jszip-utils-ie.min.js"></script>
<![endif]-->
<script type="text/javascript" src="../../../../jquery/jquery-3.6.0.min.js"></script>
<script type="text/javascript" src="../../../../jquery/jquery-ui.min.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="org.gradle.api.plugins Class Hierarchy (Gradle API 8.4)";
        }
    }
    catch(err) {
    }
//-->
var pathtoroot = "../../../../";
var useModuleDirectories = false;
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="fixedNav">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li>Class</li>
<li class="navBarCell1Rev">Tree</li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses.html">All&nbsp;Classes</a></li>
</ul>
<ul class="navListSearch">
<li><label for="search">SEARCH:</label>
<input type="text" id="search" value="search" disabled="disabled">
<input type="reset" id="reset" value="reset" disabled="disabled">
</li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
</div>
<div class="navPadding">&nbsp;</div>
<script type="text/javascript"><!--
$('.navPadding').css('padding-top', $('.fixedNav').css("height"));
//-->
</script>
<div class="header">
<h1 class="title">Hierarchy For Package org.gradle.api.plugins</h1>
<span class="packageHierarchyLabel">Package Hierarchies:</span>
<ul class="horizontal">
<li><a href="../../../../overview-tree.html">All Packages</a></li>
</ul>
</div>
<div class="contentContainer">
<h2 title="Class Hierarchy">Class Hierarchy</h2>
<ul>
<li class="circle">java.lang.Object
<ul>
<li class="circle">org.gradle.api.plugins.<a href="ApplicationPlugin.html" title="class in org.gradle.api.plugins"><span class="typeNameLink">ApplicationPlugin</span></a> (implements org.gradle.api.<a href="../Plugin.html" title="interface in org.gradle.api">Plugin</a>&lt;T&gt;)</li>
<li class="circle">org.gradle.api.plugins.<a href="ApplicationPluginConvention.html" title="class in org.gradle.api.plugins"><span class="typeNameLink">ApplicationPluginConvention</span></a></li>
<li class="circle">org.gradle.api.plugins.<a href="BasePlugin.html" title="class in org.gradle.api.plugins"><span class="typeNameLink">BasePlugin</span></a> (implements org.gradle.api.<a href="../Plugin.html" title="interface in org.gradle.api">Plugin</a>&lt;T&gt;)</li>
<li class="circle">org.gradle.api.plugins.<a href="BasePluginConvention.html" title="class in org.gradle.api.plugins"><span class="typeNameLink">BasePluginConvention</span></a></li>
<li class="circle">org.gradle.api.plugins.<a href="GroovyBasePlugin.html" title="class in org.gradle.api.plugins"><span class="typeNameLink">GroovyBasePlugin</span></a> (implements org.gradle.api.<a href="../Plugin.html" title="interface in org.gradle.api">Plugin</a>&lt;T&gt;)</li>
<li class="circle">org.gradle.api.plugins.<a href="GroovyPlugin.html" title="class in org.gradle.api.plugins"><span class="typeNameLink">GroovyPlugin</span></a> (implements org.gradle.api.<a href="../Plugin.html" title="interface in org.gradle.api">Plugin</a>&lt;T&gt;)</li>
<li class="circle">org.gradle.api.plugins.<a href="HelpTasksPlugin.html" title="class in org.gradle.api.plugins"><span class="typeNameLink">HelpTasksPlugin</span></a> (implements org.gradle.api.<a href="../Plugin.html" title="interface in org.gradle.api">Plugin</a>&lt;T&gt;)</li>
<li class="circle">org.gradle.api.plugins.<a href="JavaBasePlugin.html" title="class in org.gradle.api.plugins"><span class="typeNameLink">JavaBasePlugin</span></a> (implements org.gradle.api.<a href="../Plugin.html" title="interface in org.gradle.api">Plugin</a>&lt;T&gt;)</li>
<li class="circle">org.gradle.api.plugins.<a href="JavaLibraryDistributionPlugin.html" title="class in org.gradle.api.plugins"><span class="typeNameLink">JavaLibraryDistributionPlugin</span></a> (implements org.gradle.api.<a href="../Plugin.html" title="interface in org.gradle.api">Plugin</a>&lt;T&gt;)</li>
<li class="circle">org.gradle.api.plugins.<a href="JavaLibraryPlugin.html" title="class in org.gradle.api.plugins"><span class="typeNameLink">JavaLibraryPlugin</span></a> (implements org.gradle.api.<a href="../Plugin.html" title="interface in org.gradle.api">Plugin</a>&lt;T&gt;)</li>
<li class="circle">org.gradle.api.plugins.<a href="JavaPlatformPlugin.html" title="class in org.gradle.api.plugins"><span class="typeNameLink">JavaPlatformPlugin</span></a> (implements org.gradle.api.<a href="../Plugin.html" title="interface in org.gradle.api">Plugin</a>&lt;T&gt;)</li>
<li class="circle">org.gradle.api.plugins.<a href="JavaPlugin.html" title="class in org.gradle.api.plugins"><span class="typeNameLink">JavaPlugin</span></a> (implements org.gradle.api.<a href="../Plugin.html" title="interface in org.gradle.api">Plugin</a>&lt;T&gt;)</li>
<li class="circle">org.gradle.api.plugins.<a href="JavaPluginConvention.html" title="class in org.gradle.api.plugins"><span class="typeNameLink">JavaPluginConvention</span></a></li>
<li class="circle">org.gradle.api.plugins.<a href="JavaTestFixturesPlugin.html" title="class in org.gradle.api.plugins"><span class="typeNameLink">JavaTestFixturesPlugin</span></a> (implements org.gradle.api.<a href="../Plugin.html" title="interface in org.gradle.api">Plugin</a>&lt;T&gt;)</li>
<li class="circle">org.gradle.api.plugins.<a href="JvmEcosystemPlugin.html" title="class in org.gradle.api.plugins"><span class="typeNameLink">JvmEcosystemPlugin</span></a> (implements org.gradle.api.<a href="../Plugin.html" title="interface in org.gradle.api">Plugin</a>&lt;T&gt;)</li>
<li class="circle">org.gradle.api.plugins.<a href="JvmTestSuitePlugin.html" title="class in org.gradle.api.plugins"><span class="typeNameLink">JvmTestSuitePlugin</span></a> (implements org.gradle.api.<a href="../Plugin.html" title="interface in org.gradle.api">Plugin</a>&lt;T&gt;)</li>
<li class="circle">org.gradle.api.plugins.<a href="JvmToolchainManagementPlugin.html" title="class in org.gradle.api.plugins"><span class="typeNameLink">JvmToolchainManagementPlugin</span></a> (implements org.gradle.api.<a href="../Plugin.html" title="interface in org.gradle.api">Plugin</a>&lt;T&gt;)</li>
<li class="circle">org.gradle.api.plugins.<a href="JvmToolchainsPlugin.html" title="class in org.gradle.api.plugins"><span class="typeNameLink">JvmToolchainsPlugin</span></a> (implements org.gradle.api.<a href="../Plugin.html" title="interface in org.gradle.api">Plugin</a>&lt;T&gt;)</li>
<li class="circle">org.gradle.api.plugins.<a href="ProjectReportsPlugin.html" title="class in org.gradle.api.plugins"><span class="typeNameLink">ProjectReportsPlugin</span></a> (implements org.gradle.api.<a href="../Plugin.html" title="interface in org.gradle.api">Plugin</a>&lt;T&gt;)</li>
<li class="circle">org.gradle.api.plugins.<a href="ProjectReportsPluginConvention.html" title="class in org.gradle.api.plugins"><span class="typeNameLink">ProjectReportsPluginConvention</span></a></li>
<li class="circle">org.gradle.api.plugins.<a href="ReportingBasePlugin.html" title="class in org.gradle.api.plugins"><span class="typeNameLink">ReportingBasePlugin</span></a> (implements org.gradle.api.<a href="../Plugin.html" title="interface in org.gradle.api">Plugin</a>&lt;T&gt;)</li>
<li class="circle">org.gradle.api.plugins.<a href="TestReportAggregationPlugin.html" title="class in org.gradle.api.plugins"><span class="typeNameLink">TestReportAggregationPlugin</span></a> (implements org.gradle.api.<a href="../Plugin.html" title="interface in org.gradle.api">Plugin</a>&lt;T&gt;)</li>
<li class="circle">java.lang.Throwable (implements java.io.Serializable)
<ul>
<li class="circle">java.lang.Exception
<ul>
<li class="circle">java.lang.RuntimeException
<ul>
<li class="circle">org.gradle.api.<a href="../GradleException.html" title="class in org.gradle.api"><span class="typeNameLink">GradleException</span></a>
<ul>
<li class="circle">org.gradle.api.plugins.<a href="InvalidPluginException.html" title="class in org.gradle.api.plugins"><span class="typeNameLink">InvalidPluginException</span></a></li>
<li class="circle">org.gradle.api.<a href="../InvalidUserDataException.html" title="class in org.gradle.api"><span class="typeNameLink">InvalidUserDataException</span></a>
<ul>
<li class="circle">org.gradle.api.plugins.<a href="ExtraPropertiesExtension.UnknownPropertyException.html" title="class in org.gradle.api.plugins"><span class="typeNameLink">ExtraPropertiesExtension.UnknownPropertyException</span></a></li>
<li class="circle">org.gradle.api.plugins.<a href="UnknownPluginException.html" title="class in org.gradle.api.plugins"><span class="typeNameLink">UnknownPluginException</span></a></li>
</ul>
</li>
<li class="circle">org.gradle.api.plugins.<a href="PluginInstantiationException.html" title="class in org.gradle.api.plugins"><span class="typeNameLink">PluginInstantiationException</span></a></li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
<li class="circle">org.gradle.api.plugins.<a href="WarPlugin.html" title="class in org.gradle.api.plugins"><span class="typeNameLink">WarPlugin</span></a> (implements org.gradle.api.<a href="../Plugin.html" title="interface in org.gradle.api">Plugin</a>&lt;T&gt;)</li>
<li class="circle">org.gradle.api.plugins.<a href="WarPluginConvention.html" title="class in org.gradle.api.plugins"><span class="typeNameLink">WarPluginConvention</span></a></li>
</ul>
</li>
</ul>
<h2 title="Interface Hierarchy">Interface Hierarchy</h2>
<ul>
<li class="circle">org.gradle.api.plugins.<a href="AppliedPlugin.html" title="interface in org.gradle.api.plugins"><span class="typeNameLink">AppliedPlugin</span></a></li>
<li class="circle">org.gradle.api.plugins.<a href="BasePluginExtension.html" title="interface in org.gradle.api.plugins"><span class="typeNameLink">BasePluginExtension</span></a></li>
<li class="circle">org.gradle.api.plugins.<a href="ExtensionAware.html" title="interface in org.gradle.api.plugins"><span class="typeNameLink">ExtensionAware</span></a></li>
<li class="circle">org.gradle.api.plugins.<a href="ExtensionContainer.html" title="interface in org.gradle.api.plugins"><span class="typeNameLink">ExtensionContainer</span></a>
<ul>
<li class="circle">org.gradle.api.plugins.<a href="Convention.html" title="interface in org.gradle.api.plugins"><span class="typeNameLink">Convention</span></a></li>
</ul>
</li>
<li class="circle">org.gradle.api.plugins.<a href="ExtraPropertiesExtension.html" title="interface in org.gradle.api.plugins"><span class="typeNameLink">ExtraPropertiesExtension</span></a></li>
<li class="circle">org.gradle.api.plugins.<a href="FeatureSpec.html" title="interface in org.gradle.api.plugins"><span class="typeNameLink">FeatureSpec</span></a></li>
<li class="circle">java.lang.Iterable&lt;T&gt;
<ul>
<li class="circle">java.util.Collection&lt;E&gt;
<ul>
<li class="circle">org.gradle.api.<a href="../DomainObjectCollection.html" title="interface in org.gradle.api"><span class="typeNameLink">DomainObjectCollection</span></a>&lt;T&gt;
<ul>
<li class="circle">org.gradle.api.<a href="../DomainObjectSet.html" title="interface in org.gradle.api"><span class="typeNameLink">DomainObjectSet</span></a>&lt;T&gt; (also extends java.util.Set&lt;E&gt;)
<ul>
<li class="circle">org.gradle.api.plugins.<a href="PluginCollection.html" title="interface in org.gradle.api.plugins"><span class="typeNameLink">PluginCollection</span></a>&lt;T&gt;
<ul>
<li class="circle">org.gradle.api.plugins.<a href="PluginContainer.html" title="interface in org.gradle.api.plugins"><span class="typeNameLink">PluginContainer</span></a></li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
<li class="circle">java.util.Set&lt;E&gt;
<ul>
<li class="circle">org.gradle.api.<a href="../DomainObjectSet.html" title="interface in org.gradle.api"><span class="typeNameLink">DomainObjectSet</span></a>&lt;T&gt; (also extends org.gradle.api.<a href="../DomainObjectCollection.html" title="interface in org.gradle.api">DomainObjectCollection</a>&lt;T&gt;)
<ul>
<li class="circle">org.gradle.api.plugins.<a href="PluginCollection.html" title="interface in org.gradle.api.plugins"><span class="typeNameLink">PluginCollection</span></a>&lt;T&gt;
<ul>
<li class="circle">org.gradle.api.plugins.<a href="PluginContainer.html" title="interface in org.gradle.api.plugins"><span class="typeNameLink">PluginContainer</span></a></li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
<li class="circle">org.gradle.api.plugins.<a href="ExtensionsSchema.html" title="interface in org.gradle.api.plugins"><span class="typeNameLink">ExtensionsSchema</span></a> (also extends org.gradle.api.<a href="../NamedDomainObjectCollectionSchema.html" title="interface in org.gradle.api">NamedDomainObjectCollectionSchema</a>)</li>
</ul>
</li>
<li class="circle">org.gradle.api.plugins.<a href="JavaApplication.html" title="interface in org.gradle.api.plugins"><span class="typeNameLink">JavaApplication</span></a></li>
<li class="circle">org.gradle.api.plugins.<a href="JavaPlatformExtension.html" title="interface in org.gradle.api.plugins"><span class="typeNameLink">JavaPlatformExtension</span></a></li>
<li class="circle">org.gradle.api.plugins.<a href="JavaPluginExtension.html" title="interface in org.gradle.api.plugins"><span class="typeNameLink">JavaPluginExtension</span></a></li>
<li class="circle">org.gradle.api.plugins.<a href="JavaResolutionConsistency.html" title="interface in org.gradle.api.plugins"><span class="typeNameLink">JavaResolutionConsistency</span></a></li>
<li class="circle">org.gradle.api.<a href="../NamedDomainObjectCollectionSchema.html" title="interface in org.gradle.api"><span class="typeNameLink">NamedDomainObjectCollectionSchema</span></a>
<ul>
<li class="circle">org.gradle.api.plugins.<a href="ExtensionsSchema.html" title="interface in org.gradle.api.plugins"><span class="typeNameLink">ExtensionsSchema</span></a> (also extends java.lang.Iterable&lt;T&gt;)</li>
</ul>
</li>
<li class="circle">org.gradle.api.<a href="../NamedDomainObjectCollectionSchema.NamedDomainObjectSchema.html" title="interface in org.gradle.api"><span class="typeNameLink">NamedDomainObjectCollectionSchema.NamedDomainObjectSchema</span></a>
<ul>
<li class="circle">org.gradle.api.plugins.<a href="ExtensionsSchema.ExtensionSchema.html" title="interface in org.gradle.api.plugins"><span class="typeNameLink">ExtensionsSchema.ExtensionSchema</span></a></li>
</ul>
</li>
<li class="circle">org.gradle.api.plugins.<a href="ObjectConfigurationAction.html" title="interface in org.gradle.api.plugins"><span class="typeNameLink">ObjectConfigurationAction</span></a></li>
<li class="circle">org.gradle.api.plugins.<a href="PluginAware.html" title="interface in org.gradle.api.plugins"><span class="typeNameLink">PluginAware</span></a></li>
<li class="circle">org.gradle.api.plugins.<a href="PluginManager.html" title="interface in org.gradle.api.plugins"><span class="typeNameLink">PluginManager</span></a></li>
</ul>
</div>
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li>Class</li>
<li class="navBarCell1Rev">Tree</li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
