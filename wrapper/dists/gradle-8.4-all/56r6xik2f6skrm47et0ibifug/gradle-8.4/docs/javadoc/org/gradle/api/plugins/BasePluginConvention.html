<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc -->
<title>BasePluginConvention (Gradle API 8.4)</title>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<link rel="stylesheet" type="text/css" href="../../../../javadoc.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../jquery/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
<script type="text/javascript" src="../../../../jquery/jszip/dist/jszip.min.js"></script>
<script type="text/javascript" src="../../../../jquery/jszip-utils/dist/jszip-utils.min.js"></script>
<!--[if IE]>
<script type="text/javascript" src="../../../../jquery/jszip-utils/dist/jszip-utils-ie.min.js"></script>
<![endif]-->
<script type="text/javascript" src="../../../../jquery/jquery-3.6.0.min.js"></script>
<script type="text/javascript" src="../../../../jquery/jquery-ui.min.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="BasePluginConvention (Gradle API 8.4)";
        }
    }
    catch(err) {
    }
//-->
var data = {"i0":38,"i1":38,"i2":38,"i3":38,"i4":38,"i5":38,"i6":38,"i7":38};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],4:["t3","Abstract Methods"],32:["t6","Deprecated Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
var pathtoroot = "../../../../";
var useModuleDirectories = false;
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="fixedNav">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses.html">All&nbsp;Classes</a></li>
</ul>
<ul class="navListSearch">
<li><label for="search">SEARCH:</label>
<input type="text" id="search" value="search" disabled="disabled">
<input type="reset" id="reset" value="reset" disabled="disabled">
</li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
</div>
<div class="navPadding">&nbsp;</div>
<script type="text/javascript"><!--
$('.navPadding').css('padding-top', $('.fixedNav').css("height"));
//-->
</script>
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle"><span class="packageLabelInType">Package</span>&nbsp;<a href="package-summary.html">org.gradle.api.plugins</a></div>
<h2 title="Class BasePluginConvention" class="title">Class BasePluginConvention</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>org.gradle.api.plugins.BasePluginConvention</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<hr>
<pre>@Deprecated
public abstract class <span class="typeNameLabel">BasePluginConvention</span>
extends java.lang.Object</pre>
<div class="deprecationBlock"><span class="deprecatedLabel">Deprecated.</span>
<div class="deprecationComment">Use <a href="BasePluginExtension.html" title="interface in org.gradle.api.plugins"><code>BasePluginExtension</code></a> instead. This class is scheduled for removal in Gradle 9.0.</div>
</div>
<div class="block"><p>A <a href="Convention.html" title="interface in org.gradle.api.plugins"><code>Convention</code></a> used for the BasePlugin.</p></div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="memberSummary" summary="Constructor Summary table, listing constructors, and an explanation">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Constructor</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tr class="altColor">
<th class="colConstructorName" scope="row"><code><span class="memberNameLink"><a href="#BasePluginConvention--">BasePluginConvention</a></span>()</code></th>
<td class="colLast">
<div class="block"><span class="deprecatedLabel">Deprecated.</span></div>
&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t3" class="tableTab"><span><a href="javascript:show(4);">Abstract Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t6" class="tableTab"><span><a href="javascript:show(32);">Deprecated Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colSecond" scope="col">Method</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>abstract java.lang.String</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getArchivesBaseName--">getArchivesBaseName</a></span>()</code></th>
<td class="colLast">
<div class="block"><span class="deprecatedLabel">Deprecated.</span></div>
<div class="block">The base name to use for archive files.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>abstract <a href="../file/DirectoryProperty.html" title="interface in org.gradle.api.file">DirectoryProperty</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getDistsDirectory--">getDistsDirectory</a></span>()</code></th>
<td class="colLast">
<div class="block"><span class="deprecatedLabel">Deprecated.</span></div>
<div class="block">Returns the directory to generate TAR and ZIP archives into.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>abstract java.lang.String</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getDistsDirName--">getDistsDirName</a></span>()</code></th>
<td class="colLast">
<div class="block"><span class="deprecatedLabel">Deprecated.</span></div>
<div class="block">The name for the distributions directory.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>abstract <a href="../file/DirectoryProperty.html" title="interface in org.gradle.api.file">DirectoryProperty</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getLibsDirectory--">getLibsDirectory</a></span>()</code></th>
<td class="colLast">
<div class="block"><span class="deprecatedLabel">Deprecated.</span></div>
<div class="block">Returns the directory to generate JAR and WAR archives into.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>abstract java.lang.String</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getLibsDirName--">getLibsDirName</a></span>()</code></th>
<td class="colLast">
<div class="block"><span class="deprecatedLabel">Deprecated.</span></div>
<div class="block">The name for the libs directory.</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>abstract void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setArchivesBaseName-java.lang.String-">setArchivesBaseName</a></span>&#8203;(java.lang.String&nbsp;archivesBaseName)</code></th>
<td class="colLast">
<div class="block"><span class="deprecatedLabel">Deprecated.</span></div>
&nbsp;</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>abstract void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setDistsDirName-java.lang.String-">setDistsDirName</a></span>&#8203;(java.lang.String&nbsp;distsDirName)</code></th>
<td class="colLast">
<div class="block"><span class="deprecatedLabel">Deprecated.</span></div>
&nbsp;</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>abstract void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setLibsDirName-java.lang.String-">setLibsDirName</a></span>&#8203;(java.lang.String&nbsp;libsDirName)</code></th>
<td class="colLast">
<div class="block"><span class="deprecatedLabel">Deprecated.</span></div>
&nbsp;</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>clone, equals, finalize, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a name="BasePluginConvention--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>BasePluginConvention</h4>
<pre>public&nbsp;BasePluginConvention()</pre>
<div class="deprecationBlock"><span class="deprecatedLabel">Deprecated.</span></div>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="getDistsDirectory--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getDistsDirectory</h4>
<pre class="methodSignature">public abstract&nbsp;<a href="../file/DirectoryProperty.html" title="interface in org.gradle.api.file">DirectoryProperty</a>&nbsp;getDistsDirectory()</pre>
<div class="deprecationBlock"><span class="deprecatedLabel">Deprecated.</span></div>
<div class="block">Returns the directory to generate TAR and ZIP archives into.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The directory. Never returns null.</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>6.0</dd>
</dl>
</li>
</ul>
<a name="getLibsDirectory--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getLibsDirectory</h4>
<pre class="methodSignature">public abstract&nbsp;<a href="../file/DirectoryProperty.html" title="interface in org.gradle.api.file">DirectoryProperty</a>&nbsp;getLibsDirectory()</pre>
<div class="deprecationBlock"><span class="deprecatedLabel">Deprecated.</span></div>
<div class="block">Returns the directory to generate JAR and WAR archives into.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The directory. Never returns null.</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>6.0</dd>
</dl>
</li>
</ul>
<a name="getDistsDirName--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getDistsDirName</h4>
<pre class="methodSignature">public abstract&nbsp;java.lang.String&nbsp;getDistsDirName()</pre>
<div class="deprecationBlock"><span class="deprecatedLabel">Deprecated.</span></div>
<div class="block">The name for the distributions directory. This in interpreted relative to the project' build directory.</div>
</li>
</ul>
<a name="setDistsDirName-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setDistsDirName</h4>
<pre class="methodSignature">public abstract&nbsp;void&nbsp;setDistsDirName&#8203;(java.lang.String&nbsp;distsDirName)</pre>
<div class="deprecationBlock"><span class="deprecatedLabel">Deprecated.</span></div>
</li>
</ul>
<a name="getLibsDirName--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getLibsDirName</h4>
<pre class="methodSignature">public abstract&nbsp;java.lang.String&nbsp;getLibsDirName()</pre>
<div class="deprecationBlock"><span class="deprecatedLabel">Deprecated.</span></div>
<div class="block">The name for the libs directory. This in interpreted relative to the project' build directory.</div>
</li>
</ul>
<a name="setLibsDirName-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setLibsDirName</h4>
<pre class="methodSignature">public abstract&nbsp;void&nbsp;setLibsDirName&#8203;(java.lang.String&nbsp;libsDirName)</pre>
<div class="deprecationBlock"><span class="deprecatedLabel">Deprecated.</span></div>
</li>
</ul>
<a name="getArchivesBaseName--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getArchivesBaseName</h4>
<pre class="methodSignature">public abstract&nbsp;java.lang.String&nbsp;getArchivesBaseName()</pre>
<div class="deprecationBlock"><span class="deprecatedLabel">Deprecated.</span></div>
<div class="block">The base name to use for archive files.</div>
</li>
</ul>
<a name="setArchivesBaseName-java.lang.String-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>setArchivesBaseName</h4>
<pre class="methodSignature">public abstract&nbsp;void&nbsp;setArchivesBaseName&#8203;(java.lang.String&nbsp;archivesBaseName)</pre>
<div class="deprecationBlock"><span class="deprecatedLabel">Deprecated.</span></div>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
