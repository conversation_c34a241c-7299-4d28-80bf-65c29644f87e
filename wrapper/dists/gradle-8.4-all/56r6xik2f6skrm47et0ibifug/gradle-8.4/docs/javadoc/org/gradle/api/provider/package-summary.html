<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc -->
<title>org.gradle.api.provider (Gradle API 8.4)</title>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<link rel="stylesheet" type="text/css" href="../../../../javadoc.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../jquery/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
<script type="text/javascript" src="../../../../jquery/jszip/dist/jszip.min.js"></script>
<script type="text/javascript" src="../../../../jquery/jszip-utils/dist/jszip-utils.min.js"></script>
<!--[if IE]>
<script type="text/javascript" src="../../../../jquery/jszip-utils/dist/jszip-utils-ie.min.js"></script>
<![endif]-->
<script type="text/javascript" src="../../../../jquery/jquery-3.6.0.min.js"></script>
<script type="text/javascript" src="../../../../jquery/jquery-ui.min.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="org.gradle.api.provider (Gradle API 8.4)";
        }
    }
    catch(err) {
    }
//-->
var pathtoroot = "../../../../";
var useModuleDirectories = false;
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="fixedNav">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../index.html">Overview</a></li>
<li class="navBarCell1Rev">Package</li>
<li>Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses.html">All&nbsp;Classes</a></li>
</ul>
<ul class="navListSearch">
<li><label for="search">SEARCH:</label>
<input type="text" id="search" value="search" disabled="disabled">
<input type="reset" id="reset" value="reset" disabled="disabled">
</li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
</div>
<div class="navPadding">&nbsp;</div>
<script type="text/javascript"><!--
$('.navPadding').css('padding-top', $('.fixedNav').css("height"));
//-->
</script>
<div class="header">
<p><a href="../NonNullApi.html" title="annotation in org.gradle.api">@NonNullApi</a>
</p>
<h1 title="Package" class="title">Package&nbsp;org.gradle.api.provider</h1>
</div>
<div class="contentContainer"><a name="package.description">
<!--   -->
</a>
<div class="block">The interfaces for value providers.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>4.0</dd>
</dl>
<ul class="blockList">
<li class="blockList">
<table class="typeSummary" summary="Interface Summary table, listing interfaces, and an explanation">
<caption><span>Interface Summary</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Interface</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tbody>
<tr class="altColor">
<th class="colFirst" scope="row"><a href="HasConfigurableValue.html" title="interface in org.gradle.api.provider">HasConfigurableValue</a></th>
<td class="colLast">
<div class="block">Represents an object that holds a value that is configurable, meaning that the value or some source for the value, such as a <a href="Provider.html" title="interface in org.gradle.api.provider"><code>Provider</code></a>,
 can be specified directly on the object.</div>
</td>
</tr>
<tr class="rowColor">
<th class="colFirst" scope="row"><a href="HasMultipleValues.html" title="interface in org.gradle.api.provider">HasMultipleValues</a>&lt;T&gt;</th>
<td class="colLast">
<div class="block">Represents a property whose value can be set using multiple elements of type <a href="HasMultipleValues.html" title="interface in org.gradle.api.provider"><code>HasMultipleValues</code></a>, such as a collection property.</div>
</td>
</tr>
<tr class="altColor">
<th class="colFirst" scope="row"><a href="ListProperty.html" title="interface in org.gradle.api.provider">ListProperty</a>&lt;T&gt;</th>
<td class="colLast">
<div class="block">Represents a property whose type is a <code>List</code> of elements of type <a href="ListProperty.html" title="interface in org.gradle.api.provider"><code>ListProperty</code></a>.</div>
</td>
</tr>
<tr class="rowColor">
<th class="colFirst" scope="row"><a href="MapProperty.html" title="interface in org.gradle.api.provider">MapProperty</a>&lt;K,&#8203;V&gt;</th>
<td class="colLast">
<div class="block">Represents a property whose type is a <code>Map</code> of keys of type <a href="MapProperty.html" title="interface in org.gradle.api.provider"><code>MapProperty</code></a> and values of type <a href="MapProperty.html" title="interface in org.gradle.api.provider"><code>MapProperty</code></a>.</div>
</td>
</tr>
<tr class="altColor">
<th class="colFirst" scope="row"><a href="Property.html" title="interface in org.gradle.api.provider">Property</a>&lt;T&gt;</th>
<td class="colLast">
<div class="block">A container object that represents a configurable value of a specific type.</div>
</td>
</tr>
<tr class="rowColor">
<th class="colFirst" scope="row"><a href="Provider.html" title="interface in org.gradle.api.provider">Provider</a>&lt;T&gt;</th>
<td class="colLast">
<div class="block">A container object that provides a value of a specific type.</div>
</td>
</tr>
<tr class="altColor">
<th class="colFirst" scope="row"><a href="ProviderConvertible.html" title="interface in org.gradle.api.provider">ProviderConvertible</a>&lt;T&gt;</th>
<td class="colLast">
<div class="block">An object that can be converted to a <a href="Provider.html" title="interface in org.gradle.api.provider"><code>Provider</code></a>.</div>
</td>
</tr>
<tr class="rowColor">
<th class="colFirst" scope="row"><a href="ProviderFactory.html" title="interface in org.gradle.api.provider">ProviderFactory</a></th>
<td class="colLast">
<div class="block">A factory for creating instances of <a href="Provider.html" title="interface in org.gradle.api.provider"><code>Provider</code></a>.</div>
</td>
</tr>
<tr class="altColor">
<th class="colFirst" scope="row"><a href="SetProperty.html" title="interface in org.gradle.api.provider">SetProperty</a>&lt;T&gt;</th>
<td class="colLast">
<div class="block">Represents a property whose type is a <code>Set</code> of elements of type <a href="SetProperty.html" title="interface in org.gradle.api.provider"><code>SetProperty</code></a>.</div>
</td>
</tr>
<tr class="rowColor">
<th class="colFirst" scope="row"><a href="ValueSource.html" title="interface in org.gradle.api.provider">ValueSource</a>&lt;T,&#8203;P extends <a href="ValueSourceParameters.html" title="interface in org.gradle.api.provider">ValueSourceParameters</a>&gt;</th>
<td class="colLast">
<div class="block">Represents an external source of information used by a Gradle build.</div>
</td>
</tr>
<tr class="altColor">
<th class="colFirst" scope="row"><a href="ValueSourceParameters.html" title="interface in org.gradle.api.provider">ValueSourceParameters</a></th>
<td class="colLast">
<div class="block">Marker interface for parameter objects to <a href="ValueSource.html" title="interface in org.gradle.api.provider"><code>ValueSource</code></a>s.</div>
</td>
</tr>
<tr class="rowColor">
<th class="colFirst" scope="row"><a href="ValueSourceSpec.html" title="interface in org.gradle.api.provider">ValueSourceSpec</a>&lt;P extends <a href="ValueSourceParameters.html" title="interface in org.gradle.api.provider">ValueSourceParameters</a>&gt;</th>
<td class="colLast">
<div class="block">Base configuration for value source definitions.</div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList">
<table class="typeSummary" summary="Class Summary table, listing classes, and an explanation">
<caption><span>Class Summary</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Class</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tbody>
<tr class="altColor">
<th class="colFirst" scope="row"><a href="ValueSourceParameters.None.html" title="class in org.gradle.api.provider">ValueSourceParameters.None</a></th>
<td class="colLast">
<div class="block">Used for sources without parameters.</div>
</td>
</tr>
</tbody>
</table>
</li>
</ul>
</div>
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../index.html">Overview</a></li>
<li class="navBarCell1Rev">Package</li>
<li>Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
