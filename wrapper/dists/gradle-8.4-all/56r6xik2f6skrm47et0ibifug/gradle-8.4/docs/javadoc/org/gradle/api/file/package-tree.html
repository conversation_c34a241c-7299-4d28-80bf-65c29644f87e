<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc -->
<title>org.gradle.api.file Class Hierarchy (Gradle API 8.4)</title>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<link rel="stylesheet" type="text/css" href="../../../../javadoc.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../jquery/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
<script type="text/javascript" src="../../../../jquery/jszip/dist/jszip.min.js"></script>
<script type="text/javascript" src="../../../../jquery/jszip-utils/dist/jszip-utils.min.js"></script>
<!--[if IE]>
<script type="text/javascript" src="../../../../jquery/jszip-utils/dist/jszip-utils-ie.min.js"></script>
<![endif]-->
<script type="text/javascript" src="../../../../jquery/jquery-3.6.0.min.js"></script>
<script type="text/javascript" src="../../../../jquery/jquery-ui.min.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="org.gradle.api.file Class Hierarchy (Gradle API 8.4)";
        }
    }
    catch(err) {
    }
//-->
var pathtoroot = "../../../../";
var useModuleDirectories = false;
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="fixedNav">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li>Class</li>
<li class="navBarCell1Rev">Tree</li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses.html">All&nbsp;Classes</a></li>
</ul>
<ul class="navListSearch">
<li><label for="search">SEARCH:</label>
<input type="text" id="search" value="search" disabled="disabled">
<input type="reset" id="reset" value="reset" disabled="disabled">
</li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
</div>
<div class="navPadding">&nbsp;</div>
<script type="text/javascript"><!--
$('.navPadding').css('padding-top', $('.fixedNav').css("height"));
//-->
</script>
<div class="header">
<h1 class="title">Hierarchy For Package org.gradle.api.file</h1>
<span class="packageHierarchyLabel">Package Hierarchies:</span>
<ul class="horizontal">
<li><a href="../../../../overview-tree.html">All Packages</a></li>
</ul>
</div>
<div class="contentContainer">
<h2 title="Class Hierarchy">Class Hierarchy</h2>
<ul>
<li class="circle">java.lang.Object
<ul>
<li class="circle">org.gradle.api.file.<a href="EmptyFileVisitor.html" title="class in org.gradle.api.file"><span class="typeNameLink">EmptyFileVisitor</span></a> (implements org.gradle.api.file.<a href="FileVisitor.html" title="interface in org.gradle.api.file">FileVisitor</a>)</li>
<li class="circle">org.gradle.api.file.<a href="RelativePath.html" title="class in org.gradle.api.file"><span class="typeNameLink">RelativePath</span></a> (implements java.lang.CharSequence, java.lang.Comparable&lt;T&gt;, java.io.Serializable)</li>
<li class="circle">java.lang.Throwable (implements java.io.Serializable)
<ul>
<li class="circle">java.lang.Exception
<ul>
<li class="circle">java.lang.RuntimeException
<ul>
<li class="circle">org.gradle.api.<a href="../GradleException.html" title="class in org.gradle.api"><span class="typeNameLink">GradleException</span></a>
<ul>
<li class="circle">org.gradle.api.file.<a href="DuplicateFileCopyingException.html" title="class in org.gradle.api.file"><span class="typeNameLink">DuplicateFileCopyingException</span></a></li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
<h2 title="Interface Hierarchy">Interface Hierarchy</h2>
<ul>
<li class="circle">org.gradle.api.tasks.<a href="../tasks/AntBuilderAware.html" title="interface in org.gradle.api.tasks"><span class="typeNameLink">AntBuilderAware</span></a>
<ul>
<li class="circle">org.gradle.api.file.<a href="FileCollection.html" title="interface in org.gradle.api.file"><span class="typeNameLink">FileCollection</span></a> (also extends org.gradle.api.<a href="../Buildable.html" title="interface in org.gradle.api">Buildable</a>, java.lang.Iterable&lt;T&gt;)
<ul>
<li class="circle">org.gradle.api.file.<a href="ConfigurableFileCollection.html" title="interface in org.gradle.api.file"><span class="typeNameLink">ConfigurableFileCollection</span></a> (also extends org.gradle.api.provider.<a href="../provider/HasConfigurableValue.html" title="interface in org.gradle.api.provider">HasConfigurableValue</a>)</li>
<li class="circle">org.gradle.api.file.<a href="FileTree.html" title="interface in org.gradle.api.file"><span class="typeNameLink">FileTree</span></a>
<ul>
<li class="circle">org.gradle.api.file.<a href="ConfigurableFileTree.html" title="interface in org.gradle.api.file"><span class="typeNameLink">ConfigurableFileTree</span></a> (also extends org.gradle.api.<a href="../Buildable.html" title="interface in org.gradle.api">Buildable</a>, org.gradle.api.file.<a href="DirectoryTree.html" title="interface in org.gradle.api.file">DirectoryTree</a>, org.gradle.api.tasks.util.<a href="../tasks/util/PatternFilterable.html" title="interface in org.gradle.api.tasks.util">PatternFilterable</a>)</li>
<li class="circle">org.gradle.api.file.<a href="SourceDirectorySet.html" title="interface in org.gradle.api.file"><span class="typeNameLink">SourceDirectorySet</span></a> (also extends org.gradle.api.<a href="../Describable.html" title="interface in org.gradle.api">Describable</a>, org.gradle.api.<a href="../Named.html" title="interface in org.gradle.api">Named</a>, org.gradle.api.tasks.util.<a href="../tasks/util/PatternFilterable.html" title="interface in org.gradle.api.tasks.util">PatternFilterable</a>)</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
<li class="circle">org.gradle.api.file.<a href="ArchiveOperations.html" title="interface in org.gradle.api.file"><span class="typeNameLink">ArchiveOperations</span></a></li>
<li class="circle">org.gradle.api.<a href="../Buildable.html" title="interface in org.gradle.api"><span class="typeNameLink">Buildable</span></a>
<ul>
<li class="circle">org.gradle.api.file.<a href="ConfigurableFileTree.html" title="interface in org.gradle.api.file"><span class="typeNameLink">ConfigurableFileTree</span></a> (also extends org.gradle.api.file.<a href="DirectoryTree.html" title="interface in org.gradle.api.file">DirectoryTree</a>, org.gradle.api.file.<a href="FileTree.html" title="interface in org.gradle.api.file">FileTree</a>, org.gradle.api.tasks.util.<a href="../tasks/util/PatternFilterable.html" title="interface in org.gradle.api.tasks.util">PatternFilterable</a>)</li>
<li class="circle">org.gradle.api.file.<a href="FileCollection.html" title="interface in org.gradle.api.file"><span class="typeNameLink">FileCollection</span></a> (also extends org.gradle.api.tasks.<a href="../tasks/AntBuilderAware.html" title="interface in org.gradle.api.tasks">AntBuilderAware</a>, java.lang.Iterable&lt;T&gt;)
<ul>
<li class="circle">org.gradle.api.file.<a href="ConfigurableFileCollection.html" title="interface in org.gradle.api.file"><span class="typeNameLink">ConfigurableFileCollection</span></a> (also extends org.gradle.api.provider.<a href="../provider/HasConfigurableValue.html" title="interface in org.gradle.api.provider">HasConfigurableValue</a>)</li>
<li class="circle">org.gradle.api.file.<a href="FileTree.html" title="interface in org.gradle.api.file"><span class="typeNameLink">FileTree</span></a>
<ul>
<li class="circle">org.gradle.api.file.<a href="ConfigurableFileTree.html" title="interface in org.gradle.api.file"><span class="typeNameLink">ConfigurableFileTree</span></a> (also extends org.gradle.api.<a href="../Buildable.html" title="interface in org.gradle.api">Buildable</a>, org.gradle.api.file.<a href="DirectoryTree.html" title="interface in org.gradle.api.file">DirectoryTree</a>, org.gradle.api.tasks.util.<a href="../tasks/util/PatternFilterable.html" title="interface in org.gradle.api.tasks.util">PatternFilterable</a>)</li>
<li class="circle">org.gradle.api.file.<a href="SourceDirectorySet.html" title="interface in org.gradle.api.file"><span class="typeNameLink">SourceDirectorySet</span></a> (also extends org.gradle.api.<a href="../Describable.html" title="interface in org.gradle.api">Describable</a>, org.gradle.api.<a href="../Named.html" title="interface in org.gradle.api">Named</a>, org.gradle.api.tasks.util.<a href="../tasks/util/PatternFilterable.html" title="interface in org.gradle.api.tasks.util">PatternFilterable</a>)</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
<li class="circle">org.gradle.api.file.<a href="ContentFilterable.html" title="interface in org.gradle.api.file"><span class="typeNameLink">ContentFilterable</span></a>
<ul>
<li class="circle">org.gradle.api.file.<a href="CopyProcessingSpec.html" title="interface in org.gradle.api.file"><span class="typeNameLink">CopyProcessingSpec</span></a>
<ul>
<li class="circle">org.gradle.api.file.<a href="CopySpec.html" title="interface in org.gradle.api.file"><span class="typeNameLink">CopySpec</span></a> (also extends org.gradle.api.file.<a href="CopySourceSpec.html" title="interface in org.gradle.api.file">CopySourceSpec</a>, org.gradle.api.tasks.util.<a href="../tasks/util/PatternFilterable.html" title="interface in org.gradle.api.tasks.util">PatternFilterable</a>)
<ul>
<li class="circle">org.gradle.api.file.<a href="SyncSpec.html" title="interface in org.gradle.api.file"><span class="typeNameLink">SyncSpec</span></a></li>
</ul>
</li>
</ul>
</li>
<li class="circle">org.gradle.api.file.<a href="FileCopyDetails.html" title="interface in org.gradle.api.file"><span class="typeNameLink">FileCopyDetails</span></a> (also extends org.gradle.api.file.<a href="FileTreeElement.html" title="interface in org.gradle.api.file">FileTreeElement</a>)</li>
</ul>
</li>
<li class="circle">org.gradle.api.file.<a href="CopySourceSpec.html" title="interface in org.gradle.api.file"><span class="typeNameLink">CopySourceSpec</span></a>
<ul>
<li class="circle">org.gradle.api.file.<a href="CopySpec.html" title="interface in org.gradle.api.file"><span class="typeNameLink">CopySpec</span></a> (also extends org.gradle.api.file.<a href="CopyProcessingSpec.html" title="interface in org.gradle.api.file">CopyProcessingSpec</a>, org.gradle.api.tasks.util.<a href="../tasks/util/PatternFilterable.html" title="interface in org.gradle.api.tasks.util">PatternFilterable</a>)
<ul>
<li class="circle">org.gradle.api.file.<a href="SyncSpec.html" title="interface in org.gradle.api.file"><span class="typeNameLink">SyncSpec</span></a></li>
</ul>
</li>
</ul>
</li>
<li class="circle">org.gradle.api.file.<a href="DeleteSpec.html" title="interface in org.gradle.api.file"><span class="typeNameLink">DeleteSpec</span></a></li>
<li class="circle">org.gradle.api.<a href="../Describable.html" title="interface in org.gradle.api"><span class="typeNameLink">Describable</span></a>
<ul>
<li class="circle">org.gradle.api.file.<a href="SourceDirectorySet.html" title="interface in org.gradle.api.file"><span class="typeNameLink">SourceDirectorySet</span></a> (also extends org.gradle.api.file.<a href="FileTree.html" title="interface in org.gradle.api.file">FileTree</a>, org.gradle.api.<a href="../Named.html" title="interface in org.gradle.api">Named</a>, org.gradle.api.tasks.util.<a href="../tasks/util/PatternFilterable.html" title="interface in org.gradle.api.tasks.util">PatternFilterable</a>)</li>
</ul>
</li>
<li class="circle">org.gradle.api.file.<a href="DirectoryTree.html" title="interface in org.gradle.api.file"><span class="typeNameLink">DirectoryTree</span></a>
<ul>
<li class="circle">org.gradle.api.file.<a href="ConfigurableFileTree.html" title="interface in org.gradle.api.file"><span class="typeNameLink">ConfigurableFileTree</span></a> (also extends org.gradle.api.<a href="../Buildable.html" title="interface in org.gradle.api">Buildable</a>, org.gradle.api.file.<a href="FileTree.html" title="interface in org.gradle.api.file">FileTree</a>, org.gradle.api.tasks.util.<a href="../tasks/util/PatternFilterable.html" title="interface in org.gradle.api.tasks.util">PatternFilterable</a>)</li>
</ul>
</li>
<li class="circle">org.gradle.api.file.<a href="ExpandDetails.html" title="interface in org.gradle.api.file"><span class="typeNameLink">ExpandDetails</span></a></li>
<li class="circle">org.gradle.api.file.<a href="FileContents.html" title="interface in org.gradle.api.file"><span class="typeNameLink">FileContents</span></a></li>
<li class="circle">org.gradle.api.file.<a href="FilePermissions.html" title="interface in org.gradle.api.file"><span class="typeNameLink">FilePermissions</span></a>
<ul>
<li class="circle">org.gradle.api.file.<a href="ConfigurableFilePermissions.html" title="interface in org.gradle.api.file"><span class="typeNameLink">ConfigurableFilePermissions</span></a></li>
</ul>
</li>
<li class="circle">org.gradle.api.file.<a href="FileSystemLocation.html" title="interface in org.gradle.api.file"><span class="typeNameLink">FileSystemLocation</span></a>
<ul>
<li class="circle">org.gradle.api.file.<a href="Directory.html" title="interface in org.gradle.api.file"><span class="typeNameLink">Directory</span></a></li>
<li class="circle">org.gradle.api.file.<a href="RegularFile.html" title="interface in org.gradle.api.file"><span class="typeNameLink">RegularFile</span></a></li>
</ul>
</li>
<li class="circle">org.gradle.api.file.<a href="FileSystemOperations.html" title="interface in org.gradle.api.file"><span class="typeNameLink">FileSystemOperations</span></a></li>
<li class="circle">org.gradle.api.file.<a href="FileTreeElement.html" title="interface in org.gradle.api.file"><span class="typeNameLink">FileTreeElement</span></a>
<ul>
<li class="circle">org.gradle.api.file.<a href="FileCopyDetails.html" title="interface in org.gradle.api.file"><span class="typeNameLink">FileCopyDetails</span></a> (also extends org.gradle.api.file.<a href="ContentFilterable.html" title="interface in org.gradle.api.file">ContentFilterable</a>)</li>
<li class="circle">org.gradle.api.file.<a href="FileVisitDetails.html" title="interface in org.gradle.api.file"><span class="typeNameLink">FileVisitDetails</span></a></li>
</ul>
</li>
<li class="circle">org.gradle.api.file.<a href="FileVisitor.html" title="interface in org.gradle.api.file"><span class="typeNameLink">FileVisitor</span></a>
<ul>
<li class="circle">org.gradle.api.file.<a href="ReproducibleFileVisitor.html" title="interface in org.gradle.api.file"><span class="typeNameLink">ReproducibleFileVisitor</span></a></li>
</ul>
</li>
<li class="circle">org.gradle.api.provider.<a href="../provider/HasConfigurableValue.html" title="interface in org.gradle.api.provider"><span class="typeNameLink">HasConfigurableValue</span></a>
<ul>
<li class="circle">org.gradle.api.file.<a href="ConfigurableFileCollection.html" title="interface in org.gradle.api.file"><span class="typeNameLink">ConfigurableFileCollection</span></a> (also extends org.gradle.api.file.<a href="FileCollection.html" title="interface in org.gradle.api.file">FileCollection</a>)</li>
<li class="circle">org.gradle.api.provider.<a href="../provider/Property.html" title="interface in org.gradle.api.provider"><span class="typeNameLink">Property</span></a>&lt;T&gt; (also extends org.gradle.api.provider.<a href="../provider/Provider.html" title="interface in org.gradle.api.provider">Provider</a>&lt;T&gt;)
<ul>
<li class="circle">org.gradle.api.file.<a href="FileSystemLocationProperty.html" title="interface in org.gradle.api.file"><span class="typeNameLink">FileSystemLocationProperty</span></a>&lt;T&gt;
<ul>
<li class="circle">org.gradle.api.file.<a href="DirectoryProperty.html" title="interface in org.gradle.api.file"><span class="typeNameLink">DirectoryProperty</span></a></li>
<li class="circle">org.gradle.api.file.<a href="RegularFileProperty.html" title="interface in org.gradle.api.file"><span class="typeNameLink">RegularFileProperty</span></a></li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
<li class="circle">java.lang.Iterable&lt;T&gt;
<ul>
<li class="circle">org.gradle.api.file.<a href="FileCollection.html" title="interface in org.gradle.api.file"><span class="typeNameLink">FileCollection</span></a> (also extends org.gradle.api.tasks.<a href="../tasks/AntBuilderAware.html" title="interface in org.gradle.api.tasks">AntBuilderAware</a>, org.gradle.api.<a href="../Buildable.html" title="interface in org.gradle.api">Buildable</a>)
<ul>
<li class="circle">org.gradle.api.file.<a href="ConfigurableFileCollection.html" title="interface in org.gradle.api.file"><span class="typeNameLink">ConfigurableFileCollection</span></a> (also extends org.gradle.api.provider.<a href="../provider/HasConfigurableValue.html" title="interface in org.gradle.api.provider">HasConfigurableValue</a>)</li>
<li class="circle">org.gradle.api.file.<a href="FileTree.html" title="interface in org.gradle.api.file"><span class="typeNameLink">FileTree</span></a>
<ul>
<li class="circle">org.gradle.api.file.<a href="ConfigurableFileTree.html" title="interface in org.gradle.api.file"><span class="typeNameLink">ConfigurableFileTree</span></a> (also extends org.gradle.api.<a href="../Buildable.html" title="interface in org.gradle.api">Buildable</a>, org.gradle.api.file.<a href="DirectoryTree.html" title="interface in org.gradle.api.file">DirectoryTree</a>, org.gradle.api.tasks.util.<a href="../tasks/util/PatternFilterable.html" title="interface in org.gradle.api.tasks.util">PatternFilterable</a>)</li>
<li class="circle">org.gradle.api.file.<a href="SourceDirectorySet.html" title="interface in org.gradle.api.file"><span class="typeNameLink">SourceDirectorySet</span></a> (also extends org.gradle.api.<a href="../Describable.html" title="interface in org.gradle.api">Describable</a>, org.gradle.api.<a href="../Named.html" title="interface in org.gradle.api">Named</a>, org.gradle.api.tasks.util.<a href="../tasks/util/PatternFilterable.html" title="interface in org.gradle.api.tasks.util">PatternFilterable</a>)</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
<li class="circle">org.gradle.api.<a href="../Named.html" title="interface in org.gradle.api"><span class="typeNameLink">Named</span></a>
<ul>
<li class="circle">org.gradle.api.file.<a href="SourceDirectorySet.html" title="interface in org.gradle.api.file"><span class="typeNameLink">SourceDirectorySet</span></a> (also extends org.gradle.api.<a href="../Describable.html" title="interface in org.gradle.api">Describable</a>, org.gradle.api.file.<a href="FileTree.html" title="interface in org.gradle.api.file">FileTree</a>, org.gradle.api.tasks.util.<a href="../tasks/util/PatternFilterable.html" title="interface in org.gradle.api.tasks.util">PatternFilterable</a>)</li>
</ul>
</li>
<li class="circle">org.gradle.api.tasks.util.<a href="../tasks/util/PatternFilterable.html" title="interface in org.gradle.api.tasks.util"><span class="typeNameLink">PatternFilterable</span></a>
<ul>
<li class="circle">org.gradle.api.file.<a href="ConfigurableFileTree.html" title="interface in org.gradle.api.file"><span class="typeNameLink">ConfigurableFileTree</span></a> (also extends org.gradle.api.<a href="../Buildable.html" title="interface in org.gradle.api">Buildable</a>, org.gradle.api.file.<a href="DirectoryTree.html" title="interface in org.gradle.api.file">DirectoryTree</a>, org.gradle.api.file.<a href="FileTree.html" title="interface in org.gradle.api.file">FileTree</a>)</li>
<li class="circle">org.gradle.api.file.<a href="CopySpec.html" title="interface in org.gradle.api.file"><span class="typeNameLink">CopySpec</span></a> (also extends org.gradle.api.file.<a href="CopyProcessingSpec.html" title="interface in org.gradle.api.file">CopyProcessingSpec</a>, org.gradle.api.file.<a href="CopySourceSpec.html" title="interface in org.gradle.api.file">CopySourceSpec</a>)
<ul>
<li class="circle">org.gradle.api.file.<a href="SyncSpec.html" title="interface in org.gradle.api.file"><span class="typeNameLink">SyncSpec</span></a></li>
</ul>
</li>
<li class="circle">org.gradle.api.file.<a href="SourceDirectorySet.html" title="interface in org.gradle.api.file"><span class="typeNameLink">SourceDirectorySet</span></a> (also extends org.gradle.api.<a href="../Describable.html" title="interface in org.gradle.api">Describable</a>, org.gradle.api.file.<a href="FileTree.html" title="interface in org.gradle.api.file">FileTree</a>, org.gradle.api.<a href="../Named.html" title="interface in org.gradle.api">Named</a>)</li>
</ul>
</li>
<li class="circle">org.gradle.api.file.<a href="ProjectLayout.html" title="interface in org.gradle.api.file"><span class="typeNameLink">ProjectLayout</span></a></li>
<li class="circle">org.gradle.api.provider.<a href="../provider/Provider.html" title="interface in org.gradle.api.provider"><span class="typeNameLink">Provider</span></a>&lt;T&gt;
<ul>
<li class="circle">org.gradle.api.provider.<a href="../provider/Property.html" title="interface in org.gradle.api.provider"><span class="typeNameLink">Property</span></a>&lt;T&gt; (also extends org.gradle.api.provider.<a href="../provider/HasConfigurableValue.html" title="interface in org.gradle.api.provider">HasConfigurableValue</a>)
<ul>
<li class="circle">org.gradle.api.file.<a href="FileSystemLocationProperty.html" title="interface in org.gradle.api.file"><span class="typeNameLink">FileSystemLocationProperty</span></a>&lt;T&gt;
<ul>
<li class="circle">org.gradle.api.file.<a href="DirectoryProperty.html" title="interface in org.gradle.api.file"><span class="typeNameLink">DirectoryProperty</span></a></li>
<li class="circle">org.gradle.api.file.<a href="RegularFileProperty.html" title="interface in org.gradle.api.file"><span class="typeNameLink">RegularFileProperty</span></a></li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
<li class="circle">org.gradle.api.file.<a href="UserClassFilePermissions.html" title="interface in org.gradle.api.file"><span class="typeNameLink">UserClassFilePermissions</span></a>
<ul>
<li class="circle">org.gradle.api.file.<a href="ConfigurableUserClassFilePermissions.html" title="interface in org.gradle.api.file"><span class="typeNameLink">ConfigurableUserClassFilePermissions</span></a></li>
</ul>
</li>
</ul>
<h2 title="Enum Hierarchy">Enum Hierarchy</h2>
<ul>
<li class="circle">java.lang.Object
<ul>
<li class="circle">java.lang.Enum&lt;E&gt; (implements java.lang.Comparable&lt;T&gt;, java.io.Serializable)
<ul>
<li class="circle">org.gradle.api.file.<a href="DuplicatesStrategy.html" title="enum in org.gradle.api.file"><span class="typeNameLink">DuplicatesStrategy</span></a></li>
<li class="circle">org.gradle.api.file.<a href="FileCollection.AntType.html" title="enum in org.gradle.api.file"><span class="typeNameLink">FileCollection.AntType</span></a></li>
<li class="circle">org.gradle.api.file.<a href="FileType.html" title="enum in org.gradle.api.file"><span class="typeNameLink">FileType</span></a></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li>Class</li>
<li class="navBarCell1Rev">Tree</li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
