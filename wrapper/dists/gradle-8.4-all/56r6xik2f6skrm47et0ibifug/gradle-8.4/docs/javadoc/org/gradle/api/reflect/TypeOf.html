<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc -->
<title>TypeOf (Gradle API 8.4)</title>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<link rel="stylesheet" type="text/css" href="../../../../javadoc.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../jquery/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
<script type="text/javascript" src="../../../../jquery/jszip/dist/jszip.min.js"></script>
<script type="text/javascript" src="../../../../jquery/jszip-utils/dist/jszip-utils.min.js"></script>
<!--[if IE]>
<script type="text/javascript" src="../../../../jquery/jszip-utils/dist/jszip-utils-ie.min.js"></script>
<![endif]-->
<script type="text/javascript" src="../../../../jquery/jquery-3.6.0.min.js"></script>
<script type="text/javascript" src="../../../../jquery/jquery-ui.min.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="TypeOf (Gradle API 8.4)";
        }
    }
    catch(err) {
    }
//-->
var data = {"i0":10,"i1":10,"i2":10,"i3":10,"i4":10,"i5":10,"i6":10,"i7":10,"i8":10,"i9":10,"i10":10,"i11":10,"i12":10,"i13":10,"i14":10,"i15":10,"i16":10,"i17":10,"i18":9,"i19":10,"i20":9,"i21":9};
var tabs = {65535:["t0","All Methods"],1:["t1","Static Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
var pathtoroot = "../../../../";
var useModuleDirectories = false;
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="fixedNav">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses.html">All&nbsp;Classes</a></li>
</ul>
<ul class="navListSearch">
<li><label for="search">SEARCH:</label>
<input type="text" id="search" value="search" disabled="disabled">
<input type="reset" id="reset" value="reset" disabled="disabled">
</li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
</div>
<div class="navPadding">&nbsp;</div>
<script type="text/javascript"><!--
$('.navPadding').css('padding-top', $('.fixedNav').css("height"));
//-->
</script>
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle"><span class="packageLabelInType">Package</span>&nbsp;<a href="package-summary.html">org.gradle.api.reflect</a></div>
<h2 title="Class TypeOf" class="title">Class TypeOf&lt;T&gt;</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>org.gradle.api.reflect.TypeOf&lt;T&gt;</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt><span class="paramLabel">Type Parameters:</span></dt>
<dd><code>T</code> - Parameterized type</dd>
</dl>
<hr>
<pre>public abstract class <span class="typeNameLabel">TypeOf&lt;T&gt;</span>
extends java.lang.Object</pre>
<div class="block">Provides a way to preserve high-fidelity <code>Type</code> information on generic types.

 Capture a generic type with an anonymous subclass. For example: <pre>   <code>
   new TypeOf&lt;NamedDomainObjectContainer&lt;ArtifactRepository&gt;&gt;() {}</code></pre></div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>3.5</dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="memberSummary" summary="Constructor Summary table, listing constructors, and an explanation">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier</th>
<th class="colSecond" scope="col">Constructor</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected </code></td>
<th class="colConstructorName" scope="row"><code><span class="memberNameLink"><a href="#TypeOf--">TypeOf</a></span>()</code></th>
<td class="colLast">&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t1" class="tableTab"><span><a href="javascript:show(1);">Static Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colSecond" scope="col">Method</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#equals-java.lang.Object-">equals</a></span>&#8203;(java.lang.Object&nbsp;o)</code></th>
<td class="colLast">&nbsp;</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>java.util.List&lt;<a href="TypeOf.html" title="class in org.gradle.api.reflect">TypeOf</a>&lt;?&gt;&gt;</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getActualTypeArguments--">getActualTypeArguments</a></span>()</code></th>
<td class="colLast">
<div class="block">Returns the list of type arguments used in the construction of this parameterized type.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code><a href="TypeOf.html" title="class in org.gradle.api.reflect">TypeOf</a>&lt;?&gt;</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getComponentType--">getComponentType</a></span>()</code></th>
<td class="colLast">
<div class="block">Returns the component type of the array type this object represents.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>java.lang.Class&lt;<a href="TypeOf.html" title="type parameter in TypeOf">T</a>&gt;</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getConcreteClass--">getConcreteClass</a></span>()</code></th>
<td class="colLast">
<div class="block">
 This returns the underlying, concrete Java <code>Class</code>.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getFullyQualifiedName--">getFullyQualifiedName</a></span>()</code></th>
<td class="colLast">
<div class="block">Fully Qualified name.</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code><a href="TypeOf.html" title="class in org.gradle.api.reflect">TypeOf</a>&lt;?&gt;</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getLowerBound--">getLowerBound</a></span>()</code></th>
<td class="colLast">
<div class="block">Returns the first declared lower-bound of the wildcard type expression represented by this type.</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code><a href="TypeOf.html" title="class in org.gradle.api.reflect">TypeOf</a>&lt;?&gt;</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getParameterizedTypeDefinition--">getParameterizedTypeDefinition</a></span>()</code></th>
<td class="colLast">
<div class="block">Returns an object that represents the type from which this parameterized type was constructed.</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>java.lang.String</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getSimpleName--">getSimpleName</a></span>()</code></th>
<td class="colLast">
<div class="block">Simple name.</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code><a href="TypeOf.html" title="class in org.gradle.api.reflect">TypeOf</a>&lt;?&gt;</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getUpperBound--">getUpperBound</a></span>()</code></th>
<td class="colLast">
<div class="block">Returns the first declared upper-bound of the wildcard type expression represented by this type.</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>int</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#hashCode--">hashCode</a></span>()</code></th>
<td class="colLast">&nbsp;</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#isArray--">isArray</a></span>()</code></th>
<td class="colLast">
<div class="block">Queries whether this object represents an array, generic or otherwise.</div>
</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#isAssignableFrom-java.lang.reflect.Type-">isAssignableFrom</a></span>&#8203;(java.lang.reflect.Type&nbsp;type)</code></th>
<td class="colLast">
<div class="block">Is this type assignable from the given type?</div>
</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#isAssignableFrom-org.gradle.api.reflect.TypeOf-">isAssignableFrom</a></span>&#8203;(<a href="TypeOf.html" title="class in org.gradle.api.reflect">TypeOf</a>&lt;?&gt;&nbsp;type)</code></th>
<td class="colLast">
<div class="block">Is this type assignable from the given type?</div>
</td>
</tr>
<tr id="i13" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#isParameterized--">isParameterized</a></span>()</code></th>
<td class="colLast">
<div class="block">Queries whether this object represents a parameterized type.</div>
</td>
</tr>
<tr id="i14" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#isPublic--">isPublic</a></span>()</code></th>
<td class="colLast">
<div class="block">Queries whether the type represented by this object is public (<code>Modifier.isPublic(int)</code>).</div>
</td>
</tr>
<tr id="i15" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#isSimple--">isSimple</a></span>()</code></th>
<td class="colLast">
<div class="block">Queries whether this object represents a simple (non-composite) type, not an array and not a generic type.</div>
</td>
</tr>
<tr id="i16" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#isSynthetic--">isSynthetic</a></span>()</code></th>
<td class="colLast">
<div class="block">Queries whether this object represents a synthetic type as defined by <code>Class.isSynthetic()</code>.</div>
</td>
</tr>
<tr id="i17" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#isWildcard--">isWildcard</a></span>()</code></th>
<td class="colLast">
<div class="block">Queries whether this object represents a wildcard type expression, such as
 <code>?</code>, <code>? extends Number</code>, or <code>? super Integer</code>.</div>
</td>
</tr>
<tr id="i18" class="altColor">
<td class="colFirst"><code>static <a href="TypeOf.html" title="class in org.gradle.api.reflect">TypeOf</a>&lt;?&gt;</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#parameterizedTypeOf-org.gradle.api.reflect.TypeOf-org.gradle.api.reflect.TypeOf...-">parameterizedTypeOf</a></span>&#8203;(<a href="TypeOf.html" title="class in org.gradle.api.reflect">TypeOf</a>&lt;?&gt;&nbsp;parameterizedType,
                   <a href="TypeOf.html" title="class in org.gradle.api.reflect">TypeOf</a>&lt;?&gt;...&nbsp;typeArguments)</code></th>
<td class="colLast">
<div class="block">Constructs a new parameterized type from a given parameterized type definition and an array of type arguments.</div>
</td>
</tr>
<tr id="i19" class="rowColor">
<td class="colFirst"><code>java.lang.String</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#toString--">toString</a></span>()</code></th>
<td class="colLast">&nbsp;</td>
</tr>
<tr id="i20" class="altColor">
<td class="colFirst"><code>static &lt;T&gt;&nbsp;<a href="TypeOf.html" title="class in org.gradle.api.reflect">TypeOf</a>&lt;T&gt;</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#typeOf-java.lang.Class-">typeOf</a></span>&#8203;(java.lang.Class&lt;T&gt;&nbsp;type)</code></th>
<td class="colLast">
<div class="block">Creates an instance of TypeOf for the given Class.</div>
</td>
</tr>
<tr id="i21" class="rowColor">
<td class="colFirst"><code>static &lt;T&gt;&nbsp;<a href="TypeOf.html" title="class in org.gradle.api.reflect">TypeOf</a>&lt;T&gt;</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#typeOf-java.lang.reflect.Type-">typeOf</a></span>&#8203;(java.lang.reflect.Type&nbsp;type)</code></th>
<td class="colLast">
<div class="block">Creates an instance of TypeOf for the given Type.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>clone, finalize, getClass, notify, notifyAll, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a name="TypeOf--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>TypeOf</h4>
<pre>protected&nbsp;TypeOf()</pre>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="typeOf-java.lang.Class-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>typeOf</h4>
<pre class="methodSignature">public static&nbsp;&lt;T&gt;&nbsp;<a href="TypeOf.html" title="class in org.gradle.api.reflect">TypeOf</a>&lt;T&gt;&nbsp;typeOf&#8203;(java.lang.Class&lt;T&gt;&nbsp;type)</pre>
<div class="block">Creates an instance of TypeOf for the given Class.</div>
<dl>
<dt><span class="paramLabel">Type Parameters:</span></dt>
<dd><code>T</code> - the parameterized type of the given Class</dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>type</code> - the Class</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the TypeOf that captures the generic type of the given Class</dd>
</dl>
</li>
</ul>
<a name="typeOf-java.lang.reflect.Type-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>typeOf</h4>
<pre class="methodSignature">public static&nbsp;&lt;T&gt;&nbsp;<a href="TypeOf.html" title="class in org.gradle.api.reflect">TypeOf</a>&lt;T&gt;&nbsp;typeOf&#8203;(java.lang.reflect.Type&nbsp;type)</pre>
<div class="block">Creates an instance of TypeOf for the given Type.</div>
<dl>
<dt><span class="paramLabel">Type Parameters:</span></dt>
<dd><code>T</code> - the parameterized type of the given Type</dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>type</code> - the Type</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the TypeOf that captures the generic type of the given Type</dd>
</dl>
</li>
</ul>
<a name="parameterizedTypeOf-org.gradle.api.reflect.TypeOf-org.gradle.api.reflect.TypeOf...-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>parameterizedTypeOf</h4>
<pre class="methodSignature">public static&nbsp;<a href="TypeOf.html" title="class in org.gradle.api.reflect">TypeOf</a>&lt;?&gt;&nbsp;parameterizedTypeOf&#8203;(<a href="TypeOf.html" title="class in org.gradle.api.reflect">TypeOf</a>&lt;?&gt;&nbsp;parameterizedType,
                                            <a href="TypeOf.html" title="class in org.gradle.api.reflect">TypeOf</a>&lt;?&gt;...&nbsp;typeArguments)</pre>
<div class="block">Constructs a new parameterized type from a given parameterized type definition and an array of type arguments.

 For example, <code>parameterizedTypeOf(new TypeOf&lt;List&lt;?&gt;&gt;() {}, new TypeOf&lt;String&gt;() {})</code> is equivalent to
 <code>new TypeOf&lt;List&lt;String&gt;&gt;() {}</code>, except both the parameterized type definition and type arguments can be dynamically computed.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>parameterizedType</code> - the parameterized type from which to construct the new parameterized type</dd>
<dd><code>typeArguments</code> - the arguments with which to construct the new parameterized type</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="#isParameterized--"><code>isParameterized()</code></a></dd>
</dl>
</li>
</ul>
<a name="isSimple--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isSimple</h4>
<pre class="methodSignature">public&nbsp;boolean&nbsp;isSimple()</pre>
<div class="block">Queries whether this object represents a simple (non-composite) type, not an array and not a generic type.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>true if this object represents a simple type.</dd>
</dl>
</li>
</ul>
<a name="isSynthetic--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isSynthetic</h4>
<pre class="methodSignature">public&nbsp;boolean&nbsp;isSynthetic()</pre>
<div class="block">Queries whether this object represents a synthetic type as defined by <code>Class.isSynthetic()</code>.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>true if this object represents a synthetic type.</dd>
</dl>
</li>
</ul>
<a name="isPublic--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isPublic</h4>
<pre class="methodSignature">public&nbsp;boolean&nbsp;isPublic()</pre>
<div class="block">Queries whether the type represented by this object is public (<code>Modifier.isPublic(int)</code>).</div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><code>Modifier.isPublic(int)</code>, 
<code>Class.getModifiers()</code></dd>
</dl>
</li>
</ul>
<a name="isArray--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isArray</h4>
<pre class="methodSignature">public&nbsp;boolean&nbsp;isArray()</pre>
<div class="block">Queries whether this object represents an array, generic or otherwise.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>true if this object represents an array.</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="#getComponentType--"><code>getComponentType()</code></a></dd>
</dl>
</li>
</ul>
<a name="getComponentType--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getComponentType</h4>
<pre class="methodSignature">@Nullable
public&nbsp;<a href="TypeOf.html" title="class in org.gradle.api.reflect">TypeOf</a>&lt;?&gt;&nbsp;getComponentType()</pre>
<div class="block">Returns the component type of the array type this object represents.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>null if this object does not represent an array type.</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="#isArray--"><code>isArray()</code></a></dd>
</dl>
</li>
</ul>
<a name="isParameterized--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isParameterized</h4>
<pre class="methodSignature">public&nbsp;boolean&nbsp;isParameterized()</pre>
<div class="block">Queries whether this object represents a parameterized type.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>true if this object represents a parameterized type.</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="#getParameterizedTypeDefinition--"><code>getParameterizedTypeDefinition()</code></a>, 
<a href="#getActualTypeArguments--"><code>getActualTypeArguments()</code></a></dd>
</dl>
</li>
</ul>
<a name="getParameterizedTypeDefinition--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getParameterizedTypeDefinition</h4>
<pre class="methodSignature">public&nbsp;<a href="TypeOf.html" title="class in org.gradle.api.reflect">TypeOf</a>&lt;?&gt;&nbsp;getParameterizedTypeDefinition()</pre>
<div class="block">Returns an object that represents the type from which this parameterized type was constructed.</div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="#isParameterized--"><code>isParameterized()</code></a></dd>
</dl>
</li>
</ul>
<a name="getActualTypeArguments--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getActualTypeArguments</h4>
<pre class="methodSignature">public&nbsp;java.util.List&lt;<a href="TypeOf.html" title="class in org.gradle.api.reflect">TypeOf</a>&lt;?&gt;&gt;&nbsp;getActualTypeArguments()</pre>
<div class="block">Returns the list of type arguments used in the construction of this parameterized type.</div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="#isParameterized--"><code>isParameterized()</code></a></dd>
</dl>
</li>
</ul>
<a name="isWildcard--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isWildcard</h4>
<pre class="methodSignature">public&nbsp;boolean&nbsp;isWildcard()</pre>
<div class="block">Queries whether this object represents a wildcard type expression, such as
 <code>?</code>, <code>? extends Number</code>, or <code>? super Integer</code>.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>true if this object represents a wildcard type expression.</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="#getUpperBound--"><code>getUpperBound()</code></a></dd>
</dl>
</li>
</ul>
<a name="getUpperBound--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getUpperBound</h4>
<pre class="methodSignature">@Nullable
public&nbsp;<a href="TypeOf.html" title="class in org.gradle.api.reflect">TypeOf</a>&lt;?&gt;&nbsp;getUpperBound()</pre>
<div class="block">Returns the first declared upper-bound of the wildcard type expression represented by this type.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>null if no upper-bound has been explicitly declared.</dd>
</dl>
</li>
</ul>
<a name="getLowerBound--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getLowerBound</h4>
<pre class="methodSignature">@Nullable
public&nbsp;<a href="TypeOf.html" title="class in org.gradle.api.reflect">TypeOf</a>&lt;?&gt;&nbsp;getLowerBound()</pre>
<div class="block">Returns the first declared lower-bound of the wildcard type expression represented by this type.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>null if no lower-bound has been explicitly declared.</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>6.0</dd>
</dl>
</li>
</ul>
<a name="isAssignableFrom-org.gradle.api.reflect.TypeOf-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isAssignableFrom</h4>
<pre class="methodSignature">public final&nbsp;boolean&nbsp;isAssignableFrom&#8203;(<a href="TypeOf.html" title="class in org.gradle.api.reflect">TypeOf</a>&lt;?&gt;&nbsp;type)</pre>
<div class="block">Is this type assignable from the given type?</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>type</code> - the given type</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>true if this type is assignable from the given type, false otherwise</dd>
</dl>
</li>
</ul>
<a name="isAssignableFrom-java.lang.reflect.Type-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isAssignableFrom</h4>
<pre class="methodSignature">public final&nbsp;boolean&nbsp;isAssignableFrom&#8203;(java.lang.reflect.Type&nbsp;type)</pre>
<div class="block">Is this type assignable from the given type?</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>type</code> - the given type</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>true if this type is assignable from the given type, false otherwise</dd>
</dl>
</li>
</ul>
<a name="getSimpleName--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getSimpleName</h4>
<pre class="methodSignature">public&nbsp;java.lang.String&nbsp;getSimpleName()</pre>
<div class="block">Simple name.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>this type's simple name</dd>
</dl>
</li>
</ul>
<a name="getFullyQualifiedName--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getFullyQualifiedName</h4>
<pre class="methodSignature">public&nbsp;java.lang.String&nbsp;getFullyQualifiedName()</pre>
<div class="block">Fully Qualified name.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>this type's FQN</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>7.4</dd>
</dl>
</li>
</ul>
<a name="getConcreteClass--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getConcreteClass</h4>
<pre class="methodSignature">public&nbsp;java.lang.Class&lt;<a href="TypeOf.html" title="type parameter in TypeOf">T</a>&gt;&nbsp;getConcreteClass()</pre>
<div class="block"><p>
 This returns the underlying, concrete Java <code>Class</code>.
 </p>
 <p>
 For example, a simple <code>TypeOf&lt;String&gt;</code> will be the given generic type <code>String.class</code>.
 <br>
 Generic types like <code>TypeOf&lt;List&lt;String&gt;&gt;</code> would have the concrete type of <code>List.class</code>.
 <br>
 For array types like <code>TypeOf&lt;String[]&gt;</code>, the concrete type will be an array of the component type (<code>String[].class</code>).
 </p></div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>Underlying Java Class of this type.</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>5.0</dd>
</dl>
</li>
</ul>
<a name="toString--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>toString</h4>
<pre class="methodSignature">public final&nbsp;java.lang.String&nbsp;toString()</pre>
<dl>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code>toString</code>&nbsp;in class&nbsp;<code>java.lang.Object</code></dd>
</dl>
</li>
</ul>
<a name="equals-java.lang.Object-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>equals</h4>
<pre class="methodSignature">public&nbsp;boolean&nbsp;equals&#8203;(java.lang.Object&nbsp;o)</pre>
<dl>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code>equals</code>&nbsp;in class&nbsp;<code>java.lang.Object</code></dd>
</dl>
</li>
</ul>
<a name="hashCode--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>hashCode</h4>
<pre class="methodSignature">public&nbsp;int&nbsp;hashCode()</pre>
<dl>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code>hashCode</code>&nbsp;in class&nbsp;<code>java.lang.Object</code></dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
