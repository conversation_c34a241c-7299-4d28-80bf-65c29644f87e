<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc -->
<title>WriteProperties (Gradle API 8.4)</title>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<link rel="stylesheet" type="text/css" href="../../../../javadoc.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../jquery/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
<script type="text/javascript" src="../../../../jquery/jszip/dist/jszip.min.js"></script>
<script type="text/javascript" src="../../../../jquery/jszip-utils/dist/jszip-utils.min.js"></script>
<!--[if IE]>
<script type="text/javascript" src="../../../../jquery/jszip-utils/dist/jszip-utils-ie.min.js"></script>
<![endif]-->
<script type="text/javascript" src="../../../../jquery/jquery-3.6.0.min.js"></script>
<script type="text/javascript" src="../../../../jquery/jquery-ui.min.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="WriteProperties (Gradle API 8.4)";
        }
    }
    catch(err) {
    }
//-->
var data = {"i0":10,"i1":6,"i2":10,"i3":10,"i4":42,"i5":10,"i6":10,"i7":10,"i8":10,"i9":10,"i10":10,"i11":42,"i12":42,"i13":10,"i14":10};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],4:["t3","Abstract Methods"],8:["t4","Concrete Methods"],32:["t6","Deprecated Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
var pathtoroot = "../../../../";
var useModuleDirectories = false;
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="fixedNav">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses.html">All&nbsp;Classes</a></li>
</ul>
<ul class="navListSearch">
<li><label for="search">SEARCH:</label>
<input type="text" id="search" value="search" disabled="disabled">
<input type="reset" id="reset" value="reset" disabled="disabled">
</li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li><a href="#nested.class.summary">Nested</a>&nbsp;|&nbsp;</li>
<li><a href="#field.summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
</div>
<div class="navPadding">&nbsp;</div>
<script type="text/javascript"><!--
$('.navPadding').css('padding-top', $('.fixedNav').css("height"));
//-->
</script>
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle"><span class="packageLabelInType">Package</span>&nbsp;<a href="package-summary.html">org.gradle.api.tasks</a></div>
<h2 title="Class WriteProperties" class="title">Class WriteProperties</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>org.gradle.api.internal.AbstractTask</li>
<li>
<ul class="inheritance">
<li><a href="../DefaultTask.html" title="class in org.gradle.api">org.gradle.api.DefaultTask</a></li>
<li>
<ul class="inheritance">
<li>org.gradle.api.tasks.WriteProperties</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Implemented Interfaces:</dt>
<dd><code>java.lang.Comparable&lt;<a href="../Task.html" title="interface in org.gradle.api">Task</a>&gt;</code>, <code>org.gradle.api.internal.DynamicObjectAware</code>, <code>org.gradle.api.internal.TaskInternal</code>, <code><a href="../plugins/ExtensionAware.html" title="interface in org.gradle.api.plugins">ExtensionAware</a></code>, <code><a href="../Task.html" title="interface in org.gradle.api">Task</a></code>, <code><a href="../../util/Configurable.html" title="interface in org.gradle.util">Configurable</a>&lt;<a href="../Task.html" title="interface in org.gradle.api">Task</a>&gt;</code></dd>
</dl>
<hr>
<pre><a href="CacheableTask.html" title="annotation in org.gradle.api.tasks">@CacheableTask</a>
public abstract class <span class="typeNameLabel">WriteProperties</span>
extends <a href="../DefaultTask.html" title="class in org.gradle.api">DefaultTask</a></pre>
<div class="block">Writes a <code>Properties</code> in a way that the results can be expected to be reproducible.

 <p>There are a number of differences compared to how properties are stored:</p>
 <ul>
     <li>no timestamp comment is generated at the beginning of the file</li>
     <li>the lines in the resulting files are separated by a pre-set separator (defaults to
         '\n') instead of the system default line separator</li>
     <li>the properties are sorted alphabetically</li>
 </ul>

 <p>Like with <code>Properties</code>, Unicode characters are escaped when using the
 default Latin-1 (ISO-8559-1) encoding.</p></div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>3.3</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><code>Properties.store(OutputStream, String)</code></dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== NESTED CLASS SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="nested.class.summary">
<!--   -->
</a>
<h3>Nested Class Summary</h3>
<ul class="blockList">
<li class="blockList"><a name="nested.classes.inherited.from.class.org.gradle.api.Task">
<!--   -->
</a>
<h3>Nested classes/interfaces inherited from interface&nbsp;org.gradle.api.<a href="../Task.html" title="interface in org.gradle.api">Task</a></h3>
<code><a href="../Task.Namer.html" title="class in org.gradle.api">Task.Namer</a></code></li>
</ul>
</li>
</ul>
<!-- =========== FIELD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.summary">
<!--   -->
</a>
<h3>Field Summary</h3>
<ul class="blockList">
<li class="blockList"><a name="fields.inherited.from.class.org.gradle.api.Task">
<!--   -->
</a>
<h3>Fields inherited from interface&nbsp;org.gradle.api.<a href="../Task.html" title="interface in org.gradle.api">Task</a></h3>
<code><a href="../Task.html#TASK_ACTION">TASK_ACTION</a>, <a href="../Task.html#TASK_CONSTRUCTOR_ARGS">TASK_CONSTRUCTOR_ARGS</a>, <a href="../Task.html#TASK_DEPENDS_ON">TASK_DEPENDS_ON</a>, <a href="../Task.html#TASK_DESCRIPTION">TASK_DESCRIPTION</a>, <a href="../Task.html#TASK_GROUP">TASK_GROUP</a>, <a href="../Task.html#TASK_NAME">TASK_NAME</a>, <a href="../Task.html#TASK_OVERWRITE">TASK_OVERWRITE</a>, <a href="../Task.html#TASK_TYPE">TASK_TYPE</a></code></li>
</ul>
</li>
</ul>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="memberSummary" summary="Constructor Summary table, listing constructors, and an explanation">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Constructor</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tr class="altColor">
<th class="colConstructorName" scope="row"><code><span class="memberNameLink"><a href="#WriteProperties--">WriteProperties</a></span>()</code></th>
<td class="colLast">&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t3" class="tableTab"><span><a href="javascript:show(4);">Abstract Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t6" class="tableTab"><span><a href="javascript:show(32);">Deprecated Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colSecond" scope="col">Method</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getComment--">getComment</a></span>()</code></th>
<td class="colLast">
<div class="block">Returns the optional comment to add at the beginning of the properties file.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>abstract <a href="../file/RegularFileProperty.html" title="interface in org.gradle.api.file">RegularFileProperty</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getDestinationFile--">getDestinationFile</a></span>()</code></th>
<td class="colLast">
<div class="block">The output properties file.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getEncoding--">getEncoding</a></span>()</code></th>
<td class="colLast">
<div class="block">Returns the encoding used to write the properties file.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>java.lang.String</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getLineSeparator--">getLineSeparator</a></span>()</code></th>
<td class="colLast">
<div class="block">Returns the line separator to be used when creating the properties file.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>java.io.File</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getOutputFile--">getOutputFile</a></span>()</code></th>
<td class="colLast">
<div class="block"><span class="deprecatedLabel">Deprecated.</span></div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>java.util.Map&lt;java.lang.String,&#8203;java.lang.String&gt;</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getProperties--">getProperties</a></span>()</code></th>
<td class="colLast">
<div class="block">Returns an immutable view of properties to be written to the properties file.</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#properties-java.util.Map-">properties</a></span>&#8203;(java.util.Map&lt;java.lang.String,&#8203;java.lang.Object&gt;&nbsp;properties)</code></th>
<td class="colLast">
<div class="block">Adds multiple properties to be written to the properties file.</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#property-java.lang.String-java.lang.Object-">property</a></span>&#8203;(java.lang.String&nbsp;name,
        java.lang.Object&nbsp;value)</code></th>
<td class="colLast">
<div class="block">Adds a property to be written to the properties file.</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setComment-java.lang.String-">setComment</a></span>&#8203;(java.lang.String&nbsp;comment)</code></th>
<td class="colLast">
<div class="block">Sets the optional comment to add at the beginning of the properties file.</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setEncoding-java.lang.String-">setEncoding</a></span>&#8203;(java.lang.String&nbsp;encoding)</code></th>
<td class="colLast">
<div class="block">Sets the encoding used to write the properties file.</div>
</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setLineSeparator-java.lang.String-">setLineSeparator</a></span>&#8203;(java.lang.String&nbsp;lineSeparator)</code></th>
<td class="colLast">
<div class="block">Sets the line separator to be used when creating the properties file.</div>
</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setOutputFile-java.io.File-">setOutputFile</a></span>&#8203;(java.io.File&nbsp;outputFile)</code></th>
<td class="colLast">
<div class="block"><span class="deprecatedLabel">Deprecated.</span></div>
</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setOutputFile-java.lang.Object-">setOutputFile</a></span>&#8203;(java.lang.Object&nbsp;outputFile)</code></th>
<td class="colLast">
<div class="block"><span class="deprecatedLabel">Deprecated.</span></div>
</td>
</tr>
<tr id="i13" class="rowColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setProperties-java.util.Map-">setProperties</a></span>&#8203;(java.util.Map&lt;java.lang.String,&#8203;java.lang.Object&gt;&nbsp;properties)</code></th>
<td class="colLast">
<div class="block">Sets all properties to be written to the properties file replacing any existing properties.</div>
</td>
</tr>
<tr id="i14" class="altColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#writeProperties--">writeProperties</a></span>()</code></th>
<td class="colLast">&nbsp;</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.org.gradle.api.DefaultTask">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;org.gradle.api.<a href="../DefaultTask.html" title="class in org.gradle.api">DefaultTask</a></h3>
<code><a href="../DefaultTask.html#compareTo-org.gradle.api.Task-">compareTo</a>, <a href="../DefaultTask.html#configure-groovy.lang.Closure-">configure</a>, <a href="../DefaultTask.html#dependsOn-java.lang.Object...-">dependsOn</a>, <a href="../DefaultTask.html#doFirst-groovy.lang.Closure-">doFirst</a>, <a href="../DefaultTask.html#doFirst-java.lang.String-org.gradle.api.Action-">doFirst</a>, <a href="../DefaultTask.html#doFirst-org.gradle.api.Action-">doFirst</a>, <a href="../DefaultTask.html#doLast-groovy.lang.Closure-">doLast</a>, <a href="../DefaultTask.html#doLast-java.lang.String-org.gradle.api.Action-">doLast</a>, <a href="../DefaultTask.html#doLast-org.gradle.api.Action-">doLast</a>, <a href="../DefaultTask.html#finalizedBy-java.lang.Object...-">finalizedBy</a>, <a href="../DefaultTask.html#getActions--">getActions</a>, <a href="../DefaultTask.html#getAnt--">getAnt</a>, <a href="../DefaultTask.html#getDependsOn--">getDependsOn</a>, <a href="../DefaultTask.html#getDescription--">getDescription</a>, <a href="../DefaultTask.html#getDestroyables--">getDestroyables</a>, <a href="../DefaultTask.html#getDidWork--">getDidWork</a>, <a href="../DefaultTask.html#getEnabled--">getEnabled</a>, <a href="../DefaultTask.html#getExtensions--">getExtensions</a>, <a href="../DefaultTask.html#getFinalizedBy--">getFinalizedBy</a>, <a href="../DefaultTask.html#getGroup--">getGroup</a>, <a href="../DefaultTask.html#getInputs--">getInputs</a>, <a href="../DefaultTask.html#getLocalState--">getLocalState</a>, <a href="../DefaultTask.html#getLogger--">getLogger</a>, <a href="../DefaultTask.html#getLogging--">getLogging</a>, <a href="../DefaultTask.html#getMustRunAfter--">getMustRunAfter</a>, <a href="../DefaultTask.html#getName--">getName</a>, <a href="../DefaultTask.html#getOutputs--">getOutputs</a>, <a href="../DefaultTask.html#getPath--">getPath</a>, <a href="../DefaultTask.html#getProject--">getProject</a>, <a href="../DefaultTask.html#getShouldRunAfter--">getShouldRunAfter</a>, <a href="../DefaultTask.html#getState--">getState</a>, <a href="../DefaultTask.html#getTaskDependencies--">getTaskDependencies</a>, <a href="../DefaultTask.html#getTemporaryDir--">getTemporaryDir</a>, <a href="../DefaultTask.html#getTimeout--">getTimeout</a>, <a href="../DefaultTask.html#hasProperty-java.lang.String-">hasProperty</a>, <a href="../DefaultTask.html#mustRunAfter-java.lang.Object...-">mustRunAfter</a>, <a href="../DefaultTask.html#onlyIf-groovy.lang.Closure-">onlyIf</a>, <a href="../DefaultTask.html#onlyIf-java.lang.String-org.gradle.api.specs.Spec-">onlyIf</a>, <a href="../DefaultTask.html#onlyIf-org.gradle.api.specs.Spec-">onlyIf</a>, <a href="../DefaultTask.html#property-java.lang.String-">property</a>, <a href="../DefaultTask.html#setActions-java.util.List-">setActions</a>, <a href="../DefaultTask.html#setDependsOn-java.lang.Iterable-">setDependsOn</a>, <a href="../DefaultTask.html#setDescription-java.lang.String-">setDescription</a>, <a href="../DefaultTask.html#setDidWork-boolean-">setDidWork</a>, <a href="../DefaultTask.html#setEnabled-boolean-">setEnabled</a>, <a href="../DefaultTask.html#setFinalizedBy-java.lang.Iterable-">setFinalizedBy</a>, <a href="../DefaultTask.html#setGroup-java.lang.String-">setGroup</a>, <a href="../DefaultTask.html#setMustRunAfter-java.lang.Iterable-">setMustRunAfter</a>, <a href="../DefaultTask.html#setOnlyIf-groovy.lang.Closure-">setOnlyIf</a>, <a href="../DefaultTask.html#setOnlyIf-java.lang.String-org.gradle.api.specs.Spec-">setOnlyIf</a>, <a href="../DefaultTask.html#setOnlyIf-org.gradle.api.specs.Spec-">setOnlyIf</a>, <a href="../DefaultTask.html#setProperty-java.lang.String-java.lang.Object-">setProperty</a>, <a href="../DefaultTask.html#setShouldRunAfter-java.lang.Iterable-">setShouldRunAfter</a>, <a href="../DefaultTask.html#shouldRunAfter-java.lang.Object...-">shouldRunAfter</a>, <a href="../DefaultTask.html#usesService-org.gradle.api.provider.Provider-">usesService</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.org.gradle.api.internal.AbstractTask">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;org.gradle.api.internal.AbstractTask</h3>
<code>acceptServiceReferences, appendParallelSafeAction, doNotTrackState, getAsDynamicObject, getConvention, getIdentityPath, getImpliesSubProjects, getLifecycleDependencies, getOnlyIf, getReasonNotToTrackState, getReasonTaskIsIncompatibleWithConfigurationCache, getRequiredServices, getServices, getSharedResources, getStandardOutputCapture, getTaskActions, getTaskIdentity, getTemporaryDirFactory, hasTaskActions, injectIntoNewInstance, isCompatibleWithConfigurationCache, isEnabled, isHasCustomActions, notCompatibleWithConfigurationCache, prependParallelSafeAction, setImpliesSubProjects</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>clone, equals, finalize, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.org.gradle.api.Task">
<!--   -->
</a>
<h3>Methods inherited from interface&nbsp;org.gradle.api.<a href="../Task.html" title="interface in org.gradle.api">Task</a></h3>
<code><a href="../Task.html#doNotTrackState-java.lang.String-">doNotTrackState</a>, <a href="../Task.html#getConvention--">getConvention</a>, <a href="../Task.html#notCompatibleWithConfigurationCache-java.lang.String-">notCompatibleWithConfigurationCache</a></code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a name="WriteProperties--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>WriteProperties</h4>
<pre>public&nbsp;WriteProperties()</pre>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="getProperties--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getProperties</h4>
<pre class="methodSignature"><a href="Input.html" title="annotation in org.gradle.api.tasks">@Input</a>
public&nbsp;java.util.Map&lt;java.lang.String,&#8203;java.lang.String&gt;&nbsp;getProperties()</pre>
<div class="block">Returns an immutable view of properties to be written to the properties file.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>3.3</dd>
</dl>
</li>
</ul>
<a name="setProperties-java.util.Map-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setProperties</h4>
<pre class="methodSignature">public&nbsp;void&nbsp;setProperties&#8203;(java.util.Map&lt;java.lang.String,&#8203;java.lang.Object&gt;&nbsp;properties)</pre>
<div class="block">Sets all properties to be written to the properties file replacing any existing properties.</div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="#properties-java.util.Map-"><code>properties(Map)</code></a>, 
<a href="#property-java.lang.String-java.lang.Object-"><code>property(String, Object)</code></a></dd>
</dl>
</li>
</ul>
<a name="property-java.lang.String-java.lang.Object-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>property</h4>
<pre class="methodSignature">public&nbsp;void&nbsp;property&#8203;(java.lang.String&nbsp;name,
                     java.lang.Object&nbsp;value)</pre>
<div class="block">Adds a property to be written to the properties file.
 <p>
 A property's value will be coerced to a <code>String</code> with <code>String#valueOf(Object)</code> or a
 <code>Callable</code> returning a value to be coerced into a <code>String</code>.
 </p>
 <p>
 Values are not allowed to be null.
 </p></div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>name</code> - Name of the property</dd>
<dd><code>value</code> - Value of the property</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>3.4</dd>
</dl>
</li>
</ul>
<a name="properties-java.util.Map-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>properties</h4>
<pre class="methodSignature">public&nbsp;void&nbsp;properties&#8203;(java.util.Map&lt;java.lang.String,&#8203;java.lang.Object&gt;&nbsp;properties)</pre>
<div class="block">Adds multiple properties to be written to the properties file.
 <p>
 This is a convenience method for calling <a href="#property-java.lang.String-java.lang.Object-"><code>property(String, Object)</code></a> multiple times.
 </p></div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>properties</code> - Properties to be added</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>3.4</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="#property-java.lang.String-java.lang.Object-"><code>property(String, Object)</code></a></dd>
</dl>
</li>
</ul>
<a name="getLineSeparator--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getLineSeparator</h4>
<pre class="methodSignature"><a href="Input.html" title="annotation in org.gradle.api.tasks">@Input</a>
public&nbsp;java.lang.String&nbsp;getLineSeparator()</pre>
<div class="block">Returns the line separator to be used when creating the properties file.
 Defaults to `\n`.</div>
</li>
</ul>
<a name="setLineSeparator-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setLineSeparator</h4>
<pre class="methodSignature">public&nbsp;void&nbsp;setLineSeparator&#8203;(java.lang.String&nbsp;lineSeparator)</pre>
<div class="block">Sets the line separator to be used when creating the properties file.</div>
</li>
</ul>
<a name="getComment--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getComment</h4>
<pre class="methodSignature">@Nullable
<a href="Optional.html" title="annotation in org.gradle.api.tasks">@Optional</a>
<a href="Input.html" title="annotation in org.gradle.api.tasks">@Input</a>
public&nbsp;java.lang.String&nbsp;getComment()</pre>
<div class="block">Returns the optional comment to add at the beginning of the properties file.</div>
</li>
</ul>
<a name="setComment-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setComment</h4>
<pre class="methodSignature">public&nbsp;void&nbsp;setComment&#8203;(@Nullable
                       java.lang.String&nbsp;comment)</pre>
<div class="block">Sets the optional comment to add at the beginning of the properties file.</div>
</li>
</ul>
<a name="getEncoding--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getEncoding</h4>
<pre class="methodSignature"><a href="Input.html" title="annotation in org.gradle.api.tasks">@Input</a>
public&nbsp;java.lang.String&nbsp;getEncoding()</pre>
<div class="block">Returns the encoding used to write the properties file. Defaults to ISO_8859_1.
 If set to anything different, unicode escaping is turned off.</div>
</li>
</ul>
<a name="setEncoding-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setEncoding</h4>
<pre class="methodSignature">public&nbsp;void&nbsp;setEncoding&#8203;(java.lang.String&nbsp;encoding)</pre>
<div class="block">Sets the encoding used to write the properties file. Defaults to ISO_8859_1.
 If set to anything different, unicode escaping is turned off.</div>
</li>
</ul>
<a name="getOutputFile--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getOutputFile</h4>
<pre class="methodSignature"><a href="Internal.html" title="annotation in org.gradle.api.tasks">@Internal</a>
@Deprecated
public&nbsp;java.io.File&nbsp;getOutputFile()</pre>
<div class="deprecationBlock"><span class="deprecatedLabel">Deprecated.</span></div>
<div class="block">Returns the output file to write the properties to.</div>
</li>
</ul>
<a name="setOutputFile-java.io.File-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setOutputFile</h4>
<pre class="methodSignature">@Deprecated
public&nbsp;void&nbsp;setOutputFile&#8203;(java.io.File&nbsp;outputFile)</pre>
<div class="deprecationBlock"><span class="deprecatedLabel">Deprecated.</span></div>
<div class="block">Sets the output file to write the properties to.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>4.0</dd>
</dl>
</li>
</ul>
<a name="setOutputFile-java.lang.Object-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setOutputFile</h4>
<pre class="methodSignature">@Deprecated
public&nbsp;void&nbsp;setOutputFile&#8203;(java.lang.Object&nbsp;outputFile)</pre>
<div class="deprecationBlock"><span class="deprecatedLabel">Deprecated.</span></div>
<div class="block">Sets the output file to write the properties to.</div>
</li>
</ul>
<a name="getDestinationFile--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getDestinationFile</h4>
<pre class="methodSignature"><a href="OutputFile.html" title="annotation in org.gradle.api.tasks">@OutputFile</a>
public abstract&nbsp;<a href="../file/RegularFileProperty.html" title="interface in org.gradle.api.file">RegularFileProperty</a>&nbsp;getDestinationFile()</pre>
<div class="block">The output properties file.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>8.1</dd>
</dl>
</li>
</ul>
<a name="writeProperties--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>writeProperties</h4>
<pre class="methodSignature">public&nbsp;void&nbsp;writeProperties()
                     throws java.io.IOException</pre>
<dl>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code>java.io.IOException</code></dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li><a href="#nested.class.summary">Nested</a>&nbsp;|&nbsp;</li>
<li><a href="#field.summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
