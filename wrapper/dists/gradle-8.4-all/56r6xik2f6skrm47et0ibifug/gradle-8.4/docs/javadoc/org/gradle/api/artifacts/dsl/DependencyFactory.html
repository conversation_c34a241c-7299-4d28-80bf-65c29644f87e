<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc -->
<title>DependencyFactory (Gradle API 8.4)</title>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<link rel="stylesheet" type="text/css" href="../../../../../javadoc.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../jquery/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
<script type="text/javascript" src="../../../../../jquery/jszip/dist/jszip.min.js"></script>
<script type="text/javascript" src="../../../../../jquery/jszip-utils/dist/jszip-utils.min.js"></script>
<!--[if IE]>
<script type="text/javascript" src="../../../../../jquery/jszip-utils/dist/jszip-utils-ie.min.js"></script>
<![endif]-->
<script type="text/javascript" src="../../../../../jquery/jquery-3.6.0.min.js"></script>
<script type="text/javascript" src="../../../../../jquery/jquery-ui.min.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="DependencyFactory (Gradle API 8.4)";
        }
    }
    catch(err) {
    }
//-->
var data = {"i0":6,"i1":6,"i2":6,"i3":6,"i4":6,"i5":6,"i6":6,"i7":6};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],4:["t3","Abstract Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
var pathtoroot = "../../../../../";
var useModuleDirectories = false;
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="fixedNav">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../../allclasses.html">All&nbsp;Classes</a></li>
</ul>
<ul class="navListSearch">
<li><label for="search">SEARCH:</label>
<input type="text" id="search" value="search" disabled="disabled">
<input type="reset" id="reset" value="reset" disabled="disabled">
</li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
</div>
<div class="navPadding">&nbsp;</div>
<script type="text/javascript"><!--
$('.navPadding').css('padding-top', $('.fixedNav').css("height"));
//-->
</script>
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle"><span class="packageLabelInType">Package</span>&nbsp;<a href="package-summary.html">org.gradle.api.artifacts.dsl</a></div>
<h2 title="Interface DependencyFactory" class="title">Interface DependencyFactory</h2>
</div>
<div class="contentContainer">
<div class="description">
<ul class="blockList">
<li class="blockList">
<hr>
<pre><a href="../../NonExtensible.html" title="annotation in org.gradle.api">@NonExtensible</a>
<a href="../../Incubating.html" title="annotation in org.gradle.api">@Incubating</a>
public interface <span class="typeNameLabel">DependencyFactory</span></pre>
<div class="block">Factory class for creating <a href="../Dependency.html" title="interface in org.gradle.api.artifacts"><code>Dependency</code></a> instances, with strong typing.

 <p>
 An instance of the factory can be injected into a task, plugin or other object by annotating a public constructor or property getter method with <code>javax.inject.Inject</code>.
 It is also available via <a href="../../Project.html#getDependencyFactory--"><code>Project.getDependencyFactory()</code></a>.
 </p>

 <p>
 <b>Note:</b> This interface is not intended for implementation by build script or plugin authors.
 </p></div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>7.6</dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t3" class="tableTab"><span><a href="javascript:show(4);">Abstract Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colSecond" scope="col">Method</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code><a href="../ExternalModuleDependency.html" title="interface in org.gradle.api.artifacts">ExternalModuleDependency</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#create-java.lang.CharSequence-">create</a></span>&#8203;(java.lang.CharSequence&nbsp;dependencyNotation)</code></th>
<td class="colLast">
<div class="block">Create an <a href="../ExternalModuleDependency.html" title="interface in org.gradle.api.artifacts"><code>ExternalModuleDependency</code></a> from the <code>"<i>group</i>:<i>name</i>:<i>version</i>:<i>classifier</i>@<i>extension</i>"</code> notation.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code><a href="../ExternalModuleDependency.html" title="interface in org.gradle.api.artifacts">ExternalModuleDependency</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#create-java.lang.String-java.lang.String-java.lang.String-">create</a></span>&#8203;(java.lang.String&nbsp;group,
      java.lang.String&nbsp;name,
      java.lang.String&nbsp;version)</code></th>
<td class="colLast">
<div class="block">Create an <a href="../ExternalModuleDependency.html" title="interface in org.gradle.api.artifacts"><code>ExternalModuleDependency</code></a> from a series of strings.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code><a href="../ExternalModuleDependency.html" title="interface in org.gradle.api.artifacts">ExternalModuleDependency</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#create-java.lang.String-java.lang.String-java.lang.String-java.lang.String-java.lang.String-">create</a></span>&#8203;(java.lang.String&nbsp;group,
      java.lang.String&nbsp;name,
      java.lang.String&nbsp;version,
      java.lang.String&nbsp;classifier,
      java.lang.String&nbsp;extension)</code></th>
<td class="colLast">
<div class="block">Create an <a href="../ExternalModuleDependency.html" title="interface in org.gradle.api.artifacts"><code>ExternalModuleDependency</code></a> from a series of strings.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code><a href="../FileCollectionDependency.html" title="interface in org.gradle.api.artifacts">FileCollectionDependency</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#create-org.gradle.api.file.FileCollection-">create</a></span>&#8203;(<a href="../../file/FileCollection.html" title="interface in org.gradle.api.file">FileCollection</a>&nbsp;fileCollection)</code></th>
<td class="colLast">
<div class="block">Create a <a href="../FileCollectionDependency.html" title="interface in org.gradle.api.artifacts"><code>FileCollectionDependency</code></a> from a <a href="../../file/FileCollection.html" title="interface in org.gradle.api.file"><code>FileCollection</code></a>.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code><a href="../ProjectDependency.html" title="interface in org.gradle.api.artifacts">ProjectDependency</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#create-org.gradle.api.Project-">create</a></span>&#8203;(<a href="../../Project.html" title="interface in org.gradle.api">Project</a>&nbsp;project)</code></th>
<td class="colLast">
<div class="block">Create a <a href="../ProjectDependency.html" title="interface in org.gradle.api.artifacts"><code>ProjectDependency</code></a> from a <a href="../../Project.html" title="interface in org.gradle.api"><code>Project</code></a>.</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code><a href="../Dependency.html" title="interface in org.gradle.api.artifacts">Dependency</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#gradleApi--">gradleApi</a></span>()</code></th>
<td class="colLast">
<div class="block">Creates a dependency on the API of the current version of Gradle.</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code><a href="../Dependency.html" title="interface in org.gradle.api.artifacts">Dependency</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#gradleTestKit--">gradleTestKit</a></span>()</code></th>
<td class="colLast">
<div class="block">Creates a dependency on the <a href="https://docs.gradle.org/current/userguide/test_kit.html" target="_top">Gradle test-kit</a> API.</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code><a href="../Dependency.html" title="interface in org.gradle.api.artifacts">Dependency</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#localGroovy--">localGroovy</a></span>()</code></th>
<td class="colLast">
<div class="block">Creates a dependency on the version of Groovy that is distributed with the current version of Gradle.</div>
</td>
</tr>
</table>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="create-java.lang.CharSequence-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>create</h4>
<pre class="methodSignature"><a href="../ExternalModuleDependency.html" title="interface in org.gradle.api.artifacts">ExternalModuleDependency</a>&nbsp;create&#8203;(java.lang.CharSequence&nbsp;dependencyNotation)</pre>
<div class="block">Create an <a href="../ExternalModuleDependency.html" title="interface in org.gradle.api.artifacts"><code>ExternalModuleDependency</code></a> from the <code>"<i>group</i>:<i>name</i>:<i>version</i>:<i>classifier</i>@<i>extension</i>"</code> notation.

 <p>
 Classifier and extension may each separately be omitted. Version may be omitted if there is no classifier.
 </p></div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>dependencyNotation</code> - the dependency notation</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the new dependency</dd>
</dl>
</li>
</ul>
<a name="create-java.lang.String-java.lang.String-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>create</h4>
<pre class="methodSignature"><a href="../ExternalModuleDependency.html" title="interface in org.gradle.api.artifacts">ExternalModuleDependency</a>&nbsp;create&#8203;(@Nullable
                                java.lang.String&nbsp;group,
                                java.lang.String&nbsp;name,
                                @Nullable
                                java.lang.String&nbsp;version)</pre>
<div class="block">Create an <a href="../ExternalModuleDependency.html" title="interface in org.gradle.api.artifacts"><code>ExternalModuleDependency</code></a> from a series of strings.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>group</code> - the group (optional)</dd>
<dd><code>name</code> - the name</dd>
<dd><code>version</code> - the version (optional)</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the new dependency</dd>
</dl>
</li>
</ul>
<a name="create-java.lang.String-java.lang.String-java.lang.String-java.lang.String-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>create</h4>
<pre class="methodSignature"><a href="../ExternalModuleDependency.html" title="interface in org.gradle.api.artifacts">ExternalModuleDependency</a>&nbsp;create&#8203;(@Nullable
                                java.lang.String&nbsp;group,
                                java.lang.String&nbsp;name,
                                @Nullable
                                java.lang.String&nbsp;version,
                                @Nullable
                                java.lang.String&nbsp;classifier,
                                @Nullable
                                java.lang.String&nbsp;extension)</pre>
<div class="block">Create an <a href="../ExternalModuleDependency.html" title="interface in org.gradle.api.artifacts"><code>ExternalModuleDependency</code></a> from a series of strings.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>group</code> - the group (optional)</dd>
<dd><code>name</code> - the name</dd>
<dd><code>version</code> - the version (optional)</dd>
<dd><code>classifier</code> - the classifier (optional)</dd>
<dd><code>extension</code> - the extension (optional)</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the new dependency</dd>
</dl>
</li>
</ul>
<a name="create-org.gradle.api.file.FileCollection-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>create</h4>
<pre class="methodSignature"><a href="../FileCollectionDependency.html" title="interface in org.gradle.api.artifacts">FileCollectionDependency</a>&nbsp;create&#8203;(<a href="../../file/FileCollection.html" title="interface in org.gradle.api.file">FileCollection</a>&nbsp;fileCollection)</pre>
<div class="block">Create a <a href="../FileCollectionDependency.html" title="interface in org.gradle.api.artifacts"><code>FileCollectionDependency</code></a> from a <a href="../../file/FileCollection.html" title="interface in org.gradle.api.file"><code>FileCollection</code></a>.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>fileCollection</code> - the file collection</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the new dependency</dd>
</dl>
</li>
</ul>
<a name="create-org.gradle.api.Project-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>create</h4>
<pre class="methodSignature"><a href="../ProjectDependency.html" title="interface in org.gradle.api.artifacts">ProjectDependency</a>&nbsp;create&#8203;(<a href="../../Project.html" title="interface in org.gradle.api">Project</a>&nbsp;project)</pre>
<div class="block">Create a <a href="../ProjectDependency.html" title="interface in org.gradle.api.artifacts"><code>ProjectDependency</code></a> from a <a href="../../Project.html" title="interface in org.gradle.api"><code>Project</code></a>.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>project</code> - the project</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the new dependency</dd>
</dl>
</li>
</ul>
<a name="gradleApi--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>gradleApi</h4>
<pre class="methodSignature"><a href="../Dependency.html" title="interface in org.gradle.api.artifacts">Dependency</a>&nbsp;gradleApi()</pre>
<div class="block">Creates a dependency on the API of the current version of Gradle.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The dependency.</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>7.6</dd>
</dl>
</li>
</ul>
<a name="gradleTestKit--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>gradleTestKit</h4>
<pre class="methodSignature"><a href="../Dependency.html" title="interface in org.gradle.api.artifacts">Dependency</a>&nbsp;gradleTestKit()</pre>
<div class="block">Creates a dependency on the <a href="https://docs.gradle.org/current/userguide/test_kit.html" target="_top">Gradle test-kit</a> API.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The dependency.</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>7.6</dd>
</dl>
</li>
</ul>
<a name="localGroovy--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>localGroovy</h4>
<pre class="methodSignature"><a href="../Dependency.html" title="interface in org.gradle.api.artifacts">Dependency</a>&nbsp;localGroovy()</pre>
<div class="block">Creates a dependency on the version of Groovy that is distributed with the current version of Gradle.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The dependency.</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>7.6</dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../../allclasses.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
