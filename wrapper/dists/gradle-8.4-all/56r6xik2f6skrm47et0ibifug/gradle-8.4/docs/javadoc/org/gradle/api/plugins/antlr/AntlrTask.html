<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc -->
<title>AntlrTask (Gradle API 8.4)</title>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<link rel="stylesheet" type="text/css" href="../../../../../javadoc.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../jquery/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
<script type="text/javascript" src="../../../../../jquery/jszip/dist/jszip.min.js"></script>
<script type="text/javascript" src="../../../../../jquery/jszip-utils/dist/jszip-utils.min.js"></script>
<!--[if IE]>
<script type="text/javascript" src="../../../../../jquery/jszip-utils/dist/jszip-utils-ie.min.js"></script>
<![endif]-->
<script type="text/javascript" src="../../../../../jquery/jquery-3.6.0.min.js"></script>
<script type="text/javascript" src="../../../../../jquery/jquery-ui.min.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="AntlrTask (Gradle API 8.4)";
        }
    }
    catch(err) {
    }
//-->
var data = {"i0":10,"i1":10,"i2":10,"i3":10,"i4":10,"i5":10,"i6":10,"i7":10,"i8":10,"i9":10,"i10":10,"i11":10,"i12":10,"i13":10,"i14":10,"i15":10,"i16":10,"i17":10,"i18":10,"i19":10,"i20":10,"i21":10,"i22":10,"i23":10};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
var pathtoroot = "../../../../../";
var useModuleDirectories = false;
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="fixedNav">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../../allclasses.html">All&nbsp;Classes</a></li>
</ul>
<ul class="navListSearch">
<li><label for="search">SEARCH:</label>
<input type="text" id="search" value="search" disabled="disabled">
<input type="reset" id="reset" value="reset" disabled="disabled">
</li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li><a href="#nested.class.summary">Nested</a>&nbsp;|&nbsp;</li>
<li><a href="#field.summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
</div>
<div class="navPadding">&nbsp;</div>
<script type="text/javascript"><!--
$('.navPadding').css('padding-top', $('.fixedNav').css("height"));
//-->
</script>
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle"><span class="packageLabelInType">Package</span>&nbsp;<a href="package-summary.html">org.gradle.api.plugins.antlr</a></div>
<h2 title="Class AntlrTask" class="title">Class AntlrTask</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>org.gradle.api.internal.AbstractTask</li>
<li>
<ul class="inheritance">
<li><a href="../../DefaultTask.html" title="class in org.gradle.api">org.gradle.api.DefaultTask</a></li>
<li>
<ul class="inheritance">
<li>org.gradle.api.internal.ConventionTask</li>
<li>
<ul class="inheritance">
<li><a href="../../tasks/SourceTask.html" title="class in org.gradle.api.tasks">org.gradle.api.tasks.SourceTask</a></li>
<li>
<ul class="inheritance">
<li>org.gradle.api.plugins.antlr.AntlrTask</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Implemented Interfaces:</dt>
<dd><code>java.lang.Comparable&lt;<a href="../../Task.html" title="interface in org.gradle.api">Task</a>&gt;</code>, <code>org.gradle.api.internal.DynamicObjectAware</code>, <code>org.gradle.api.internal.IConventionAware</code>, <code>org.gradle.api.internal.TaskInternal</code>, <code><a href="../ExtensionAware.html" title="interface in org.gradle.api.plugins">ExtensionAware</a></code>, <code><a href="../../Task.html" title="interface in org.gradle.api">Task</a></code>, <code><a href="../../tasks/util/PatternFilterable.html" title="interface in org.gradle.api.tasks.util">PatternFilterable</a></code>, <code><a href="../../../util/Configurable.html" title="interface in org.gradle.util">Configurable</a>&lt;<a href="../../Task.html" title="interface in org.gradle.api">Task</a>&gt;</code></dd>
</dl>
<hr>
<pre><a href="../../NonNullApi.html" title="annotation in org.gradle.api">@NonNullApi</a>
<a href="../../tasks/CacheableTask.html" title="annotation in org.gradle.api.tasks">@CacheableTask</a>
public abstract class <span class="typeNameLabel">AntlrTask</span>
extends <a href="../../tasks/SourceTask.html" title="class in org.gradle.api.tasks">SourceTask</a></pre>
<div class="block">Generates parsers from Antlr grammars.</div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== NESTED CLASS SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="nested.class.summary">
<!--   -->
</a>
<h3>Nested Class Summary</h3>
<ul class="blockList">
<li class="blockList"><a name="nested.classes.inherited.from.class.org.gradle.api.Task">
<!--   -->
</a>
<h3>Nested classes/interfaces inherited from interface&nbsp;org.gradle.api.<a href="../../Task.html" title="interface in org.gradle.api">Task</a></h3>
<code><a href="../../Task.Namer.html" title="class in org.gradle.api">Task.Namer</a></code></li>
</ul>
</li>
</ul>
<!-- =========== FIELD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.summary">
<!--   -->
</a>
<h3>Field Summary</h3>
<ul class="blockList">
<li class="blockList"><a name="fields.inherited.from.class.org.gradle.api.Task">
<!--   -->
</a>
<h3>Fields inherited from interface&nbsp;org.gradle.api.<a href="../../Task.html" title="interface in org.gradle.api">Task</a></h3>
<code><a href="../../Task.html#TASK_ACTION">TASK_ACTION</a>, <a href="../../Task.html#TASK_CONSTRUCTOR_ARGS">TASK_CONSTRUCTOR_ARGS</a>, <a href="../../Task.html#TASK_DEPENDS_ON">TASK_DEPENDS_ON</a>, <a href="../../Task.html#TASK_DESCRIPTION">TASK_DESCRIPTION</a>, <a href="../../Task.html#TASK_GROUP">TASK_GROUP</a>, <a href="../../Task.html#TASK_NAME">TASK_NAME</a>, <a href="../../Task.html#TASK_OVERWRITE">TASK_OVERWRITE</a>, <a href="../../Task.html#TASK_TYPE">TASK_TYPE</a></code></li>
</ul>
</li>
</ul>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="memberSummary" summary="Constructor Summary table, listing constructors, and an explanation">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Constructor</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tr class="altColor">
<th class="colConstructorName" scope="row"><code><span class="memberNameLink"><a href="#AntlrTask--">AntlrTask</a></span>()</code></th>
<td class="colLast">&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colSecond" scope="col">Method</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#execute-org.gradle.work.InputChanges-">execute</a></span>&#8203;(<a href="../../../work/InputChanges.html" title="interface in org.gradle.work">InputChanges</a>&nbsp;inputChanges)</code></th>
<td class="colLast">
<div class="block">Generate the parsers.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code><a href="../../file/FileCollection.html" title="interface in org.gradle.api.file">FileCollection</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getAntlrClasspath--">getAntlrClasspath</a></span>()</code></th>
<td class="colLast">
<div class="block">Returns the classpath containing the Ant ANTLR task implementation.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>java.util.List&lt;java.lang.String&gt;</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getArguments--">getArguments</a></span>()</code></th>
<td class="colLast">
<div class="block">List of command-line arguments passed to the antlr process</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>protected org.gradle.internal.file.Deleter</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getDeleter--">getDeleter</a></span>()</code></th>
<td class="colLast">&nbsp;</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getMaxHeapSize--">getMaxHeapSize</a></span>()</code></th>
<td class="colLast">
<div class="block">The maximum heap size for the forked antlr process (ex: '1g').</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>java.io.File</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getOutputDirectory--">getOutputDirectory</a></span>()</code></th>
<td class="colLast">
<div class="block">Returns the directory to generate the parser source files into.</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>protected <a href="../../file/ProjectLayout.html" title="interface in org.gradle.api.file">ProjectLayout</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getProjectLayout--">getProjectLayout</a></span>()</code></th>
<td class="colLast">&nbsp;</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code><a href="../../file/FileTree.html" title="interface in org.gradle.api.file">FileTree</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getSource--">getSource</a></span>()</code></th>
<td class="colLast">
<div class="block">Returns the source for this task, after the include and exclude patterns have been applied.</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>protected <a href="../../file/FileCollection.html" title="interface in org.gradle.api.file">FileCollection</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getStableSources--">getStableSources</a></span>()</code></th>
<td class="colLast">
<div class="block">The sources for incremental change detection.</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>protected org.gradle.process.internal.worker.WorkerProcessFactory</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getWorkerProcessBuilderFactory--">getWorkerProcessBuilderFactory</a></span>()</code></th>
<td class="colLast">&nbsp;</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#isTrace--">isTrace</a></span>()</code></th>
<td class="colLast">
<div class="block">Specifies that all rules call <code>traceIn</code>/<code>traceOut</code>.</div>
</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#isTraceLexer--">isTraceLexer</a></span>()</code></th>
<td class="colLast">
<div class="block">Specifies that all lexer rules call <code>traceIn</code>/<code>traceOut</code>.</div>
</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#isTraceParser--">isTraceParser</a></span>()</code></th>
<td class="colLast">
<div class="block">Specifies that all parser rules call <code>traceIn</code>/<code>traceOut</code>.</div>
</td>
</tr>
<tr id="i13" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#isTraceTreeWalker--">isTraceTreeWalker</a></span>()</code></th>
<td class="colLast">
<div class="block">Specifies that all tree walker rules call <code>traceIn</code>/<code>traceOut</code>.</div>
</td>
</tr>
<tr id="i14" class="altColor">
<td class="colFirst"><code>protected void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setAntlrClasspath-org.gradle.api.file.FileCollection-">setAntlrClasspath</a></span>&#8203;(<a href="../../file/FileCollection.html" title="interface in org.gradle.api.file">FileCollection</a>&nbsp;antlrClasspath)</code></th>
<td class="colLast">
<div class="block">Specifies the classpath containing the Ant ANTLR task implementation.</div>
</td>
</tr>
<tr id="i15" class="rowColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setArguments-java.util.List-">setArguments</a></span>&#8203;(java.util.List&lt;java.lang.String&gt;&nbsp;arguments)</code></th>
<td class="colLast">&nbsp;</td>
</tr>
<tr id="i16" class="altColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setMaxHeapSize-java.lang.String-">setMaxHeapSize</a></span>&#8203;(java.lang.String&nbsp;maxHeapSize)</code></th>
<td class="colLast">&nbsp;</td>
</tr>
<tr id="i17" class="rowColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setOutputDirectory-java.io.File-">setOutputDirectory</a></span>&#8203;(java.io.File&nbsp;outputDirectory)</code></th>
<td class="colLast">
<div class="block">Specifies the directory to generate the parser source files into.</div>
</td>
</tr>
<tr id="i18" class="altColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setSource-java.lang.Object-">setSource</a></span>&#8203;(java.lang.Object&nbsp;source)</code></th>
<td class="colLast">
<div class="block">Sets the source for this task.</div>
</td>
</tr>
<tr id="i19" class="rowColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setSource-org.gradle.api.file.FileTree-">setSource</a></span>&#8203;(<a href="../../file/FileTree.html" title="interface in org.gradle.api.file">FileTree</a>&nbsp;source)</code></th>
<td class="colLast">
<div class="block">Sets the source for this task.</div>
</td>
</tr>
<tr id="i20" class="altColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setTrace-boolean-">setTrace</a></span>&#8203;(boolean&nbsp;trace)</code></th>
<td class="colLast">&nbsp;</td>
</tr>
<tr id="i21" class="rowColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setTraceLexer-boolean-">setTraceLexer</a></span>&#8203;(boolean&nbsp;traceLexer)</code></th>
<td class="colLast">&nbsp;</td>
</tr>
<tr id="i22" class="altColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setTraceParser-boolean-">setTraceParser</a></span>&#8203;(boolean&nbsp;traceParser)</code></th>
<td class="colLast">&nbsp;</td>
</tr>
<tr id="i23" class="rowColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setTraceTreeWalker-boolean-">setTraceTreeWalker</a></span>&#8203;(boolean&nbsp;traceTreeWalker)</code></th>
<td class="colLast">&nbsp;</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.org.gradle.api.tasks.SourceTask">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;org.gradle.api.tasks.<a href="../../tasks/SourceTask.html" title="class in org.gradle.api.tasks">SourceTask</a></h3>
<code><a href="../../tasks/SourceTask.html#exclude-groovy.lang.Closure-">exclude</a>, <a href="../../tasks/SourceTask.html#exclude-java.lang.Iterable-">exclude</a>, <a href="../../tasks/SourceTask.html#exclude-java.lang.String...-">exclude</a>, <a href="../../tasks/SourceTask.html#exclude-org.gradle.api.specs.Spec-">exclude</a>, <a href="../../tasks/SourceTask.html#getExcludes--">getExcludes</a>, <a href="../../tasks/SourceTask.html#getIncludes--">getIncludes</a>, <a href="../../tasks/SourceTask.html#getPatternSet--">getPatternSet</a>, <a href="../../tasks/SourceTask.html#getPatternSetFactory--">getPatternSetFactory</a>, <a href="../../tasks/SourceTask.html#include-groovy.lang.Closure-">include</a>, <a href="../../tasks/SourceTask.html#include-java.lang.Iterable-">include</a>, <a href="../../tasks/SourceTask.html#include-java.lang.String...-">include</a>, <a href="../../tasks/SourceTask.html#include-org.gradle.api.specs.Spec-">include</a>, <a href="../../tasks/SourceTask.html#setExcludes-java.lang.Iterable-">setExcludes</a>, <a href="../../tasks/SourceTask.html#setIncludes-java.lang.Iterable-">setIncludes</a>, <a href="../../tasks/SourceTask.html#source-java.lang.Object...-">source</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.org.gradle.api.internal.ConventionTask">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;org.gradle.api.internal.ConventionTask</h3>
<code>conventionMapping, conventionMapping, getConventionMapping</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.org.gradle.api.DefaultTask">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;org.gradle.api.<a href="../../DefaultTask.html" title="class in org.gradle.api">DefaultTask</a></h3>
<code><a href="../../DefaultTask.html#compareTo-org.gradle.api.Task-">compareTo</a>, <a href="../../DefaultTask.html#configure-groovy.lang.Closure-">configure</a>, <a href="../../DefaultTask.html#dependsOn-java.lang.Object...-">dependsOn</a>, <a href="../../DefaultTask.html#doFirst-groovy.lang.Closure-">doFirst</a>, <a href="../../DefaultTask.html#doFirst-java.lang.String-org.gradle.api.Action-">doFirst</a>, <a href="../../DefaultTask.html#doFirst-org.gradle.api.Action-">doFirst</a>, <a href="../../DefaultTask.html#doLast-groovy.lang.Closure-">doLast</a>, <a href="../../DefaultTask.html#doLast-java.lang.String-org.gradle.api.Action-">doLast</a>, <a href="../../DefaultTask.html#doLast-org.gradle.api.Action-">doLast</a>, <a href="../../DefaultTask.html#finalizedBy-java.lang.Object...-">finalizedBy</a>, <a href="../../DefaultTask.html#getActions--">getActions</a>, <a href="../../DefaultTask.html#getAnt--">getAnt</a>, <a href="../../DefaultTask.html#getDependsOn--">getDependsOn</a>, <a href="../../DefaultTask.html#getDescription--">getDescription</a>, <a href="../../DefaultTask.html#getDestroyables--">getDestroyables</a>, <a href="../../DefaultTask.html#getDidWork--">getDidWork</a>, <a href="../../DefaultTask.html#getEnabled--">getEnabled</a>, <a href="../../DefaultTask.html#getExtensions--">getExtensions</a>, <a href="../../DefaultTask.html#getFinalizedBy--">getFinalizedBy</a>, <a href="../../DefaultTask.html#getGroup--">getGroup</a>, <a href="../../DefaultTask.html#getInputs--">getInputs</a>, <a href="../../DefaultTask.html#getLocalState--">getLocalState</a>, <a href="../../DefaultTask.html#getLogger--">getLogger</a>, <a href="../../DefaultTask.html#getLogging--">getLogging</a>, <a href="../../DefaultTask.html#getMustRunAfter--">getMustRunAfter</a>, <a href="../../DefaultTask.html#getName--">getName</a>, <a href="../../DefaultTask.html#getOutputs--">getOutputs</a>, <a href="../../DefaultTask.html#getPath--">getPath</a>, <a href="../../DefaultTask.html#getProject--">getProject</a>, <a href="../../DefaultTask.html#getShouldRunAfter--">getShouldRunAfter</a>, <a href="../../DefaultTask.html#getState--">getState</a>, <a href="../../DefaultTask.html#getTaskDependencies--">getTaskDependencies</a>, <a href="../../DefaultTask.html#getTemporaryDir--">getTemporaryDir</a>, <a href="../../DefaultTask.html#getTimeout--">getTimeout</a>, <a href="../../DefaultTask.html#hasProperty-java.lang.String-">hasProperty</a>, <a href="../../DefaultTask.html#mustRunAfter-java.lang.Object...-">mustRunAfter</a>, <a href="../../DefaultTask.html#onlyIf-groovy.lang.Closure-">onlyIf</a>, <a href="../../DefaultTask.html#onlyIf-java.lang.String-org.gradle.api.specs.Spec-">onlyIf</a>, <a href="../../DefaultTask.html#onlyIf-org.gradle.api.specs.Spec-">onlyIf</a>, <a href="../../DefaultTask.html#property-java.lang.String-">property</a>, <a href="../../DefaultTask.html#setActions-java.util.List-">setActions</a>, <a href="../../DefaultTask.html#setDependsOn-java.lang.Iterable-">setDependsOn</a>, <a href="../../DefaultTask.html#setDescription-java.lang.String-">setDescription</a>, <a href="../../DefaultTask.html#setDidWork-boolean-">setDidWork</a>, <a href="../../DefaultTask.html#setEnabled-boolean-">setEnabled</a>, <a href="../../DefaultTask.html#setFinalizedBy-java.lang.Iterable-">setFinalizedBy</a>, <a href="../../DefaultTask.html#setGroup-java.lang.String-">setGroup</a>, <a href="../../DefaultTask.html#setMustRunAfter-java.lang.Iterable-">setMustRunAfter</a>, <a href="../../DefaultTask.html#setOnlyIf-groovy.lang.Closure-">setOnlyIf</a>, <a href="../../DefaultTask.html#setOnlyIf-java.lang.String-org.gradle.api.specs.Spec-">setOnlyIf</a>, <a href="../../DefaultTask.html#setOnlyIf-org.gradle.api.specs.Spec-">setOnlyIf</a>, <a href="../../DefaultTask.html#setProperty-java.lang.String-java.lang.Object-">setProperty</a>, <a href="../../DefaultTask.html#setShouldRunAfter-java.lang.Iterable-">setShouldRunAfter</a>, <a href="../../DefaultTask.html#shouldRunAfter-java.lang.Object...-">shouldRunAfter</a>, <a href="../../DefaultTask.html#usesService-org.gradle.api.provider.Provider-">usesService</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.org.gradle.api.internal.AbstractTask">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;org.gradle.api.internal.AbstractTask</h3>
<code>acceptServiceReferences, appendParallelSafeAction, doNotTrackState, getAsDynamicObject, getConvention, getIdentityPath, getImpliesSubProjects, getLifecycleDependencies, getOnlyIf, getReasonNotToTrackState, getReasonTaskIsIncompatibleWithConfigurationCache, getRequiredServices, getServices, getSharedResources, getStandardOutputCapture, getTaskActions, getTaskIdentity, getTemporaryDirFactory, hasTaskActions, injectIntoNewInstance, isCompatibleWithConfigurationCache, isEnabled, isHasCustomActions, notCompatibleWithConfigurationCache, prependParallelSafeAction, setImpliesSubProjects</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>clone, equals, finalize, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.org.gradle.api.Task">
<!--   -->
</a>
<h3>Methods inherited from interface&nbsp;org.gradle.api.<a href="../../Task.html" title="interface in org.gradle.api">Task</a></h3>
<code><a href="../../Task.html#doNotTrackState-java.lang.String-">doNotTrackState</a>, <a href="../../Task.html#getConvention--">getConvention</a>, <a href="../../Task.html#notCompatibleWithConfigurationCache-java.lang.String-">notCompatibleWithConfigurationCache</a></code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a name="AntlrTask--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>AntlrTask</h4>
<pre>public&nbsp;AntlrTask()</pre>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="isTrace--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isTrace</h4>
<pre class="methodSignature"><a href="../../tasks/Input.html" title="annotation in org.gradle.api.tasks">@Input</a>
public&nbsp;boolean&nbsp;isTrace()</pre>
<div class="block">Specifies that all rules call <code>traceIn</code>/<code>traceOut</code>.</div>
</li>
</ul>
<a name="setTrace-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setTrace</h4>
<pre class="methodSignature">public&nbsp;void&nbsp;setTrace&#8203;(boolean&nbsp;trace)</pre>
</li>
</ul>
<a name="isTraceLexer--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isTraceLexer</h4>
<pre class="methodSignature"><a href="../../tasks/Input.html" title="annotation in org.gradle.api.tasks">@Input</a>
public&nbsp;boolean&nbsp;isTraceLexer()</pre>
<div class="block">Specifies that all lexer rules call <code>traceIn</code>/<code>traceOut</code>.</div>
</li>
</ul>
<a name="setTraceLexer-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setTraceLexer</h4>
<pre class="methodSignature">public&nbsp;void&nbsp;setTraceLexer&#8203;(boolean&nbsp;traceLexer)</pre>
</li>
</ul>
<a name="isTraceParser--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isTraceParser</h4>
<pre class="methodSignature"><a href="../../tasks/Input.html" title="annotation in org.gradle.api.tasks">@Input</a>
public&nbsp;boolean&nbsp;isTraceParser()</pre>
<div class="block">Specifies that all parser rules call <code>traceIn</code>/<code>traceOut</code>.</div>
</li>
</ul>
<a name="setTraceParser-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setTraceParser</h4>
<pre class="methodSignature">public&nbsp;void&nbsp;setTraceParser&#8203;(boolean&nbsp;traceParser)</pre>
</li>
</ul>
<a name="isTraceTreeWalker--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isTraceTreeWalker</h4>
<pre class="methodSignature"><a href="../../tasks/Input.html" title="annotation in org.gradle.api.tasks">@Input</a>
public&nbsp;boolean&nbsp;isTraceTreeWalker()</pre>
<div class="block">Specifies that all tree walker rules call <code>traceIn</code>/<code>traceOut</code>.</div>
</li>
</ul>
<a name="setTraceTreeWalker-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setTraceTreeWalker</h4>
<pre class="methodSignature">public&nbsp;void&nbsp;setTraceTreeWalker&#8203;(boolean&nbsp;traceTreeWalker)</pre>
</li>
</ul>
<a name="getMaxHeapSize--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getMaxHeapSize</h4>
<pre class="methodSignature"><a href="../../tasks/Internal.html" title="annotation in org.gradle.api.tasks">@Internal</a>
public&nbsp;java.lang.String&nbsp;getMaxHeapSize()</pre>
<div class="block">The maximum heap size for the forked antlr process (ex: '1g').</div>
</li>
</ul>
<a name="setMaxHeapSize-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setMaxHeapSize</h4>
<pre class="methodSignature">public&nbsp;void&nbsp;setMaxHeapSize&#8203;(java.lang.String&nbsp;maxHeapSize)</pre>
</li>
</ul>
<a name="setArguments-java.util.List-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setArguments</h4>
<pre class="methodSignature">public&nbsp;void&nbsp;setArguments&#8203;(java.util.List&lt;java.lang.String&gt;&nbsp;arguments)</pre>
</li>
</ul>
<a name="getArguments--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getArguments</h4>
<pre class="methodSignature"><a href="../../tasks/Input.html" title="annotation in org.gradle.api.tasks">@Input</a>
public&nbsp;java.util.List&lt;java.lang.String&gt;&nbsp;getArguments()</pre>
<div class="block">List of command-line arguments passed to the antlr process</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The antlr command-line arguments</dd>
</dl>
</li>
</ul>
<a name="getOutputDirectory--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getOutputDirectory</h4>
<pre class="methodSignature"><a href="../../tasks/OutputDirectory.html" title="annotation in org.gradle.api.tasks">@OutputDirectory</a>
public&nbsp;java.io.File&nbsp;getOutputDirectory()</pre>
<div class="block">Returns the directory to generate the parser source files into.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The output directory.</dd>
</dl>
</li>
</ul>
<a name="setOutputDirectory-java.io.File-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setOutputDirectory</h4>
<pre class="methodSignature">public&nbsp;void&nbsp;setOutputDirectory&#8203;(java.io.File&nbsp;outputDirectory)</pre>
<div class="block">Specifies the directory to generate the parser source files into.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>outputDirectory</code> - The output directory. Must not be null.</dd>
</dl>
</li>
</ul>
<a name="getAntlrClasspath--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getAntlrClasspath</h4>
<pre class="methodSignature"><a href="../../tasks/Classpath.html" title="annotation in org.gradle.api.tasks">@Classpath</a>
public&nbsp;<a href="../../file/FileCollection.html" title="interface in org.gradle.api.file">FileCollection</a>&nbsp;getAntlrClasspath()</pre>
<div class="block">Returns the classpath containing the Ant ANTLR task implementation.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The Ant task implementation classpath.</dd>
</dl>
</li>
</ul>
<a name="setAntlrClasspath-org.gradle.api.file.FileCollection-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setAntlrClasspath</h4>
<pre class="methodSignature">protected&nbsp;void&nbsp;setAntlrClasspath&#8203;(<a href="../../file/FileCollection.html" title="interface in org.gradle.api.file">FileCollection</a>&nbsp;antlrClasspath)</pre>
<div class="block">Specifies the classpath containing the Ant ANTLR task implementation.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>antlrClasspath</code> - The Ant task implementation classpath. Must not be null.</dd>
</dl>
</li>
</ul>
<a name="getWorkerProcessBuilderFactory--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getWorkerProcessBuilderFactory</h4>
<pre class="methodSignature">@Inject
protected&nbsp;org.gradle.process.internal.worker.WorkerProcessFactory&nbsp;getWorkerProcessBuilderFactory()</pre>
</li>
</ul>
<a name="getProjectLayout--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getProjectLayout</h4>
<pre class="methodSignature">@Inject
protected&nbsp;<a href="../../file/ProjectLayout.html" title="interface in org.gradle.api.file">ProjectLayout</a>&nbsp;getProjectLayout()</pre>
</li>
</ul>
<a name="execute-org.gradle.work.InputChanges-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>execute</h4>
<pre class="methodSignature">public&nbsp;void&nbsp;execute&#8203;(<a href="../../../work/InputChanges.html" title="interface in org.gradle.work">InputChanges</a>&nbsp;inputChanges)</pre>
<div class="block">Generate the parsers.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>6.0</dd>
</dl>
</li>
</ul>
<a name="setSource-org.gradle.api.file.FileTree-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setSource</h4>
<pre class="methodSignature">public&nbsp;void&nbsp;setSource&#8203;(<a href="../../file/FileTree.html" title="interface in org.gradle.api.file">FileTree</a>&nbsp;source)</pre>
<div class="block">Sets the source for this task. Delegates to <a href="#setSource-java.lang.Object-"><code>setSource(Object)</code></a>.

 If the source is of type <a href="../../file/SourceDirectorySet.html" title="interface in org.gradle.api.file"><code>SourceDirectorySet</code></a>, then the relative path of each source grammar files
 is used to determine the relative output path of the generated source
 If the source is not of type <a href="../../file/SourceDirectorySet.html" title="interface in org.gradle.api.file"><code>SourceDirectorySet</code></a>, then the generated source files end up
 flattened in the specified output directory.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code><a href="../../tasks/SourceTask.html#setSource-org.gradle.api.file.FileTree-">setSource</a></code>&nbsp;in class&nbsp;<code><a href="../../tasks/SourceTask.html" title="class in org.gradle.api.tasks">SourceTask</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>source</code> - The source.</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>4.0</dd>
</dl>
</li>
</ul>
<a name="setSource-java.lang.Object-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setSource</h4>
<pre class="methodSignature">public&nbsp;void&nbsp;setSource&#8203;(java.lang.Object&nbsp;source)</pre>
<div class="block">Sets the source for this task. Delegates to <a href="../../tasks/SourceTask.html#setSource-java.lang.Object-"><code>SourceTask.setSource(Object)</code></a>.

 If the source is of type <a href="../../file/SourceDirectorySet.html" title="interface in org.gradle.api.file"><code>SourceDirectorySet</code></a>, then the relative path of each source grammar files
 is used to determine the relative output path of the generated source
 If the source is not of type <a href="../../file/SourceDirectorySet.html" title="interface in org.gradle.api.file"><code>SourceDirectorySet</code></a>, then the generated source files end up
 flattened in the specified output directory.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code><a href="../../tasks/SourceTask.html#setSource-java.lang.Object-">setSource</a></code>&nbsp;in class&nbsp;<code><a href="../../tasks/SourceTask.html" title="class in org.gradle.api.tasks">SourceTask</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>source</code> - The source.</dd>
</dl>
</li>
</ul>
<a name="getSource--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getSource</h4>
<pre class="methodSignature"><a href="../../tasks/Internal.html" title="annotation in org.gradle.api.tasks">@Internal</a>("tracked via stableSources")
public&nbsp;<a href="../../file/FileTree.html" title="interface in org.gradle.api.file">FileTree</a>&nbsp;getSource()</pre>
<div class="block">Returns the source for this task, after the include and exclude patterns have been applied. Ignores source files which do not exist.

 <p>
 The <a href="../../tasks/PathSensitivity.html" title="enum in org.gradle.api.tasks"><code>PathSensitivity</code></a> for the sources is configured to be <a href="../../tasks/PathSensitivity.html#ABSOLUTE"><code>PathSensitivity.ABSOLUTE</code></a>.
 If your sources are less strict, please change it accordingly by overriding this method in your subclass.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code><a href="../../tasks/SourceTask.html#getSource--">getSource</a></code>&nbsp;in class&nbsp;<code><a href="../../tasks/SourceTask.html" title="class in org.gradle.api.tasks">SourceTask</a></code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The source.</dd>
</dl>
</li>
</ul>
<a name="getStableSources--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getStableSources</h4>
<pre class="methodSignature"><a href="../../tasks/SkipWhenEmpty.html" title="annotation in org.gradle.api.tasks">@SkipWhenEmpty</a>
<a href="../../tasks/IgnoreEmptyDirectories.html" title="annotation in org.gradle.api.tasks">@IgnoreEmptyDirectories</a>
<a href="../../tasks/PathSensitive.html" title="annotation in org.gradle.api.tasks">@PathSensitive</a>(<a href="../../tasks/PathSensitivity.html#RELATIVE">RELATIVE</a>)
<a href="../../tasks/InputFiles.html" title="annotation in org.gradle.api.tasks">@InputFiles</a>
protected&nbsp;<a href="../../file/FileCollection.html" title="interface in org.gradle.api.file">FileCollection</a>&nbsp;getStableSources()</pre>
<div class="block">The sources for incremental change detection.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>6.0</dd>
</dl>
</li>
</ul>
<a name="getDeleter--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>getDeleter</h4>
<pre class="methodSignature">@Inject
protected&nbsp;org.gradle.internal.file.Deleter&nbsp;getDeleter()</pre>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../../allclasses.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li><a href="#nested.class.summary">Nested</a>&nbsp;|&nbsp;</li>
<li><a href="#field.summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
