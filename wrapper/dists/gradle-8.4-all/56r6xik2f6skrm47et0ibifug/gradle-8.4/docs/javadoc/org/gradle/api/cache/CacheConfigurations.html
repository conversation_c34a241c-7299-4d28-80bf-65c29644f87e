<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc -->
<title>CacheConfigurations (Gradle API 8.4)</title>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<link rel="stylesheet" type="text/css" href="../../../../javadoc.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../jquery/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
<script type="text/javascript" src="../../../../jquery/jszip/dist/jszip.min.js"></script>
<script type="text/javascript" src="../../../../jquery/jszip-utils/dist/jszip-utils.min.js"></script>
<!--[if IE]>
<script type="text/javascript" src="../../../../jquery/jszip-utils/dist/jszip-utils-ie.min.js"></script>
<![endif]-->
<script type="text/javascript" src="../../../../jquery/jquery-3.6.0.min.js"></script>
<script type="text/javascript" src="../../../../jquery/jquery-ui.min.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="CacheConfigurations (Gradle API 8.4)";
        }
    }
    catch(err) {
    }
//-->
var data = {"i0":6,"i1":6,"i2":6,"i3":6,"i4":6,"i5":6,"i6":6,"i7":6,"i8":6,"i9":6};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],4:["t3","Abstract Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
var pathtoroot = "../../../../";
var useModuleDirectories = false;
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="fixedNav">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses.html">All&nbsp;Classes</a></li>
</ul>
<ul class="navListSearch">
<li><label for="search">SEARCH:</label>
<input type="text" id="search" value="search" disabled="disabled">
<input type="reset" id="reset" value="reset" disabled="disabled">
</li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
</div>
<div class="navPadding">&nbsp;</div>
<script type="text/javascript"><!--
$('.navPadding').css('padding-top', $('.fixedNav').css("height"));
//-->
</script>
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle"><span class="packageLabelInType">Package</span>&nbsp;<a href="package-summary.html">org.gradle.api.cache</a></div>
<h2 title="Interface CacheConfigurations" class="title">Interface CacheConfigurations</h2>
</div>
<div class="contentContainer">
<div class="description">
<ul class="blockList">
<li class="blockList">
<hr>
<pre><a href="../Incubating.html" title="annotation in org.gradle.api">@Incubating</a>
public interface <span class="typeNameLabel">CacheConfigurations</span></pre>
<div class="block">Configures caches stored in the user home directory.  Note that these values can be read at any time,
 but can only be configured via an init script, ideally stored in the init.d directory in the user home
 directory.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>8.0</dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t3" class="tableTab"><span><a href="javascript:show(4);">Abstract Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colSecond" scope="col">Method</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#createdResources-org.gradle.api.Action-">createdResources</a></span>&#8203;(<a href="../Action.html" title="interface in org.gradle.api">Action</a>&lt;? super <a href="CacheResourceConfiguration.html" title="interface in org.gradle.api.cache">CacheResourceConfiguration</a>&gt;&nbsp;cacheConfiguration)</code></th>
<td class="colLast">
<div class="block">Configures caching for resources that are created by Gradle during the course of a build.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#downloadedResources-org.gradle.api.Action-">downloadedResources</a></span>&#8203;(<a href="../Action.html" title="interface in org.gradle.api">Action</a>&lt;? super <a href="CacheResourceConfiguration.html" title="interface in org.gradle.api.cache">CacheResourceConfiguration</a>&gt;&nbsp;cacheConfiguration)</code></th>
<td class="colLast">
<div class="block">Configures caching for resources that are downloaded during Gradle builds.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code><a href="../provider/Property.html" title="interface in org.gradle.api.provider">Property</a>&lt;<a href="Cleanup.html" title="interface in org.gradle.api.cache">Cleanup</a>&gt;</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getCleanup--">getCleanup</a></span>()</code></th>
<td class="colLast">
<div class="block">Returns the cache cleanup settings that apply to all caches.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code><a href="CacheResourceConfiguration.html" title="interface in org.gradle.api.cache">CacheResourceConfiguration</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getCreatedResources--">getCreatedResources</a></span>()</code></th>
<td class="colLast">
<div class="block">Returns the cache configuration for created resources.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code><a href="CacheResourceConfiguration.html" title="interface in org.gradle.api.cache">CacheResourceConfiguration</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getDownloadedResources--">getDownloadedResources</a></span>()</code></th>
<td class="colLast">
<div class="block">Returns the cache configuration for downloaded resources.</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code><a href="../provider/Property.html" title="interface in org.gradle.api.provider">Property</a>&lt;<a href="MarkingStrategy.html" title="interface in org.gradle.api.cache">MarkingStrategy</a>&gt;</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getMarkingStrategy--">getMarkingStrategy</a></span>()</code></th>
<td class="colLast">
<div class="block">Configures how caches should be marked, if at all.</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code><a href="CacheResourceConfiguration.html" title="interface in org.gradle.api.cache">CacheResourceConfiguration</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getReleasedWrappers--">getReleasedWrappers</a></span>()</code></th>
<td class="colLast">
<div class="block">Returns the cache configuration for wrapper distributions that are released Gradle versions.</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code><a href="CacheResourceConfiguration.html" title="interface in org.gradle.api.cache">CacheResourceConfiguration</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getSnapshotWrappers--">getSnapshotWrappers</a></span>()</code></th>
<td class="colLast">
<div class="block">Returns the cache configuration for wrapper distributions that are released Gradle versions.</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#releasedWrappers-org.gradle.api.Action-">releasedWrappers</a></span>&#8203;(<a href="../Action.html" title="interface in org.gradle.api">Action</a>&lt;? super <a href="CacheResourceConfiguration.html" title="interface in org.gradle.api.cache">CacheResourceConfiguration</a>&gt;&nbsp;cacheConfiguration)</code></th>
<td class="colLast">
<div class="block">Configures caching for wrapper distributions that are released Gradle versions.</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#snapshotWrappers-org.gradle.api.Action-">snapshotWrappers</a></span>&#8203;(<a href="../Action.html" title="interface in org.gradle.api">Action</a>&lt;? super <a href="CacheResourceConfiguration.html" title="interface in org.gradle.api.cache">CacheResourceConfiguration</a>&gt;&nbsp;cacheConfiguration)</code></th>
<td class="colLast">
<div class="block">Configures caching for wrapper distributions that are snapshot Gradle versions.</div>
</td>
</tr>
</table>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="releasedWrappers-org.gradle.api.Action-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>releasedWrappers</h4>
<pre class="methodSignature">void&nbsp;releasedWrappers&#8203;(<a href="../Action.html" title="interface in org.gradle.api">Action</a>&lt;? super <a href="CacheResourceConfiguration.html" title="interface in org.gradle.api.cache">CacheResourceConfiguration</a>&gt;&nbsp;cacheConfiguration)</pre>
<div class="block">Configures caching for wrapper distributions that are released Gradle versions.  By default, released
 distributions are removed after 30 days of not being used.</div>
</li>
</ul>
<a name="getReleasedWrappers--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getReleasedWrappers</h4>
<pre class="methodSignature"><a href="CacheResourceConfiguration.html" title="interface in org.gradle.api.cache">CacheResourceConfiguration</a>&nbsp;getReleasedWrappers()</pre>
<div class="block">Returns the cache configuration for wrapper distributions that are released Gradle versions.</div>
</li>
</ul>
<a name="snapshotWrappers-org.gradle.api.Action-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>snapshotWrappers</h4>
<pre class="methodSignature">void&nbsp;snapshotWrappers&#8203;(<a href="../Action.html" title="interface in org.gradle.api">Action</a>&lt;? super <a href="CacheResourceConfiguration.html" title="interface in org.gradle.api.cache">CacheResourceConfiguration</a>&gt;&nbsp;cacheConfiguration)</pre>
<div class="block">Configures caching for wrapper distributions that are snapshot Gradle versions.  By default, snapshot
 distributions are removed after 7 days of not being used.</div>
</li>
</ul>
<a name="getSnapshotWrappers--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getSnapshotWrappers</h4>
<pre class="methodSignature"><a href="CacheResourceConfiguration.html" title="interface in org.gradle.api.cache">CacheResourceConfiguration</a>&nbsp;getSnapshotWrappers()</pre>
<div class="block">Returns the cache configuration for wrapper distributions that are released Gradle versions.</div>
</li>
</ul>
<a name="downloadedResources-org.gradle.api.Action-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>downloadedResources</h4>
<pre class="methodSignature">void&nbsp;downloadedResources&#8203;(<a href="../Action.html" title="interface in org.gradle.api">Action</a>&lt;? super <a href="CacheResourceConfiguration.html" title="interface in org.gradle.api.cache">CacheResourceConfiguration</a>&gt;&nbsp;cacheConfiguration)</pre>
<div class="block">Configures caching for resources that are downloaded during Gradle builds.  By default, downloaded
 resources are removed after 30 days of not being used.</div>
</li>
</ul>
<a name="getDownloadedResources--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getDownloadedResources</h4>
<pre class="methodSignature"><a href="CacheResourceConfiguration.html" title="interface in org.gradle.api.cache">CacheResourceConfiguration</a>&nbsp;getDownloadedResources()</pre>
<div class="block">Returns the cache configuration for downloaded resources.</div>
</li>
</ul>
<a name="createdResources-org.gradle.api.Action-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createdResources</h4>
<pre class="methodSignature">void&nbsp;createdResources&#8203;(<a href="../Action.html" title="interface in org.gradle.api">Action</a>&lt;? super <a href="CacheResourceConfiguration.html" title="interface in org.gradle.api.cache">CacheResourceConfiguration</a>&gt;&nbsp;cacheConfiguration)</pre>
<div class="block">Configures caching for resources that are created by Gradle during the course of a build.  By default, created
 resources are removed after 7 days of not being used.</div>
</li>
</ul>
<a name="getCreatedResources--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCreatedResources</h4>
<pre class="methodSignature"><a href="CacheResourceConfiguration.html" title="interface in org.gradle.api.cache">CacheResourceConfiguration</a>&nbsp;getCreatedResources()</pre>
<div class="block">Returns the cache configuration for created resources.</div>
</li>
</ul>
<a name="getCleanup--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCleanup</h4>
<pre class="methodSignature"><a href="../provider/Property.html" title="interface in org.gradle.api.provider">Property</a>&lt;<a href="Cleanup.html" title="interface in org.gradle.api.cache">Cleanup</a>&gt;&nbsp;getCleanup()</pre>
<div class="block">Returns the cache cleanup settings that apply to all caches.</div>
</li>
</ul>
<a name="getMarkingStrategy--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>getMarkingStrategy</h4>
<pre class="methodSignature"><a href="../provider/Property.html" title="interface in org.gradle.api.provider">Property</a>&lt;<a href="MarkingStrategy.html" title="interface in org.gradle.api.cache">MarkingStrategy</a>&gt;&nbsp;getMarkingStrategy()</pre>
<div class="block">Configures how caches should be marked, if at all.

 <p>
 By default, caches are marked using <a href="MarkingStrategy.html#CACHEDIR_TAG"><code>MarkingStrategy.CACHEDIR_TAG</code></a>.
 </p></div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>8.1</dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
