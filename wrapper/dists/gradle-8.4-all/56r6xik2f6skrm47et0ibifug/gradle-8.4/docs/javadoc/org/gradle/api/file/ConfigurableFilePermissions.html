<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc -->
<title>ConfigurableFilePermissions (Gradle API 8.4)</title>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<link rel="stylesheet" type="text/css" href="../../../../javadoc.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../jquery/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
<script type="text/javascript" src="../../../../jquery/jszip/dist/jszip.min.js"></script>
<script type="text/javascript" src="../../../../jquery/jszip-utils/dist/jszip-utils.min.js"></script>
<!--[if IE]>
<script type="text/javascript" src="../../../../jquery/jszip-utils/dist/jszip-utils-ie.min.js"></script>
<![endif]-->
<script type="text/javascript" src="../../../../jquery/jquery-3.6.0.min.js"></script>
<script type="text/javascript" src="../../../../jquery/jquery-ui.min.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="ConfigurableFilePermissions (Gradle API 8.4)";
        }
    }
    catch(err) {
    }
//-->
var data = {"i0":6,"i1":6,"i2":6,"i3":6,"i4":6,"i5":6,"i6":6,"i7":6};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],4:["t3","Abstract Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
var pathtoroot = "../../../../";
var useModuleDirectories = false;
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="fixedNav">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses.html">All&nbsp;Classes</a></li>
</ul>
<ul class="navListSearch">
<li><label for="search">SEARCH:</label>
<input type="text" id="search" value="search" disabled="disabled">
<input type="reset" id="reset" value="reset" disabled="disabled">
</li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
</div>
<div class="navPadding">&nbsp;</div>
<script type="text/javascript"><!--
$('.navPadding').css('padding-top', $('.fixedNav').css("height"));
//-->
</script>
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle"><span class="packageLabelInType">Package</span>&nbsp;<a href="package-summary.html">org.gradle.api.file</a></div>
<h2 title="Interface ConfigurableFilePermissions" class="title">Interface ConfigurableFilePermissions</h2>
</div>
<div class="contentContainer">
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Superinterfaces:</dt>
<dd><code><a href="FilePermissions.html" title="interface in org.gradle.api.file">FilePermissions</a></code></dd>
</dl>
<hr>
<pre><a href="../Incubating.html" title="annotation in org.gradle.api">@Incubating</a>
public interface <span class="typeNameLabel">ConfigurableFilePermissions</span>
extends <a href="FilePermissions.html" title="interface in org.gradle.api.file">FilePermissions</a></pre>
<div class="block">Provides the means of specifying file and directory access permissions for all classes of system users.
 <p>
 For details on classes of users and file/directory permissions see <a href="FilePermissions.html" title="interface in org.gradle.api.file"><code>FilePermissions</code></a>.
 <p>
 An example usage of this functionality would be configuring a copy task and explicitly specifying the destination file permissions:
 <pre>
 from(...)
 into(...)
 filePermissions {
     user {
         read = true
         execute = true
     }
     other.execute = false
 }
 </pre></div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>8.3</dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t3" class="tableTab"><span><a href="javascript:show(4);">Abstract Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colSecond" scope="col">Method</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code><a href="ConfigurableUserClassFilePermissions.html" title="interface in org.gradle.api.file">ConfigurableUserClassFilePermissions</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getGroup--">getGroup</a></span>()</code></th>
<td class="colLast">
<div class="block">Returns the permissions a user, who is a member of the group that the file/directory belongs to, has for the file/directory.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code><a href="ConfigurableUserClassFilePermissions.html" title="interface in org.gradle.api.file">ConfigurableUserClassFilePermissions</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getOther--">getOther</a></span>()</code></th>
<td class="colLast">
<div class="block">Returns the permissions all other users (non-owner, non-group) have for the file/directory.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code><a href="ConfigurableUserClassFilePermissions.html" title="interface in org.gradle.api.file">ConfigurableUserClassFilePermissions</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getUser--">getUser</a></span>()</code></th>
<td class="colLast">
<div class="block">Returns the permissions the owner of the file has for the file/directory.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#group-org.gradle.api.Action-">group</a></span>&#8203;(<a href="../Action.html" title="interface in org.gradle.api">Action</a>&lt;? super <a href="ConfigurableUserClassFilePermissions.html" title="interface in org.gradle.api.file">ConfigurableUserClassFilePermissions</a>&gt;&nbsp;configureAction)</code></th>
<td class="colLast">
<div class="block">Modifies the permissions a user, who is a member of the group that the file/directory belongs to, has for the file/directory.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#other-org.gradle.api.Action-">other</a></span>&#8203;(<a href="../Action.html" title="interface in org.gradle.api">Action</a>&lt;? super <a href="ConfigurableUserClassFilePermissions.html" title="interface in org.gradle.api.file">ConfigurableUserClassFilePermissions</a>&gt;&nbsp;configureAction)</code></th>
<td class="colLast">
<div class="block">Modifies the permissions all other users (non-owner, non-group) have
 for the file/directory.</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#unix-int-">unix</a></span>&#8203;(int&nbsp;unixNumeric)</code></th>
<td class="colLast">
<div class="block">Sets Unix style numeric permissions.</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#unix-java.lang.String-">unix</a></span>&#8203;(java.lang.String&nbsp;unixNumericOrSymbolic)</code></th>
<td class="colLast">
<div class="block">Sets Unix style permissions.</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#user-org.gradle.api.Action-">user</a></span>&#8203;(<a href="../Action.html" title="interface in org.gradle.api">Action</a>&lt;? super <a href="ConfigurableUserClassFilePermissions.html" title="interface in org.gradle.api.file">ConfigurableUserClassFilePermissions</a>&gt;&nbsp;configureAction)</code></th>
<td class="colLast">
<div class="block">Modifies the permissions the owner of the file has for the file/directory.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.org.gradle.api.file.FilePermissions">
<!--   -->
</a>
<h3>Methods inherited from interface&nbsp;org.gradle.api.file.<a href="FilePermissions.html" title="interface in org.gradle.api.file">FilePermissions</a></h3>
<code><a href="FilePermissions.html#toUnixNumeric--">toUnixNumeric</a></code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="getUser--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getUser</h4>
<pre class="methodSignature"><a href="ConfigurableUserClassFilePermissions.html" title="interface in org.gradle.api.file">ConfigurableUserClassFilePermissions</a>&nbsp;getUser()</pre>
<div class="block">Returns the permissions the owner of the file has for the file/directory.
 <p>
 The returned object is live, modifying it will change the user's permissions.
 <p>
 For further details on permissions see <a href="ConfigurableUserClassFilePermissions.html" title="interface in org.gradle.api.file"><code>ConfigurableUserClassFilePermissions</code></a>.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="FilePermissions.html#getUser--">getUser</a></code>&nbsp;in interface&nbsp;<code><a href="FilePermissions.html" title="interface in org.gradle.api.file">FilePermissions</a></code></dd>
</dl>
</li>
</ul>
<a name="user-org.gradle.api.Action-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>user</h4>
<pre class="methodSignature">void&nbsp;user&#8203;(<a href="../Action.html" title="interface in org.gradle.api">Action</a>&lt;? super <a href="ConfigurableUserClassFilePermissions.html" title="interface in org.gradle.api.file">ConfigurableUserClassFilePermissions</a>&gt;&nbsp;configureAction)</pre>
<div class="block">Modifies the permissions the owner of the file has for the file/directory.
 <p>
 For further details on permissions see <a href="ConfigurableUserClassFilePermissions.html" title="interface in org.gradle.api.file"><code>ConfigurableUserClassFilePermissions</code></a>.
 <p>
 Note that the provided configuration action only applies incremental modifications on top of whatever permission
 the user has at the moment and that the default values permissions start out are different for files and directories
 (see <a href="UserClassFilePermissions.html" title="interface in org.gradle.api.file"><code>UserClassFilePermissions</code></a>).</div>
</li>
</ul>
<a name="getGroup--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getGroup</h4>
<pre class="methodSignature"><a href="ConfigurableUserClassFilePermissions.html" title="interface in org.gradle.api.file">ConfigurableUserClassFilePermissions</a>&nbsp;getGroup()</pre>
<div class="block">Returns the permissions a user, who is a member of the group that the file/directory belongs to, has for the file/directory.
 <p>
 The returned object is live, modifying it will change the user's permissions.
 <p>
 For further details on permissions see <a href="ConfigurableUserClassFilePermissions.html" title="interface in org.gradle.api.file"><code>ConfigurableUserClassFilePermissions</code></a>.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="FilePermissions.html#getGroup--">getGroup</a></code>&nbsp;in interface&nbsp;<code><a href="FilePermissions.html" title="interface in org.gradle.api.file">FilePermissions</a></code></dd>
</dl>
</li>
</ul>
<a name="group-org.gradle.api.Action-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>group</h4>
<pre class="methodSignature">void&nbsp;group&#8203;(<a href="../Action.html" title="interface in org.gradle.api">Action</a>&lt;? super <a href="ConfigurableUserClassFilePermissions.html" title="interface in org.gradle.api.file">ConfigurableUserClassFilePermissions</a>&gt;&nbsp;configureAction)</pre>
<div class="block">Modifies the permissions a user, who is a member of the group that the file/directory belongs to, has for the file/directory.
 <p>
 For further details on permissions see <a href="ConfigurableUserClassFilePermissions.html" title="interface in org.gradle.api.file"><code>ConfigurableUserClassFilePermissions</code></a>.
 <p>
 Note that the provided configuration action only applies incremental modifications on top of whatever permission
 the user has at the moment and that the default values permissions start out are different for files and directories
 (see <a href="UserClassFilePermissions.html" title="interface in org.gradle.api.file"><code>UserClassFilePermissions</code></a>).</div>
</li>
</ul>
<a name="getOther--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getOther</h4>
<pre class="methodSignature"><a href="ConfigurableUserClassFilePermissions.html" title="interface in org.gradle.api.file">ConfigurableUserClassFilePermissions</a>&nbsp;getOther()</pre>
<div class="block">Returns the permissions all other users (non-owner, non-group) have for the file/directory.
 <p>
 The returned object is live, modifying it will change the user's permissions.
 <p>
 For further details on permissions see <a href="ConfigurableUserClassFilePermissions.html" title="interface in org.gradle.api.file"><code>ConfigurableUserClassFilePermissions</code></a>.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="FilePermissions.html#getOther--">getOther</a></code>&nbsp;in interface&nbsp;<code><a href="FilePermissions.html" title="interface in org.gradle.api.file">FilePermissions</a></code></dd>
</dl>
</li>
</ul>
<a name="other-org.gradle.api.Action-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>other</h4>
<pre class="methodSignature">void&nbsp;other&#8203;(<a href="../Action.html" title="interface in org.gradle.api">Action</a>&lt;? super <a href="ConfigurableUserClassFilePermissions.html" title="interface in org.gradle.api.file">ConfigurableUserClassFilePermissions</a>&gt;&nbsp;configureAction)</pre>
<div class="block">Modifies the permissions all other users (non-owner, non-group) have
 for the file/directory.
 <p>
 For further details on permissions see <a href="ConfigurableUserClassFilePermissions.html" title="interface in org.gradle.api.file"><code>ConfigurableUserClassFilePermissions</code></a>.
 <p>
 Note that the provided configuration action only applies incremental modifications on top of whatever permission
 the user has at the moment and that the default values permissions start out are different for files and directories
 (see <a href="UserClassFilePermissions.html" title="interface in org.gradle.api.file"><code>UserClassFilePermissions</code></a>).</div>
</li>
</ul>
<a name="unix-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>unix</h4>
<pre class="methodSignature">void&nbsp;unix&#8203;(java.lang.String&nbsp;unixNumericOrSymbolic)</pre>
<div class="block">Sets Unix style permissions. Accept values in two styles of notation:
 <ul>
     <li>NUMERIC notation: uses 3 octal (base-8) digits representing permissions for the 3 categories of users; for example "755"</li>
     <li>SYMBOLIC notation: uses 3 sets of 3 characters, each set representing the permissions for one of the user categories; for example "rwxr-xr-x"</li>
 </ul>
 <p>
 The NUMERIC notation consist of 3 digits having values from 0 to 7.
 1st digit represents the OWNER, 2nd represents the GROUP while the 3rd represents OTHER users.
 <p>
 Each of the digits is the sum of its component bits in the binary numeral system.
 Each of the 3 bits represents a permission.
 1st bit is the READ bit, adds 4 to the digit (binary 100).
 2nd bit is the WRITE bit, adds 2 to the digit (binary 010).
 3rd bit is the EXECUTE bit, adds 1 to the digit (binary 001).
 <p>
 See the examples below.
 <p>
 NOTE: providing the 3 numeric digits can also be done in the octal literal form, so "0740" will
 be handled identically to "740".
 <p>
 The SYMBOLIC notation consists of 3 sets of 3 characters. The 1st set represents the OWNER,
 the 2nd set represents the GROUP, the 3rd set represents OTHER users.
 <p>
 Each of the tree characters represents the read, write and execute permissions:
 <ul>
     <li><code>r</code> if READING is permitted, <code>-</code> if it is not; must be 1st in the set</li>
     <li><code>w</code> if WRITING is permitted, <code>-</code> if it is not; must be 2nd in the set</li>
     <li><code>x</code> if EXECUTING is permitted, <code>-</code> if it is not; must be 3rd in the set</li>
 </ul>
 <p>
 Examples:
 <table>
   <tr>
     <th>Numeric</th>
     <th>Symbolic</th>
     <th>Meaning</th>
   </tr>
   <tr>
     <td>000</td>
     <td>---------</td>
     <td>no permissions</td>
   </tr>
   <tr>
     <td>700</td>
     <td>rwx------</td>
     <td>read, write &amp; execute only for owner</td>
   </tr>
   <tr>
     <td>770</td>
     <td>rwxrwx---</td>
     <td>read, write &amp; execute for owner and group</td>
   </tr>
   <tr>
     <td>111</td>
     <td>--x--x--x</td>
     <td>execute</td>
   </tr>
   <tr>
     <td>222</td>
     <td>-w--w--w-</td>
     <td>write</td>
   </tr>
   <tr>
     <td>333</td>
     <td>-wx-wx-wx</td>
     <td>write &amp; execute</td>
   </tr>
   <tr>
     <td>444</td>
     <td>r--r--r--</td>
     <td>read</td>
   </tr>
   <tr>
     <td>555</td>
     <td>r-xr-xr-x</td>
     <td>read &amp; execute</td>
   </tr>
   <tr>
     <td>666</td>
     <td>rw-rw-rw-</td>
     <td>read &amp; write</td>
   </tr>
   <tr>
     <td>740</td>
     <td>rwxr-----</td>
     <td>owner can read, write &amp; execute; group can only read; others have no permissions</td>
   </tr>
 </table>
 <p>
 An example usage of this method would be configuring a copy task and explicitly specifying the destination file permissions:
 <pre>
 from(...)
 into(...)
 filePermissions { unix("r--r--r--") }
 </pre></div>
</li>
</ul>
<a name="unix-int-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>unix</h4>
<pre class="methodSignature">void&nbsp;unix&#8203;(int&nbsp;unixNumeric)</pre>
<div class="block">Sets Unix style numeric permissions. See <a href="#unix-java.lang.String-"><code>unix(String)</code></a> for details.</div>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
