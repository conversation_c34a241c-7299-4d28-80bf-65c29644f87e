<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc -->
<title>ApplicationPluginConvention (Gradle API 8.4)</title>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<link rel="stylesheet" type="text/css" href="../../../../javadoc.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../jquery/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
<script type="text/javascript" src="../../../../jquery/jszip/dist/jszip.min.js"></script>
<script type="text/javascript" src="../../../../jquery/jszip-utils/dist/jszip-utils.min.js"></script>
<!--[if IE]>
<script type="text/javascript" src="../../../../jquery/jszip-utils/dist/jszip-utils-ie.min.js"></script>
<![endif]-->
<script type="text/javascript" src="../../../../jquery/jquery-3.6.0.min.js"></script>
<script type="text/javascript" src="../../../../jquery/jquery-ui.min.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="ApplicationPluginConvention (Gradle API 8.4)";
        }
    }
    catch(err) {
    }
//-->
var data = {"i0":38,"i1":38,"i2":38,"i3":38,"i4":38,"i5":38,"i6":38,"i7":38,"i8":38,"i9":38,"i10":38};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],4:["t3","Abstract Methods"],32:["t6","Deprecated Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
var pathtoroot = "../../../../";
var useModuleDirectories = false;
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="fixedNav">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses.html">All&nbsp;Classes</a></li>
</ul>
<ul class="navListSearch">
<li><label for="search">SEARCH:</label>
<input type="text" id="search" value="search" disabled="disabled">
<input type="reset" id="reset" value="reset" disabled="disabled">
</li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
</div>
<div class="navPadding">&nbsp;</div>
<script type="text/javascript"><!--
$('.navPadding').css('padding-top', $('.fixedNav').css("height"));
//-->
</script>
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle"><span class="packageLabelInType">Package</span>&nbsp;<a href="package-summary.html">org.gradle.api.plugins</a></div>
<h2 title="Class ApplicationPluginConvention" class="title">Class ApplicationPluginConvention</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>org.gradle.api.plugins.ApplicationPluginConvention</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<hr>
<pre>@Deprecated
public abstract class <span class="typeNameLabel">ApplicationPluginConvention</span>
extends java.lang.Object</pre>
<div class="deprecationBlock"><span class="deprecatedLabel">Deprecated.</span>
<div class="deprecationComment">Use <a href="JavaApplication.html" title="interface in org.gradle.api.plugins"><code>JavaApplication</code></a> instead. This class is scheduled for removal in Gradle 9.0.</div>
</div>
<div class="block"><p>The <a href="Convention.html" title="interface in org.gradle.api.plugins"><code>Convention</code></a> used for configuring the <a href="ApplicationPlugin.html" title="class in org.gradle.api.plugins"><code>ApplicationPlugin</code></a>.</p></div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="memberSummary" summary="Constructor Summary table, listing constructors, and an explanation">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Constructor</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tr class="altColor">
<th class="colConstructorName" scope="row"><code><span class="memberNameLink"><a href="#ApplicationPluginConvention--">ApplicationPluginConvention</a></span>()</code></th>
<td class="colLast">
<div class="block"><span class="deprecatedLabel">Deprecated.</span></div>
&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t3" class="tableTab"><span><a href="javascript:show(4);">Abstract Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t6" class="tableTab"><span><a href="javascript:show(32);">Deprecated Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colSecond" scope="col">Method</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>abstract java.lang.Iterable&lt;java.lang.String&gt;</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getApplicationDefaultJvmArgs--">getApplicationDefaultJvmArgs</a></span>()</code></th>
<td class="colLast">
<div class="block"><span class="deprecatedLabel">Deprecated.</span></div>
<div class="block">Array of string arguments to pass to the JVM when running the application</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>abstract <a href="../file/CopySpec.html" title="interface in org.gradle.api.file">CopySpec</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getApplicationDistribution--">getApplicationDistribution</a></span>()</code></th>
<td class="colLast">
<div class="block"><span class="deprecatedLabel">Deprecated.</span></div>
<div class="block">The specification of the contents of the distribution.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>abstract java.lang.String</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getApplicationName--">getApplicationName</a></span>()</code></th>
<td class="colLast">
<div class="block"><span class="deprecatedLabel">Deprecated.</span></div>
<div class="block">The name of the application.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>abstract java.lang.String</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getExecutableDir--">getExecutableDir</a></span>()</code></th>
<td class="colLast">
<div class="block"><span class="deprecatedLabel">Deprecated.</span></div>
<div class="block">Directory to place executables in</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>abstract java.lang.String</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getMainClassName--">getMainClassName</a></span>()</code></th>
<td class="colLast">
<div class="block"><span class="deprecatedLabel">Deprecated.</span></div>
<div class="block">The fully qualified name of the application's main class.</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>abstract <a href="../Project.html" title="interface in org.gradle.api">Project</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getProject--">getProject</a></span>()</code></th>
<td class="colLast">
<div class="block"><span class="deprecatedLabel">Deprecated.</span></div>
&nbsp;</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>abstract void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setApplicationDefaultJvmArgs-java.lang.Iterable-">setApplicationDefaultJvmArgs</a></span>&#8203;(java.lang.Iterable&lt;java.lang.String&gt;&nbsp;applicationDefaultJvmArgs)</code></th>
<td class="colLast">
<div class="block"><span class="deprecatedLabel">Deprecated.</span></div>
<div class="block">Array of string arguments to pass to the JVM when running the application</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>abstract void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setApplicationDistribution-org.gradle.api.file.CopySpec-">setApplicationDistribution</a></span>&#8203;(<a href="../file/CopySpec.html" title="interface in org.gradle.api.file">CopySpec</a>&nbsp;applicationDistribution)</code></th>
<td class="colLast">
<div class="block"><span class="deprecatedLabel">Deprecated.</span></div>
&nbsp;</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>abstract void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setApplicationName-java.lang.String-">setApplicationName</a></span>&#8203;(java.lang.String&nbsp;applicationName)</code></th>
<td class="colLast">
<div class="block"><span class="deprecatedLabel">Deprecated.</span></div>
<div class="block">The name of the application.</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>abstract void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setExecutableDir-java.lang.String-">setExecutableDir</a></span>&#8203;(java.lang.String&nbsp;executableDir)</code></th>
<td class="colLast">
<div class="block"><span class="deprecatedLabel">Deprecated.</span></div>
<div class="block">Directory to place executables in</div>
</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code>abstract void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setMainClassName-java.lang.String-">setMainClassName</a></span>&#8203;(java.lang.String&nbsp;mainClassName)</code></th>
<td class="colLast">
<div class="block"><span class="deprecatedLabel">Deprecated.</span></div>
<div class="block">The fully qualified name of the application's main class.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>clone, equals, finalize, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a name="ApplicationPluginConvention--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>ApplicationPluginConvention</h4>
<pre>public&nbsp;ApplicationPluginConvention()</pre>
<div class="deprecationBlock"><span class="deprecatedLabel">Deprecated.</span></div>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="getApplicationName--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getApplicationName</h4>
<pre class="methodSignature">public abstract&nbsp;java.lang.String&nbsp;getApplicationName()</pre>
<div class="deprecationBlock"><span class="deprecatedLabel">Deprecated.</span></div>
<div class="block">The name of the application.</div>
</li>
</ul>
<a name="setApplicationName-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setApplicationName</h4>
<pre class="methodSignature">public abstract&nbsp;void&nbsp;setApplicationName&#8203;(java.lang.String&nbsp;applicationName)</pre>
<div class="deprecationBlock"><span class="deprecatedLabel">Deprecated.</span></div>
<div class="block">The name of the application.</div>
</li>
</ul>
<a name="getMainClassName--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getMainClassName</h4>
<pre class="methodSignature">public abstract&nbsp;java.lang.String&nbsp;getMainClassName()</pre>
<div class="deprecationBlock"><span class="deprecatedLabel">Deprecated.</span></div>
<div class="block">The fully qualified name of the application's main class.</div>
</li>
</ul>
<a name="setMainClassName-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setMainClassName</h4>
<pre class="methodSignature">public abstract&nbsp;void&nbsp;setMainClassName&#8203;(java.lang.String&nbsp;mainClassName)</pre>
<div class="deprecationBlock"><span class="deprecatedLabel">Deprecated.</span></div>
<div class="block">The fully qualified name of the application's main class.</div>
</li>
</ul>
<a name="getApplicationDefaultJvmArgs--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getApplicationDefaultJvmArgs</h4>
<pre class="methodSignature">public abstract&nbsp;java.lang.Iterable&lt;java.lang.String&gt;&nbsp;getApplicationDefaultJvmArgs()</pre>
<div class="deprecationBlock"><span class="deprecatedLabel">Deprecated.</span></div>
<div class="block">Array of string arguments to pass to the JVM when running the application</div>
</li>
</ul>
<a name="setApplicationDefaultJvmArgs-java.lang.Iterable-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setApplicationDefaultJvmArgs</h4>
<pre class="methodSignature">public abstract&nbsp;void&nbsp;setApplicationDefaultJvmArgs&#8203;(java.lang.Iterable&lt;java.lang.String&gt;&nbsp;applicationDefaultJvmArgs)</pre>
<div class="deprecationBlock"><span class="deprecatedLabel">Deprecated.</span></div>
<div class="block">Array of string arguments to pass to the JVM when running the application</div>
</li>
</ul>
<a name="getExecutableDir--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getExecutableDir</h4>
<pre class="methodSignature">public abstract&nbsp;java.lang.String&nbsp;getExecutableDir()</pre>
<div class="deprecationBlock"><span class="deprecatedLabel">Deprecated.</span></div>
<div class="block">Directory to place executables in</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>4.5</dd>
</dl>
</li>
</ul>
<a name="setExecutableDir-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setExecutableDir</h4>
<pre class="methodSignature">public abstract&nbsp;void&nbsp;setExecutableDir&#8203;(java.lang.String&nbsp;executableDir)</pre>
<div class="deprecationBlock"><span class="deprecatedLabel">Deprecated.</span></div>
<div class="block">Directory to place executables in</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>4.5</dd>
</dl>
</li>
</ul>
<a name="getApplicationDistribution--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getApplicationDistribution</h4>
<pre class="methodSignature">public abstract&nbsp;<a href="../file/CopySpec.html" title="interface in org.gradle.api.file">CopySpec</a>&nbsp;getApplicationDistribution()</pre>
<div class="deprecationBlock"><span class="deprecatedLabel">Deprecated.</span></div>
<div class="block"><p>The specification of the contents of the distribution.</p>
 <p>
 Use this <a href="../file/CopySpec.html" title="interface in org.gradle.api.file"><code>CopySpec</code></a> to include extra files/resource in the application distribution.
 <pre class='autoTested'>
 plugins {
     id 'application'
 }

 application {
     applicationDistribution.from("some/dir") {
       include "*.txt"
     }
 }
 </pre>
 <p>
 Note that the application plugin pre configures this spec to; include the contents of "<code>src/dist</code>",
 copy the application start scripts into the "<code>bin</code>" directory, and copy the built jar and its dependencies
 into the "<code>lib</code>" directory.</div>
</li>
</ul>
<a name="setApplicationDistribution-org.gradle.api.file.CopySpec-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setApplicationDistribution</h4>
<pre class="methodSignature">public abstract&nbsp;void&nbsp;setApplicationDistribution&#8203;(<a href="../file/CopySpec.html" title="interface in org.gradle.api.file">CopySpec</a>&nbsp;applicationDistribution)</pre>
<div class="deprecationBlock"><span class="deprecatedLabel">Deprecated.</span></div>
</li>
</ul>
<a name="getProject--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>getProject</h4>
<pre class="methodSignature">public abstract&nbsp;<a href="../Project.html" title="interface in org.gradle.api">Project</a>&nbsp;getProject()</pre>
<div class="deprecationBlock"><span class="deprecatedLabel">Deprecated.</span></div>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
