<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc -->
<title>CodeNarcExtension (Gradle API 8.4)</title>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<link rel="stylesheet" type="text/css" href="../../../../../javadoc.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../jquery/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
<script type="text/javascript" src="../../../../../jquery/jszip/dist/jszip.min.js"></script>
<script type="text/javascript" src="../../../../../jquery/jszip-utils/dist/jszip-utils.min.js"></script>
<!--[if IE]>
<script type="text/javascript" src="../../../../../jquery/jszip-utils/dist/jszip-utils-ie.min.js"></script>
<![endif]-->
<script type="text/javascript" src="../../../../../jquery/jquery-3.6.0.min.js"></script>
<script type="text/javascript" src="../../../../../jquery/jquery-ui.min.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="CodeNarcExtension (Gradle API 8.4)";
        }
    }
    catch(err) {
    }
//-->
var data = {"i0":10,"i1":10,"i2":10,"i3":10,"i4":10,"i5":10,"i6":10,"i7":10,"i8":10,"i9":10,"i10":10,"i11":10};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
var pathtoroot = "../../../../../";
var useModuleDirectories = false;
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="fixedNav">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../../allclasses.html">All&nbsp;Classes</a></li>
</ul>
<ul class="navListSearch">
<li><label for="search">SEARCH:</label>
<input type="text" id="search" value="search" disabled="disabled">
<input type="reset" id="reset" value="reset" disabled="disabled">
</li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
</div>
<div class="navPadding">&nbsp;</div>
<script type="text/javascript"><!--
$('.navPadding').css('padding-top', $('.fixedNav').css("height"));
//-->
</script>
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle"><span class="packageLabelInType">Package</span>&nbsp;<a href="package-summary.html">org.gradle.api.plugins.quality</a></div>
<h2 title="Class CodeNarcExtension" class="title">Class CodeNarcExtension</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li><a href="CodeQualityExtension.html" title="class in org.gradle.api.plugins.quality">org.gradle.api.plugins.quality.CodeQualityExtension</a></li>
<li>
<ul class="inheritance">
<li>org.gradle.api.plugins.quality.CodeNarcExtension</li>
</ul>
</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<hr>
<pre>public abstract class <span class="typeNameLabel">CodeNarcExtension</span>
extends <a href="CodeQualityExtension.html" title="class in org.gradle.api.plugins.quality">CodeQualityExtension</a></pre>
<div class="block">Configuration options for the CodeNarc plugin.</div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="CodeNarcPlugin.html" title="class in org.gradle.api.plugins.quality"><code>CodeNarcPlugin</code></a></dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="memberSummary" summary="Constructor Summary table, listing constructors, and an explanation">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Constructor</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tr class="altColor">
<th class="colConstructorName" scope="row"><code><span class="memberNameLink"><a href="#CodeNarcExtension-org.gradle.api.Project-">CodeNarcExtension</a></span>&#8203;(<a href="../../Project.html" title="interface in org.gradle.api">Project</a>&nbsp;project)</code></th>
<td class="colLast">&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colSecond" scope="col">Method</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code><a href="../../resources/TextResource.html" title="interface in org.gradle.api.resources">TextResource</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getConfig--">getConfig</a></span>()</code></th>
<td class="colLast">
<div class="block">The CodeNarc configuration to use.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>java.io.File</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getConfigFile--">getConfigFile</a></span>()</code></th>
<td class="colLast">
<div class="block">The CodeNarc configuration file to use.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>int</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getMaxPriority1Violations--">getMaxPriority1Violations</a></span>()</code></th>
<td class="colLast">
<div class="block">The maximum number of priority 1 violations allowed before failing the build.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>int</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getMaxPriority2Violations--">getMaxPriority2Violations</a></span>()</code></th>
<td class="colLast">
<div class="block">The maximum number of priority 2 violations allowed before failing the build.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>int</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getMaxPriority3Violations--">getMaxPriority3Violations</a></span>()</code></th>
<td class="colLast">
<div class="block">The maximum number of priority 3 violations allowed before failing the build.</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>java.lang.String</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getReportFormat--">getReportFormat</a></span>()</code></th>
<td class="colLast">
<div class="block">The format type of the CodeNarc report.</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setConfig-org.gradle.api.resources.TextResource-">setConfig</a></span>&#8203;(<a href="../../resources/TextResource.html" title="interface in org.gradle.api.resources">TextResource</a>&nbsp;config)</code></th>
<td class="colLast">
<div class="block">The CodeNarc configuration to use.</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setConfigFile-java.io.File-">setConfigFile</a></span>&#8203;(java.io.File&nbsp;file)</code></th>
<td class="colLast">
<div class="block">The CodeNarc configuration file to use.</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setMaxPriority1Violations-int-">setMaxPriority1Violations</a></span>&#8203;(int&nbsp;maxPriority1Violations)</code></th>
<td class="colLast">
<div class="block">The maximum number of priority 1 violations allowed before failing the build.</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setMaxPriority2Violations-int-">setMaxPriority2Violations</a></span>&#8203;(int&nbsp;maxPriority2Violations)</code></th>
<td class="colLast">
<div class="block">The maximum number of priority 2 violations allowed before failing the build.</div>
</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setMaxPriority3Violations-int-">setMaxPriority3Violations</a></span>&#8203;(int&nbsp;maxPriority3Violations)</code></th>
<td class="colLast">
<div class="block">The maximum number of priority 3 violations allowed before failing the build.</div>
</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setReportFormat-java.lang.String-">setReportFormat</a></span>&#8203;(java.lang.String&nbsp;reportFormat)</code></th>
<td class="colLast">
<div class="block">The format type of the CodeNarc report.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.org.gradle.api.plugins.quality.CodeQualityExtension">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;org.gradle.api.plugins.quality.<a href="CodeQualityExtension.html" title="class in org.gradle.api.plugins.quality">CodeQualityExtension</a></h3>
<code><a href="CodeQualityExtension.html#getReportsDir--">getReportsDir</a>, <a href="CodeQualityExtension.html#getSourceSets--">getSourceSets</a>, <a href="CodeQualityExtension.html#getToolVersion--">getToolVersion</a>, <a href="CodeQualityExtension.html#isIgnoreFailures--">isIgnoreFailures</a>, <a href="CodeQualityExtension.html#setIgnoreFailures-boolean-">setIgnoreFailures</a>, <a href="CodeQualityExtension.html#setReportsDir-java.io.File-">setReportsDir</a>, <a href="CodeQualityExtension.html#setSourceSets-java.util.Collection-">setSourceSets</a>, <a href="CodeQualityExtension.html#setToolVersion-java.lang.String-">setToolVersion</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>clone, equals, finalize, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a name="CodeNarcExtension-org.gradle.api.Project-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>CodeNarcExtension</h4>
<pre>public&nbsp;CodeNarcExtension&#8203;(<a href="../../Project.html" title="interface in org.gradle.api">Project</a>&nbsp;project)</pre>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="getConfig--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getConfig</h4>
<pre class="methodSignature">public&nbsp;<a href="../../resources/TextResource.html" title="interface in org.gradle.api.resources">TextResource</a>&nbsp;getConfig()</pre>
<div class="block">The CodeNarc configuration to use. Replaces the <code>configFile</code> property.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>2.2</dd>
</dl>
</li>
</ul>
<a name="setConfig-org.gradle.api.resources.TextResource-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setConfig</h4>
<pre class="methodSignature">public&nbsp;void&nbsp;setConfig&#8203;(<a href="../../resources/TextResource.html" title="interface in org.gradle.api.resources">TextResource</a>&nbsp;config)</pre>
<div class="block">The CodeNarc configuration to use. Replaces the <code>configFile</code> property.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>2.2</dd>
</dl>
</li>
</ul>
<a name="getConfigFile--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getConfigFile</h4>
<pre class="methodSignature">public&nbsp;java.io.File&nbsp;getConfigFile()</pre>
<div class="block">The CodeNarc configuration file to use.</div>
</li>
</ul>
<a name="setConfigFile-java.io.File-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setConfigFile</h4>
<pre class="methodSignature">public&nbsp;void&nbsp;setConfigFile&#8203;(java.io.File&nbsp;file)</pre>
<div class="block">The CodeNarc configuration file to use.</div>
</li>
</ul>
<a name="getMaxPriority1Violations--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getMaxPriority1Violations</h4>
<pre class="methodSignature">public&nbsp;int&nbsp;getMaxPriority1Violations()</pre>
<div class="block">The maximum number of priority 1 violations allowed before failing the build.</div>
</li>
</ul>
<a name="setMaxPriority1Violations-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setMaxPriority1Violations</h4>
<pre class="methodSignature">public&nbsp;void&nbsp;setMaxPriority1Violations&#8203;(int&nbsp;maxPriority1Violations)</pre>
<div class="block">The maximum number of priority 1 violations allowed before failing the build.</div>
</li>
</ul>
<a name="getMaxPriority2Violations--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getMaxPriority2Violations</h4>
<pre class="methodSignature">public&nbsp;int&nbsp;getMaxPriority2Violations()</pre>
<div class="block">The maximum number of priority 2 violations allowed before failing the build.</div>
</li>
</ul>
<a name="setMaxPriority2Violations-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setMaxPriority2Violations</h4>
<pre class="methodSignature">public&nbsp;void&nbsp;setMaxPriority2Violations&#8203;(int&nbsp;maxPriority2Violations)</pre>
<div class="block">The maximum number of priority 2 violations allowed before failing the build.</div>
</li>
</ul>
<a name="getMaxPriority3Violations--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getMaxPriority3Violations</h4>
<pre class="methodSignature">public&nbsp;int&nbsp;getMaxPriority3Violations()</pre>
<div class="block">The maximum number of priority 3 violations allowed before failing the build.</div>
</li>
</ul>
<a name="setMaxPriority3Violations-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setMaxPriority3Violations</h4>
<pre class="methodSignature">public&nbsp;void&nbsp;setMaxPriority3Violations&#8203;(int&nbsp;maxPriority3Violations)</pre>
<div class="block">The maximum number of priority 3 violations allowed before failing the build.</div>
</li>
</ul>
<a name="getReportFormat--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getReportFormat</h4>
<pre class="methodSignature">public&nbsp;java.lang.String&nbsp;getReportFormat()</pre>
<div class="block">The format type of the CodeNarc report. One of <code>html</code>, <code>xml</code>, <code>text</code>, <code>console</code>.</div>
</li>
</ul>
<a name="setReportFormat-java.lang.String-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>setReportFormat</h4>
<pre class="methodSignature">public&nbsp;void&nbsp;setReportFormat&#8203;(java.lang.String&nbsp;reportFormat)</pre>
<div class="block">The format type of the CodeNarc report. One of <code>html</code>, <code>xml</code>, <code>text</code>, <code>console</code>.</div>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../../allclasses.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
