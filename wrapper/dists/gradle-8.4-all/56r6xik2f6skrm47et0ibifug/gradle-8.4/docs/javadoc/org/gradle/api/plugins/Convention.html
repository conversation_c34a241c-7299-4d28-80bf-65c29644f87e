<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc -->
<title>Convention (Gradle API 8.4)</title>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<link rel="stylesheet" type="text/css" href="../../../../javadoc.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../jquery/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
<script type="text/javascript" src="../../../../jquery/jszip/dist/jszip.min.js"></script>
<script type="text/javascript" src="../../../../jquery/jszip-utils/dist/jszip-utils.min.js"></script>
<!--[if IE]>
<script type="text/javascript" src="../../../../jquery/jszip-utils/dist/jszip-utils-ie.min.js"></script>
<![endif]-->
<script type="text/javascript" src="../../../../jquery/jquery-3.6.0.min.js"></script>
<script type="text/javascript" src="../../../../jquery/jquery-ui.min.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="Convention (Gradle API 8.4)";
        }
    }
    catch(err) {
    }
//-->
var data = {"i0":38,"i1":38,"i2":38,"i3":38};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],4:["t3","Abstract Methods"],32:["t6","Deprecated Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
var pathtoroot = "../../../../";
var useModuleDirectories = false;
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="fixedNav">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses.html">All&nbsp;Classes</a></li>
</ul>
<ul class="navListSearch">
<li><label for="search">SEARCH:</label>
<input type="text" id="search" value="search" disabled="disabled">
<input type="reset" id="reset" value="reset" disabled="disabled">
</li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
</div>
<div class="navPadding">&nbsp;</div>
<script type="text/javascript"><!--
$('.navPadding').css('padding-top', $('.fixedNav').css("height"));
//-->
</script>
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle"><span class="packageLabelInType">Package</span>&nbsp;<a href="package-summary.html">org.gradle.api.plugins</a></div>
<h2 title="Interface Convention" class="title">Interface Convention</h2>
</div>
<div class="contentContainer">
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Superinterfaces:</dt>
<dd><code><a href="ExtensionContainer.html" title="interface in org.gradle.api.plugins">ExtensionContainer</a></code></dd>
</dl>
<hr>
<pre>@Deprecated
public interface <span class="typeNameLabel">Convention</span>
extends <a href="ExtensionContainer.html" title="interface in org.gradle.api.plugins">ExtensionContainer</a></pre>
<div class="deprecationBlock"><span class="deprecatedLabel">Deprecated.</span>
<div class="deprecationComment">Use extensions instead. This is scheduled for removal in Gradle 9.</div>
</div>
<div class="block"><p>A <code>Convention</code> manages a set of <i>convention objects</i>. When you add a convention object to a <code>
 Convention</code>, and the properties and methods of the convention object become available as properties and methods of
 the object which the convention is associated to. A convention object is simply a POJO or POGO. Usually, a <code>
 Convention</code> is used by plugins to extend a <a href="../Project.html" title="interface in org.gradle.api"><code>Project</code></a> or a <a href="../Task.html" title="interface in org.gradle.api"><code>Task</code></a>.</p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="ExtensionAware.html" title="interface in org.gradle.api.plugins"><code>ExtensionAware</code></a></dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t3" class="tableTab"><span><a href="javascript:show(4);">Abstract Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t6" class="tableTab"><span><a href="javascript:show(32);">Deprecated Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colSecond" scope="col">Method</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>&lt;T&gt;&nbsp;T</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#findPlugin-java.lang.Class-">findPlugin</a></span>&#8203;(java.lang.Class&lt;T&gt;&nbsp;type)</code></th>
<td class="colLast">
<div class="block"><span class="deprecatedLabel">Deprecated.</span>
<div class="deprecationComment">Use extensions instead.</div>
</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>org.gradle.internal.metaobject.DynamicObject</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getExtensionsAsDynamicObject--">getExtensionsAsDynamicObject</a></span>()</code></th>
<td class="colLast">
<div class="block"><span class="deprecatedLabel">Deprecated.</span></div>
<div class="block">Returns a dynamic object which represents the properties and methods contributed by the extensions and convention objects contained in this
 convention.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>&lt;T&gt;&nbsp;T</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getPlugin-java.lang.Class-">getPlugin</a></span>&#8203;(java.lang.Class&lt;T&gt;&nbsp;type)</code></th>
<td class="colLast">
<div class="block"><span class="deprecatedLabel">Deprecated.</span>
<div class="deprecationComment">Use extensions instead.</div>
</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>java.util.Map&lt;java.lang.String,&#8203;java.lang.Object&gt;</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getPlugins--">getPlugins</a></span>()</code></th>
<td class="colLast">
<div class="block"><span class="deprecatedLabel">Deprecated.</span>
<div class="deprecationComment">Use extensions instead.</div>
</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.org.gradle.api.plugins.ExtensionContainer">
<!--   -->
</a>
<h3>Methods inherited from interface&nbsp;org.gradle.api.plugins.<a href="ExtensionContainer.html" title="interface in org.gradle.api.plugins">ExtensionContainer</a></h3>
<code><a href="ExtensionContainer.html#add-java.lang.Class-java.lang.String-T-">add</a>, <a href="ExtensionContainer.html#add-java.lang.String-java.lang.Object-">add</a>, <a href="ExtensionContainer.html#add-org.gradle.api.reflect.TypeOf-java.lang.String-T-">add</a>, <a href="ExtensionContainer.html#configure-java.lang.Class-org.gradle.api.Action-">configure</a>, <a href="ExtensionContainer.html#configure-java.lang.String-org.gradle.api.Action-">configure</a>, <a href="ExtensionContainer.html#configure-org.gradle.api.reflect.TypeOf-org.gradle.api.Action-">configure</a>, <a href="ExtensionContainer.html#create-java.lang.Class-java.lang.String-java.lang.Class-java.lang.Object...-">create</a>, <a href="ExtensionContainer.html#create-java.lang.String-java.lang.Class-java.lang.Object...-">create</a>, <a href="ExtensionContainer.html#create-org.gradle.api.reflect.TypeOf-java.lang.String-java.lang.Class-java.lang.Object...-">create</a>, <a href="ExtensionContainer.html#findByName-java.lang.String-">findByName</a>, <a href="ExtensionContainer.html#findByType-java.lang.Class-">findByType</a>, <a href="ExtensionContainer.html#findByType-org.gradle.api.reflect.TypeOf-">findByType</a>, <a href="ExtensionContainer.html#getByName-java.lang.String-">getByName</a>, <a href="ExtensionContainer.html#getByType-java.lang.Class-">getByType</a>, <a href="ExtensionContainer.html#getByType-org.gradle.api.reflect.TypeOf-">getByType</a>, <a href="ExtensionContainer.html#getExtensionsSchema--">getExtensionsSchema</a>, <a href="ExtensionContainer.html#getExtraProperties--">getExtraProperties</a></code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="getPlugins--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getPlugins</h4>
<pre class="methodSignature">@Deprecated
java.util.Map&lt;java.lang.String,&#8203;java.lang.Object&gt;&nbsp;getPlugins()</pre>
<div class="deprecationBlock"><span class="deprecatedLabel">Deprecated.</span>
<div class="deprecationComment">Use extensions instead. This is scheduled for removal in Gradle 9.</div>
</div>
<div class="block">Returns the plugin convention objects contained in this convention.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The plugins. Returns an empty map when this convention does not contain any convention objects.</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="ExtensionAware.html" title="interface in org.gradle.api.plugins"><code>ExtensionAware</code></a></dd>
</dl>
</li>
</ul>
<a name="getPlugin-java.lang.Class-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getPlugin</h4>
<pre class="methodSignature">@Deprecated
&lt;T&gt;&nbsp;T&nbsp;getPlugin&#8203;(java.lang.Class&lt;T&gt;&nbsp;type)
         throws java.lang.IllegalStateException</pre>
<div class="deprecationBlock"><span class="deprecatedLabel">Deprecated.</span>
<div class="deprecationComment">Use extensions instead. This is scheduled for removal in Gradle 9.</div>
</div>
<div class="block">Locates the plugin convention object with the given type.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>type</code> - The convention object type.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The object. Never returns null.</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code>java.lang.IllegalStateException</code> - When there is no such object contained in this convention, or when there are
 multiple such objects.</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="ExtensionAware.html" title="interface in org.gradle.api.plugins"><code>ExtensionAware</code></a></dd>
</dl>
</li>
</ul>
<a name="findPlugin-java.lang.Class-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>findPlugin</h4>
<pre class="methodSignature">@Nullable
@Deprecated
&lt;T&gt;&nbsp;T&nbsp;findPlugin&#8203;(java.lang.Class&lt;T&gt;&nbsp;type)
          throws java.lang.IllegalStateException</pre>
<div class="deprecationBlock"><span class="deprecatedLabel">Deprecated.</span>
<div class="deprecationComment">Use extensions instead. This is scheduled for removal in Gradle 9.</div>
</div>
<div class="block">Locates the plugin convention object with the given type.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>type</code> - The convention object type.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The object. Returns null if there is no such object.</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code>java.lang.IllegalStateException</code> - When there are multiple matching objects.</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="ExtensionAware.html" title="interface in org.gradle.api.plugins"><code>ExtensionAware</code></a></dd>
</dl>
</li>
</ul>
<a name="getExtensionsAsDynamicObject--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>getExtensionsAsDynamicObject</h4>
<pre class="methodSignature">org.gradle.internal.metaobject.DynamicObject&nbsp;getExtensionsAsDynamicObject()</pre>
<div class="deprecationBlock"><span class="deprecatedLabel">Deprecated.</span></div>
<div class="block">Returns a dynamic object which represents the properties and methods contributed by the extensions and convention objects contained in this
 convention.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The dynamic object</dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
