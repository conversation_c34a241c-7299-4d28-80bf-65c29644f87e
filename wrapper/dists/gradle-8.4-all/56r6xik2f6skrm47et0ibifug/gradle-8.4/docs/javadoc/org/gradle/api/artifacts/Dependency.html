<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc -->
<title>Dependency (Gradle API 8.4)</title>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<link rel="stylesheet" type="text/css" href="../../../../javadoc.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../jquery/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
<script type="text/javascript" src="../../../../jquery/jszip/dist/jszip.min.js"></script>
<script type="text/javascript" src="../../../../jquery/jszip-utils/dist/jszip-utils.min.js"></script>
<!--[if IE]>
<script type="text/javascript" src="../../../../jquery/jszip-utils/dist/jszip-utils-ie.min.js"></script>
<![endif]-->
<script type="text/javascript" src="../../../../jquery/jquery-3.6.0.min.js"></script>
<script type="text/javascript" src="../../../../jquery/jquery-ui.min.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="Dependency (Gradle API 8.4)";
        }
    }
    catch(err) {
    }
//-->
var data = {"i0":6,"i1":6,"i2":6,"i3":6,"i4":6,"i5":6,"i6":6};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],4:["t3","Abstract Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
var pathtoroot = "../../../../";
var useModuleDirectories = false;
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="fixedNav">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses.html">All&nbsp;Classes</a></li>
</ul>
<ul class="navListSearch">
<li><label for="search">SEARCH:</label>
<input type="text" id="search" value="search" disabled="disabled">
<input type="reset" id="reset" value="reset" disabled="disabled">
</li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field.summary">Field</a>&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#field.detail">Field</a>&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
</div>
<div class="navPadding">&nbsp;</div>
<script type="text/javascript"><!--
$('.navPadding').css('padding-top', $('.fixedNav').css("height"));
//-->
</script>
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle"><span class="packageLabelInType">Package</span>&nbsp;<a href="package-summary.html">org.gradle.api.artifacts</a></div>
<h2 title="Interface Dependency" class="title">Interface Dependency</h2>
</div>
<div class="contentContainer">
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Known Subinterfaces:</dt>
<dd><code><a href="ClientModule.html" title="interface in org.gradle.api.artifacts">ClientModule</a></code>, <code><a href="ExternalDependency.html" title="interface in org.gradle.api.artifacts">ExternalDependency</a></code>, <code><a href="ExternalModuleDependency.html" title="interface in org.gradle.api.artifacts">ExternalModuleDependency</a></code>, <code><a href="FileCollectionDependency.html" title="interface in org.gradle.api.artifacts">FileCollectionDependency</a></code>, <code><a href="MinimalExternalModuleDependency.html" title="interface in org.gradle.api.artifacts">MinimalExternalModuleDependency</a></code>, <code><a href="ModuleDependency.html" title="interface in org.gradle.api.artifacts">ModuleDependency</a></code>, <code><a href="ProjectDependency.html" title="interface in org.gradle.api.artifacts">ProjectDependency</a></code>, <code><a href="SelfResolvingDependency.html" title="interface in org.gradle.api.artifacts">SelfResolvingDependency</a></code></dd>
</dl>
<hr>
<pre>public interface <span class="typeNameLabel">Dependency</span></pre>
<div class="block">A <code>Dependency</code> represents a dependency on the artifacts from a particular source. A source can be an Ivy
 module, a Maven POM, another Gradle project, a collection of Files, etc... A source can have zero or more artifacts.</div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- =========== FIELD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.summary">
<!--   -->
</a>
<h3>Field Summary</h3>
<table class="memberSummary" summary="Field Summary table, listing fields, and an explanation">
<caption><span>Fields</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colSecond" scope="col">Field</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#ARCHIVES_CONFIGURATION">ARCHIVES_CONFIGURATION</a></span></code></th>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#CLASSIFIER">CLASSIFIER</a></span></code></th>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#DEFAULT_CONFIGURATION">DEFAULT_CONFIGURATION</a></span></code></th>
<td class="colLast">&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t3" class="tableTab"><span><a href="javascript:show(4);">Abstract Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colSecond" scope="col">Method</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#because-java.lang.String-">because</a></span>&#8203;(java.lang.String&nbsp;reason)</code></th>
<td class="colLast">
<div class="block">Sets the reason why this dependency should be used.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#contentEquals-org.gradle.api.artifacts.Dependency-">contentEquals</a></span>&#8203;(<a href="Dependency.html" title="interface in org.gradle.api.artifacts">Dependency</a>&nbsp;dependency)</code></th>
<td class="colLast">
<div class="block">Returns whether two dependencies have identical values for their properties.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code><a href="Dependency.html" title="interface in org.gradle.api.artifacts">Dependency</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#copy--">copy</a></span>()</code></th>
<td class="colLast">
<div class="block">Creates and returns a new dependency with the property values of this one.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>java.lang.String</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getGroup--">getGroup</a></span>()</code></th>
<td class="colLast">
<div class="block">Returns the group of this dependency.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getName--">getName</a></span>()</code></th>
<td class="colLast">
<div class="block">Returns the name of this dependency.</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>java.lang.String</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getReason--">getReason</a></span>()</code></th>
<td class="colLast">
<div class="block">Returns a reason why this dependency should be used, in particular with regards to its version.</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getVersion--">getVersion</a></span>()</code></th>
<td class="colLast">
<div class="block">Returns the version of this dependency.</div>
</td>
</tr>
</table>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ FIELD DETAIL =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.detail">
<!--   -->
</a>
<h3>Field Detail</h3>
<a name="DEFAULT_CONFIGURATION">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DEFAULT_CONFIGURATION</h4>
<pre>static final&nbsp;java.lang.String DEFAULT_CONFIGURATION</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../../constant-values.html#org.gradle.api.artifacts.Dependency.DEFAULT_CONFIGURATION">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="ARCHIVES_CONFIGURATION">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ARCHIVES_CONFIGURATION</h4>
<pre>static final&nbsp;java.lang.String ARCHIVES_CONFIGURATION</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../../constant-values.html#org.gradle.api.artifacts.Dependency.ARCHIVES_CONFIGURATION">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CLASSIFIER">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>CLASSIFIER</h4>
<pre>static final&nbsp;java.lang.String CLASSIFIER</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../../constant-values.html#org.gradle.api.artifacts.Dependency.CLASSIFIER">Constant Field Values</a></dd>
</dl>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="getGroup--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getGroup</h4>
<pre class="methodSignature">@Nullable
java.lang.String&nbsp;getGroup()</pre>
<div class="block">Returns the group of this dependency. The group is often required to find the artifacts of a dependency in a
 repository. For example, the group name corresponds to a directory name in a Maven like repository. Might return
 null.</div>
</li>
</ul>
<a name="getName--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getName</h4>
<pre class="methodSignature">java.lang.String&nbsp;getName()</pre>
<div class="block">Returns the name of this dependency. The name is almost always required to find the artifacts of a dependency in
 a repository. Never returns null.</div>
</li>
</ul>
<a name="getVersion--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getVersion</h4>
<pre class="methodSignature">@Nullable
java.lang.String&nbsp;getVersion()</pre>
<div class="block">Returns the version of this dependency. The version is often required to find the artifacts of a dependency in a
 repository. For example the version name corresponds to a directory name in a Maven like repository. Might return
 null.</div>
</li>
</ul>
<a name="contentEquals-org.gradle.api.artifacts.Dependency-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>contentEquals</h4>
<pre class="methodSignature">boolean&nbsp;contentEquals&#8203;(<a href="Dependency.html" title="interface in org.gradle.api.artifacts">Dependency</a>&nbsp;dependency)</pre>
<div class="block">Returns whether two dependencies have identical values for their properties. A dependency is an entity with a
 key. Therefore dependencies might be equal and yet have different properties.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>dependency</code> - The dependency to compare this dependency with</dd>
</dl>
</li>
</ul>
<a name="copy--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>copy</h4>
<pre class="methodSignature"><a href="Dependency.html" title="interface in org.gradle.api.artifacts">Dependency</a>&nbsp;copy()</pre>
<div class="block">Creates and returns a new dependency with the property values of this one.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The copy. Never returns null.</dd>
</dl>
</li>
</ul>
<a name="getReason--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getReason</h4>
<pre class="methodSignature">@Nullable
java.lang.String&nbsp;getReason()</pre>
<div class="block">Returns a reason why this dependency should be used, in particular with regards to its version. The dependency report
 will use it to explain why a specific dependency was selected, or why a specific dependency version was used.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>a reason to use this dependency</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>4.6</dd>
</dl>
</li>
</ul>
<a name="because-java.lang.String-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>because</h4>
<pre class="methodSignature">void&nbsp;because&#8203;(@Nullable
             java.lang.String&nbsp;reason)</pre>
<div class="block">Sets the reason why this dependency should be used.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>4.6</dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field.summary">Field</a>&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#field.detail">Field</a>&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
