<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc -->
<title>PatternSet (Gradle API 8.4)</title>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<link rel="stylesheet" type="text/css" href="../../../../../javadoc.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../jquery/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
<script type="text/javascript" src="../../../../../jquery/jszip/dist/jszip.min.js"></script>
<script type="text/javascript" src="../../../../../jquery/jszip-utils/dist/jszip-utils.min.js"></script>
<!--[if IE]>
<script type="text/javascript" src="../../../../../jquery/jszip-utils/dist/jszip-utils-ie.min.js"></script>
<![endif]-->
<script type="text/javascript" src="../../../../../jquery/jquery-3.6.0.min.js"></script>
<script type="text/javascript" src="../../../../../jquery/jquery-ui.min.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="PatternSet (Gradle API 8.4)";
        }
    }
    catch(err) {
    }
//-->
var data = {"i0":10,"i1":10,"i2":10,"i3":10,"i4":10,"i5":10,"i6":10,"i7":10,"i8":10,"i9":10,"i10":10,"i11":10,"i12":10,"i13":10,"i14":10,"i15":10,"i16":10,"i17":10,"i18":10,"i19":10,"i20":10,"i21":10,"i22":10,"i23":10,"i24":10,"i25":10,"i26":10,"i27":10};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
var pathtoroot = "../../../../../";
var useModuleDirectories = false;
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="fixedNav">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../../allclasses.html">All&nbsp;Classes</a></li>
</ul>
<ul class="navListSearch">
<li><label for="search">SEARCH:</label>
<input type="text" id="search" value="search" disabled="disabled">
<input type="reset" id="reset" value="reset" disabled="disabled">
</li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
</div>
<div class="navPadding">&nbsp;</div>
<script type="text/javascript"><!--
$('.navPadding').css('padding-top', $('.fixedNav').css("height"));
//-->
</script>
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle"><span class="packageLabelInType">Package</span>&nbsp;<a href="package-summary.html">org.gradle.api.tasks.util</a></div>
<h2 title="Class PatternSet" class="title">Class PatternSet</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>org.gradle.api.tasks.util.PatternSet</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Implemented Interfaces:</dt>
<dd><code><a href="../AntBuilderAware.html" title="interface in org.gradle.api.tasks">AntBuilderAware</a></code>, <code><a href="PatternFilterable.html" title="interface in org.gradle.api.tasks.util">PatternFilterable</a></code></dd>
</dl>
<hr>
<pre>public class <span class="typeNameLabel">PatternSet</span>
extends java.lang.Object
implements <a href="../AntBuilderAware.html" title="interface in org.gradle.api.tasks">AntBuilderAware</a>, <a href="PatternFilterable.html" title="interface in org.gradle.api.tasks.util">PatternFilterable</a></pre>
<div class="block">Standalone implementation of <a href="PatternFilterable.html" title="interface in org.gradle.api.tasks.util"><code>PatternFilterable</code></a>.</div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="memberSummary" summary="Constructor Summary table, listing constructors, and an explanation">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier</th>
<th class="colSecond" scope="col">Constructor</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>&nbsp;</code></td>
<th class="colConstructorName" scope="row"><code><span class="memberNameLink"><a href="#PatternSet--">PatternSet</a></span>()</code></th>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected </code></td>
<th class="colConstructorName" scope="row"><code><span class="memberNameLink"><a href="#PatternSet-org.gradle.api.tasks.util.internal.PatternSpecFactory-">PatternSet</a></span>&#8203;(org.gradle.api.tasks.util.internal.PatternSpecFactory&nbsp;patternSpecFactory)</code></th>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected </code></td>
<th class="colConstructorName" scope="row"><code><span class="memberNameLink"><a href="#PatternSet-org.gradle.api.tasks.util.PatternSet-">PatternSet</a></span>&#8203;(<a href="PatternSet.html" title="class in org.gradle.api.tasks.util">PatternSet</a>&nbsp;patternSet)</code></th>
<td class="colLast">&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colSecond" scope="col">Method</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>java.lang.Object</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#addToAntBuilder-java.lang.Object-java.lang.String-">addToAntBuilder</a></span>&#8203;(java.lang.Object&nbsp;node,
               java.lang.String&nbsp;childNodeName)</code></th>
<td class="colLast">&nbsp;</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code><a href="PatternSet.html" title="class in org.gradle.api.tasks.util">PatternSet</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#copyFrom-org.gradle.api.tasks.util.PatternFilterable-">copyFrom</a></span>&#8203;(<a href="PatternFilterable.html" title="interface in org.gradle.api.tasks.util">PatternFilterable</a>&nbsp;sourcePattern)</code></th>
<td class="colLast">&nbsp;</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>protected <a href="PatternSet.html" title="class in org.gradle.api.tasks.util">PatternSet</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#doCopyFrom-org.gradle.api.tasks.util.PatternSet-">doCopyFrom</a></span>&#8203;(<a href="PatternSet.html" title="class in org.gradle.api.tasks.util">PatternSet</a>&nbsp;from)</code></th>
<td class="colLast">&nbsp;</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#equals-java.lang.Object-">equals</a></span>&#8203;(java.lang.Object&nbsp;o)</code></th>
<td class="colLast">&nbsp;</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code><a href="PatternSet.html" title="class in org.gradle.api.tasks.util">PatternSet</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#exclude-groovy.lang.Closure-">exclude</a></span>&#8203;(<a href="https://docs.groovy-lang.org/docs/groovy-3.0.17/html/gapi/groovy/lang/Closure.html?is-external=true" title="class or interface in groovy.lang" class="externalLink">Closure</a>&nbsp;closure)</code></th>
<td class="colLast">
<div class="block">Adds an exclude spec.</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code><a href="PatternSet.html" title="class in org.gradle.api.tasks.util">PatternSet</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#exclude-java.lang.Iterable-">exclude</a></span>&#8203;(java.lang.Iterable&nbsp;excludes)</code></th>
<td class="colLast">
<div class="block">Adds an ANT style exclude pattern.</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code><a href="PatternSet.html" title="class in org.gradle.api.tasks.util">PatternSet</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#exclude-java.lang.String...-">exclude</a></span>&#8203;(java.lang.String...&nbsp;excludes)</code></th>
<td class="colLast">
<div class="block">Adds an ANT style exclude pattern.</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code><a href="PatternSet.html" title="class in org.gradle.api.tasks.util">PatternSet</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#exclude-org.gradle.api.specs.Spec-">exclude</a></span>&#8203;(<a href="../../specs/Spec.html" title="interface in org.gradle.api.specs">Spec</a>&lt;<a href="../../file/FileTreeElement.html" title="interface in org.gradle.api.file">FileTreeElement</a>&gt;&nbsp;spec)</code></th>
<td class="colLast">
<div class="block">Adds an exclude spec.</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code><a href="PatternSet.html" title="class in org.gradle.api.tasks.util">PatternSet</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#excludeSpecs-java.lang.Iterable-">excludeSpecs</a></span>&#8203;(java.lang.Iterable&lt;<a href="../../specs/Spec.html" title="interface in org.gradle.api.specs">Spec</a>&lt;<a href="../../file/FileTreeElement.html" title="interface in org.gradle.api.file">FileTreeElement</a>&gt;&gt;&nbsp;excludes)</code></th>
<td class="colLast">&nbsp;</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code><a href="../../specs/Spec.html" title="interface in org.gradle.api.specs">Spec</a>&lt;<a href="../../file/FileTreeElement.html" title="interface in org.gradle.api.file">FileTreeElement</a>&gt;</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getAsExcludeSpec--">getAsExcludeSpec</a></span>()</code></th>
<td class="colLast">&nbsp;</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code><a href="../../specs/Spec.html" title="interface in org.gradle.api.specs">Spec</a>&lt;<a href="../../file/FileTreeElement.html" title="interface in org.gradle.api.file">FileTreeElement</a>&gt;</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getAsIncludeSpec--">getAsIncludeSpec</a></span>()</code></th>
<td class="colLast">&nbsp;</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code><a href="../../specs/Spec.html" title="interface in org.gradle.api.specs">Spec</a>&lt;<a href="../../file/FileTreeElement.html" title="interface in org.gradle.api.file">FileTreeElement</a>&gt;</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getAsSpec--">getAsSpec</a></span>()</code></th>
<td class="colLast">&nbsp;</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code>java.util.Set&lt;java.lang.String&gt;</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getExcludes--">getExcludes</a></span>()</code></th>
<td class="colLast">
<div class="block">Returns the set of exclude patterns.</div>
</td>
</tr>
<tr id="i13" class="rowColor">
<td class="colFirst"><code>java.util.Set&lt;<a href="../../specs/Spec.html" title="interface in org.gradle.api.specs">Spec</a>&lt;<a href="../../file/FileTreeElement.html" title="interface in org.gradle.api.file">FileTreeElement</a>&gt;&gt;</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getExcludeSpecs--">getExcludeSpecs</a></span>()</code></th>
<td class="colLast">&nbsp;</td>
</tr>
<tr id="i14" class="altColor">
<td class="colFirst"><code>java.util.Set&lt;java.lang.String&gt;</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getIncludes--">getIncludes</a></span>()</code></th>
<td class="colLast">
<div class="block">Returns the set of include patterns.</div>
</td>
</tr>
<tr id="i15" class="rowColor">
<td class="colFirst"><code>java.util.Set&lt;<a href="../../specs/Spec.html" title="interface in org.gradle.api.specs">Spec</a>&lt;<a href="../../file/FileTreeElement.html" title="interface in org.gradle.api.file">FileTreeElement</a>&gt;&gt;</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getIncludeSpecs--">getIncludeSpecs</a></span>()</code></th>
<td class="colLast">&nbsp;</td>
</tr>
<tr id="i16" class="altColor">
<td class="colFirst"><code>int</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#hashCode--">hashCode</a></span>()</code></th>
<td class="colLast">&nbsp;</td>
</tr>
<tr id="i17" class="rowColor">
<td class="colFirst"><code><a href="PatternSet.html" title="class in org.gradle.api.tasks.util">PatternSet</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#include-groovy.lang.Closure-">include</a></span>&#8203;(<a href="https://docs.groovy-lang.org/docs/groovy-3.0.17/html/gapi/groovy/lang/Closure.html?is-external=true" title="class or interface in groovy.lang" class="externalLink">Closure</a>&nbsp;closure)</code></th>
<td class="colLast">
<div class="block">Adds an include spec.</div>
</td>
</tr>
<tr id="i18" class="altColor">
<td class="colFirst"><code><a href="PatternSet.html" title="class in org.gradle.api.tasks.util">PatternSet</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#include-java.lang.Iterable-">include</a></span>&#8203;(java.lang.Iterable&nbsp;includes)</code></th>
<td class="colLast">
<div class="block">Adds an ANT style include pattern.</div>
</td>
</tr>
<tr id="i19" class="rowColor">
<td class="colFirst"><code><a href="PatternSet.html" title="class in org.gradle.api.tasks.util">PatternSet</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#include-java.lang.String...-">include</a></span>&#8203;(java.lang.String...&nbsp;includes)</code></th>
<td class="colLast">
<div class="block">Adds an ANT style include pattern.</div>
</td>
</tr>
<tr id="i20" class="altColor">
<td class="colFirst"><code><a href="PatternSet.html" title="class in org.gradle.api.tasks.util">PatternSet</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#include-org.gradle.api.specs.Spec-">include</a></span>&#8203;(<a href="../../specs/Spec.html" title="interface in org.gradle.api.specs">Spec</a>&lt;<a href="../../file/FileTreeElement.html" title="interface in org.gradle.api.file">FileTreeElement</a>&gt;&nbsp;spec)</code></th>
<td class="colLast">
<div class="block">Adds an include spec.</div>
</td>
</tr>
<tr id="i21" class="rowColor">
<td class="colFirst"><code><a href="PatternSet.html" title="class in org.gradle.api.tasks.util">PatternSet</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#includeSpecs-java.lang.Iterable-">includeSpecs</a></span>&#8203;(java.lang.Iterable&lt;<a href="../../specs/Spec.html" title="interface in org.gradle.api.specs">Spec</a>&lt;<a href="../../file/FileTreeElement.html" title="interface in org.gradle.api.file">FileTreeElement</a>&gt;&gt;&nbsp;includeSpecs)</code></th>
<td class="colLast">&nbsp;</td>
</tr>
<tr id="i22" class="altColor">
<td class="colFirst"><code><a href="PatternSet.html" title="class in org.gradle.api.tasks.util">PatternSet</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#intersect--">intersect</a></span>()</code></th>
<td class="colLast">&nbsp;</td>
</tr>
<tr id="i23" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#isCaseSensitive--">isCaseSensitive</a></span>()</code></th>
<td class="colLast">&nbsp;</td>
</tr>
<tr id="i24" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#isEmpty--">isEmpty</a></span>()</code></th>
<td class="colLast">
<div class="block">The PatternSet is considered empty when no includes or excludes have been added.</div>
</td>
</tr>
<tr id="i25" class="rowColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setCaseSensitive-boolean-">setCaseSensitive</a></span>&#8203;(boolean&nbsp;caseSensitive)</code></th>
<td class="colLast">&nbsp;</td>
</tr>
<tr id="i26" class="altColor">
<td class="colFirst"><code><a href="PatternSet.html" title="class in org.gradle.api.tasks.util">PatternSet</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setExcludes-java.lang.Iterable-">setExcludes</a></span>&#8203;(java.lang.Iterable&lt;java.lang.String&gt;&nbsp;excludes)</code></th>
<td class="colLast">
<div class="block">Set the allowable exclude patterns.</div>
</td>
</tr>
<tr id="i27" class="rowColor">
<td class="colFirst"><code><a href="PatternSet.html" title="class in org.gradle.api.tasks.util">PatternSet</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setIncludes-java.lang.Iterable-">setIncludes</a></span>&#8203;(java.lang.Iterable&lt;java.lang.String&gt;&nbsp;includes)</code></th>
<td class="colLast">
<div class="block">Set the allowable include patterns.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>clone, finalize, getClass, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a name="PatternSet--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>PatternSet</h4>
<pre>public&nbsp;PatternSet()</pre>
</li>
</ul>
<a name="PatternSet-org.gradle.api.tasks.util.PatternSet-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>PatternSet</h4>
<pre>protected&nbsp;PatternSet&#8203;(<a href="PatternSet.html" title="class in org.gradle.api.tasks.util">PatternSet</a>&nbsp;patternSet)</pre>
</li>
</ul>
<a name="PatternSet-org.gradle.api.tasks.util.internal.PatternSpecFactory-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>PatternSet</h4>
<pre>protected&nbsp;PatternSet&#8203;(org.gradle.api.tasks.util.internal.PatternSpecFactory&nbsp;patternSpecFactory)</pre>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="equals-java.lang.Object-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>equals</h4>
<pre class="methodSignature">public&nbsp;boolean&nbsp;equals&#8203;(@Nullable
                      java.lang.Object&nbsp;o)</pre>
<dl>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code>equals</code>&nbsp;in class&nbsp;<code>java.lang.Object</code></dd>
</dl>
</li>
</ul>
<a name="hashCode--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>hashCode</h4>
<pre class="methodSignature">public&nbsp;int&nbsp;hashCode()</pre>
<dl>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code>hashCode</code>&nbsp;in class&nbsp;<code>java.lang.Object</code></dd>
</dl>
</li>
</ul>
<a name="copyFrom-org.gradle.api.tasks.util.PatternFilterable-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>copyFrom</h4>
<pre class="methodSignature">public&nbsp;<a href="PatternSet.html" title="class in org.gradle.api.tasks.util">PatternSet</a>&nbsp;copyFrom&#8203;(<a href="PatternFilterable.html" title="interface in org.gradle.api.tasks.util">PatternFilterable</a>&nbsp;sourcePattern)</pre>
</li>
</ul>
<a name="doCopyFrom-org.gradle.api.tasks.util.PatternSet-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>doCopyFrom</h4>
<pre class="methodSignature">protected&nbsp;<a href="PatternSet.html" title="class in org.gradle.api.tasks.util">PatternSet</a>&nbsp;doCopyFrom&#8203;(<a href="PatternSet.html" title="class in org.gradle.api.tasks.util">PatternSet</a>&nbsp;from)</pre>
</li>
</ul>
<a name="intersect--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>intersect</h4>
<pre class="methodSignature">public&nbsp;<a href="PatternSet.html" title="class in org.gradle.api.tasks.util">PatternSet</a>&nbsp;intersect()</pre>
</li>
</ul>
<a name="isEmpty--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isEmpty</h4>
<pre class="methodSignature">public&nbsp;boolean&nbsp;isEmpty()</pre>
<div class="block">The PatternSet is considered empty when no includes or excludes have been added.

 The Spec returned by getAsSpec method only contains the default excludes patterns
 in this case.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>true when no includes or excludes have been added to this instance</dd>
</dl>
</li>
</ul>
<a name="getAsSpec--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getAsSpec</h4>
<pre class="methodSignature">public&nbsp;<a href="../../specs/Spec.html" title="interface in org.gradle.api.specs">Spec</a>&lt;<a href="../../file/FileTreeElement.html" title="interface in org.gradle.api.file">FileTreeElement</a>&gt;&nbsp;getAsSpec()</pre>
</li>
</ul>
<a name="getAsIncludeSpec--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getAsIncludeSpec</h4>
<pre class="methodSignature">public&nbsp;<a href="../../specs/Spec.html" title="interface in org.gradle.api.specs">Spec</a>&lt;<a href="../../file/FileTreeElement.html" title="interface in org.gradle.api.file">FileTreeElement</a>&gt;&nbsp;getAsIncludeSpec()</pre>
</li>
</ul>
<a name="getAsExcludeSpec--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getAsExcludeSpec</h4>
<pre class="methodSignature">public&nbsp;<a href="../../specs/Spec.html" title="interface in org.gradle.api.specs">Spec</a>&lt;<a href="../../file/FileTreeElement.html" title="interface in org.gradle.api.file">FileTreeElement</a>&gt;&nbsp;getAsExcludeSpec()</pre>
</li>
</ul>
<a name="getIncludes--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getIncludes</h4>
<pre class="methodSignature">public&nbsp;java.util.Set&lt;java.lang.String&gt;&nbsp;getIncludes()</pre>
<div class="block"><span class="descfrmTypeLabel">Description copied from interface:&nbsp;<code><a href="PatternFilterable.html#getIncludes--">PatternFilterable</a></code></span></div>
<div class="block">Returns the set of include patterns.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="PatternFilterable.html#getIncludes--">getIncludes</a></code>&nbsp;in interface&nbsp;<code><a href="PatternFilterable.html" title="interface in org.gradle.api.tasks.util">PatternFilterable</a></code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The include patterns. Returns an empty set when there are no include patterns.</dd>
</dl>
</li>
</ul>
<a name="getIncludeSpecs--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getIncludeSpecs</h4>
<pre class="methodSignature">public&nbsp;java.util.Set&lt;<a href="../../specs/Spec.html" title="interface in org.gradle.api.specs">Spec</a>&lt;<a href="../../file/FileTreeElement.html" title="interface in org.gradle.api.file">FileTreeElement</a>&gt;&gt;&nbsp;getIncludeSpecs()</pre>
</li>
</ul>
<a name="setIncludes-java.lang.Iterable-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setIncludes</h4>
<pre class="methodSignature">public&nbsp;<a href="PatternSet.html" title="class in org.gradle.api.tasks.util">PatternSet</a>&nbsp;setIncludes&#8203;(java.lang.Iterable&lt;java.lang.String&gt;&nbsp;includes)</pre>
<div class="block"><span class="descfrmTypeLabel">Description copied from interface:&nbsp;<code><a href="PatternFilterable.html#setIncludes-java.lang.Iterable-">PatternFilterable</a></code></span></div>
<div class="block">Set the allowable include patterns.  Note that unlike <a href="PatternFilterable.html#include-java.lang.Iterable-"><code>PatternFilterable.include(Iterable)</code></a> this replaces any previously
 defined includes.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="PatternFilterable.html#setIncludes-java.lang.Iterable-">setIncludes</a></code>&nbsp;in interface&nbsp;<code><a href="PatternFilterable.html" title="interface in org.gradle.api.tasks.util">PatternFilterable</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>includes</code> - an Iterable providing new include patterns</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>this</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="PatternFilterable.html" title="interface in org.gradle.api.tasks.util"><code>Pattern Format</code></a></dd>
</dl>
</li>
</ul>
<a name="include-java.lang.String...-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>include</h4>
<pre class="methodSignature">public&nbsp;<a href="PatternSet.html" title="class in org.gradle.api.tasks.util">PatternSet</a>&nbsp;include&#8203;(java.lang.String...&nbsp;includes)</pre>
<div class="block"><span class="descfrmTypeLabel">Description copied from interface:&nbsp;<code><a href="PatternFilterable.html#include-java.lang.String...-">PatternFilterable</a></code></span></div>
<div class="block">Adds an ANT style include pattern. This method may be called multiple times to append new patterns and multiple
 patterns may be specified in a single call.

 If includes are not provided, then all files in this container will be included. If includes are provided, then a
 file must match at least one of the include patterns to be processed.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="PatternFilterable.html#include-java.lang.String...-">include</a></code>&nbsp;in interface&nbsp;<code><a href="PatternFilterable.html" title="interface in org.gradle.api.tasks.util">PatternFilterable</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>includes</code> - a vararg list of include patterns</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>this</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="PatternFilterable.html" title="interface in org.gradle.api.tasks.util"><code>Pattern Format</code></a></dd>
</dl>
</li>
</ul>
<a name="include-java.lang.Iterable-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>include</h4>
<pre class="methodSignature">public&nbsp;<a href="PatternSet.html" title="class in org.gradle.api.tasks.util">PatternSet</a>&nbsp;include&#8203;(java.lang.Iterable&nbsp;includes)</pre>
<div class="block"><span class="descfrmTypeLabel">Description copied from interface:&nbsp;<code><a href="PatternFilterable.html#include-java.lang.Iterable-">PatternFilterable</a></code></span></div>
<div class="block">Adds an ANT style include pattern. This method may be called multiple times to append new patterns and multiple
 patterns may be specified in a single call.

 If includes are not provided, then all files in this container will be included. If includes are provided, then a
 file must match at least one of the include patterns to be processed.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="PatternFilterable.html#include-java.lang.Iterable-">include</a></code>&nbsp;in interface&nbsp;<code><a href="PatternFilterable.html" title="interface in org.gradle.api.tasks.util">PatternFilterable</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>includes</code> - a Iterable providing more include patterns</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>this</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="PatternFilterable.html" title="interface in org.gradle.api.tasks.util"><code>Pattern Format</code></a></dd>
</dl>
</li>
</ul>
<a name="include-org.gradle.api.specs.Spec-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>include</h4>
<pre class="methodSignature">public&nbsp;<a href="PatternSet.html" title="class in org.gradle.api.tasks.util">PatternSet</a>&nbsp;include&#8203;(<a href="../../specs/Spec.html" title="interface in org.gradle.api.specs">Spec</a>&lt;<a href="../../file/FileTreeElement.html" title="interface in org.gradle.api.file">FileTreeElement</a>&gt;&nbsp;spec)</pre>
<div class="block"><span class="descfrmTypeLabel">Description copied from interface:&nbsp;<code><a href="PatternFilterable.html#include-org.gradle.api.specs.Spec-">PatternFilterable</a></code></span></div>
<div class="block">Adds an include spec. This method may be called multiple times to append new specs.

 If includes are not provided, then all files in this container will be included. If includes are provided, then a
 file must match at least one of the include patterns or specs to be included.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="PatternFilterable.html#include-org.gradle.api.specs.Spec-">include</a></code>&nbsp;in interface&nbsp;<code><a href="PatternFilterable.html" title="interface in org.gradle.api.tasks.util">PatternFilterable</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>spec</code> - the spec to add</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>this</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="PatternFilterable.html" title="interface in org.gradle.api.tasks.util"><code>Pattern Format</code></a></dd>
</dl>
</li>
</ul>
<a name="getExcludes--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getExcludes</h4>
<pre class="methodSignature">public&nbsp;java.util.Set&lt;java.lang.String&gt;&nbsp;getExcludes()</pre>
<div class="block"><span class="descfrmTypeLabel">Description copied from interface:&nbsp;<code><a href="PatternFilterable.html#getExcludes--">PatternFilterable</a></code></span></div>
<div class="block">Returns the set of exclude patterns.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="PatternFilterable.html#getExcludes--">getExcludes</a></code>&nbsp;in interface&nbsp;<code><a href="PatternFilterable.html" title="interface in org.gradle.api.tasks.util">PatternFilterable</a></code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The exclude patterns. Returns an empty set when there are no exclude patterns.</dd>
</dl>
</li>
</ul>
<a name="getExcludeSpecs--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getExcludeSpecs</h4>
<pre class="methodSignature">public&nbsp;java.util.Set&lt;<a href="../../specs/Spec.html" title="interface in org.gradle.api.specs">Spec</a>&lt;<a href="../../file/FileTreeElement.html" title="interface in org.gradle.api.file">FileTreeElement</a>&gt;&gt;&nbsp;getExcludeSpecs()</pre>
</li>
</ul>
<a name="setExcludes-java.lang.Iterable-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setExcludes</h4>
<pre class="methodSignature">public&nbsp;<a href="PatternSet.html" title="class in org.gradle.api.tasks.util">PatternSet</a>&nbsp;setExcludes&#8203;(java.lang.Iterable&lt;java.lang.String&gt;&nbsp;excludes)</pre>
<div class="block"><span class="descfrmTypeLabel">Description copied from interface:&nbsp;<code><a href="PatternFilterable.html#setExcludes-java.lang.Iterable-">PatternFilterable</a></code></span></div>
<div class="block">Set the allowable exclude patterns.  Note that unlike <a href="PatternFilterable.html#exclude-java.lang.Iterable-"><code>PatternFilterable.exclude(Iterable)</code></a> this replaces any previously
 defined excludes.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="PatternFilterable.html#setExcludes-java.lang.Iterable-">setExcludes</a></code>&nbsp;in interface&nbsp;<code><a href="PatternFilterable.html" title="interface in org.gradle.api.tasks.util">PatternFilterable</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>excludes</code> - an Iterable providing new exclude patterns</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>this</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="PatternFilterable.html" title="interface in org.gradle.api.tasks.util"><code>Pattern Format</code></a></dd>
</dl>
</li>
</ul>
<a name="isCaseSensitive--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isCaseSensitive</h4>
<pre class="methodSignature">public&nbsp;boolean&nbsp;isCaseSensitive()</pre>
</li>
</ul>
<a name="setCaseSensitive-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCaseSensitive</h4>
<pre class="methodSignature">public&nbsp;void&nbsp;setCaseSensitive&#8203;(boolean&nbsp;caseSensitive)</pre>
</li>
</ul>
<a name="includeSpecs-java.lang.Iterable-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>includeSpecs</h4>
<pre class="methodSignature">public&nbsp;<a href="PatternSet.html" title="class in org.gradle.api.tasks.util">PatternSet</a>&nbsp;includeSpecs&#8203;(java.lang.Iterable&lt;<a href="../../specs/Spec.html" title="interface in org.gradle.api.specs">Spec</a>&lt;<a href="../../file/FileTreeElement.html" title="interface in org.gradle.api.file">FileTreeElement</a>&gt;&gt;&nbsp;includeSpecs)</pre>
</li>
</ul>
<a name="include-groovy.lang.Closure-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>include</h4>
<pre class="methodSignature">public&nbsp;<a href="PatternSet.html" title="class in org.gradle.api.tasks.util">PatternSet</a>&nbsp;include&#8203;(<a href="https://docs.groovy-lang.org/docs/groovy-3.0.17/html/gapi/groovy/lang/Closure.html?is-external=true" title="class or interface in groovy.lang" class="externalLink">Closure</a>&nbsp;closure)</pre>
<div class="block"><span class="descfrmTypeLabel">Description copied from interface:&nbsp;<code><a href="PatternFilterable.html#include-groovy.lang.Closure-">PatternFilterable</a></code></span></div>
<div class="block">Adds an include spec. This method may be called multiple times to append new specs. The given closure is passed a
 <a href="../../file/FileTreeElement.html" title="interface in org.gradle.api.file"><code>FileTreeElement</code></a> as its parameter.

 If includes are not provided, then all files in this container will be included. If includes are provided, then a
 file must match at least one of the include patterns or specs to be included.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="PatternFilterable.html#include-groovy.lang.Closure-">include</a></code>&nbsp;in interface&nbsp;<code><a href="PatternFilterable.html" title="interface in org.gradle.api.tasks.util">PatternFilterable</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>closure</code> - the spec to add</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>this</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="PatternFilterable.html" title="interface in org.gradle.api.tasks.util"><code>Pattern Format</code></a></dd>
</dl>
</li>
</ul>
<a name="exclude-java.lang.String...-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>exclude</h4>
<pre class="methodSignature">public&nbsp;<a href="PatternSet.html" title="class in org.gradle.api.tasks.util">PatternSet</a>&nbsp;exclude&#8203;(java.lang.String...&nbsp;excludes)</pre>
<div class="block"><span class="descfrmTypeLabel">Description copied from interface:&nbsp;<code><a href="PatternFilterable.html#exclude-java.lang.String...-">PatternFilterable</a></code></span></div>
<div class="block">Adds an ANT style exclude pattern. This method may be called multiple times to append new patterns and multiple
 patterns may be specified in a single call.

 If excludes are not provided, then no files will be excluded. If excludes are provided, then files must not match
 any exclude pattern to be processed.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="PatternFilterable.html#exclude-java.lang.String...-">exclude</a></code>&nbsp;in interface&nbsp;<code><a href="PatternFilterable.html" title="interface in org.gradle.api.tasks.util">PatternFilterable</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>excludes</code> - a vararg list of exclude patterns</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>this</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="PatternFilterable.html" title="interface in org.gradle.api.tasks.util"><code>Pattern Format</code></a></dd>
</dl>
</li>
</ul>
<a name="exclude-java.lang.Iterable-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>exclude</h4>
<pre class="methodSignature">public&nbsp;<a href="PatternSet.html" title="class in org.gradle.api.tasks.util">PatternSet</a>&nbsp;exclude&#8203;(java.lang.Iterable&nbsp;excludes)</pre>
<div class="block"><span class="descfrmTypeLabel">Description copied from interface:&nbsp;<code><a href="PatternFilterable.html#exclude-java.lang.Iterable-">PatternFilterable</a></code></span></div>
<div class="block">Adds an ANT style exclude pattern. This method may be called multiple times to append new patterns and multiple
 patterns may be specified in a single call.

 If excludes are not provided, then no files will be excluded. If excludes are provided, then files must not match
 any exclude pattern to be processed.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="PatternFilterable.html#exclude-java.lang.Iterable-">exclude</a></code>&nbsp;in interface&nbsp;<code><a href="PatternFilterable.html" title="interface in org.gradle.api.tasks.util">PatternFilterable</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>excludes</code> - a Iterable providing new exclude patterns</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>this</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="PatternFilterable.html" title="interface in org.gradle.api.tasks.util"><code>Pattern Format</code></a></dd>
</dl>
</li>
</ul>
<a name="exclude-org.gradle.api.specs.Spec-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>exclude</h4>
<pre class="methodSignature">public&nbsp;<a href="PatternSet.html" title="class in org.gradle.api.tasks.util">PatternSet</a>&nbsp;exclude&#8203;(<a href="../../specs/Spec.html" title="interface in org.gradle.api.specs">Spec</a>&lt;<a href="../../file/FileTreeElement.html" title="interface in org.gradle.api.file">FileTreeElement</a>&gt;&nbsp;spec)</pre>
<div class="block"><span class="descfrmTypeLabel">Description copied from interface:&nbsp;<code><a href="PatternFilterable.html#exclude-org.gradle.api.specs.Spec-">PatternFilterable</a></code></span></div>
<div class="block">Adds an exclude spec. This method may be called multiple times to append new specs.

 If excludes are not provided, then no files will be excluded. If excludes are provided, then files must not match
 any exclude pattern to be processed.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="PatternFilterable.html#exclude-org.gradle.api.specs.Spec-">exclude</a></code>&nbsp;in interface&nbsp;<code><a href="PatternFilterable.html" title="interface in org.gradle.api.tasks.util">PatternFilterable</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>spec</code> - the spec to add</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>this</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="PatternFilterable.html" title="interface in org.gradle.api.tasks.util"><code>Pattern Format</code></a></dd>
</dl>
</li>
</ul>
<a name="excludeSpecs-java.lang.Iterable-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>excludeSpecs</h4>
<pre class="methodSignature">public&nbsp;<a href="PatternSet.html" title="class in org.gradle.api.tasks.util">PatternSet</a>&nbsp;excludeSpecs&#8203;(java.lang.Iterable&lt;<a href="../../specs/Spec.html" title="interface in org.gradle.api.specs">Spec</a>&lt;<a href="../../file/FileTreeElement.html" title="interface in org.gradle.api.file">FileTreeElement</a>&gt;&gt;&nbsp;excludes)</pre>
</li>
</ul>
<a name="exclude-groovy.lang.Closure-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>exclude</h4>
<pre class="methodSignature">public&nbsp;<a href="PatternSet.html" title="class in org.gradle.api.tasks.util">PatternSet</a>&nbsp;exclude&#8203;(<a href="https://docs.groovy-lang.org/docs/groovy-3.0.17/html/gapi/groovy/lang/Closure.html?is-external=true" title="class or interface in groovy.lang" class="externalLink">Closure</a>&nbsp;closure)</pre>
<div class="block"><span class="descfrmTypeLabel">Description copied from interface:&nbsp;<code><a href="PatternFilterable.html#exclude-groovy.lang.Closure-">PatternFilterable</a></code></span></div>
<div class="block">Adds an exclude spec. This method may be called multiple times to append new specs.The given closure is passed a
 <a href="../../file/FileTreeElement.html" title="interface in org.gradle.api.file"><code>FileTreeElement</code></a> as its parameter. The closure should return true or false. Example:

 <pre class='autoTested'>
 copySpec {
   from 'source'
   into 'destination'
   //an example of excluding files from certain configuration:
   exclude { it.file in configurations.someConf.files }
 }
 </pre>

 If excludes are not provided, then no files will be excluded. If excludes are provided, then files must not match
 any exclude pattern to be processed.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="PatternFilterable.html#exclude-groovy.lang.Closure-">exclude</a></code>&nbsp;in interface&nbsp;<code><a href="PatternFilterable.html" title="interface in org.gradle.api.tasks.util">PatternFilterable</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>closure</code> - the spec to add</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>this</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../file/FileTreeElement.html" title="interface in org.gradle.api.file"><code>FileTreeElement</code></a></dd>
</dl>
</li>
</ul>
<a name="addToAntBuilder-java.lang.Object-java.lang.String-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>addToAntBuilder</h4>
<pre class="methodSignature">public&nbsp;java.lang.Object&nbsp;addToAntBuilder&#8203;(java.lang.Object&nbsp;node,
                                        java.lang.String&nbsp;childNodeName)</pre>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../AntBuilderAware.html#addToAntBuilder-java.lang.Object-java.lang.String-">addToAntBuilder</a></code>&nbsp;in interface&nbsp;<code><a href="../AntBuilderAware.html" title="interface in org.gradle.api.tasks">AntBuilderAware</a></code></dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../../allclasses.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
