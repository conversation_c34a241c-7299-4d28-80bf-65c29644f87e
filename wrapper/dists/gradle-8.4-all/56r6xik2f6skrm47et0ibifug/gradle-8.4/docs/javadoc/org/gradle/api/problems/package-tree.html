<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc -->
<title>org.gradle.api.problems Class Hierarchy (Gradle API 8.4)</title>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<link rel="stylesheet" type="text/css" href="../../../../javadoc.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../jquery/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
<script type="text/javascript" src="../../../../jquery/jszip/dist/jszip.min.js"></script>
<script type="text/javascript" src="../../../../jquery/jszip-utils/dist/jszip-utils.min.js"></script>
<!--[if IE]>
<script type="text/javascript" src="../../../../jquery/jszip-utils/dist/jszip-utils-ie.min.js"></script>
<![endif]-->
<script type="text/javascript" src="../../../../jquery/jquery-3.6.0.min.js"></script>
<script type="text/javascript" src="../../../../jquery/jquery-ui.min.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="org.gradle.api.problems Class Hierarchy (Gradle API 8.4)";
        }
    }
    catch(err) {
    }
//-->
var pathtoroot = "../../../../";
var useModuleDirectories = false;
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="fixedNav">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li>Class</li>
<li class="navBarCell1Rev">Tree</li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses.html">All&nbsp;Classes</a></li>
</ul>
<ul class="navListSearch">
<li><label for="search">SEARCH:</label>
<input type="text" id="search" value="search" disabled="disabled">
<input type="reset" id="reset" value="reset" disabled="disabled">
</li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
</div>
<div class="navPadding">&nbsp;</div>
<script type="text/javascript"><!--
$('.navPadding').css('padding-top', $('.fixedNav').css("height"));
//-->
</script>
<div class="header">
<h1 class="title">Hierarchy For Package org.gradle.api.problems</h1>
<span class="packageHierarchyLabel">Package Hierarchies:</span>
<ul class="horizontal">
<li><a href="../../../../overview-tree.html">All Packages</a></li>
</ul>
</div>
<div class="contentContainer">
<h2 title="Class Hierarchy">Class Hierarchy</h2>
<ul>
<li class="circle">java.lang.Object
<ul>
<li class="circle">org.gradle.api.problems.<a href="ProblemGroup.html" title="class in org.gradle.api.problems"><span class="typeNameLink">ProblemGroup</span></a></li>
</ul>
</li>
</ul>
<h2 title="Interface Hierarchy">Interface Hierarchy</h2>
<ul>
<li class="circle">org.gradle.api.problems.<a href="DocLink.html" title="interface in org.gradle.api.problems"><span class="typeNameLink">DocLink</span></a></li>
<li class="circle">org.gradle.api.problems.<a href="Problem.html" title="interface in org.gradle.api.problems"><span class="typeNameLink">Problem</span></a>
<ul>
<li class="circle">org.gradle.api.problems.<a href="ReportableProblem.html" title="interface in org.gradle.api.problems"><span class="typeNameLink">ReportableProblem</span></a></li>
</ul>
</li>
<li class="circle">org.gradle.api.problems.<a href="ProblemBuilder.html" title="interface in org.gradle.api.problems"><span class="typeNameLink">ProblemBuilder</span></a></li>
<li class="circle">org.gradle.api.problems.<a href="ProblemBuilderDefiningDocumentation.html" title="interface in org.gradle.api.problems"><span class="typeNameLink">ProblemBuilderDefiningDocumentation</span></a></li>
<li class="circle">org.gradle.api.problems.<a href="ProblemBuilderDefiningGroup.html" title="interface in org.gradle.api.problems"><span class="typeNameLink">ProblemBuilderDefiningGroup</span></a></li>
<li class="circle">org.gradle.api.problems.<a href="ProblemBuilderDefiningLabel.html" title="interface in org.gradle.api.problems"><span class="typeNameLink">ProblemBuilderDefiningLabel</span></a></li>
<li class="circle">org.gradle.api.problems.<a href="ProblemBuilderDefiningLocation.html" title="interface in org.gradle.api.problems"><span class="typeNameLink">ProblemBuilderDefiningLocation</span></a></li>
<li class="circle">org.gradle.api.problems.<a href="ProblemBuilderDefiningType.html" title="interface in org.gradle.api.problems"><span class="typeNameLink">ProblemBuilderDefiningType</span></a></li>
<li class="circle">org.gradle.api.problems.<a href="ProblemBuilderSpec.html" title="interface in org.gradle.api.problems"><span class="typeNameLink">ProblemBuilderSpec</span></a></li>
<li class="circle">org.gradle.api.problems.<a href="ProblemLocation.html" title="interface in org.gradle.api.problems"><span class="typeNameLink">ProblemLocation</span></a></li>
<li class="circle">org.gradle.api.problems.<a href="Problems.html" title="interface in org.gradle.api.problems"><span class="typeNameLink">Problems</span></a></li>
</ul>
<h2 title="Enum Hierarchy">Enum Hierarchy</h2>
<ul>
<li class="circle">java.lang.Object
<ul>
<li class="circle">java.lang.Enum&lt;E&gt; (implements java.lang.Comparable&lt;T&gt;, java.io.Serializable)
<ul>
<li class="circle">org.gradle.api.problems.<a href="Severity.html" title="enum in org.gradle.api.problems"><span class="typeNameLink">Severity</span></a></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li>Class</li>
<li class="navBarCell1Rev">Tree</li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
