<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc -->
<title>SetProperty (Gradle API 8.4)</title>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<link rel="stylesheet" type="text/css" href="../../../../javadoc.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../jquery/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
<script type="text/javascript" src="../../../../jquery/jszip/dist/jszip.min.js"></script>
<script type="text/javascript" src="../../../../jquery/jszip-utils/dist/jszip-utils.min.js"></script>
<!--[if IE]>
<script type="text/javascript" src="../../../../jquery/jszip-utils/dist/jszip-utils-ie.min.js"></script>
<![endif]-->
<script type="text/javascript" src="../../../../jquery/jquery-3.6.0.min.js"></script>
<script type="text/javascript" src="../../../../jquery/jquery-ui.min.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="SetProperty (Gradle API 8.4)";
        }
    }
    catch(err) {
    }
//-->
var data = {"i0":6,"i1":6,"i2":6,"i3":6,"i4":6};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],4:["t3","Abstract Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
var pathtoroot = "../../../../";
var useModuleDirectories = false;
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="fixedNav">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses.html">All&nbsp;Classes</a></li>
</ul>
<ul class="navListSearch">
<li><label for="search">SEARCH:</label>
<input type="text" id="search" value="search" disabled="disabled">
<input type="reset" id="reset" value="reset" disabled="disabled">
</li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
</div>
<div class="navPadding">&nbsp;</div>
<script type="text/javascript"><!--
$('.navPadding').css('padding-top', $('.fixedNav').css("height"));
//-->
</script>
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle"><span class="packageLabelInType">Package</span>&nbsp;<a href="package-summary.html">org.gradle.api.provider</a></div>
<h2 title="Interface SetProperty" class="title">Interface SetProperty&lt;T&gt;</h2>
</div>
<div class="contentContainer">
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt><span class="paramLabel">Type Parameters:</span></dt>
<dd><code>T</code> - the type of elements.</dd>
</dl>
<dl>
<dt>All Superinterfaces:</dt>
<dd><code><a href="HasConfigurableValue.html" title="interface in org.gradle.api.provider">HasConfigurableValue</a></code>, <code><a href="HasMultipleValues.html" title="interface in org.gradle.api.provider">HasMultipleValues</a>&lt;T&gt;</code>, <code><a href="Provider.html" title="interface in org.gradle.api.provider">Provider</a>&lt;java.util.Set&lt;T&gt;&gt;</code></dd>
</dl>
<hr>
<pre>public interface <span class="typeNameLabel">SetProperty&lt;T&gt;</span>
extends <a href="Provider.html" title="interface in org.gradle.api.provider">Provider</a>&lt;java.util.Set&lt;T&gt;&gt;, <a href="HasMultipleValues.html" title="interface in org.gradle.api.provider">HasMultipleValues</a>&lt;T&gt;</pre>
<div class="block">Represents a property whose type is a <code>Set</code> of elements of type <a href="SetProperty.html" title="interface in org.gradle.api.provider"><code>SetProperty</code></a>. Retains iteration order.

 <p>
 You can create a <a href="SetProperty.html" title="interface in org.gradle.api.provider"><code>SetProperty</code></a> instance using factory method <a href="../model/ObjectFactory.html#setProperty-java.lang.Class-"><code>ObjectFactory.setProperty(Class)</code></a>.
 </p>

 <p><b>Note:</b> This interface is not intended for implementation by build script or plugin authors.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>4.5</dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t3" class="tableTab"><span><a href="javascript:show(4);">Abstract Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colSecond" scope="col">Method</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code><a href="SetProperty.html" title="interface in org.gradle.api.provider">SetProperty</a>&lt;<a href="SetProperty.html" title="type parameter in SetProperty">T</a>&gt;</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#convention-java.lang.Iterable-">convention</a></span>&#8203;(java.lang.Iterable&lt;? extends <a href="SetProperty.html" title="type parameter in SetProperty">T</a>&gt;&nbsp;elements)</code></th>
<td class="colLast">
<div class="block">Specifies the value to use as the convention for this property.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code><a href="SetProperty.html" title="interface in org.gradle.api.provider">SetProperty</a>&lt;<a href="SetProperty.html" title="type parameter in SetProperty">T</a>&gt;</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#convention-org.gradle.api.provider.Provider-">convention</a></span>&#8203;(<a href="Provider.html" title="interface in org.gradle.api.provider">Provider</a>&lt;? extends java.lang.Iterable&lt;? extends <a href="SetProperty.html" title="type parameter in SetProperty">T</a>&gt;&gt;&nbsp;provider)</code></th>
<td class="colLast">
<div class="block">Specifies the provider of the value to use as the convention for this property.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code><a href="SetProperty.html" title="interface in org.gradle.api.provider">SetProperty</a>&lt;<a href="SetProperty.html" title="type parameter in SetProperty">T</a>&gt;</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#empty--">empty</a></span>()</code></th>
<td class="colLast">
<div class="block">Sets the value of this property to an empty collection, and replaces any existing value.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code><a href="SetProperty.html" title="interface in org.gradle.api.provider">SetProperty</a>&lt;<a href="SetProperty.html" title="type parameter in SetProperty">T</a>&gt;</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#value-java.lang.Iterable-">value</a></span>&#8203;(java.lang.Iterable&lt;? extends <a href="SetProperty.html" title="type parameter in SetProperty">T</a>&gt;&nbsp;elements)</code></th>
<td class="colLast">
<div class="block">Sets the value of the property to the elements of the given iterable, and replaces any existing value.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code><a href="SetProperty.html" title="interface in org.gradle.api.provider">SetProperty</a>&lt;<a href="SetProperty.html" title="type parameter in SetProperty">T</a>&gt;</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#value-org.gradle.api.provider.Provider-">value</a></span>&#8203;(<a href="Provider.html" title="interface in org.gradle.api.provider">Provider</a>&lt;? extends java.lang.Iterable&lt;? extends <a href="SetProperty.html" title="type parameter in SetProperty">T</a>&gt;&gt;&nbsp;provider)</code></th>
<td class="colLast">
<div class="block">Sets the property to have the same value of the given provider, and replaces any existing value.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.org.gradle.api.provider.HasConfigurableValue">
<!--   -->
</a>
<h3>Methods inherited from interface&nbsp;org.gradle.api.provider.<a href="HasConfigurableValue.html" title="interface in org.gradle.api.provider">HasConfigurableValue</a></h3>
<code><a href="HasConfigurableValue.html#disallowChanges--">disallowChanges</a>, <a href="HasConfigurableValue.html#disallowUnsafeRead--">disallowUnsafeRead</a>, <a href="HasConfigurableValue.html#finalizeValueOnRead--">finalizeValueOnRead</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.org.gradle.api.provider.HasMultipleValues">
<!--   -->
</a>
<h3>Methods inherited from interface&nbsp;org.gradle.api.provider.<a href="HasMultipleValues.html" title="interface in org.gradle.api.provider">HasMultipleValues</a></h3>
<code><a href="HasMultipleValues.html#add-org.gradle.api.provider.Provider-">add</a>, <a href="HasMultipleValues.html#add-T-">add</a>, <a href="HasMultipleValues.html#addAll-java.lang.Iterable-">addAll</a>, <a href="HasMultipleValues.html#addAll-org.gradle.api.provider.Provider-">addAll</a>, <a href="HasMultipleValues.html#addAll-T...-">addAll</a>, <a href="HasMultipleValues.html#finalizeValue--">finalizeValue</a>, <a href="HasMultipleValues.html#set-java.lang.Iterable-">set</a>, <a href="HasMultipleValues.html#set-org.gradle.api.provider.Provider-">set</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.org.gradle.api.provider.Provider">
<!--   -->
</a>
<h3>Methods inherited from interface&nbsp;org.gradle.api.provider.<a href="Provider.html" title="interface in org.gradle.api.provider">Provider</a></h3>
<code><a href="Provider.html#filter-java.util.function.Predicate-">filter</a>, <a href="Provider.html#flatMap-org.gradle.api.Transformer-">flatMap</a>, <a href="Provider.html#forUseAtConfigurationTime--">forUseAtConfigurationTime</a>, <a href="Provider.html#get--">get</a>, <a href="Provider.html#getOrElse-T-">getOrElse</a>, <a href="Provider.html#getOrNull--">getOrNull</a>, <a href="Provider.html#isPresent--">isPresent</a>, <a href="Provider.html#map-org.gradle.api.Transformer-">map</a>, <a href="Provider.html#orElse-org.gradle.api.provider.Provider-">orElse</a>, <a href="Provider.html#orElse-T-">orElse</a>, <a href="Provider.html#zip-org.gradle.api.provider.Provider-java.util.function.BiFunction-">zip</a></code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="empty--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>empty</h4>
<pre class="methodSignature"><a href="SetProperty.html" title="interface in org.gradle.api.provider">SetProperty</a>&lt;<a href="SetProperty.html" title="type parameter in SetProperty">T</a>&gt;&nbsp;empty()</pre>
<div class="block">Sets the value of this property to an empty collection, and replaces any existing value.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="HasMultipleValues.html#empty--">empty</a></code>&nbsp;in interface&nbsp;<code><a href="HasMultipleValues.html" title="interface in org.gradle.api.provider">HasMultipleValues</a>&lt;<a href="SetProperty.html" title="type parameter in SetProperty">T</a>&gt;</code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>this property.</dd>
</dl>
</li>
</ul>
<a name="value-java.lang.Iterable-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>value</h4>
<pre class="methodSignature"><a href="SetProperty.html" title="interface in org.gradle.api.provider">SetProperty</a>&lt;<a href="SetProperty.html" title="type parameter in SetProperty">T</a>&gt;&nbsp;value&#8203;(@Nullable
                     java.lang.Iterable&lt;? extends <a href="SetProperty.html" title="type parameter in SetProperty">T</a>&gt;&nbsp;elements)</pre>
<div class="block">Sets the value of the property to the elements of the given iterable, and replaces any existing value. This property will query the elements of the iterable each time the value of this property is queried.

 <p>This is the same as <a href="HasMultipleValues.html#set-java.lang.Iterable-"><code>HasMultipleValues.set(Iterable)</code></a> but returns this property to allow method chaining.</p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="HasMultipleValues.html#value-java.lang.Iterable-">value</a></code>&nbsp;in interface&nbsp;<code><a href="HasMultipleValues.html" title="interface in org.gradle.api.provider">HasMultipleValues</a>&lt;<a href="SetProperty.html" title="type parameter in SetProperty">T</a>&gt;</code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>elements</code> - The elements, can be null.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>this</dd>
</dl>
</li>
</ul>
<a name="value-org.gradle.api.provider.Provider-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>value</h4>
<pre class="methodSignature"><a href="SetProperty.html" title="interface in org.gradle.api.provider">SetProperty</a>&lt;<a href="SetProperty.html" title="type parameter in SetProperty">T</a>&gt;&nbsp;value&#8203;(<a href="Provider.html" title="interface in org.gradle.api.provider">Provider</a>&lt;? extends java.lang.Iterable&lt;? extends <a href="SetProperty.html" title="type parameter in SetProperty">T</a>&gt;&gt;&nbsp;provider)</pre>
<div class="block">Sets the property to have the same value of the given provider, and replaces any existing value. This property will track the value of the provider and query its value each time the value of this property is queried. When the provider has no value, this property will also have no value.

 <p>This is the same as <a href="HasMultipleValues.html#set-org.gradle.api.provider.Provider-"><code>HasMultipleValues.set(Provider)</code></a> but returns this property to allow method chaining.</p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="HasMultipleValues.html#value-org.gradle.api.provider.Provider-">value</a></code>&nbsp;in interface&nbsp;<code><a href="HasMultipleValues.html" title="interface in org.gradle.api.provider">HasMultipleValues</a>&lt;<a href="SetProperty.html" title="type parameter in SetProperty">T</a>&gt;</code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>provider</code> - Provider of the elements.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>this</dd>
</dl>
</li>
</ul>
<a name="convention-java.lang.Iterable-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>convention</h4>
<pre class="methodSignature"><a href="SetProperty.html" title="interface in org.gradle.api.provider">SetProperty</a>&lt;<a href="SetProperty.html" title="type parameter in SetProperty">T</a>&gt;&nbsp;convention&#8203;(@Nullable
                          java.lang.Iterable&lt;? extends <a href="SetProperty.html" title="type parameter in SetProperty">T</a>&gt;&nbsp;elements)</pre>
<div class="block">Specifies the value to use as the convention for this property. The convention is used when no value has been set for this property.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="HasMultipleValues.html#convention-java.lang.Iterable-">convention</a></code>&nbsp;in interface&nbsp;<code><a href="HasMultipleValues.html" title="interface in org.gradle.api.provider">HasMultipleValues</a>&lt;<a href="SetProperty.html" title="type parameter in SetProperty">T</a>&gt;</code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>elements</code> - The elements, or <code>null</code> when the convention is that the property has no value.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>this</dd>
</dl>
</li>
</ul>
<a name="convention-org.gradle.api.provider.Provider-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>convention</h4>
<pre class="methodSignature"><a href="SetProperty.html" title="interface in org.gradle.api.provider">SetProperty</a>&lt;<a href="SetProperty.html" title="type parameter in SetProperty">T</a>&gt;&nbsp;convention&#8203;(<a href="Provider.html" title="interface in org.gradle.api.provider">Provider</a>&lt;? extends java.lang.Iterable&lt;? extends <a href="SetProperty.html" title="type parameter in SetProperty">T</a>&gt;&gt;&nbsp;provider)</pre>
<div class="block">Specifies the provider of the value to use as the convention for this property. The convention is used when no value has been set for this property.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="HasMultipleValues.html#convention-org.gradle.api.provider.Provider-">convention</a></code>&nbsp;in interface&nbsp;<code><a href="HasMultipleValues.html" title="interface in org.gradle.api.provider">HasMultipleValues</a>&lt;<a href="SetProperty.html" title="type parameter in SetProperty">T</a>&gt;</code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>provider</code> - The provider of the elements</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>this</dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
