<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc -->
<title>Dependencies (Gradle API 8.4)</title>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<link rel="stylesheet" type="text/css" href="../../../../../javadoc.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../jquery/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
<script type="text/javascript" src="../../../../../jquery/jszip/dist/jszip.min.js"></script>
<script type="text/javascript" src="../../../../../jquery/jszip-utils/dist/jszip-utils.min.js"></script>
<!--[if IE]>
<script type="text/javascript" src="../../../../../jquery/jszip-utils/dist/jszip-utils-ie.min.js"></script>
<![endif]-->
<script type="text/javascript" src="../../../../../jquery/jquery-3.6.0.min.js"></script>
<script type="text/javascript" src="../../../../../jquery/jquery-ui.min.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="Dependencies (Gradle API 8.4)";
        }
    }
    catch(err) {
    }
//-->
var data = {"i0":6,"i1":6,"i2":6,"i3":18,"i4":18,"i5":18,"i6":18};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],4:["t3","Abstract Methods"],16:["t5","Default Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
var pathtoroot = "../../../../../";
var useModuleDirectories = false;
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="fixedNav">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../../allclasses.html">All&nbsp;Classes</a></li>
</ul>
<ul class="navListSearch">
<li><label for="search">SEARCH:</label>
<input type="text" id="search" value="search" disabled="disabled">
<input type="reset" id="reset" value="reset" disabled="disabled">
</li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
</div>
<div class="navPadding">&nbsp;</div>
<script type="text/javascript"><!--
$('.navPadding').css('padding-top', $('.fixedNav').css("height"));
//-->
</script>
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle"><span class="packageLabelInType">Package</span>&nbsp;<a href="package-summary.html">org.gradle.api.artifacts.dsl</a></div>
<h2 title="Interface Dependencies" class="title">Interface Dependencies</h2>
</div>
<div class="contentContainer">
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Known Subinterfaces:</dt>
<dd><code><a href="GradleDependencies.html" title="interface in org.gradle.api.artifacts.dsl">GradleDependencies</a></code>, <code><a href="../../plugins/jvm/JvmComponentDependencies.html" title="interface in org.gradle.api.plugins.jvm">JvmComponentDependencies</a></code></dd>
</dl>
<hr>
<pre><a href="../../Incubating.html" title="annotation in org.gradle.api">@Incubating</a>
public interface <span class="typeNameLabel">Dependencies</span></pre>
<div class="block">Universal APIs that are available for all <code>dependencies</code> blocks.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>7.6</dd>
<dt><span class="simpleTagLabel">API Note:</span></dt>
<dd>This interface is intended to be used to mix-in DSL methods for <code>dependencies</code> blocks.</dd>
<dt><span class="simpleTagLabel">Implementation Requirements:</span></dt>
<dd>The default implementation of all methods should not be overridden.</dd>
<dt><span class="simpleTagLabel">Implementation Note:</span></dt>
<dd>Changes to this interface may require changes to the
 <code>extension module for Groovy DSL</code> or
 <code>extension functions for Kotlin DSL</code>.</dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t3" class="tableTab"><span><a href="javascript:show(4);">Abstract Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t5" class="tableTab"><span><a href="javascript:show(16);">Default Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colSecond" scope="col">Method</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code><a href="DependencyFactory.html" title="interface in org.gradle.api.artifacts.dsl">DependencyFactory</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getDependencyFactory--">getDependencyFactory</a></span>()</code></th>
<td class="colLast">
<div class="block">A dependency factory is used to convert supported dependency notations into <a href="../Dependency.html" title="interface in org.gradle.api.artifacts"><code>Dependency</code></a> instances.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code><a href="../../model/ObjectFactory.html" title="interface in org.gradle.api.model">ObjectFactory</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getObjectFactory--">getObjectFactory</a></span>()</code></th>
<td class="colLast">
<div class="block">Injected service to create named objects.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code><a href="../../Project.html" title="interface in org.gradle.api">Project</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getProject--">getProject</a></span>()</code></th>
<td class="colLast">
<div class="block">The current project.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>default <a href="../ExternalModuleDependency.html" title="interface in org.gradle.api.artifacts">ExternalModuleDependency</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#module-java.lang.CharSequence-">module</a></span>&#8203;(java.lang.CharSequence&nbsp;dependencyNotation)</code></th>
<td class="colLast">
<div class="block">Create an <a href="../ExternalModuleDependency.html" title="interface in org.gradle.api.artifacts"><code>ExternalModuleDependency</code></a> from the given notation.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>default <a href="../ExternalModuleDependency.html" title="interface in org.gradle.api.artifacts">ExternalModuleDependency</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#module-java.lang.String-java.lang.String-java.lang.String-">module</a></span>&#8203;(java.lang.String&nbsp;group,
      java.lang.String&nbsp;name,
      java.lang.String&nbsp;version)</code></th>
<td class="colLast">
<div class="block">Create an <a href="../ExternalModuleDependency.html" title="interface in org.gradle.api.artifacts"><code>ExternalModuleDependency</code></a> from a series of strings.</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>default <a href="../ProjectDependency.html" title="interface in org.gradle.api.artifacts">ProjectDependency</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#project--">project</a></span>()</code></th>
<td class="colLast">
<div class="block">Returns the current project as a <a href="../ProjectDependency.html" title="interface in org.gradle.api.artifacts"><code>ProjectDependency</code></a>.</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>default <a href="../ProjectDependency.html" title="interface in org.gradle.api.artifacts">ProjectDependency</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#project-java.lang.String-">project</a></span>&#8203;(java.lang.String&nbsp;projectPath)</code></th>
<td class="colLast">
<div class="block">Converts an absolute or relative path to a project into a <a href="../ProjectDependency.html" title="interface in org.gradle.api.artifacts"><code>ProjectDependency</code></a>.</div>
</td>
</tr>
</table>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="getDependencyFactory--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getDependencyFactory</h4>
<pre class="methodSignature">@Inject
<a href="DependencyFactory.html" title="interface in org.gradle.api.artifacts.dsl">DependencyFactory</a>&nbsp;getDependencyFactory()</pre>
<div class="block">A dependency factory is used to convert supported dependency notations into <a href="../Dependency.html" title="interface in org.gradle.api.artifacts"><code>Dependency</code></a> instances.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>a dependency factory</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="DependencyFactory.html" title="interface in org.gradle.api.artifacts.dsl"><code>DependencyFactory</code></a></dd>
<dt><span class="simpleTagLabel">Implementation Requirements:</span></dt>
<dd>Do not implement this method. Gradle generates the implementation automatically.</dd>
</dl>
</li>
</ul>
<a name="getProject--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getProject</h4>
<pre class="methodSignature">@Inject
<a href="../../Project.html" title="interface in org.gradle.api">Project</a>&nbsp;getProject()</pre>
<div class="block">The current project. You need to use <a href="#project--"><code>project()</code></a> or <a href="#project-java.lang.String-"><code>project(String)</code></a> to add a <a href="../ProjectDependency.html" title="interface in org.gradle.api.artifacts"><code>ProjectDependency</code></a>.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>current project</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>8.0</dd>
<dt><span class="simpleTagLabel">Implementation Requirements:</span></dt>
<dd>Do not implement this method. Gradle generates the implementation automatically.</dd>
</dl>
</li>
</ul>
<a name="project-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>project</h4>
<pre class="methodSignature">default&nbsp;<a href="../ProjectDependency.html" title="interface in org.gradle.api.artifacts">ProjectDependency</a>&nbsp;project&#8203;(java.lang.String&nbsp;projectPath)</pre>
<div class="block">Converts an absolute or relative path to a project into a <a href="../ProjectDependency.html" title="interface in org.gradle.api.artifacts"><code>ProjectDependency</code></a>. Project paths are separated by colons.

 This method fails if the project cannot be found.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>projectPath</code> - an absolute or relative path (from the current project) to a project</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>a <a href="../ProjectDependency.html" title="interface in org.gradle.api.artifacts"><code>ProjectDependency</code></a> for the given path</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../Project.html#project-java.lang.String-"><code>Project.project(String)</code></a></dd>
</dl>
</li>
</ul>
<a name="project--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>project</h4>
<pre class="methodSignature">default&nbsp;<a href="../ProjectDependency.html" title="interface in org.gradle.api.artifacts">ProjectDependency</a>&nbsp;project()</pre>
<div class="block">Returns the current project as a <a href="../ProjectDependency.html" title="interface in org.gradle.api.artifacts"><code>ProjectDependency</code></a>.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the current project as a dependency</dd>
</dl>
</li>
</ul>
<a name="module-java.lang.CharSequence-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>module</h4>
<pre class="methodSignature">default&nbsp;<a href="../ExternalModuleDependency.html" title="interface in org.gradle.api.artifacts">ExternalModuleDependency</a>&nbsp;module&#8203;(java.lang.CharSequence&nbsp;dependencyNotation)</pre>
<div class="block">Create an <a href="../ExternalModuleDependency.html" title="interface in org.gradle.api.artifacts"><code>ExternalModuleDependency</code></a> from the given notation.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>dependencyNotation</code> - dependency to add</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the new dependency</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="DependencyFactory.html#create-java.lang.CharSequence-"><code>Valid dependency notation for this method</code></a></dd>
</dl>
</li>
</ul>
<a name="module-java.lang.String-java.lang.String-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>module</h4>
<pre class="methodSignature">default&nbsp;<a href="../ExternalModuleDependency.html" title="interface in org.gradle.api.artifacts">ExternalModuleDependency</a>&nbsp;module&#8203;(@Nullable
                                        java.lang.String&nbsp;group,
                                        java.lang.String&nbsp;name,
                                        @Nullable
                                        java.lang.String&nbsp;version)</pre>
<div class="block">Create an <a href="../ExternalModuleDependency.html" title="interface in org.gradle.api.artifacts"><code>ExternalModuleDependency</code></a> from a series of strings.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>group</code> - the group (optional)</dd>
<dd><code>name</code> - the name</dd>
<dd><code>version</code> - the version (optional)</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the new dependency</dd>
</dl>
</li>
</ul>
<a name="getObjectFactory--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>getObjectFactory</h4>
<pre class="methodSignature">@Inject
<a href="../../model/ObjectFactory.html" title="interface in org.gradle.api.model">ObjectFactory</a>&nbsp;getObjectFactory()</pre>
<div class="block">Injected service to create named objects.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>injected service</dd>
<dt><span class="simpleTagLabel">Implementation Requirements:</span></dt>
<dd>Do not implement this method. Gradle generates the implementation automatically.</dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../../allclasses.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
