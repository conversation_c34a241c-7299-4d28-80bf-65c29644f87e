<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc -->
<title>CheckstylePlugin (Gradle API 8.4)</title>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<link rel="stylesheet" type="text/css" href="../../../../../javadoc.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../jquery/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
<script type="text/javascript" src="../../../../../jquery/jszip/dist/jszip.min.js"></script>
<script type="text/javascript" src="../../../../../jquery/jszip-utils/dist/jszip-utils.min.js"></script>
<!--[if IE]>
<script type="text/javascript" src="../../../../../jquery/jszip-utils/dist/jszip-utils-ie.min.js"></script>
<![endif]-->
<script type="text/javascript" src="../../../../../jquery/jquery-3.6.0.min.js"></script>
<script type="text/javascript" src="../../../../../jquery/jquery-ui.min.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="CheckstylePlugin (Gradle API 8.4)";
        }
    }
    catch(err) {
    }
//-->
var data = {"i0":10,"i1":10,"i2":10,"i3":10,"i4":10,"i5":10,"i6":10};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
var pathtoroot = "../../../../../";
var useModuleDirectories = false;
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="fixedNav">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../../allclasses.html">All&nbsp;Classes</a></li>
</ul>
<ul class="navListSearch">
<li><label for="search">SEARCH:</label>
<input type="text" id="search" value="search" disabled="disabled">
<input type="reset" id="reset" value="reset" disabled="disabled">
</li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field.summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#field.detail">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
</div>
<div class="navPadding">&nbsp;</div>
<script type="text/javascript"><!--
$('.navPadding').css('padding-top', $('.fixedNav').css("height"));
//-->
</script>
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle"><span class="packageLabelInType">Package</span>&nbsp;<a href="package-summary.html">org.gradle.api.plugins.quality</a></div>
<h2 title="Class CheckstylePlugin" class="title">Class CheckstylePlugin</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>org.gradle.api.plugins.quality.internal.AbstractCodeQualityPlugin&lt;<a href="Checkstyle.html" title="class in org.gradle.api.plugins.quality">Checkstyle</a>&gt;</li>
<li>
<ul class="inheritance">
<li>org.gradle.api.plugins.quality.CheckstylePlugin</li>
</ul>
</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Implemented Interfaces:</dt>
<dd><code><a href="../../Plugin.html" title="interface in org.gradle.api">Plugin</a>&lt;org.gradle.api.internal.project.ProjectInternal&gt;</code></dd>
</dl>
<hr>
<pre>public abstract class <span class="typeNameLabel">CheckstylePlugin</span>
extends org.gradle.api.plugins.quality.internal.AbstractCodeQualityPlugin&lt;<a href="Checkstyle.html" title="class in org.gradle.api.plugins.quality">Checkstyle</a>&gt;</pre>
<div class="block">Checkstyle Plugin.</div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="https://docs.gradle.org/current/userguide/checkstyle_plugin.html">Checkstyle plugin reference</a></dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- =========== FIELD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.summary">
<!--   -->
</a>
<h3>Field Summary</h3>
<table class="memberSummary" summary="Field Summary table, listing fields, and an explanation">
<caption><span>Fields</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colSecond" scope="col">Field</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#DEFAULT_CHECKSTYLE_VERSION">DEFAULT_CHECKSTYLE_VERSION</a></span></code></th>
<td class="colLast">&nbsp;</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="fields.inherited.from.class.org.gradle.api.plugins.quality.internal.AbstractCodeQualityPlugin">
<!--   -->
</a>
<h3>Fields inherited from class&nbsp;org.gradle.api.plugins.quality.internal.AbstractCodeQualityPlugin</h3>
<code>project</code></li>
</ul>
</li>
</ul>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="memberSummary" summary="Constructor Summary table, listing constructors, and an explanation">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Constructor</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tr class="altColor">
<th class="colConstructorName" scope="row"><code><span class="memberNameLink"><a href="#CheckstylePlugin--">CheckstylePlugin</a></span>()</code></th>
<td class="colLast">&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colSecond" scope="col">Method</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>protected void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#configureConfiguration-org.gradle.api.artifacts.Configuration-">configureConfiguration</a></span>&#8203;(<a href="../../artifacts/Configuration.html" title="interface in org.gradle.api.artifacts">Configuration</a>&nbsp;configuration)</code></th>
<td class="colLast">&nbsp;</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>protected void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#configureForSourceSet-org.gradle.api.tasks.SourceSet-org.gradle.api.plugins.quality.Checkstyle-">configureForSourceSet</a></span>&#8203;(<a href="../../tasks/SourceSet.html" title="interface in org.gradle.api.tasks">SourceSet</a>&nbsp;sourceSet,
                     <a href="Checkstyle.html" title="class in org.gradle.api.plugins.quality">Checkstyle</a>&nbsp;task)</code></th>
<td class="colLast">&nbsp;</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>protected void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#configureTaskDefaults-org.gradle.api.plugins.quality.Checkstyle-java.lang.String-">configureTaskDefaults</a></span>&#8203;(<a href="Checkstyle.html" title="class in org.gradle.api.plugins.quality">Checkstyle</a>&nbsp;task,
                     java.lang.String&nbsp;baseName)</code></th>
<td class="colLast">&nbsp;</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>protected <a href="CodeQualityExtension.html" title="class in org.gradle.api.plugins.quality">CodeQualityExtension</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#createExtension--">createExtension</a></span>()</code></th>
<td class="colLast">&nbsp;</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>protected java.lang.Class&lt;<a href="Checkstyle.html" title="class in org.gradle.api.plugins.quality">Checkstyle</a>&gt;</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getTaskType--">getTaskType</a></span>()</code></th>
<td class="colLast">&nbsp;</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>protected <a href="../../../jvm/toolchain/JavaToolchainService.html" title="interface in org.gradle.jvm.toolchain">JavaToolchainService</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getToolchainService--">getToolchainService</a></span>()</code></th>
<td class="colLast">&nbsp;</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>protected java.lang.String</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getToolName--">getToolName</a></span>()</code></th>
<td class="colLast">&nbsp;</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.org.gradle.api.plugins.quality.internal.AbstractCodeQualityPlugin">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;org.gradle.api.plugins.quality.internal.AbstractCodeQualityPlugin</h3>
<code>apply, beforeApply, conventionMappingOf, createConfigurations, getBasePlugin, getConfigurationName, getJavaPluginExtension, getJvmPluginServices, getReportName, getTaskBaseName, withBasePlugin</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>clone, equals, finalize, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ FIELD DETAIL =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.detail">
<!--   -->
</a>
<h3>Field Detail</h3>
<a name="DEFAULT_CHECKSTYLE_VERSION">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>DEFAULT_CHECKSTYLE_VERSION</h4>
<pre>public static final&nbsp;java.lang.String DEFAULT_CHECKSTYLE_VERSION</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../../../constant-values.html#org.gradle.api.plugins.quality.CheckstylePlugin.DEFAULT_CHECKSTYLE_VERSION">Constant Field Values</a></dd>
</dl>
</li>
</ul>
</li>
</ul>
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a name="CheckstylePlugin--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>CheckstylePlugin</h4>
<pre>public&nbsp;CheckstylePlugin()</pre>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="getToolName--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getToolName</h4>
<pre class="methodSignature">protected&nbsp;java.lang.String&nbsp;getToolName()</pre>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code>getToolName</code>&nbsp;in class&nbsp;<code>org.gradle.api.plugins.quality.internal.AbstractCodeQualityPlugin&lt;<a href="Checkstyle.html" title="class in org.gradle.api.plugins.quality">Checkstyle</a>&gt;</code></dd>
</dl>
</li>
</ul>
<a name="getTaskType--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getTaskType</h4>
<pre class="methodSignature">protected&nbsp;java.lang.Class&lt;<a href="Checkstyle.html" title="class in org.gradle.api.plugins.quality">Checkstyle</a>&gt;&nbsp;getTaskType()</pre>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code>getTaskType</code>&nbsp;in class&nbsp;<code>org.gradle.api.plugins.quality.internal.AbstractCodeQualityPlugin&lt;<a href="Checkstyle.html" title="class in org.gradle.api.plugins.quality">Checkstyle</a>&gt;</code></dd>
</dl>
</li>
</ul>
<a name="getToolchainService--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getToolchainService</h4>
<pre class="methodSignature">@Inject
protected&nbsp;<a href="../../../jvm/toolchain/JavaToolchainService.html" title="interface in org.gradle.jvm.toolchain">JavaToolchainService</a>&nbsp;getToolchainService()</pre>
</li>
</ul>
<a name="createExtension--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createExtension</h4>
<pre class="methodSignature">protected&nbsp;<a href="CodeQualityExtension.html" title="class in org.gradle.api.plugins.quality">CodeQualityExtension</a>&nbsp;createExtension()</pre>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code>createExtension</code>&nbsp;in class&nbsp;<code>org.gradle.api.plugins.quality.internal.AbstractCodeQualityPlugin&lt;<a href="Checkstyle.html" title="class in org.gradle.api.plugins.quality">Checkstyle</a>&gt;</code></dd>
</dl>
</li>
</ul>
<a name="configureConfiguration-org.gradle.api.artifacts.Configuration-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>configureConfiguration</h4>
<pre class="methodSignature">protected&nbsp;void&nbsp;configureConfiguration&#8203;(<a href="../../artifacts/Configuration.html" title="interface in org.gradle.api.artifacts">Configuration</a>&nbsp;configuration)</pre>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code>configureConfiguration</code>&nbsp;in class&nbsp;<code>org.gradle.api.plugins.quality.internal.AbstractCodeQualityPlugin&lt;<a href="Checkstyle.html" title="class in org.gradle.api.plugins.quality">Checkstyle</a>&gt;</code></dd>
</dl>
</li>
</ul>
<a name="configureTaskDefaults-org.gradle.api.plugins.quality.Checkstyle-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>configureTaskDefaults</h4>
<pre class="methodSignature">protected&nbsp;void&nbsp;configureTaskDefaults&#8203;(<a href="Checkstyle.html" title="class in org.gradle.api.plugins.quality">Checkstyle</a>&nbsp;task,
                                     java.lang.String&nbsp;baseName)</pre>
<dl>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code>configureTaskDefaults</code>&nbsp;in class&nbsp;<code>org.gradle.api.plugins.quality.internal.AbstractCodeQualityPlugin&lt;<a href="Checkstyle.html" title="class in org.gradle.api.plugins.quality">Checkstyle</a>&gt;</code></dd>
</dl>
</li>
</ul>
<a name="configureForSourceSet-org.gradle.api.tasks.SourceSet-org.gradle.api.plugins.quality.Checkstyle-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>configureForSourceSet</h4>
<pre class="methodSignature">protected&nbsp;void&nbsp;configureForSourceSet&#8203;(<a href="../../tasks/SourceSet.html" title="interface in org.gradle.api.tasks">SourceSet</a>&nbsp;sourceSet,
                                     <a href="Checkstyle.html" title="class in org.gradle.api.plugins.quality">Checkstyle</a>&nbsp;task)</pre>
<dl>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code>configureForSourceSet</code>&nbsp;in class&nbsp;<code>org.gradle.api.plugins.quality.internal.AbstractCodeQualityPlugin&lt;<a href="Checkstyle.html" title="class in org.gradle.api.plugins.quality">Checkstyle</a>&gt;</code></dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../../allclasses.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field.summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#field.detail">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
