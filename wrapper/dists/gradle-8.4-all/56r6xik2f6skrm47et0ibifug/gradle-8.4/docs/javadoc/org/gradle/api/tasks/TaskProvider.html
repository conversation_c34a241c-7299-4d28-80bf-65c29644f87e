<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc -->
<title>TaskProvider (Gradle API 8.4)</title>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<link rel="stylesheet" type="text/css" href="../../../../javadoc.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../jquery/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
<script type="text/javascript" src="../../../../jquery/jszip/dist/jszip.min.js"></script>
<script type="text/javascript" src="../../../../jquery/jszip-utils/dist/jszip-utils.min.js"></script>
<!--[if IE]>
<script type="text/javascript" src="../../../../jquery/jszip-utils/dist/jszip-utils-ie.min.js"></script>
<![endif]-->
<script type="text/javascript" src="../../../../jquery/jquery-3.6.0.min.js"></script>
<script type="text/javascript" src="../../../../jquery/jquery-ui.min.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="TaskProvider (Gradle API 8.4)";
        }
    }
    catch(err) {
    }
//-->
var data = {"i0":6,"i1":6};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],4:["t3","Abstract Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
var pathtoroot = "../../../../";
var useModuleDirectories = false;
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="fixedNav">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses.html">All&nbsp;Classes</a></li>
</ul>
<ul class="navListSearch">
<li><label for="search">SEARCH:</label>
<input type="text" id="search" value="search" disabled="disabled">
<input type="reset" id="reset" value="reset" disabled="disabled">
</li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
</div>
<div class="navPadding">&nbsp;</div>
<script type="text/javascript"><!--
$('.navPadding').css('padding-top', $('.fixedNav').css("height"));
//-->
</script>
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle"><span class="packageLabelInType">Package</span>&nbsp;<a href="package-summary.html">org.gradle.api.tasks</a></div>
<h2 title="Interface TaskProvider" class="title">Interface TaskProvider&lt;T extends <a href="../Task.html" title="interface in org.gradle.api">Task</a>&gt;</h2>
</div>
<div class="contentContainer">
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt><span class="paramLabel">Type Parameters:</span></dt>
<dd><code>T</code> - Task type</dd>
</dl>
<dl>
<dt>All Superinterfaces:</dt>
<dd><code><a href="../NamedDomainObjectProvider.html" title="interface in org.gradle.api">NamedDomainObjectProvider</a>&lt;T&gt;</code>, <code><a href="../provider/Provider.html" title="interface in org.gradle.api.provider">Provider</a>&lt;T&gt;</code></dd>
</dl>
<hr>
<pre>public interface <span class="typeNameLabel">TaskProvider&lt;T extends <a href="../Task.html" title="interface in org.gradle.api">Task</a>&gt;</span>
extends <a href="../NamedDomainObjectProvider.html" title="interface in org.gradle.api">NamedDomainObjectProvider</a>&lt;T&gt;</pre>
<div class="block">Providers a task of the given type.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>4.8</dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t3" class="tableTab"><span><a href="javascript:show(4);">Abstract Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colSecond" scope="col">Method</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#configure-org.gradle.api.Action-">configure</a></span>&#8203;(<a href="../Action.html" title="interface in org.gradle.api">Action</a>&lt;? super <a href="TaskProvider.html" title="type parameter in TaskProvider">T</a>&gt;&nbsp;action)</code></th>
<td class="colLast">
<div class="block">Configures the task with the given action.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>java.lang.String</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getName--">getName</a></span>()</code></th>
<td class="colLast">
<div class="block">The task name referenced by this provider.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.org.gradle.api.provider.Provider">
<!--   -->
</a>
<h3>Methods inherited from interface&nbsp;org.gradle.api.provider.<a href="../provider/Provider.html" title="interface in org.gradle.api.provider">Provider</a></h3>
<code><a href="../provider/Provider.html#filter-java.util.function.Predicate-">filter</a>, <a href="../provider/Provider.html#flatMap-org.gradle.api.Transformer-">flatMap</a>, <a href="../provider/Provider.html#forUseAtConfigurationTime--">forUseAtConfigurationTime</a>, <a href="../provider/Provider.html#get--">get</a>, <a href="../provider/Provider.html#getOrElse-T-">getOrElse</a>, <a href="../provider/Provider.html#getOrNull--">getOrNull</a>, <a href="../provider/Provider.html#isPresent--">isPresent</a>, <a href="../provider/Provider.html#map-org.gradle.api.Transformer-">map</a>, <a href="../provider/Provider.html#orElse-org.gradle.api.provider.Provider-">orElse</a>, <a href="../provider/Provider.html#orElse-T-">orElse</a>, <a href="../provider/Provider.html#zip-org.gradle.api.provider.Provider-java.util.function.BiFunction-">zip</a></code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="configure-org.gradle.api.Action-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>configure</h4>
<pre class="methodSignature">void&nbsp;configure&#8203;(<a href="../Action.html" title="interface in org.gradle.api">Action</a>&lt;? super <a href="TaskProvider.html" title="type parameter in TaskProvider">T</a>&gt;&nbsp;action)</pre>
<div class="block">Configures the task with the given action. Actions are run in the order added.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../NamedDomainObjectProvider.html#configure-org.gradle.api.Action-">configure</a></code>&nbsp;in interface&nbsp;<code><a href="../NamedDomainObjectProvider.html" title="interface in org.gradle.api">NamedDomainObjectProvider</a>&lt;<a href="TaskProvider.html" title="type parameter in TaskProvider">T</a> extends <a href="../Task.html" title="interface in org.gradle.api">Task</a>&gt;</code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>action</code> - A <a href="../Action.html" title="interface in org.gradle.api"><code>Action</code></a> that can configure the task when required.</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>4.8</dd>
</dl>
</li>
</ul>
<a name="getName--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>getName</h4>
<pre class="methodSignature">java.lang.String&nbsp;getName()</pre>
<div class="block">The task name referenced by this provider.
 <p>
 Must be constant for the life of the object.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../NamedDomainObjectProvider.html#getName--">getName</a></code>&nbsp;in interface&nbsp;<code><a href="../NamedDomainObjectProvider.html" title="interface in org.gradle.api">NamedDomainObjectProvider</a>&lt;<a href="TaskProvider.html" title="type parameter in TaskProvider">T</a> extends <a href="../Task.html" title="interface in org.gradle.api">Task</a>&gt;</code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The task name. Never null.</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>4.9</dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
