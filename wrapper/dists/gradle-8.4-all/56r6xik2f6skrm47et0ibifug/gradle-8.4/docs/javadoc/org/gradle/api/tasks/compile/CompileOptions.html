<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc -->
<title>CompileOptions (Gradle API 8.4)</title>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<link rel="stylesheet" type="text/css" href="../../../../../javadoc.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../jquery/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
<script type="text/javascript" src="../../../../../jquery/jszip/dist/jszip.min.js"></script>
<script type="text/javascript" src="../../../../../jquery/jszip-utils/dist/jszip-utils.min.js"></script>
<!--[if IE]>
<script type="text/javascript" src="../../../../../jquery/jszip-utils/dist/jszip-utils-ie.min.js"></script>
<![endif]-->
<script type="text/javascript" src="../../../../../jquery/jquery-3.6.0.min.js"></script>
<script type="text/javascript" src="../../../../../jquery/jquery-ui.min.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="CompileOptions (Gradle API 8.4)";
        }
    }
    catch(err) {
    }
//-->
var data = {"i0":10,"i1":10,"i2":10,"i3":42,"i4":10,"i5":10,"i6":10,"i7":10,"i8":10,"i9":10,"i10":10,"i11":10,"i12":10,"i13":10,"i14":10,"i15":10,"i16":10,"i17":10,"i18":10,"i19":10,"i20":10,"i21":10,"i22":10,"i23":10,"i24":10,"i25":10,"i26":10,"i27":42,"i28":42,"i29":10,"i30":10,"i31":10,"i32":10,"i33":10,"i34":10,"i35":10,"i36":10,"i37":10,"i38":10,"i39":10,"i40":10,"i41":10,"i42":10,"i43":10,"i44":10};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"],32:["t6","Deprecated Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
var pathtoroot = "../../../../../";
var useModuleDirectories = false;
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="fixedNav">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../../allclasses.html">All&nbsp;Classes</a></li>
</ul>
<ul class="navListSearch">
<li><label for="search">SEARCH:</label>
<input type="text" id="search" value="search" disabled="disabled">
<input type="reset" id="reset" value="reset" disabled="disabled">
</li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
</div>
<div class="navPadding">&nbsp;</div>
<script type="text/javascript"><!--
$('.navPadding').css('padding-top', $('.fixedNav').css("height"));
//-->
</script>
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle"><span class="packageLabelInType">Package</span>&nbsp;<a href="package-summary.html">org.gradle.api.tasks.compile</a></div>
<h2 title="Class CompileOptions" class="title">Class CompileOptions</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li><a href="AbstractOptions.html" title="class in org.gradle.api.tasks.compile">org.gradle.api.tasks.compile.AbstractOptions</a></li>
<li>
<ul class="inheritance">
<li>org.gradle.api.tasks.compile.CompileOptions</li>
</ul>
</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Implemented Interfaces:</dt>
<dd><code>java.io.Serializable</code></dd>
</dl>
<hr>
<pre>public abstract class <span class="typeNameLabel">CompileOptions</span>
extends <a href="AbstractOptions.html" title="class in org.gradle.api.tasks.compile">AbstractOptions</a></pre>
<div class="block">Main options for Java compilation.</div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../../../serialized-form.html#org.gradle.api.tasks.compile.CompileOptions">Serialized Form</a></dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="memberSummary" summary="Constructor Summary table, listing constructors, and an explanation">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Constructor</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tr class="altColor">
<th class="colConstructorName" scope="row"><code><span class="memberNameLink"><a href="#CompileOptions-org.gradle.api.model.ObjectFactory-">CompileOptions</a></span>&#8203;(<a href="../../model/ObjectFactory.html" title="interface in org.gradle.api.model">ObjectFactory</a>&nbsp;objectFactory)</code></th>
<td class="colLast">&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t6" class="tableTab"><span><a href="javascript:show(32);">Deprecated Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colSecond" scope="col">Method</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code><a href="CompileOptions.html" title="class in org.gradle.api.tasks.compile">CompileOptions</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#debug-java.util.Map-">debug</a></span>&#8203;(java.util.Map&lt;java.lang.String,&#8203;java.lang.Object&gt;&nbsp;debugArgs)</code></th>
<td class="colLast">
<div class="block">Convenience method to set <a href="DebugOptions.html" title="class in org.gradle.api.tasks.compile"><code>DebugOptions</code></a> with named parameter syntax.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code><a href="CompileOptions.html" title="class in org.gradle.api.tasks.compile">CompileOptions</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#fork-java.util.Map-">fork</a></span>&#8203;(java.util.Map&lt;java.lang.String,&#8203;java.lang.Object&gt;&nbsp;forkArgs)</code></th>
<td class="colLast">
<div class="block">Convenience method to set <a href="ForkOptions.html" title="class in org.gradle.api.tasks.compile"><code>ForkOptions</code></a> with named parameter syntax.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>java.util.List&lt;java.lang.String&gt;</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getAllCompilerArgs--">getAllCompilerArgs</a></span>()</code></th>
<td class="colLast">
<div class="block">Returns all compiler arguments, added to the <a href="#getCompilerArgs--"><code>getCompilerArgs()</code></a> or the <a href="#getCompilerArgumentProviders--"><code>getCompilerArgumentProviders()</code></a> property.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>java.io.File</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getAnnotationProcessorGeneratedSourcesDirectory--">getAnnotationProcessorGeneratedSourcesDirectory</a></span>()</code></th>
<td class="colLast">
<div class="block"><span class="deprecatedLabel">Deprecated.</span>
<div class="deprecationComment">Use <a href="#getGeneratedSourceOutputDirectory--"><code>getGeneratedSourceOutputDirectory()</code></a> instead.</div>
</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code><a href="../../file/FileCollection.html" title="interface in org.gradle.api.file">FileCollection</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getAnnotationProcessorPath--">getAnnotationProcessorPath</a></span>()</code></th>
<td class="colLast">
<div class="block">Returns the classpath to use to load annotation processors.</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code><a href="../../file/FileCollection.html" title="interface in org.gradle.api.file">FileCollection</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getBootstrapClasspath--">getBootstrapClasspath</a></span>()</code></th>
<td class="colLast">
<div class="block">Returns the bootstrap classpath to be used for the compiler process.</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>java.util.List&lt;java.lang.String&gt;</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getCompilerArgs--">getCompilerArgs</a></span>()</code></th>
<td class="colLast">
<div class="block">Returns any additional arguments to be passed to the compiler.</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>java.util.List&lt;<a href="../../../process/CommandLineArgumentProvider.html" title="interface in org.gradle.process">CommandLineArgumentProvider</a>&gt;</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getCompilerArgumentProviders--">getCompilerArgumentProviders</a></span>()</code></th>
<td class="colLast">
<div class="block">Compiler argument providers.</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code><a href="DebugOptions.html" title="class in org.gradle.api.tasks.compile">DebugOptions</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getDebugOptions--">getDebugOptions</a></span>()</code></th>
<td class="colLast">
<div class="block">Returns options for generating debugging information.</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>java.lang.String</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getEncoding--">getEncoding</a></span>()</code></th>
<td class="colLast">
<div class="block">Returns the character encoding to be used when reading source files.</div>
</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getExtensionDirs--">getExtensionDirs</a></span>()</code></th>
<td class="colLast">
<div class="block">Returns the extension dirs to be used for the compiler process.</div>
</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code><a href="ForkOptions.html" title="class in org.gradle.api.tasks.compile">ForkOptions</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getForkOptions--">getForkOptions</a></span>()</code></th>
<td class="colLast">
<div class="block">Returns options for running the compiler in a child process.</div>
</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code><a href="../../file/DirectoryProperty.html" title="interface in org.gradle.api.file">DirectoryProperty</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getGeneratedSourceOutputDirectory--">getGeneratedSourceOutputDirectory</a></span>()</code></th>
<td class="colLast">
<div class="block">Returns the directory to place source files generated by annotation processors.</div>
</td>
</tr>
<tr id="i13" class="rowColor">
<td class="colFirst"><code><a href="../../file/DirectoryProperty.html" title="interface in org.gradle.api.file">DirectoryProperty</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getHeaderOutputDirectory--">getHeaderOutputDirectory</a></span>()</code></th>
<td class="colLast">
<div class="block">If this option is set to a non-null directory, it will be passed to the Java compiler's `-h` option, prompting it to generate native headers to that directory.</div>
</td>
</tr>
<tr id="i14" class="altColor">
<td class="colFirst"><code><a href="../../provider/Property.html" title="interface in org.gradle.api.provider">Property</a>&lt;java.lang.Boolean&gt;</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getIncrementalAfterFailure--">getIncrementalAfterFailure</a></span>()</code></th>
<td class="colLast">
<div class="block">Used to enable or disable incremental compilation after a failure.</div>
</td>
</tr>
<tr id="i15" class="rowColor">
<td class="colFirst"><code><a href="../../provider/Property.html" title="interface in org.gradle.api.provider">Property</a>&lt;java.lang.String&gt;</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getJavaModuleMainClass--">getJavaModuleMainClass</a></span>()</code></th>
<td class="colLast">
<div class="block">Set the main class of the Java module, if the module is supposed to be executable.</div>
</td>
</tr>
<tr id="i16" class="altColor">
<td class="colFirst"><code><a href="../../provider/Property.html" title="interface in org.gradle.api.provider">Property</a>&lt;java.lang.String&gt;</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getJavaModuleVersion--">getJavaModuleVersion</a></span>()</code></th>
<td class="colLast">
<div class="block">Set the version of the Java module.</div>
</td>
</tr>
<tr id="i17" class="rowColor">
<td class="colFirst"><code><a href="../../provider/Property.html" title="interface in org.gradle.api.provider">Property</a>&lt;java.lang.Integer&gt;</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getRelease--">getRelease</a></span>()</code></th>
<td class="colLast">
<div class="block">Configures the Java language version for this compile task (<code>--release</code> compiler flag).</div>
</td>
</tr>
<tr id="i18" class="altColor">
<td class="colFirst"><code><a href="../../file/FileCollection.html" title="interface in org.gradle.api.file">FileCollection</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getSourcepath--">getSourcepath</a></span>()</code></th>
<td class="colLast">
<div class="block">The source path to use for the compilation.</div>
</td>
</tr>
<tr id="i19" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#isDebug--">isDebug</a></span>()</code></th>
<td class="colLast">
<div class="block">Tells whether to include debugging information in the generated class files.</div>
</td>
</tr>
<tr id="i20" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#isDeprecation--">isDeprecation</a></span>()</code></th>
<td class="colLast">
<div class="block">Tells whether to log details of usage of deprecated members or classes.</div>
</td>
</tr>
<tr id="i21" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#isFailOnError--">isFailOnError</a></span>()</code></th>
<td class="colLast">
<div class="block">Tells whether to fail the build when compilation fails.</div>
</td>
</tr>
<tr id="i22" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#isFork--">isFork</a></span>()</code></th>
<td class="colLast">
<div class="block">Tells whether to run the compiler in its own process.</div>
</td>
</tr>
<tr id="i23" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#isIncremental--">isIncremental</a></span>()</code></th>
<td class="colLast">
<div class="block">informs whether to use incremental compilation feature.</div>
</td>
</tr>
<tr id="i24" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#isListFiles--">isListFiles</a></span>()</code></th>
<td class="colLast">
<div class="block">Tells whether to log the files to be compiled.</div>
</td>
</tr>
<tr id="i25" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#isVerbose--">isVerbose</a></span>()</code></th>
<td class="colLast">
<div class="block">Tells whether to produce verbose output.</div>
</td>
</tr>
<tr id="i26" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#isWarnings--">isWarnings</a></span>()</code></th>
<td class="colLast">
<div class="block">Tells whether to log warning messages.</div>
</td>
</tr>
<tr id="i27" class="rowColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setAnnotationProcessorGeneratedSourcesDirectory-java.io.File-">setAnnotationProcessorGeneratedSourcesDirectory</a></span>&#8203;(java.io.File&nbsp;file)</code></th>
<td class="colLast">
<div class="block"><span class="deprecatedLabel">Deprecated.</span>
<div class="deprecationComment">Use <a href="#getGeneratedSourceOutputDirectory--"><code>getGeneratedSourceOutputDirectory()</code></a>.set() instead.</div>
</div>
</td>
</tr>
<tr id="i28" class="altColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setAnnotationProcessorGeneratedSourcesDirectory-org.gradle.api.provider.Provider-">setAnnotationProcessorGeneratedSourcesDirectory</a></span>&#8203;(<a href="../../provider/Provider.html" title="interface in org.gradle.api.provider">Provider</a>&lt;java.io.File&gt;&nbsp;file)</code></th>
<td class="colLast">
<div class="block"><span class="deprecatedLabel">Deprecated.</span>
<div class="deprecationComment">Use <a href="#getGeneratedSourceOutputDirectory--"><code>getGeneratedSourceOutputDirectory()</code></a>.set() instead.</div>
</div>
</td>
</tr>
<tr id="i29" class="rowColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setAnnotationProcessorPath-org.gradle.api.file.FileCollection-">setAnnotationProcessorPath</a></span>&#8203;(<a href="../../file/FileCollection.html" title="interface in org.gradle.api.file">FileCollection</a>&nbsp;annotationProcessorPath)</code></th>
<td class="colLast">
<div class="block">Set the classpath to use to load annotation processors.</div>
</td>
</tr>
<tr id="i30" class="altColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setBootstrapClasspath-org.gradle.api.file.FileCollection-">setBootstrapClasspath</a></span>&#8203;(<a href="../../file/FileCollection.html" title="interface in org.gradle.api.file">FileCollection</a>&nbsp;bootstrapClasspath)</code></th>
<td class="colLast">
<div class="block">Sets the bootstrap classpath to be used for the compiler process.</div>
</td>
</tr>
<tr id="i31" class="rowColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setCompilerArgs-java.util.List-">setCompilerArgs</a></span>&#8203;(java.util.List&lt;java.lang.String&gt;&nbsp;compilerArgs)</code></th>
<td class="colLast">
<div class="block">Sets any additional arguments to be passed to the compiler.</div>
</td>
</tr>
<tr id="i32" class="altColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setDebug-boolean-">setDebug</a></span>&#8203;(boolean&nbsp;debug)</code></th>
<td class="colLast">
<div class="block">Sets whether to include debugging information in the generated class files.</div>
</td>
</tr>
<tr id="i33" class="rowColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setDebugOptions-org.gradle.api.tasks.compile.DebugOptions-">setDebugOptions</a></span>&#8203;(<a href="DebugOptions.html" title="class in org.gradle.api.tasks.compile">DebugOptions</a>&nbsp;debugOptions)</code></th>
<td class="colLast">
<div class="block">Sets options for generating debugging information.</div>
</td>
</tr>
<tr id="i34" class="altColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setDeprecation-boolean-">setDeprecation</a></span>&#8203;(boolean&nbsp;deprecation)</code></th>
<td class="colLast">
<div class="block">Sets whether to log details of usage of deprecated members or classes.</div>
</td>
</tr>
<tr id="i35" class="rowColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setEncoding-java.lang.String-">setEncoding</a></span>&#8203;(java.lang.String&nbsp;encoding)</code></th>
<td class="colLast">
<div class="block">Sets the character encoding to be used when reading source files.</div>
</td>
</tr>
<tr id="i36" class="altColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setExtensionDirs-java.lang.String-">setExtensionDirs</a></span>&#8203;(java.lang.String&nbsp;extensionDirs)</code></th>
<td class="colLast">
<div class="block">Sets the extension dirs to be used for the compiler process.</div>
</td>
</tr>
<tr id="i37" class="rowColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setFailOnError-boolean-">setFailOnError</a></span>&#8203;(boolean&nbsp;failOnError)</code></th>
<td class="colLast">
<div class="block">Sets whether to fail the build when compilation fails.</div>
</td>
</tr>
<tr id="i38" class="altColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setFork-boolean-">setFork</a></span>&#8203;(boolean&nbsp;fork)</code></th>
<td class="colLast">
<div class="block">Sets whether to run the compiler in its own process.</div>
</td>
</tr>
<tr id="i39" class="rowColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setForkOptions-org.gradle.api.tasks.compile.ForkOptions-">setForkOptions</a></span>&#8203;(<a href="ForkOptions.html" title="class in org.gradle.api.tasks.compile">ForkOptions</a>&nbsp;forkOptions)</code></th>
<td class="colLast">
<div class="block">Sets options for running the compiler in a child process.</div>
</td>
</tr>
<tr id="i40" class="altColor">
<td class="colFirst"><code><a href="CompileOptions.html" title="class in org.gradle.api.tasks.compile">CompileOptions</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setIncremental-boolean-">setIncremental</a></span>&#8203;(boolean&nbsp;incremental)</code></th>
<td class="colLast">
<div class="block">Configure the java compilation to be incremental (e.g.</div>
</td>
</tr>
<tr id="i41" class="rowColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setListFiles-boolean-">setListFiles</a></span>&#8203;(boolean&nbsp;listFiles)</code></th>
<td class="colLast">
<div class="block">Sets whether to log the files to be compiled.</div>
</td>
</tr>
<tr id="i42" class="altColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setSourcepath-org.gradle.api.file.FileCollection-">setSourcepath</a></span>&#8203;(<a href="../../file/FileCollection.html" title="interface in org.gradle.api.file">FileCollection</a>&nbsp;sourcepath)</code></th>
<td class="colLast">
<div class="block">Sets the source path to use for the compilation.</div>
</td>
</tr>
<tr id="i43" class="rowColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setVerbose-boolean-">setVerbose</a></span>&#8203;(boolean&nbsp;verbose)</code></th>
<td class="colLast">
<div class="block">Sets whether to produce verbose output.</div>
</td>
</tr>
<tr id="i44" class="altColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setWarnings-boolean-">setWarnings</a></span>&#8203;(boolean&nbsp;warnings)</code></th>
<td class="colLast">
<div class="block">Sets whether to log warning messages.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.org.gradle.api.tasks.compile.AbstractOptions">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;org.gradle.api.tasks.compile.<a href="AbstractOptions.html" title="class in org.gradle.api.tasks.compile">AbstractOptions</a></h3>
<code><a href="AbstractOptions.html#define-java.util.Map-">define</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>clone, equals, finalize, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a name="CompileOptions-org.gradle.api.model.ObjectFactory-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>CompileOptions</h4>
<pre>@Inject
public&nbsp;CompileOptions&#8203;(<a href="../../model/ObjectFactory.html" title="interface in org.gradle.api.model">ObjectFactory</a>&nbsp;objectFactory)</pre>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="isFailOnError--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isFailOnError</h4>
<pre class="methodSignature"><a href="../Input.html" title="annotation in org.gradle.api.tasks">@Input</a>
public&nbsp;boolean&nbsp;isFailOnError()</pre>
<div class="block">Tells whether to fail the build when compilation fails. Defaults to <code>true</code>.</div>
</li>
</ul>
<a name="setFailOnError-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setFailOnError</h4>
<pre class="methodSignature">public&nbsp;void&nbsp;setFailOnError&#8203;(boolean&nbsp;failOnError)</pre>
<div class="block">Sets whether to fail the build when compilation fails. Defaults to <code>true</code>.</div>
</li>
</ul>
<a name="isVerbose--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isVerbose</h4>
<pre class="methodSignature"><a href="../Console.html" title="annotation in org.gradle.api.tasks">@Console</a>
public&nbsp;boolean&nbsp;isVerbose()</pre>
<div class="block">Tells whether to produce verbose output. Defaults to <code>false</code>.</div>
</li>
</ul>
<a name="setVerbose-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setVerbose</h4>
<pre class="methodSignature">public&nbsp;void&nbsp;setVerbose&#8203;(boolean&nbsp;verbose)</pre>
<div class="block">Sets whether to produce verbose output. Defaults to <code>false</code>.</div>
</li>
</ul>
<a name="isListFiles--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isListFiles</h4>
<pre class="methodSignature"><a href="../Console.html" title="annotation in org.gradle.api.tasks">@Console</a>
public&nbsp;boolean&nbsp;isListFiles()</pre>
<div class="block">Tells whether to log the files to be compiled. Defaults to <code>false</code>.</div>
</li>
</ul>
<a name="setListFiles-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setListFiles</h4>
<pre class="methodSignature">public&nbsp;void&nbsp;setListFiles&#8203;(boolean&nbsp;listFiles)</pre>
<div class="block">Sets whether to log the files to be compiled. Defaults to <code>false</code>.</div>
</li>
</ul>
<a name="isDeprecation--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isDeprecation</h4>
<pre class="methodSignature"><a href="../Console.html" title="annotation in org.gradle.api.tasks">@Console</a>
public&nbsp;boolean&nbsp;isDeprecation()</pre>
<div class="block">Tells whether to log details of usage of deprecated members or classes. Defaults to <code>false</code>.</div>
</li>
</ul>
<a name="setDeprecation-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setDeprecation</h4>
<pre class="methodSignature">public&nbsp;void&nbsp;setDeprecation&#8203;(boolean&nbsp;deprecation)</pre>
<div class="block">Sets whether to log details of usage of deprecated members or classes. Defaults to <code>false</code>.</div>
</li>
</ul>
<a name="isWarnings--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isWarnings</h4>
<pre class="methodSignature"><a href="../Console.html" title="annotation in org.gradle.api.tasks">@Console</a>
public&nbsp;boolean&nbsp;isWarnings()</pre>
<div class="block">Tells whether to log warning messages. The default is <code>true</code>.</div>
</li>
</ul>
<a name="setWarnings-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setWarnings</h4>
<pre class="methodSignature">public&nbsp;void&nbsp;setWarnings&#8203;(boolean&nbsp;warnings)</pre>
<div class="block">Sets whether to log warning messages. The default is <code>true</code>.</div>
</li>
</ul>
<a name="getEncoding--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getEncoding</h4>
<pre class="methodSignature">@Nullable
<a href="../Optional.html" title="annotation in org.gradle.api.tasks">@Optional</a>
<a href="../Input.html" title="annotation in org.gradle.api.tasks">@Input</a>
public&nbsp;java.lang.String&nbsp;getEncoding()</pre>
<div class="block">Returns the character encoding to be used when reading source files. Defaults to <code>null</code>, in which
 case the platform default encoding will be used.</div>
</li>
</ul>
<a name="setEncoding-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setEncoding</h4>
<pre class="methodSignature">public&nbsp;void&nbsp;setEncoding&#8203;(@Nullable
                        java.lang.String&nbsp;encoding)</pre>
<div class="block">Sets the character encoding to be used when reading source files. Defaults to <code>null</code>, in which
 case the platform default encoding will be used.</div>
</li>
</ul>
<a name="isDebug--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isDebug</h4>
<pre class="methodSignature"><a href="../Input.html" title="annotation in org.gradle.api.tasks">@Input</a>
public&nbsp;boolean&nbsp;isDebug()</pre>
<div class="block">Tells whether to include debugging information in the generated class files. Defaults
 to <code>true</code>. See <a href="DebugOptions.html#getDebugLevel--"><code>DebugOptions.getDebugLevel()</code></a> for which debugging information will be generated.</div>
</li>
</ul>
<a name="setDebug-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setDebug</h4>
<pre class="methodSignature">public&nbsp;void&nbsp;setDebug&#8203;(boolean&nbsp;debug)</pre>
<div class="block">Sets whether to include debugging information in the generated class files. Defaults
 to <code>true</code>. See <a href="DebugOptions.html#getDebugLevel--"><code>DebugOptions.getDebugLevel()</code></a> for which debugging information will be generated.</div>
</li>
</ul>
<a name="getDebugOptions--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getDebugOptions</h4>
<pre class="methodSignature">public&nbsp;<a href="DebugOptions.html" title="class in org.gradle.api.tasks.compile">DebugOptions</a>&nbsp;getDebugOptions()</pre>
<div class="block">Returns options for generating debugging information.</div>
</li>
</ul>
<a name="setDebugOptions-org.gradle.api.tasks.compile.DebugOptions-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setDebugOptions</h4>
<pre class="methodSignature">public&nbsp;void&nbsp;setDebugOptions&#8203;(<a href="DebugOptions.html" title="class in org.gradle.api.tasks.compile">DebugOptions</a>&nbsp;debugOptions)</pre>
<div class="block">Sets options for generating debugging information.</div>
</li>
</ul>
<a name="isFork--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isFork</h4>
<pre class="methodSignature"><a href="../Input.html" title="annotation in org.gradle.api.tasks">@Input</a>
public&nbsp;boolean&nbsp;isFork()</pre>
<div class="block">Tells whether to run the compiler in its own process. Note that this does
 not necessarily mean that a new process will be created for each compile task.
 Defaults to <code>false</code>.</div>
</li>
</ul>
<a name="setFork-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setFork</h4>
<pre class="methodSignature">public&nbsp;void&nbsp;setFork&#8203;(boolean&nbsp;fork)</pre>
<div class="block">Sets whether to run the compiler in its own process. Note that this does
 not necessarily mean that a new process will be created for each compile task.
 Defaults to <code>false</code>.</div>
</li>
</ul>
<a name="getForkOptions--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getForkOptions</h4>
<pre class="methodSignature">public&nbsp;<a href="ForkOptions.html" title="class in org.gradle.api.tasks.compile">ForkOptions</a>&nbsp;getForkOptions()</pre>
<div class="block">Returns options for running the compiler in a child process.</div>
</li>
</ul>
<a name="setForkOptions-org.gradle.api.tasks.compile.ForkOptions-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setForkOptions</h4>
<pre class="methodSignature">public&nbsp;void&nbsp;setForkOptions&#8203;(<a href="ForkOptions.html" title="class in org.gradle.api.tasks.compile">ForkOptions</a>&nbsp;forkOptions)</pre>
<div class="block">Sets options for running the compiler in a child process.</div>
</li>
</ul>
<a name="getBootstrapClasspath--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getBootstrapClasspath</h4>
<pre class="methodSignature">@Nullable
<a href="../Optional.html" title="annotation in org.gradle.api.tasks">@Optional</a>
<a href="../CompileClasspath.html" title="annotation in org.gradle.api.tasks">@CompileClasspath</a>
public&nbsp;<a href="../../file/FileCollection.html" title="interface in org.gradle.api.file">FileCollection</a>&nbsp;getBootstrapClasspath()</pre>
<div class="block">Returns the bootstrap classpath to be used for the compiler process. Defaults to <code>null</code>.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>4.3</dd>
</dl>
</li>
</ul>
<a name="setBootstrapClasspath-org.gradle.api.file.FileCollection-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setBootstrapClasspath</h4>
<pre class="methodSignature">public&nbsp;void&nbsp;setBootstrapClasspath&#8203;(@Nullable
                                  <a href="../../file/FileCollection.html" title="interface in org.gradle.api.file">FileCollection</a>&nbsp;bootstrapClasspath)</pre>
<div class="block">Sets the bootstrap classpath to be used for the compiler process. Defaults to <code>null</code>.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>4.3</dd>
</dl>
</li>
</ul>
<a name="getExtensionDirs--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getExtensionDirs</h4>
<pre class="methodSignature">@Nullable
<a href="../Optional.html" title="annotation in org.gradle.api.tasks">@Optional</a>
<a href="../Input.html" title="annotation in org.gradle.api.tasks">@Input</a>
public&nbsp;java.lang.String&nbsp;getExtensionDirs()</pre>
<div class="block">Returns the extension dirs to be used for the compiler process. Defaults to <code>null</code>.</div>
</li>
</ul>
<a name="setExtensionDirs-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setExtensionDirs</h4>
<pre class="methodSignature">public&nbsp;void&nbsp;setExtensionDirs&#8203;(@Nullable
                             java.lang.String&nbsp;extensionDirs)</pre>
<div class="block">Sets the extension dirs to be used for the compiler process. Defaults to <code>null</code>.</div>
</li>
</ul>
<a name="getCompilerArgs--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCompilerArgs</h4>
<pre class="methodSignature"><a href="../Input.html" title="annotation in org.gradle.api.tasks">@Input</a>
public&nbsp;java.util.List&lt;java.lang.String&gt;&nbsp;getCompilerArgs()</pre>
<div class="block">Returns any additional arguments to be passed to the compiler.
 Defaults to the empty list.

 Compiler arguments not supported by the DSL can be added here.

 For example, it is possible to pass the <code>--enable-preview</code> option that was added in newer Java versions:
 <pre><code>compilerArgs.add("--enable-preview")</code></pre>

 Note that if <code>--release</code> is added then <code>-target</code> and <code>-source</code>
 are ignored.</div>
</li>
</ul>
<a name="getAllCompilerArgs--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getAllCompilerArgs</h4>
<pre class="methodSignature"><a href="../Internal.html" title="annotation in org.gradle.api.tasks">@Internal</a>
public&nbsp;java.util.List&lt;java.lang.String&gt;&nbsp;getAllCompilerArgs()</pre>
<div class="block">Returns all compiler arguments, added to the <a href="#getCompilerArgs--"><code>getCompilerArgs()</code></a> or the <a href="#getCompilerArgumentProviders--"><code>getCompilerArgumentProviders()</code></a> property.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>4.5</dd>
</dl>
</li>
</ul>
<a name="getCompilerArgumentProviders--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCompilerArgumentProviders</h4>
<pre class="methodSignature">public&nbsp;java.util.List&lt;<a href="../../../process/CommandLineArgumentProvider.html" title="interface in org.gradle.process">CommandLineArgumentProvider</a>&gt;&nbsp;getCompilerArgumentProviders()</pre>
<div class="block">Compiler argument providers.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>4.5</dd>
</dl>
</li>
</ul>
<a name="setCompilerArgs-java.util.List-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCompilerArgs</h4>
<pre class="methodSignature">public&nbsp;void&nbsp;setCompilerArgs&#8203;(java.util.List&lt;java.lang.String&gt;&nbsp;compilerArgs)</pre>
<div class="block">Sets any additional arguments to be passed to the compiler.
 Defaults to the empty list.</div>
</li>
</ul>
<a name="fork-java.util.Map-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>fork</h4>
<pre class="methodSignature">public&nbsp;<a href="CompileOptions.html" title="class in org.gradle.api.tasks.compile">CompileOptions</a>&nbsp;fork&#8203;(java.util.Map&lt;java.lang.String,&#8203;java.lang.Object&gt;&nbsp;forkArgs)</pre>
<div class="block">Convenience method to set <a href="ForkOptions.html" title="class in org.gradle.api.tasks.compile"><code>ForkOptions</code></a> with named parameter syntax.
 Calling this method will set <code>fork</code> to <code>true</code>.</div>
</li>
</ul>
<a name="debug-java.util.Map-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>debug</h4>
<pre class="methodSignature">public&nbsp;<a href="CompileOptions.html" title="class in org.gradle.api.tasks.compile">CompileOptions</a>&nbsp;debug&#8203;(java.util.Map&lt;java.lang.String,&#8203;java.lang.Object&gt;&nbsp;debugArgs)</pre>
<div class="block">Convenience method to set <a href="DebugOptions.html" title="class in org.gradle.api.tasks.compile"><code>DebugOptions</code></a> with named parameter syntax.
 Calling this method will set <code>debug</code> to <code>true</code>.</div>
</li>
</ul>
<a name="setIncremental-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setIncremental</h4>
<pre class="methodSignature">public&nbsp;<a href="CompileOptions.html" title="class in org.gradle.api.tasks.compile">CompileOptions</a>&nbsp;setIncremental&#8203;(boolean&nbsp;incremental)</pre>
<div class="block">Configure the java compilation to be incremental (e.g. compiles only those java classes that were changed or that are dependencies to the changed classes).</div>
</li>
</ul>
<a name="isIncremental--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isIncremental</h4>
<pre class="methodSignature"><a href="../Internal.html" title="annotation in org.gradle.api.tasks">@Internal</a>
public&nbsp;boolean&nbsp;isIncremental()</pre>
<div class="block">informs whether to use incremental compilation feature. See <a href="#setIncremental-boolean-"><code>setIncremental(boolean)</code></a></div>
</li>
</ul>
<a name="getIncrementalAfterFailure--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getIncrementalAfterFailure</h4>
<pre class="methodSignature"><a href="../Input.html" title="annotation in org.gradle.api.tasks">@Input</a>
<a href="../Optional.html" title="annotation in org.gradle.api.tasks">@Optional</a>
<a href="../../Incubating.html" title="annotation in org.gradle.api">@Incubating</a>
public&nbsp;<a href="../../provider/Property.html" title="interface in org.gradle.api.provider">Property</a>&lt;java.lang.Boolean&gt;&nbsp;getIncrementalAfterFailure()</pre>
<div class="block">Used to enable or disable incremental compilation after a failure.
 <p>
 By default, incremental compilation after a failure is enabled for Java and Groovy.
 It has no effect for Scala. It has no effect if incremental compilation is not enabled.
 <p>
 When the Java command line compiler is used, i.e. when a custom java home is passed to forkOptions.javaHome or javac is passed to forkOptions.executable,
 this optimization is automatically disabled, since the compiler is not invoked via the compiler API.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>7.6</dd>
</dl>
</li>
</ul>
<a name="getSourcepath--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getSourcepath</h4>
<pre class="methodSignature"><a href="../Optional.html" title="annotation in org.gradle.api.tasks">@Optional</a>
@Nullable
<a href="../IgnoreEmptyDirectories.html" title="annotation in org.gradle.api.tasks">@IgnoreEmptyDirectories</a>
<a href="../PathSensitive.html" title="annotation in org.gradle.api.tasks">@PathSensitive</a>(<a href="../PathSensitivity.html#RELATIVE">RELATIVE</a>)
<a href="../InputFiles.html" title="annotation in org.gradle.api.tasks">@InputFiles</a>
public&nbsp;<a href="../../file/FileCollection.html" title="interface in org.gradle.api.file">FileCollection</a>&nbsp;getSourcepath()</pre>
<div class="block">The source path to use for the compilation.
 <p>
 The source path indicates the location of source files that <i>may</i> be compiled if necessary.
 It is effectively a complement to the class path, where the classes to be compiled against are in source form.
 It does <b>not</b> indicate the actual primary source being compiled.
 <p>
 The source path feature of the Java compiler is rarely needed for modern builds that use dependency management.
 <p>
 The default value for the source path is <code>null</code>, which indicates an <i>empty</i> source path.
 Note that this is different to the default value for the <code>-sourcepath</code> option for <code>javac</code>, which is to use the value specified by <code>-classpath</code>.
 If you wish to use any source path, it must be explicitly set.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the source path</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="#setSourcepath-org.gradle.api.file.FileCollection-"><code>setSourcepath(FileCollection)</code></a></dd>
</dl>
</li>
</ul>
<a name="setSourcepath-org.gradle.api.file.FileCollection-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setSourcepath</h4>
<pre class="methodSignature">public&nbsp;void&nbsp;setSourcepath&#8203;(@Nullable
                          <a href="../../file/FileCollection.html" title="interface in org.gradle.api.file">FileCollection</a>&nbsp;sourcepath)</pre>
<div class="block">Sets the source path to use for the compilation.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>sourcepath</code> - the source path</dd>
</dl>
</li>
</ul>
<a name="getAnnotationProcessorPath--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getAnnotationProcessorPath</h4>
<pre class="methodSignature">@Nullable
<a href="../Optional.html" title="annotation in org.gradle.api.tasks">@Optional</a>
<a href="../Classpath.html" title="annotation in org.gradle.api.tasks">@Classpath</a>
public&nbsp;<a href="../../file/FileCollection.html" title="interface in org.gradle.api.file">FileCollection</a>&nbsp;getAnnotationProcessorPath()</pre>
<div class="block">Returns the classpath to use to load annotation processors. This path is also used for annotation processor discovery.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The annotation processor path, or <code>null</code> if annotation processing is disabled.</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>3.4</dd>
</dl>
</li>
</ul>
<a name="setAnnotationProcessorPath-org.gradle.api.file.FileCollection-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setAnnotationProcessorPath</h4>
<pre class="methodSignature">public&nbsp;void&nbsp;setAnnotationProcessorPath&#8203;(@Nullable
                                       <a href="../../file/FileCollection.html" title="interface in org.gradle.api.file">FileCollection</a>&nbsp;annotationProcessorPath)</pre>
<div class="block">Set the classpath to use to load annotation processors. This path is also used for annotation processor discovery.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>annotationProcessorPath</code> - The annotation processor path, or <code>null</code> to disable annotation processing.</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>3.4</dd>
</dl>
</li>
</ul>
<a name="getRelease--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getRelease</h4>
<pre class="methodSignature"><a href="../Input.html" title="annotation in org.gradle.api.tasks">@Input</a>
<a href="../Optional.html" title="annotation in org.gradle.api.tasks">@Optional</a>
public&nbsp;<a href="../../provider/Property.html" title="interface in org.gradle.api.provider">Property</a>&lt;java.lang.Integer&gt;&nbsp;getRelease()</pre>
<div class="block">Configures the Java language version for this compile task (<code>--release</code> compiler flag).
 <p>
 If set, it will take precedences over the <a href="AbstractCompile.html#getSourceCompatibility--"><code>AbstractCompile.getSourceCompatibility()</code></a> and <a href="AbstractCompile.html#getTargetCompatibility--"><code>AbstractCompile.getTargetCompatibility()</code></a> settings.
 <p>
 This option is only taken into account by the <a href="JavaCompile.html" title="class in org.gradle.api.tasks.compile"><code>JavaCompile</code></a> task.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>6.6</dd>
</dl>
</li>
</ul>
<a name="getJavaModuleVersion--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getJavaModuleVersion</h4>
<pre class="methodSignature"><a href="../Optional.html" title="annotation in org.gradle.api.tasks">@Optional</a>
<a href="../Input.html" title="annotation in org.gradle.api.tasks">@Input</a>
public&nbsp;<a href="../../provider/Property.html" title="interface in org.gradle.api.provider">Property</a>&lt;java.lang.String&gt;&nbsp;getJavaModuleVersion()</pre>
<div class="block">Set the version of the Java module.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>6.4</dd>
</dl>
</li>
</ul>
<a name="getJavaModuleMainClass--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getJavaModuleMainClass</h4>
<pre class="methodSignature"><a href="../Optional.html" title="annotation in org.gradle.api.tasks">@Optional</a>
<a href="../Input.html" title="annotation in org.gradle.api.tasks">@Input</a>
public&nbsp;<a href="../../provider/Property.html" title="interface in org.gradle.api.provider">Property</a>&lt;java.lang.String&gt;&nbsp;getJavaModuleMainClass()</pre>
<div class="block">Set the main class of the Java module, if the module is supposed to be executable.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>6.4</dd>
</dl>
</li>
</ul>
<a name="getGeneratedSourceOutputDirectory--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getGeneratedSourceOutputDirectory</h4>
<pre class="methodSignature"><a href="../Optional.html" title="annotation in org.gradle.api.tasks">@Optional</a>
<a href="../OutputDirectory.html" title="annotation in org.gradle.api.tasks">@OutputDirectory</a>
public&nbsp;<a href="../../file/DirectoryProperty.html" title="interface in org.gradle.api.file">DirectoryProperty</a>&nbsp;getGeneratedSourceOutputDirectory()</pre>
<div class="block">Returns the directory to place source files generated by annotation processors.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>6.3</dd>
</dl>
</li>
</ul>
<a name="getAnnotationProcessorGeneratedSourcesDirectory--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getAnnotationProcessorGeneratedSourcesDirectory</h4>
<pre class="methodSignature">@Nullable
@Deprecated
<a href="../../model/ReplacedBy.html" title="annotation in org.gradle.api.model">@ReplacedBy</a>("generatedSourceOutputDirectory")
public&nbsp;java.io.File&nbsp;getAnnotationProcessorGeneratedSourcesDirectory()</pre>
<div class="deprecationBlock"><span class="deprecatedLabel">Deprecated.</span>
<div class="deprecationComment">Use <a href="#getGeneratedSourceOutputDirectory--"><code>getGeneratedSourceOutputDirectory()</code></a> instead. This method will be removed in Gradle 9.0.</div>
</div>
<div class="block">Returns the directory to place source files generated by annotation processors.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>4.3</dd>
</dl>
</li>
</ul>
<a name="setAnnotationProcessorGeneratedSourcesDirectory-java.io.File-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setAnnotationProcessorGeneratedSourcesDirectory</h4>
<pre class="methodSignature">@Deprecated
public&nbsp;void&nbsp;setAnnotationProcessorGeneratedSourcesDirectory&#8203;(@Nullable
                                                            java.io.File&nbsp;file)</pre>
<div class="deprecationBlock"><span class="deprecatedLabel">Deprecated.</span>
<div class="deprecationComment">Use <a href="#getGeneratedSourceOutputDirectory--"><code>getGeneratedSourceOutputDirectory()</code></a>.set() instead. This method will be removed in Gradle 9.0.</div>
</div>
<div class="block">Sets the directory to place source files generated by annotation processors.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>4.3</dd>
</dl>
</li>
</ul>
<a name="setAnnotationProcessorGeneratedSourcesDirectory-org.gradle.api.provider.Provider-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setAnnotationProcessorGeneratedSourcesDirectory</h4>
<pre class="methodSignature">@Deprecated
public&nbsp;void&nbsp;setAnnotationProcessorGeneratedSourcesDirectory&#8203;(<a href="../../provider/Provider.html" title="interface in org.gradle.api.provider">Provider</a>&lt;java.io.File&gt;&nbsp;file)</pre>
<div class="deprecationBlock"><span class="deprecatedLabel">Deprecated.</span>
<div class="deprecationComment">Use <a href="#getGeneratedSourceOutputDirectory--"><code>getGeneratedSourceOutputDirectory()</code></a>.set() instead. This method will be removed in Gradle 9.0.</div>
</div>
<div class="block">Sets the directory to place source files generated by annotation processors.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>4.3</dd>
</dl>
</li>
</ul>
<a name="getHeaderOutputDirectory--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>getHeaderOutputDirectory</h4>
<pre class="methodSignature"><a href="../Optional.html" title="annotation in org.gradle.api.tasks">@Optional</a>
<a href="../OutputDirectory.html" title="annotation in org.gradle.api.tasks">@OutputDirectory</a>
public&nbsp;<a href="../../file/DirectoryProperty.html" title="interface in org.gradle.api.file">DirectoryProperty</a>&nbsp;getHeaderOutputDirectory()</pre>
<div class="block">If this option is set to a non-null directory, it will be passed to the Java compiler's `-h` option, prompting it to generate native headers to that directory.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>4.10</dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../../allclasses.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
