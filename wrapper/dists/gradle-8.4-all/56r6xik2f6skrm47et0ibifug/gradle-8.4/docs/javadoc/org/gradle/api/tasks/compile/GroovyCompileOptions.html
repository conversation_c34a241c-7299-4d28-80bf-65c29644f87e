<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc -->
<title>GroovyCompileOptions (Gradle API 8.4)</title>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<link rel="stylesheet" type="text/css" href="../../../../../javadoc.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../jquery/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
<script type="text/javascript" src="../../../../../jquery/jszip/dist/jszip.min.js"></script>
<script type="text/javascript" src="../../../../../jquery/jszip-utils/dist/jszip-utils.min.js"></script>
<!--[if IE]>
<script type="text/javascript" src="../../../../../jquery/jszip-utils/dist/jszip-utils-ie.min.js"></script>
<![endif]-->
<script type="text/javascript" src="../../../../../jquery/jquery-3.6.0.min.js"></script>
<script type="text/javascript" src="../../../../../jquery/jquery-ui.min.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="GroovyCompileOptions (Gradle API 8.4)";
        }
    }
    catch(err) {
    }
//-->
var data = {"i0":10,"i1":10,"i2":10,"i3":10,"i4":10,"i5":10,"i6":10,"i7":10,"i8":10,"i9":10,"i10":10,"i11":10,"i12":10,"i13":10,"i14":10,"i15":10,"i16":10,"i17":10,"i18":10,"i19":10,"i20":10,"i21":10,"i22":10,"i23":10,"i24":10,"i25":10,"i26":10,"i27":10,"i28":10};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
var pathtoroot = "../../../../../";
var useModuleDirectories = false;
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="fixedNav">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../../allclasses.html">All&nbsp;Classes</a></li>
</ul>
<ul class="navListSearch">
<li><label for="search">SEARCH:</label>
<input type="text" id="search" value="search" disabled="disabled">
<input type="reset" id="reset" value="reset" disabled="disabled">
</li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
</div>
<div class="navPadding">&nbsp;</div>
<script type="text/javascript"><!--
$('.navPadding').css('padding-top', $('.fixedNav').css("height"));
//-->
</script>
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle"><span class="packageLabelInType">Package</span>&nbsp;<a href="package-summary.html">org.gradle.api.tasks.compile</a></div>
<h2 title="Class GroovyCompileOptions" class="title">Class GroovyCompileOptions</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li><a href="AbstractOptions.html" title="class in org.gradle.api.tasks.compile">org.gradle.api.tasks.compile.AbstractOptions</a></li>
<li>
<ul class="inheritance">
<li>org.gradle.api.tasks.compile.GroovyCompileOptions</li>
</ul>
</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Implemented Interfaces:</dt>
<dd><code>java.io.Serializable</code></dd>
</dl>
<hr>
<pre>public abstract class <span class="typeNameLabel">GroovyCompileOptions</span>
extends <a href="AbstractOptions.html" title="class in org.gradle.api.tasks.compile">AbstractOptions</a></pre>
<div class="block">Compilation options to be passed to the Groovy compiler.</div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../../../serialized-form.html#org.gradle.api.tasks.compile.GroovyCompileOptions">Serialized Form</a></dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="memberSummary" summary="Constructor Summary table, listing constructors, and an explanation">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Constructor</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tr class="altColor">
<th class="colConstructorName" scope="row"><code><span class="memberNameLink"><a href="#GroovyCompileOptions--">GroovyCompileOptions</a></span>()</code></th>
<td class="colLast">&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colSecond" scope="col">Method</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code><a href="GroovyCompileOptions.html" title="class in org.gradle.api.tasks.compile">GroovyCompileOptions</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#fork-java.util.Map-">fork</a></span>&#8203;(java.util.Map&lt;java.lang.String,&#8203;java.lang.Object&gt;&nbsp;forkArgs)</code></th>
<td class="colLast">
<div class="block">Convenience method to set <a href="GroovyForkOptions.html" title="class in org.gradle.api.tasks.compile"><code>GroovyForkOptions</code></a> with named parameter syntax.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>java.io.File</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getConfigurationScript--">getConfigurationScript</a></span>()</code></th>
<td class="colLast">
<div class="block">A Groovy script file that configures the compiler, allowing extensive control over how the code is compiled.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code><a href="../../provider/SetProperty.html" title="interface in org.gradle.api.provider">SetProperty</a>&lt;java.lang.String&gt;</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getDisabledGlobalASTTransformations--">getDisabledGlobalASTTransformations</a></span>()</code></th>
<td class="colLast">
<div class="block">Returns the set of global AST transformations which should not be loaded into the Groovy compiler.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>java.lang.String</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getEncoding--">getEncoding</a></span>()</code></th>
<td class="colLast">
<div class="block">Tells the source encoding.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>java.util.List&lt;java.lang.String&gt;</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getFileExtensions--">getFileExtensions</a></span>()</code></th>
<td class="colLast">
<div class="block">Returns the list of acceptable source file extensions.</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code><a href="GroovyForkOptions.html" title="class in org.gradle.api.tasks.compile">GroovyForkOptions</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getForkOptions--">getForkOptions</a></span>()</code></th>
<td class="colLast">
<div class="block">Returns options for running the Groovy compiler in a separate process.</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>protected <a href="../../model/ObjectFactory.html" title="interface in org.gradle.api.model">ObjectFactory</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getObjectFactory--">getObjectFactory</a></span>()</code></th>
<td class="colLast">&nbsp;</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>java.util.Map&lt;java.lang.String,&#8203;java.lang.Boolean&gt;</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getOptimizationOptions--">getOptimizationOptions</a></span>()</code></th>
<td class="colLast">
<div class="block">Returns optimization options for the Groovy compiler.</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>java.io.File</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getStubDir--">getStubDir</a></span>()</code></th>
<td class="colLast">
<div class="block">Returns the directory where Java stubs for Groovy classes will be stored during Java/Groovy joint
 compilation.</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#isFailOnError--">isFailOnError</a></span>()</code></th>
<td class="colLast">
<div class="block">Tells whether the compilation task should fail if compile errors occurred.</div>
</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#isFork--">isFork</a></span>()</code></th>
<td class="colLast">
<div class="block">Tells whether to run the Groovy compiler in a separate process.</div>
</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#isJavaAnnotationProcessing--">isJavaAnnotationProcessing</a></span>()</code></th>
<td class="colLast">
<div class="block">Whether the Groovy code should be subject to Java annotation processing.</div>
</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#isKeepStubs--">isKeepStubs</a></span>()</code></th>
<td class="colLast">
<div class="block">Tells whether Java stubs for Groovy classes generated during Java/Groovy joint compilation
 should be kept after compilation has completed.</div>
</td>
</tr>
<tr id="i13" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#isListFiles--">isListFiles</a></span>()</code></th>
<td class="colLast">
<div class="block">Tells whether to print which source files are to be compiled.</div>
</td>
</tr>
<tr id="i14" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#isParameters--">isParameters</a></span>()</code></th>
<td class="colLast">
<div class="block">Whether the Groovy compiler generate metadata for reflection on method parameter names on JDK 8 and above.</div>
</td>
</tr>
<tr id="i15" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#isVerbose--">isVerbose</a></span>()</code></th>
<td class="colLast">
<div class="block">Tells whether to turn on verbose output.</div>
</td>
</tr>
<tr id="i16" class="altColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setConfigurationScript-java.io.File-">setConfigurationScript</a></span>&#8203;(java.io.File&nbsp;configurationFile)</code></th>
<td class="colLast">
<div class="block">Sets the path to the groovy configuration file.</div>
</td>
</tr>
<tr id="i17" class="rowColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setEncoding-java.lang.String-">setEncoding</a></span>&#8203;(java.lang.String&nbsp;encoding)</code></th>
<td class="colLast">
<div class="block">Sets the source encoding.</div>
</td>
</tr>
<tr id="i18" class="altColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setFailOnError-boolean-">setFailOnError</a></span>&#8203;(boolean&nbsp;failOnError)</code></th>
<td class="colLast">
<div class="block">Sets whether the compilation task should fail if compile errors occurred.</div>
</td>
</tr>
<tr id="i19" class="rowColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setFileExtensions-java.util.List-">setFileExtensions</a></span>&#8203;(java.util.List&lt;java.lang.String&gt;&nbsp;fileExtensions)</code></th>
<td class="colLast">
<div class="block">Sets the list of acceptable source file extensions.</div>
</td>
</tr>
<tr id="i20" class="altColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setFork-boolean-">setFork</a></span>&#8203;(boolean&nbsp;fork)</code></th>
<td class="colLast">
<div class="block">Sets whether to run the Groovy compiler in a separate process.</div>
</td>
</tr>
<tr id="i21" class="rowColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setForkOptions-org.gradle.api.tasks.compile.GroovyForkOptions-">setForkOptions</a></span>&#8203;(<a href="GroovyForkOptions.html" title="class in org.gradle.api.tasks.compile">GroovyForkOptions</a>&nbsp;forkOptions)</code></th>
<td class="colLast">
<div class="block">Sets options for running the Groovy compiler in a separate process.</div>
</td>
</tr>
<tr id="i22" class="altColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setJavaAnnotationProcessing-boolean-">setJavaAnnotationProcessing</a></span>&#8203;(boolean&nbsp;javaAnnotationProcessing)</code></th>
<td class="colLast">
<div class="block">Sets whether Java annotation processors should process annotations on stubs.</div>
</td>
</tr>
<tr id="i23" class="rowColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setKeepStubs-boolean-">setKeepStubs</a></span>&#8203;(boolean&nbsp;keepStubs)</code></th>
<td class="colLast">
<div class="block">Sets whether Java stubs for Groovy classes generated during Java/Groovy joint compilation
 should be kept after compilation has completed.</div>
</td>
</tr>
<tr id="i24" class="altColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setListFiles-boolean-">setListFiles</a></span>&#8203;(boolean&nbsp;listFiles)</code></th>
<td class="colLast">
<div class="block">Sets whether to print which source files are to be compiled.</div>
</td>
</tr>
<tr id="i25" class="rowColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setOptimizationOptions-java.util.Map-">setOptimizationOptions</a></span>&#8203;(java.util.Map&lt;java.lang.String,&#8203;java.lang.Boolean&gt;&nbsp;optimizationOptions)</code></th>
<td class="colLast">
<div class="block">Sets optimization options for the Groovy compiler.</div>
</td>
</tr>
<tr id="i26" class="altColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setParameters-boolean-">setParameters</a></span>&#8203;(boolean&nbsp;parameters)</code></th>
<td class="colLast">
<div class="block">Sets whether metadata for reflection on method parameter names should be generated.</div>
</td>
</tr>
<tr id="i27" class="rowColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setStubDir-java.io.File-">setStubDir</a></span>&#8203;(java.io.File&nbsp;stubDir)</code></th>
<td class="colLast">
<div class="block">Sets the directory where Java stubs for Groovy classes will be stored during Java/Groovy joint
 compilation.</div>
</td>
</tr>
<tr id="i28" class="altColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setVerbose-boolean-">setVerbose</a></span>&#8203;(boolean&nbsp;verbose)</code></th>
<td class="colLast">
<div class="block">Sets whether to turn on verbose output.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.org.gradle.api.tasks.compile.AbstractOptions">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;org.gradle.api.tasks.compile.<a href="AbstractOptions.html" title="class in org.gradle.api.tasks.compile">AbstractOptions</a></h3>
<code><a href="AbstractOptions.html#define-java.util.Map-">define</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>clone, equals, finalize, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a name="GroovyCompileOptions--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>GroovyCompileOptions</h4>
<pre>public&nbsp;GroovyCompileOptions()</pre>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="getObjectFactory--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getObjectFactory</h4>
<pre class="methodSignature">@Inject
protected&nbsp;<a href="../../model/ObjectFactory.html" title="interface in org.gradle.api.model">ObjectFactory</a>&nbsp;getObjectFactory()</pre>
</li>
</ul>
<a name="isFailOnError--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isFailOnError</h4>
<pre class="methodSignature"><a href="../Input.html" title="annotation in org.gradle.api.tasks">@Input</a>
public&nbsp;boolean&nbsp;isFailOnError()</pre>
<div class="block">Tells whether the compilation task should fail if compile errors occurred. Defaults to <code>true</code>.</div>
</li>
</ul>
<a name="setFailOnError-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setFailOnError</h4>
<pre class="methodSignature">public&nbsp;void&nbsp;setFailOnError&#8203;(boolean&nbsp;failOnError)</pre>
<div class="block">Sets whether the compilation task should fail if compile errors occurred. Defaults to <code>true</code>.</div>
</li>
</ul>
<a name="isVerbose--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isVerbose</h4>
<pre class="methodSignature"><a href="../Console.html" title="annotation in org.gradle.api.tasks">@Console</a>
public&nbsp;boolean&nbsp;isVerbose()</pre>
<div class="block">Tells whether to turn on verbose output. Defaults to <code>false</code>.</div>
</li>
</ul>
<a name="setVerbose-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setVerbose</h4>
<pre class="methodSignature">public&nbsp;void&nbsp;setVerbose&#8203;(boolean&nbsp;verbose)</pre>
<div class="block">Sets whether to turn on verbose output. Defaults to <code>false</code>.</div>
</li>
</ul>
<a name="isListFiles--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isListFiles</h4>
<pre class="methodSignature"><a href="../Console.html" title="annotation in org.gradle.api.tasks">@Console</a>
public&nbsp;boolean&nbsp;isListFiles()</pre>
<div class="block">Tells whether to print which source files are to be compiled. Defaults to <code>false</code>.</div>
</li>
</ul>
<a name="setListFiles-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setListFiles</h4>
<pre class="methodSignature">public&nbsp;void&nbsp;setListFiles&#8203;(boolean&nbsp;listFiles)</pre>
<div class="block">Sets whether to print which source files are to be compiled. Defaults to <code>false</code>.</div>
</li>
</ul>
<a name="getEncoding--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getEncoding</h4>
<pre class="methodSignature"><a href="../Input.html" title="annotation in org.gradle.api.tasks">@Input</a>
public&nbsp;java.lang.String&nbsp;getEncoding()</pre>
<div class="block">Tells the source encoding. Defaults to <code>UTF-8</code>.</div>
</li>
</ul>
<a name="setEncoding-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setEncoding</h4>
<pre class="methodSignature">public&nbsp;void&nbsp;setEncoding&#8203;(java.lang.String&nbsp;encoding)</pre>
<div class="block">Sets the source encoding. Defaults to <code>UTF-8</code>.</div>
</li>
</ul>
<a name="isFork--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isFork</h4>
<pre class="methodSignature"><a href="../Input.html" title="annotation in org.gradle.api.tasks">@Input</a>
public&nbsp;boolean&nbsp;isFork()</pre>
<div class="block">Tells whether to run the Groovy compiler in a separate process. Defaults to <code>true</code>.</div>
</li>
</ul>
<a name="setFork-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setFork</h4>
<pre class="methodSignature">public&nbsp;void&nbsp;setFork&#8203;(boolean&nbsp;fork)</pre>
<div class="block">Sets whether to run the Groovy compiler in a separate process. Defaults to <code>true</code>.</div>
</li>
</ul>
<a name="getConfigurationScript--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getConfigurationScript</h4>
<pre class="methodSignature">@Nullable
<a href="../Optional.html" title="annotation in org.gradle.api.tasks">@Optional</a>
<a href="../PathSensitive.html" title="annotation in org.gradle.api.tasks">@PathSensitive</a>(<a href="../PathSensitivity.html#NONE">NONE</a>)
<a href="../InputFile.html" title="annotation in org.gradle.api.tasks">@InputFile</a>
public&nbsp;java.io.File&nbsp;getConfigurationScript()</pre>
<div class="block">A Groovy script file that configures the compiler, allowing extensive control over how the code is compiled.
 <p>
 The script is executed as Groovy code, with the following context:
 </p>
 <ul>
 <li>The instance of <a href="https://docs.groovy-lang.org/latest/html/gapi/org/codehaus/groovy/control/CompilerConfiguration.html">CompilerConfiguration</a> available as the <code>configuration</code> variable.</li>
 <li>All static members of <a href="https://docs.groovy-lang.org/latest/html/gapi/org/codehaus/groovy/control/customizers/builder/CompilerCustomizationBuilder.html">CompilerCustomizationBuilder</a> pre imported.</li>
 </ul>
 <p>
 This facilitates the following pattern:
 </p>
 <pre>
 withConfig(configuration) {
   // use compiler configuration DSL here
 }
 </pre>
 <p>
 For example, to activate type checking for all Groovy classes…
 </p>
 <pre>
 import groovy.transform.TypeChecked

 withConfig(configuration) {
     ast(TypeChecked)
 }
 </pre>
 <p>
 Please see <a href="https://docs.groovy-lang.org/latest/html/documentation/#compilation-customizers">the Groovy compiler customization builder documentation</a>
 for more information about the compiler configuration DSL.
 </p>
 <p>
 <b>This feature is only available if compiling with Groovy 2.1 or later.</b>
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="https://docs.groovy-lang.org/latest/html/gapi/org/codehaus/groovy/control/CompilerConfiguration.html">CompilerConfiguration</a>, 
<a href="https://docs.groovy-lang.org/latest/html/gapi/org/codehaus/groovy/control/customizers/builder/CompilerCustomizationBuilder.html">CompilerCustomizationBuilder</a></dd>
</dl>
</li>
</ul>
<a name="setConfigurationScript-java.io.File-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setConfigurationScript</h4>
<pre class="methodSignature">public&nbsp;void&nbsp;setConfigurationScript&#8203;(@Nullable
                                   java.io.File&nbsp;configurationFile)</pre>
<div class="block">Sets the path to the groovy configuration file.</div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="#getConfigurationScript--"><code>getConfigurationScript()</code></a></dd>
</dl>
</li>
</ul>
<a name="isJavaAnnotationProcessing--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isJavaAnnotationProcessing</h4>
<pre class="methodSignature"><a href="../Input.html" title="annotation in org.gradle.api.tasks">@Input</a>
public&nbsp;boolean&nbsp;isJavaAnnotationProcessing()</pre>
<div class="block">Whether the Groovy code should be subject to Java annotation processing.
 <p>
 Annotation processing of Groovy code works by having annotation processors visit the Java stubs generated by the
 Groovy compiler in order to support joint compilation of Groovy and Java source.
 <p>
 When set to <code>true</code>, stubs will be unconditionally generated for all Groovy sources, and Java annotations processors will be executed on those stubs.
 <p>
 When this option is set to <code>false</code> (the default), Groovy code will not be subject to annotation processing, but any joint compiled Java code will be.
 If the compiler argument <code>"-proc:none"</code> was specified as part of the Java compile options, the value of this flag will be ignored.
 No annotation processing will be performed regardless, on Java or Groovy source.</div>
</li>
</ul>
<a name="setJavaAnnotationProcessing-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setJavaAnnotationProcessing</h4>
<pre class="methodSignature">public&nbsp;void&nbsp;setJavaAnnotationProcessing&#8203;(boolean&nbsp;javaAnnotationProcessing)</pre>
<div class="block">Sets whether Java annotation processors should process annotations on stubs.

 Defaults to <code>false</code>.</div>
</li>
</ul>
<a name="isParameters--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isParameters</h4>
<pre class="methodSignature"><a href="../Input.html" title="annotation in org.gradle.api.tasks">@Input</a>
public&nbsp;boolean&nbsp;isParameters()</pre>
<div class="block">Whether the Groovy compiler generate metadata for reflection on method parameter names on JDK 8 and above.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>6.1</dd>
</dl>
</li>
</ul>
<a name="setParameters-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setParameters</h4>
<pre class="methodSignature">public&nbsp;void&nbsp;setParameters&#8203;(boolean&nbsp;parameters)</pre>
<div class="block">Sets whether metadata for reflection on method parameter names should be generated.
 Defaults to <code>false</code></div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>6.1</dd>
</dl>
</li>
</ul>
<a name="getForkOptions--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getForkOptions</h4>
<pre class="methodSignature">public&nbsp;<a href="GroovyForkOptions.html" title="class in org.gradle.api.tasks.compile">GroovyForkOptions</a>&nbsp;getForkOptions()</pre>
<div class="block">Returns options for running the Groovy compiler in a separate process. These options only take effect
 if <code>fork</code> is set to <code>true</code>.</div>
</li>
</ul>
<a name="setForkOptions-org.gradle.api.tasks.compile.GroovyForkOptions-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setForkOptions</h4>
<pre class="methodSignature">public&nbsp;void&nbsp;setForkOptions&#8203;(<a href="GroovyForkOptions.html" title="class in org.gradle.api.tasks.compile">GroovyForkOptions</a>&nbsp;forkOptions)</pre>
<div class="block">Sets options for running the Groovy compiler in a separate process. These options only take effect
 if <code>fork</code> is set to <code>true</code>.</div>
</li>
</ul>
<a name="getOptimizationOptions--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getOptimizationOptions</h4>
<pre class="methodSignature">@Nullable
<a href="../Optional.html" title="annotation in org.gradle.api.tasks">@Optional</a>
<a href="../Input.html" title="annotation in org.gradle.api.tasks">@Input</a>
public&nbsp;java.util.Map&lt;java.lang.String,&#8203;java.lang.Boolean&gt;&nbsp;getOptimizationOptions()</pre>
<div class="block">Returns optimization options for the Groovy compiler. Allowed values for an option are <code>true</code> and <code>false</code>.
 Only takes effect when compiling against Groovy 1.8 or higher.

 <p>Known options are:

 <dl>
     <dt>indy
     <dd>Use the invokedynamic bytecode instruction. Requires JDK7 or higher and Groovy 2.0 or higher. Disabled by default.
     <dt>int
     <dd>Optimize operations on primitive types (e.g. integers). Enabled by default.
     <dt>all
     <dd>Enable or disable all optimizations. Note that some optimizations might be mutually exclusive.
 </dl></div>
</li>
</ul>
<a name="setOptimizationOptions-java.util.Map-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setOptimizationOptions</h4>
<pre class="methodSignature">public&nbsp;void&nbsp;setOptimizationOptions&#8203;(@Nullable
                                   java.util.Map&lt;java.lang.String,&#8203;java.lang.Boolean&gt;&nbsp;optimizationOptions)</pre>
<div class="block">Sets optimization options for the Groovy compiler. Allowed values for an option are <code>true</code> and <code>false</code>.
 Only takes effect when compiling against Groovy 1.8 or higher.</div>
</li>
</ul>
<a name="getDisabledGlobalASTTransformations--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getDisabledGlobalASTTransformations</h4>
<pre class="methodSignature"><a href="../Input.html" title="annotation in org.gradle.api.tasks">@Input</a>
public&nbsp;<a href="../../provider/SetProperty.html" title="interface in org.gradle.api.provider">SetProperty</a>&lt;java.lang.String&gt;&nbsp;getDisabledGlobalASTTransformations()</pre>
<div class="block">Returns the set of global AST transformations which should not be loaded into the Groovy compiler.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>7.4</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="https://docs.groovy-lang.org/latest/html/api/org/codehaus/groovy/control/CompilerConfiguration.html#setDisabledGlobalASTTransformations(java.util.Set)">CompilerConfiguration</a></dd>
</dl>
</li>
</ul>
<a name="getStubDir--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getStubDir</h4>
<pre class="methodSignature"><a href="../Internal.html" title="annotation in org.gradle.api.tasks">@Internal</a>
public&nbsp;java.io.File&nbsp;getStubDir()</pre>
<div class="block">Returns the directory where Java stubs for Groovy classes will be stored during Java/Groovy joint
 compilation. Defaults to <code>null</code>, in which case a temporary directory will be used.</div>
</li>
</ul>
<a name="setStubDir-java.io.File-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setStubDir</h4>
<pre class="methodSignature">public&nbsp;void&nbsp;setStubDir&#8203;(java.io.File&nbsp;stubDir)</pre>
<div class="block">Sets the directory where Java stubs for Groovy classes will be stored during Java/Groovy joint
 compilation. Defaults to <code>null</code>, in which case a temporary directory will be used.</div>
</li>
</ul>
<a name="getFileExtensions--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getFileExtensions</h4>
<pre class="methodSignature"><a href="../Input.html" title="annotation in org.gradle.api.tasks">@Input</a>
public&nbsp;java.util.List&lt;java.lang.String&gt;&nbsp;getFileExtensions()</pre>
<div class="block">Returns the list of acceptable source file extensions. Only takes effect when compiling against
 Groovy 1.7 or higher. Defaults to <code>ImmutableList.of("java", "groovy")</code>.</div>
</li>
</ul>
<a name="setFileExtensions-java.util.List-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setFileExtensions</h4>
<pre class="methodSignature">public&nbsp;void&nbsp;setFileExtensions&#8203;(java.util.List&lt;java.lang.String&gt;&nbsp;fileExtensions)</pre>
<div class="block">Sets the list of acceptable source file extensions. Only takes effect when compiling against
 Groovy 1.7 or higher. Defaults to <code>ImmutableList.of("java", "groovy")</code>.</div>
</li>
</ul>
<a name="isKeepStubs--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isKeepStubs</h4>
<pre class="methodSignature"><a href="../Input.html" title="annotation in org.gradle.api.tasks">@Input</a>
public&nbsp;boolean&nbsp;isKeepStubs()</pre>
<div class="block">Tells whether Java stubs for Groovy classes generated during Java/Groovy joint compilation
 should be kept after compilation has completed. Useful for joint compilation debugging purposes.
 Defaults to <code>false</code>.</div>
</li>
</ul>
<a name="setKeepStubs-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setKeepStubs</h4>
<pre class="methodSignature">public&nbsp;void&nbsp;setKeepStubs&#8203;(boolean&nbsp;keepStubs)</pre>
<div class="block">Sets whether Java stubs for Groovy classes generated during Java/Groovy joint compilation
 should be kept after compilation has completed. Useful for joint compilation debugging purposes.
 Defaults to <code>false</code>.</div>
</li>
</ul>
<a name="fork-java.util.Map-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>fork</h4>
<pre class="methodSignature">public&nbsp;<a href="GroovyCompileOptions.html" title="class in org.gradle.api.tasks.compile">GroovyCompileOptions</a>&nbsp;fork&#8203;(java.util.Map&lt;java.lang.String,&#8203;java.lang.Object&gt;&nbsp;forkArgs)</pre>
<div class="block">Convenience method to set <a href="GroovyForkOptions.html" title="class in org.gradle.api.tasks.compile"><code>GroovyForkOptions</code></a> with named parameter syntax.
 Calling this method will set <code>fork</code> to <code>true</code>.</div>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../../allclasses.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
