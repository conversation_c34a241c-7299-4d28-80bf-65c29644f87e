<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc -->
<title>GradleBuild (Gradle API 8.4)</title>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<link rel="stylesheet" type="text/css" href="../../../../javadoc.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../jquery/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
<script type="text/javascript" src="../../../../jquery/jszip/dist/jszip.min.js"></script>
<script type="text/javascript" src="../../../../jquery/jszip-utils/dist/jszip-utils.min.js"></script>
<!--[if IE]>
<script type="text/javascript" src="../../../../jquery/jszip-utils/dist/jszip-utils-ie.min.js"></script>
<![endif]-->
<script type="text/javascript" src="../../../../jquery/jquery-3.6.0.min.js"></script>
<script type="text/javascript" src="../../../../jquery/jquery-ui.min.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="GradleBuild (Gradle API 8.4)";
        }
    }
    catch(err) {
    }
//-->
var data = {"i0":42,"i1":10,"i2":10,"i3":10,"i4":10,"i5":42,"i6":42,"i7":10,"i8":10,"i9":10,"i10":10,"i11":10,"i12":10};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"],32:["t6","Deprecated Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
var pathtoroot = "../../../../";
var useModuleDirectories = false;
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="fixedNav">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses.html">All&nbsp;Classes</a></li>
</ul>
<ul class="navListSearch">
<li><label for="search">SEARCH:</label>
<input type="text" id="search" value="search" disabled="disabled">
<input type="reset" id="reset" value="reset" disabled="disabled">
</li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li><a href="#nested.class.summary">Nested</a>&nbsp;|&nbsp;</li>
<li><a href="#field.summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
</div>
<div class="navPadding">&nbsp;</div>
<script type="text/javascript"><!--
$('.navPadding').css('padding-top', $('.fixedNav').css("height"));
//-->
</script>
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle"><span class="packageLabelInType">Package</span>&nbsp;<a href="package-summary.html">org.gradle.api.tasks</a></div>
<h2 title="Class GradleBuild" class="title">Class GradleBuild</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>org.gradle.api.internal.AbstractTask</li>
<li>
<ul class="inheritance">
<li><a href="../DefaultTask.html" title="class in org.gradle.api">org.gradle.api.DefaultTask</a></li>
<li>
<ul class="inheritance">
<li>org.gradle.api.internal.ConventionTask</li>
<li>
<ul class="inheritance">
<li>org.gradle.api.tasks.GradleBuild</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Implemented Interfaces:</dt>
<dd><code>java.lang.Comparable&lt;<a href="../Task.html" title="interface in org.gradle.api">Task</a>&gt;</code>, <code>org.gradle.api.internal.DynamicObjectAware</code>, <code>org.gradle.api.internal.IConventionAware</code>, <code>org.gradle.api.internal.TaskInternal</code>, <code><a href="../plugins/ExtensionAware.html" title="interface in org.gradle.api.plugins">ExtensionAware</a></code>, <code><a href="../Task.html" title="interface in org.gradle.api">Task</a></code>, <code><a href="../../util/Configurable.html" title="interface in org.gradle.util">Configurable</a>&lt;<a href="../Task.html" title="interface in org.gradle.api">Task</a>&gt;</code></dd>
</dl>
<hr>
<pre><a href="../../work/DisableCachingByDefault.html" title="annotation in org.gradle.work">@DisableCachingByDefault</a>(<a href="../../work/DisableCachingByDefault.html#because--">because</a>="Child Gradle build will do its own caching")
public abstract class <span class="typeNameLabel">GradleBuild</span>
extends org.gradle.api.internal.ConventionTask</pre>
<div class="block">Executes a Gradle build.</div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== NESTED CLASS SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="nested.class.summary">
<!--   -->
</a>
<h3>Nested Class Summary</h3>
<ul class="blockList">
<li class="blockList"><a name="nested.classes.inherited.from.class.org.gradle.api.Task">
<!--   -->
</a>
<h3>Nested classes/interfaces inherited from interface&nbsp;org.gradle.api.<a href="../Task.html" title="interface in org.gradle.api">Task</a></h3>
<code><a href="../Task.Namer.html" title="class in org.gradle.api">Task.Namer</a></code></li>
</ul>
</li>
</ul>
<!-- =========== FIELD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.summary">
<!--   -->
</a>
<h3>Field Summary</h3>
<ul class="blockList">
<li class="blockList"><a name="fields.inherited.from.class.org.gradle.api.Task">
<!--   -->
</a>
<h3>Fields inherited from interface&nbsp;org.gradle.api.<a href="../Task.html" title="interface in org.gradle.api">Task</a></h3>
<code><a href="../Task.html#TASK_ACTION">TASK_ACTION</a>, <a href="../Task.html#TASK_CONSTRUCTOR_ARGS">TASK_CONSTRUCTOR_ARGS</a>, <a href="../Task.html#TASK_DEPENDS_ON">TASK_DEPENDS_ON</a>, <a href="../Task.html#TASK_DESCRIPTION">TASK_DESCRIPTION</a>, <a href="../Task.html#TASK_GROUP">TASK_GROUP</a>, <a href="../Task.html#TASK_NAME">TASK_NAME</a>, <a href="../Task.html#TASK_OVERWRITE">TASK_OVERWRITE</a>, <a href="../Task.html#TASK_TYPE">TASK_TYPE</a></code></li>
</ul>
</li>
</ul>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="memberSummary" summary="Constructor Summary table, listing constructors, and an explanation">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Constructor</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tr class="altColor">
<th class="colConstructorName" scope="row"><code><span class="memberNameLink"><a href="#GradleBuild--">GradleBuild</a></span>()</code></th>
<td class="colLast">&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t6" class="tableTab"><span><a href="javascript:show(32);">Deprecated Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colSecond" scope="col">Method</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>java.io.File</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getBuildFile--">getBuildFile</a></span>()</code></th>
<td class="colLast">
<div class="block"><span class="deprecatedLabel">Deprecated.</span>
<div class="deprecationComment">Use <a href="#getDir--"><code>getDir()</code></a> instead to get the root of the nested build.</div>
</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>java.lang.String</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getBuildName--">getBuildName</a></span>()</code></th>
<td class="colLast">
<div class="block">The build name to use for the nested build.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>java.io.File</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getDir--">getDir</a></span>()</code></th>
<td class="colLast">
<div class="block">Returns the project directory for the build.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code><a href="../../StartParameter.html" title="class in org.gradle">StartParameter</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getStartParameter--">getStartParameter</a></span>()</code></th>
<td class="colLast">
<div class="block">Returns the full set of parameters that will be used to execute the build.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>java.util.List&lt;java.lang.String&gt;</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getTasks--">getTasks</a></span>()</code></th>
<td class="colLast">
<div class="block">Returns the tasks that should be executed for this build.</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setBuildFile-java.io.File-">setBuildFile</a></span>&#8203;(java.io.File&nbsp;file)</code></th>
<td class="colLast">
<div class="block"><span class="deprecatedLabel">Deprecated.</span>
<div class="deprecationComment">Use <a href="#setDir-java.io.File-"><code>setDir(File)</code></a> instead to set the root of the nested build.</div>
</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setBuildFile-java.lang.Object-">setBuildFile</a></span>&#8203;(java.lang.Object&nbsp;file)</code></th>
<td class="colLast">
<div class="block"><span class="deprecatedLabel">Deprecated.</span>
<div class="deprecationComment">Use <a href="#setDir-java.lang.Object-"><code>setDir(Object)</code></a> instead to set the root of the nested build.</div>
</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setBuildName-java.lang.String-">setBuildName</a></span>&#8203;(java.lang.String&nbsp;buildName)</code></th>
<td class="colLast">
<div class="block">Sets build name to use for the nested build.</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setDir-java.io.File-">setDir</a></span>&#8203;(java.io.File&nbsp;dir)</code></th>
<td class="colLast">
<div class="block">Sets the project directory for the build.</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setDir-java.lang.Object-">setDir</a></span>&#8203;(java.lang.Object&nbsp;dir)</code></th>
<td class="colLast">
<div class="block">Sets the project directory for the build.</div>
</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setStartParameter-org.gradle.StartParameter-">setStartParameter</a></span>&#8203;(<a href="../../StartParameter.html" title="class in org.gradle">StartParameter</a>&nbsp;startParameter)</code></th>
<td class="colLast">
<div class="block">Sets the full set of parameters that will be used to execute the build.</div>
</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setTasks-java.util.Collection-">setTasks</a></span>&#8203;(java.util.Collection&lt;java.lang.String&gt;&nbsp;tasks)</code></th>
<td class="colLast">
<div class="block">Sets the tasks that should be executed for this build.</div>
</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setTasks-java.util.List-">setTasks</a></span>&#8203;(java.util.List&lt;java.lang.String&gt;&nbsp;tasks)</code></th>
<td class="colLast">
<div class="block">Sets the tasks that should be executed for this build.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.org.gradle.api.internal.ConventionTask">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;org.gradle.api.internal.ConventionTask</h3>
<code>conventionMapping, conventionMapping, getConventionMapping</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.org.gradle.api.DefaultTask">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;org.gradle.api.<a href="../DefaultTask.html" title="class in org.gradle.api">DefaultTask</a></h3>
<code><a href="../DefaultTask.html#compareTo-org.gradle.api.Task-">compareTo</a>, <a href="../DefaultTask.html#configure-groovy.lang.Closure-">configure</a>, <a href="../DefaultTask.html#dependsOn-java.lang.Object...-">dependsOn</a>, <a href="../DefaultTask.html#doFirst-groovy.lang.Closure-">doFirst</a>, <a href="../DefaultTask.html#doFirst-java.lang.String-org.gradle.api.Action-">doFirst</a>, <a href="../DefaultTask.html#doFirst-org.gradle.api.Action-">doFirst</a>, <a href="../DefaultTask.html#doLast-groovy.lang.Closure-">doLast</a>, <a href="../DefaultTask.html#doLast-java.lang.String-org.gradle.api.Action-">doLast</a>, <a href="../DefaultTask.html#doLast-org.gradle.api.Action-">doLast</a>, <a href="../DefaultTask.html#finalizedBy-java.lang.Object...-">finalizedBy</a>, <a href="../DefaultTask.html#getActions--">getActions</a>, <a href="../DefaultTask.html#getAnt--">getAnt</a>, <a href="../DefaultTask.html#getDependsOn--">getDependsOn</a>, <a href="../DefaultTask.html#getDescription--">getDescription</a>, <a href="../DefaultTask.html#getDestroyables--">getDestroyables</a>, <a href="../DefaultTask.html#getDidWork--">getDidWork</a>, <a href="../DefaultTask.html#getEnabled--">getEnabled</a>, <a href="../DefaultTask.html#getExtensions--">getExtensions</a>, <a href="../DefaultTask.html#getFinalizedBy--">getFinalizedBy</a>, <a href="../DefaultTask.html#getGroup--">getGroup</a>, <a href="../DefaultTask.html#getInputs--">getInputs</a>, <a href="../DefaultTask.html#getLocalState--">getLocalState</a>, <a href="../DefaultTask.html#getLogger--">getLogger</a>, <a href="../DefaultTask.html#getLogging--">getLogging</a>, <a href="../DefaultTask.html#getMustRunAfter--">getMustRunAfter</a>, <a href="../DefaultTask.html#getName--">getName</a>, <a href="../DefaultTask.html#getOutputs--">getOutputs</a>, <a href="../DefaultTask.html#getPath--">getPath</a>, <a href="../DefaultTask.html#getProject--">getProject</a>, <a href="../DefaultTask.html#getShouldRunAfter--">getShouldRunAfter</a>, <a href="../DefaultTask.html#getState--">getState</a>, <a href="../DefaultTask.html#getTaskDependencies--">getTaskDependencies</a>, <a href="../DefaultTask.html#getTemporaryDir--">getTemporaryDir</a>, <a href="../DefaultTask.html#getTimeout--">getTimeout</a>, <a href="../DefaultTask.html#hasProperty-java.lang.String-">hasProperty</a>, <a href="../DefaultTask.html#mustRunAfter-java.lang.Object...-">mustRunAfter</a>, <a href="../DefaultTask.html#onlyIf-groovy.lang.Closure-">onlyIf</a>, <a href="../DefaultTask.html#onlyIf-java.lang.String-org.gradle.api.specs.Spec-">onlyIf</a>, <a href="../DefaultTask.html#onlyIf-org.gradle.api.specs.Spec-">onlyIf</a>, <a href="../DefaultTask.html#property-java.lang.String-">property</a>, <a href="../DefaultTask.html#setActions-java.util.List-">setActions</a>, <a href="../DefaultTask.html#setDependsOn-java.lang.Iterable-">setDependsOn</a>, <a href="../DefaultTask.html#setDescription-java.lang.String-">setDescription</a>, <a href="../DefaultTask.html#setDidWork-boolean-">setDidWork</a>, <a href="../DefaultTask.html#setEnabled-boolean-">setEnabled</a>, <a href="../DefaultTask.html#setFinalizedBy-java.lang.Iterable-">setFinalizedBy</a>, <a href="../DefaultTask.html#setGroup-java.lang.String-">setGroup</a>, <a href="../DefaultTask.html#setMustRunAfter-java.lang.Iterable-">setMustRunAfter</a>, <a href="../DefaultTask.html#setOnlyIf-groovy.lang.Closure-">setOnlyIf</a>, <a href="../DefaultTask.html#setOnlyIf-java.lang.String-org.gradle.api.specs.Spec-">setOnlyIf</a>, <a href="../DefaultTask.html#setOnlyIf-org.gradle.api.specs.Spec-">setOnlyIf</a>, <a href="../DefaultTask.html#setProperty-java.lang.String-java.lang.Object-">setProperty</a>, <a href="../DefaultTask.html#setShouldRunAfter-java.lang.Iterable-">setShouldRunAfter</a>, <a href="../DefaultTask.html#shouldRunAfter-java.lang.Object...-">shouldRunAfter</a>, <a href="../DefaultTask.html#usesService-org.gradle.api.provider.Provider-">usesService</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.org.gradle.api.internal.AbstractTask">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;org.gradle.api.internal.AbstractTask</h3>
<code>acceptServiceReferences, appendParallelSafeAction, doNotTrackState, getAsDynamicObject, getConvention, getIdentityPath, getImpliesSubProjects, getLifecycleDependencies, getOnlyIf, getReasonNotToTrackState, getReasonTaskIsIncompatibleWithConfigurationCache, getRequiredServices, getServices, getSharedResources, getStandardOutputCapture, getTaskActions, getTaskIdentity, getTemporaryDirFactory, hasTaskActions, injectIntoNewInstance, isCompatibleWithConfigurationCache, isEnabled, isHasCustomActions, notCompatibleWithConfigurationCache, prependParallelSafeAction, setImpliesSubProjects</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>clone, equals, finalize, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.org.gradle.api.Task">
<!--   -->
</a>
<h3>Methods inherited from interface&nbsp;org.gradle.api.<a href="../Task.html" title="interface in org.gradle.api">Task</a></h3>
<code><a href="../Task.html#doNotTrackState-java.lang.String-">doNotTrackState</a>, <a href="../Task.html#getConvention--">getConvention</a>, <a href="../Task.html#notCompatibleWithConfigurationCache-java.lang.String-">notCompatibleWithConfigurationCache</a></code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a name="GradleBuild--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>GradleBuild</h4>
<pre>public&nbsp;GradleBuild()</pre>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="getStartParameter--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getStartParameter</h4>
<pre class="methodSignature"><a href="Internal.html" title="annotation in org.gradle.api.tasks">@Internal</a>
public&nbsp;<a href="../../StartParameter.html" title="class in org.gradle">StartParameter</a>&nbsp;getStartParameter()</pre>
<div class="block">Returns the full set of parameters that will be used to execute the build.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the parameters. Never returns null.</dd>
</dl>
</li>
</ul>
<a name="setStartParameter-org.gradle.StartParameter-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setStartParameter</h4>
<pre class="methodSignature">public&nbsp;void&nbsp;setStartParameter&#8203;(<a href="../../StartParameter.html" title="class in org.gradle">StartParameter</a>&nbsp;startParameter)</pre>
<div class="block">Sets the full set of parameters that will be used to execute the build.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>startParameter</code> - the parameters. Should not be null.</dd>
</dl>
</li>
</ul>
<a name="getDir--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getDir</h4>
<pre class="methodSignature"><a href="Internal.html" title="annotation in org.gradle.api.tasks">@Internal</a>
public&nbsp;java.io.File&nbsp;getDir()</pre>
<div class="block">Returns the project directory for the build. Defaults to the project directory.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The project directory. Never returns null.</dd>
</dl>
</li>
</ul>
<a name="setDir-java.io.File-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setDir</h4>
<pre class="methodSignature">public&nbsp;void&nbsp;setDir&#8203;(java.io.File&nbsp;dir)</pre>
<div class="block">Sets the project directory for the build.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>dir</code> - The project directory. Should not be null.</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>4.0</dd>
</dl>
</li>
</ul>
<a name="setDir-java.lang.Object-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setDir</h4>
<pre class="methodSignature">public&nbsp;void&nbsp;setDir&#8203;(java.lang.Object&nbsp;dir)</pre>
<div class="block">Sets the project directory for the build.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>dir</code> - The project directory. Should not be null.</dd>
</dl>
</li>
</ul>
<a name="getBuildFile--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getBuildFile</h4>
<pre class="methodSignature">@Nullable
<a href="Optional.html" title="annotation in org.gradle.api.tasks">@Optional</a>
<a href="PathSensitive.html" title="annotation in org.gradle.api.tasks">@PathSensitive</a>(<a href="PathSensitivity.html#NAME_ONLY">NAME_ONLY</a>)
<a href="InputFile.html" title="annotation in org.gradle.api.tasks">@InputFile</a>
@Deprecated
public&nbsp;java.io.File&nbsp;getBuildFile()</pre>
<div class="deprecationBlock"><span class="deprecatedLabel">Deprecated.</span>
<div class="deprecationComment">Use <a href="#getDir--"><code>getDir()</code></a> instead to get the root of the nested build.
 This method will be removed in Gradle 9.0.</div>
</div>
<div class="block">Returns the build file that should be used for this build. Defaults to <a href="../Project.html#DEFAULT_BUILD_FILE">"build.gradle"</a> in the project directory.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The build file. May be null.</dd>
</dl>
</li>
</ul>
<a name="setBuildFile-java.io.File-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setBuildFile</h4>
<pre class="methodSignature">@Deprecated
public&nbsp;void&nbsp;setBuildFile&#8203;(@Nullable
                         java.io.File&nbsp;file)</pre>
<div class="deprecationBlock"><span class="deprecatedLabel">Deprecated.</span>
<div class="deprecationComment">Use <a href="#setDir-java.io.File-"><code>setDir(File)</code></a> instead to set the root of the nested build.
 This method will be removed in Gradle 9.0.</div>
</div>
<div class="block">Sets the build file that should be used for this build.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>file</code> - The build file. May be null to use the default build file for the build.</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>4.0</dd>
</dl>
</li>
</ul>
<a name="setBuildFile-java.lang.Object-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setBuildFile</h4>
<pre class="methodSignature">@Deprecated
public&nbsp;void&nbsp;setBuildFile&#8203;(@Nullable
                         java.lang.Object&nbsp;file)</pre>
<div class="deprecationBlock"><span class="deprecatedLabel">Deprecated.</span>
<div class="deprecationComment">Use <a href="#setDir-java.lang.Object-"><code>setDir(Object)</code></a> instead to set the root of the nested build.
 This method will be removed in Gradle 9.0.</div>
</div>
<div class="block">Sets the build file that should be used for this build.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>file</code> - The build file. May be null to use the default build file for the build.</dd>
</dl>
</li>
</ul>
<a name="getTasks--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getTasks</h4>
<pre class="methodSignature"><a href="Input.html" title="annotation in org.gradle.api.tasks">@Input</a>
public&nbsp;java.util.List&lt;java.lang.String&gt;&nbsp;getTasks()</pre>
<div class="block">Returns the tasks that should be executed for this build.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The sequence. May be empty. Never returns null.</dd>
</dl>
</li>
</ul>
<a name="setTasks-java.util.List-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setTasks</h4>
<pre class="methodSignature">public&nbsp;void&nbsp;setTasks&#8203;(java.util.List&lt;java.lang.String&gt;&nbsp;tasks)</pre>
<div class="block">Sets the tasks that should be executed for this build.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>tasks</code> - The task names. May be empty or null to use the default tasks for the build.</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>4.0</dd>
</dl>
</li>
</ul>
<a name="setTasks-java.util.Collection-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setTasks</h4>
<pre class="methodSignature">public&nbsp;void&nbsp;setTasks&#8203;(java.util.Collection&lt;java.lang.String&gt;&nbsp;tasks)</pre>
<div class="block">Sets the tasks that should be executed for this build.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>tasks</code> - The task names. May be empty or null to use the default tasks for the build.</dd>
</dl>
</li>
</ul>
<a name="getBuildName--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getBuildName</h4>
<pre class="methodSignature"><a href="Internal.html" title="annotation in org.gradle.api.tasks">@Internal</a>
public&nbsp;java.lang.String&nbsp;getBuildName()</pre>
<div class="block">The build name to use for the nested build.
 <p>
 If no value is specified, the name of the directory of the build will be used.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the build name to use for the nested build (or null if the default is to be used)</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>6.0</dd>
</dl>
</li>
</ul>
<a name="setBuildName-java.lang.String-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>setBuildName</h4>
<pre class="methodSignature">public&nbsp;void&nbsp;setBuildName&#8203;(java.lang.String&nbsp;buildName)</pre>
<div class="block">Sets build name to use for the nested build.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>buildName</code> - the build name to use for the nested build</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>6.0</dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li><a href="#nested.class.summary">Nested</a>&nbsp;|&nbsp;</li>
<li><a href="#field.summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
