<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc -->
<title>JUnitPlatformOptions (Gradle API 8.4)</title>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<link rel="stylesheet" type="text/css" href="../../../../../../javadoc.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../../jquery/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../../jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="../../../../../../script.js"></script>
<script type="text/javascript" src="../../../../../../jquery/jszip/dist/jszip.min.js"></script>
<script type="text/javascript" src="../../../../../../jquery/jszip-utils/dist/jszip-utils.min.js"></script>
<!--[if IE]>
<script type="text/javascript" src="../../../../../../jquery/jszip-utils/dist/jszip-utils-ie.min.js"></script>
<![endif]-->
<script type="text/javascript" src="../../../../../../jquery/jquery-3.6.0.min.js"></script>
<script type="text/javascript" src="../../../../../../jquery/jquery-ui.min.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="JUnitPlatformOptions (Gradle API 8.4)";
        }
    }
    catch(err) {
    }
//-->
var data = {"i0":10,"i1":10,"i2":10,"i3":10,"i4":10,"i5":10,"i6":10,"i7":10,"i8":10,"i9":10,"i10":10,"i11":10,"i12":10};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
var pathtoroot = "../../../../../../";
var useModuleDirectories = false;
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="fixedNav">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../../../allclasses.html">All&nbsp;Classes</a></li>
</ul>
<ul class="navListSearch">
<li><label for="search">SEARCH:</label>
<input type="text" id="search" value="search" disabled="disabled">
<input type="reset" id="reset" value="reset" disabled="disabled">
</li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
</div>
<div class="navPadding">&nbsp;</div>
<script type="text/javascript"><!--
$('.navPadding').css('padding-top', $('.fixedNav').css("height"));
//-->
</script>
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle"><span class="packageLabelInType">Package</span>&nbsp;<a href="package-summary.html">org.gradle.api.tasks.testing.junitplatform</a></div>
<h2 title="Class JUnitPlatformOptions" class="title">Class JUnitPlatformOptions</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li><a href="../TestFrameworkOptions.html" title="class in org.gradle.api.tasks.testing">org.gradle.api.tasks.testing.TestFrameworkOptions</a></li>
<li>
<ul class="inheritance">
<li>org.gradle.api.tasks.testing.junitplatform.JUnitPlatformOptions</li>
</ul>
</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<hr>
<pre>public class <span class="typeNameLabel">JUnitPlatformOptions</span>
extends <a href="../TestFrameworkOptions.html" title="class in org.gradle.api.tasks.testing">TestFrameworkOptions</a></pre>
<div class="block">The JUnit platform specific test options.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>4.6</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="https://junit.org/junit5/docs/current/user-guide">JUnit 5 User Guide</a></dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="memberSummary" summary="Constructor Summary table, listing constructors, and an explanation">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Constructor</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tr class="altColor">
<th class="colConstructorName" scope="row"><code><span class="memberNameLink"><a href="#JUnitPlatformOptions--">JUnitPlatformOptions</a></span>()</code></th>
<td class="colLast">&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colSecond" scope="col">Method</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#copyFrom-org.gradle.api.tasks.testing.junitplatform.JUnitPlatformOptions-">copyFrom</a></span>&#8203;(<a href="JUnitPlatformOptions.html" title="class in org.gradle.api.tasks.testing.junitplatform">JUnitPlatformOptions</a>&nbsp;other)</code></th>
<td class="colLast">
<div class="block">Copies the options from the source options into the current one.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code><a href="JUnitPlatformOptions.html" title="class in org.gradle.api.tasks.testing.junitplatform">JUnitPlatformOptions</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#excludeEngines-java.lang.String...-">excludeEngines</a></span>&#8203;(java.lang.String...&nbsp;excludeEngines)</code></th>
<td class="colLast">
<div class="block">The set of engines to exclude.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code><a href="JUnitPlatformOptions.html" title="class in org.gradle.api.tasks.testing.junitplatform">JUnitPlatformOptions</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#excludeTags-java.lang.String...-">excludeTags</a></span>&#8203;(java.lang.String...&nbsp;excludeTags)</code></th>
<td class="colLast">
<div class="block">The set of tags to exclude.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>java.util.Set&lt;java.lang.String&gt;</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getExcludeEngines--">getExcludeEngines</a></span>()</code></th>
<td class="colLast">&nbsp;</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>java.util.Set&lt;java.lang.String&gt;</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getExcludeTags--">getExcludeTags</a></span>()</code></th>
<td class="colLast">&nbsp;</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>java.util.Set&lt;java.lang.String&gt;</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getIncludeEngines--">getIncludeEngines</a></span>()</code></th>
<td class="colLast">&nbsp;</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>java.util.Set&lt;java.lang.String&gt;</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getIncludeTags--">getIncludeTags</a></span>()</code></th>
<td class="colLast">&nbsp;</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code><a href="JUnitPlatformOptions.html" title="class in org.gradle.api.tasks.testing.junitplatform">JUnitPlatformOptions</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#includeEngines-java.lang.String...-">includeEngines</a></span>&#8203;(java.lang.String...&nbsp;includeEngines)</code></th>
<td class="colLast">
<div class="block">The set of engines to run with.</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code><a href="JUnitPlatformOptions.html" title="class in org.gradle.api.tasks.testing.junitplatform">JUnitPlatformOptions</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#includeTags-java.lang.String...-">includeTags</a></span>&#8203;(java.lang.String...&nbsp;includeTags)</code></th>
<td class="colLast">
<div class="block">The set of tags to run with.</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setExcludeEngines-java.util.Set-">setExcludeEngines</a></span>&#8203;(java.util.Set&lt;java.lang.String&gt;&nbsp;excludeEngines)</code></th>
<td class="colLast">&nbsp;</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setExcludeTags-java.util.Set-">setExcludeTags</a></span>&#8203;(java.util.Set&lt;java.lang.String&gt;&nbsp;excludeTags)</code></th>
<td class="colLast">&nbsp;</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setIncludeEngines-java.util.Set-">setIncludeEngines</a></span>&#8203;(java.util.Set&lt;java.lang.String&gt;&nbsp;includeEngines)</code></th>
<td class="colLast">&nbsp;</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setIncludeTags-java.util.Set-">setIncludeTags</a></span>&#8203;(java.util.Set&lt;java.lang.String&gt;&nbsp;includeTags)</code></th>
<td class="colLast">&nbsp;</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>clone, equals, finalize, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a name="JUnitPlatformOptions--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>JUnitPlatformOptions</h4>
<pre>public&nbsp;JUnitPlatformOptions()</pre>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="copyFrom-org.gradle.api.tasks.testing.junitplatform.JUnitPlatformOptions-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>copyFrom</h4>
<pre class="methodSignature">public&nbsp;void&nbsp;copyFrom&#8203;(<a href="JUnitPlatformOptions.html" title="class in org.gradle.api.tasks.testing.junitplatform">JUnitPlatformOptions</a>&nbsp;other)</pre>
<div class="block">Copies the options from the source options into the current one.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>8.0</dd>
</dl>
</li>
</ul>
<a name="includeEngines-java.lang.String...-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>includeEngines</h4>
<pre class="methodSignature">public&nbsp;<a href="JUnitPlatformOptions.html" title="class in org.gradle.api.tasks.testing.junitplatform">JUnitPlatformOptions</a>&nbsp;includeEngines&#8203;(java.lang.String...&nbsp;includeEngines)</pre>
<div class="block">The set of engines to run with.</div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="https://junit.org/junit5/docs/current/user-guide/#launcher-api-engines-custom">Test Engine</a></dd>
</dl>
</li>
</ul>
<a name="includeTags-java.lang.String...-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>includeTags</h4>
<pre class="methodSignature">public&nbsp;<a href="JUnitPlatformOptions.html" title="class in org.gradle.api.tasks.testing.junitplatform">JUnitPlatformOptions</a>&nbsp;includeTags&#8203;(java.lang.String...&nbsp;includeTags)</pre>
<div class="block">The set of tags to run with.</div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="https://junit.org/junit5/docs/current/user-guide/#writing-tests-tagging-and-filtering">Tagging and Filtering</a></dd>
</dl>
</li>
</ul>
<a name="excludeEngines-java.lang.String...-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>excludeEngines</h4>
<pre class="methodSignature">public&nbsp;<a href="JUnitPlatformOptions.html" title="class in org.gradle.api.tasks.testing.junitplatform">JUnitPlatformOptions</a>&nbsp;excludeEngines&#8203;(java.lang.String...&nbsp;excludeEngines)</pre>
<div class="block">The set of engines to exclude.</div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="https://junit.org/junit5/docs/current/user-guide/#launcher-api-engines-custom">Test Engine</a></dd>
</dl>
</li>
</ul>
<a name="excludeTags-java.lang.String...-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>excludeTags</h4>
<pre class="methodSignature">public&nbsp;<a href="JUnitPlatformOptions.html" title="class in org.gradle.api.tasks.testing.junitplatform">JUnitPlatformOptions</a>&nbsp;excludeTags&#8203;(java.lang.String...&nbsp;excludeTags)</pre>
<div class="block">The set of tags to exclude.</div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="https://junit.org/junit5/docs/current/user-guide/#writing-tests-tagging-and-filtering">Tagging and Filtering</a></dd>
</dl>
</li>
</ul>
<a name="getIncludeEngines--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getIncludeEngines</h4>
<pre class="methodSignature"><a href="../../Input.html" title="annotation in org.gradle.api.tasks">@Input</a>
public&nbsp;java.util.Set&lt;java.lang.String&gt;&nbsp;getIncludeEngines()</pre>
</li>
</ul>
<a name="getIncludeTags--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getIncludeTags</h4>
<pre class="methodSignature"><a href="../../Input.html" title="annotation in org.gradle.api.tasks">@Input</a>
public&nbsp;java.util.Set&lt;java.lang.String&gt;&nbsp;getIncludeTags()</pre>
</li>
</ul>
<a name="setIncludeEngines-java.util.Set-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setIncludeEngines</h4>
<pre class="methodSignature">public&nbsp;void&nbsp;setIncludeEngines&#8203;(java.util.Set&lt;java.lang.String&gt;&nbsp;includeEngines)</pre>
</li>
</ul>
<a name="getExcludeEngines--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getExcludeEngines</h4>
<pre class="methodSignature"><a href="../../Input.html" title="annotation in org.gradle.api.tasks">@Input</a>
public&nbsp;java.util.Set&lt;java.lang.String&gt;&nbsp;getExcludeEngines()</pre>
</li>
</ul>
<a name="setExcludeEngines-java.util.Set-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setExcludeEngines</h4>
<pre class="methodSignature">public&nbsp;void&nbsp;setExcludeEngines&#8203;(java.util.Set&lt;java.lang.String&gt;&nbsp;excludeEngines)</pre>
</li>
</ul>
<a name="setIncludeTags-java.util.Set-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setIncludeTags</h4>
<pre class="methodSignature">public&nbsp;void&nbsp;setIncludeTags&#8203;(java.util.Set&lt;java.lang.String&gt;&nbsp;includeTags)</pre>
</li>
</ul>
<a name="getExcludeTags--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getExcludeTags</h4>
<pre class="methodSignature"><a href="../../Input.html" title="annotation in org.gradle.api.tasks">@Input</a>
public&nbsp;java.util.Set&lt;java.lang.String&gt;&nbsp;getExcludeTags()</pre>
</li>
</ul>
<a name="setExcludeTags-java.util.Set-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>setExcludeTags</h4>
<pre class="methodSignature">public&nbsp;void&nbsp;setExcludeTags&#8203;(java.util.Set&lt;java.lang.String&gt;&nbsp;excludeTags)</pre>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../../../allclasses.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
