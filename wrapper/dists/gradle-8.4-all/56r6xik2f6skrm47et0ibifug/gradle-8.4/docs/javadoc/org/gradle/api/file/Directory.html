<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc -->
<title>Directory (Gradle API 8.4)</title>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<link rel="stylesheet" type="text/css" href="../../../../javadoc.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../jquery/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
<script type="text/javascript" src="../../../../jquery/jszip/dist/jszip.min.js"></script>
<script type="text/javascript" src="../../../../jquery/jszip-utils/dist/jszip-utils.min.js"></script>
<!--[if IE]>
<script type="text/javascript" src="../../../../jquery/jszip-utils/dist/jszip-utils-ie.min.js"></script>
<![endif]-->
<script type="text/javascript" src="../../../../jquery/jquery-3.6.0.min.js"></script>
<script type="text/javascript" src="../../../../jquery/jquery-ui.min.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="Directory (Gradle API 8.4)";
        }
    }
    catch(err) {
    }
//-->
var data = {"i0":6,"i1":6,"i2":6,"i3":6,"i4":6,"i5":6,"i6":6};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],4:["t3","Abstract Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
var pathtoroot = "../../../../";
var useModuleDirectories = false;
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="fixedNav">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses.html">All&nbsp;Classes</a></li>
</ul>
<ul class="navListSearch">
<li><label for="search">SEARCH:</label>
<input type="text" id="search" value="search" disabled="disabled">
<input type="reset" id="reset" value="reset" disabled="disabled">
</li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
</div>
<div class="navPadding">&nbsp;</div>
<script type="text/javascript"><!--
$('.navPadding').css('padding-top', $('.fixedNav').css("height"));
//-->
</script>
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle"><span class="packageLabelInType">Package</span>&nbsp;<a href="package-summary.html">org.gradle.api.file</a></div>
<h2 title="Interface Directory" class="title">Interface Directory</h2>
</div>
<div class="contentContainer">
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Superinterfaces:</dt>
<dd><code><a href="FileSystemLocation.html" title="interface in org.gradle.api.file">FileSystemLocation</a></code></dd>
</dl>
<hr>
<pre>public interface <span class="typeNameLabel">Directory</span>
extends <a href="FileSystemLocation.html" title="interface in org.gradle.api.file">FileSystemLocation</a></pre>
<div class="block">Represents a directory at some fixed location on the file system.
 <p>
 <b>Note:</b> This interface is not intended for implementation by build script or plugin authors. An instance of this class can be created
 using the <a href="#dir-java.lang.String-"><code>dir(String)</code></a> method or using various methods on <a href="ProjectLayout.html" title="interface in org.gradle.api.file"><code>ProjectLayout</code></a> such as <a href="ProjectLayout.html#getProjectDirectory--"><code>ProjectLayout.getProjectDirectory()</code></a>.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>4.1</dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t3" class="tableTab"><span><a href="javascript:show(4);">Abstract Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colSecond" scope="col">Method</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code><a href="Directory.html" title="interface in org.gradle.api.file">Directory</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#dir-java.lang.String-">dir</a></span>&#8203;(java.lang.String&nbsp;path)</code></th>
<td class="colLast">
<div class="block">Returns a <a href="Directory.html" title="interface in org.gradle.api.file"><code>Directory</code></a> whose location is the given path, resolved relative to this directory.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code><a href="../provider/Provider.html" title="interface in org.gradle.api.provider">Provider</a>&lt;<a href="Directory.html" title="interface in org.gradle.api.file">Directory</a>&gt;</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#dir-org.gradle.api.provider.Provider-">dir</a></span>&#8203;(<a href="../provider/Provider.html" title="interface in org.gradle.api.provider">Provider</a>&lt;? extends java.lang.CharSequence&gt;&nbsp;path)</code></th>
<td class="colLast">
<div class="block">Returns a <a href="../provider/Provider.html" title="interface in org.gradle.api.provider"><code>Provider</code></a> whose value is a <a href="Directory.html" title="interface in org.gradle.api.file"><code>Directory</code></a> whose location is the given path resolved relative to this directory.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code><a href="RegularFile.html" title="interface in org.gradle.api.file">RegularFile</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#file-java.lang.String-">file</a></span>&#8203;(java.lang.String&nbsp;path)</code></th>
<td class="colLast">
<div class="block">Returns a <a href="RegularFile.html" title="interface in org.gradle.api.file"><code>RegularFile</code></a> whose location is the given path, resolved relative to this directory.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code><a href="../provider/Provider.html" title="interface in org.gradle.api.provider">Provider</a>&lt;<a href="RegularFile.html" title="interface in org.gradle.api.file">RegularFile</a>&gt;</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#file-org.gradle.api.provider.Provider-">file</a></span>&#8203;(<a href="../provider/Provider.html" title="interface in org.gradle.api.provider">Provider</a>&lt;? extends java.lang.CharSequence&gt;&nbsp;path)</code></th>
<td class="colLast">
<div class="block">Returns a <a href="../provider/Provider.html" title="interface in org.gradle.api.provider"><code>Provider</code></a> whose value is a <a href="RegularFile.html" title="interface in org.gradle.api.file"><code>RegularFile</code></a> whose location is the given path resolved relative to this directory.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code><a href="FileCollection.html" title="interface in org.gradle.api.file">FileCollection</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#files-java.lang.Object...-">files</a></span>&#8203;(java.lang.Object...&nbsp;paths)</code></th>
<td class="colLast">
<div class="block">Returns a <a href="FileCollection.html" title="interface in org.gradle.api.file"><code>FileCollection</code></a> containing the given files,
 whose locations are the given paths resolved relative to this directory,
 as defined by <a href="../Project.html#files-java.lang.Object...-"><code>Project.files(Object...)</code></a>.</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>java.io.File</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getAsFile--">getAsFile</a></span>()</code></th>
<td class="colLast">
<div class="block">Returns the location of this directory, as an absolute <code>File</code>.</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code><a href="FileTree.html" title="interface in org.gradle.api.file">FileTree</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getAsFileTree--">getAsFileTree</a></span>()</code></th>
<td class="colLast">
<div class="block">Returns a <a href="FileTree.html" title="interface in org.gradle.api.file"><code>FileTree</code></a> that allows the files and directories contained in this directory to be queried.</div>
</td>
</tr>
</table>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="getAsFile--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getAsFile</h4>
<pre class="methodSignature">java.io.File&nbsp;getAsFile()</pre>
<div class="block">Returns the location of this directory, as an absolute <code>File</code>.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="FileSystemLocation.html#getAsFile--">getAsFile</a></code>&nbsp;in interface&nbsp;<code><a href="FileSystemLocation.html" title="interface in org.gradle.api.file">FileSystemLocation</a></code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the File</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>4.2</dd>
</dl>
</li>
</ul>
<a name="getAsFileTree--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getAsFileTree</h4>
<pre class="methodSignature"><a href="FileTree.html" title="interface in org.gradle.api.file">FileTree</a>&nbsp;getAsFileTree()</pre>
<div class="block">Returns a <a href="FileTree.html" title="interface in org.gradle.api.file"><code>FileTree</code></a> that allows the files and directories contained in this directory to be queried.</div>
</li>
</ul>
<a name="dir-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>dir</h4>
<pre class="methodSignature"><a href="Directory.html" title="interface in org.gradle.api.file">Directory</a>&nbsp;dir&#8203;(java.lang.String&nbsp;path)</pre>
<div class="block">Returns a <a href="Directory.html" title="interface in org.gradle.api.file"><code>Directory</code></a> whose location is the given path, resolved relative to this directory.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>path</code> - The path. Can be absolute.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The directory.</dd>
</dl>
</li>
</ul>
<a name="dir-org.gradle.api.provider.Provider-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>dir</h4>
<pre class="methodSignature"><a href="../provider/Provider.html" title="interface in org.gradle.api.provider">Provider</a>&lt;<a href="Directory.html" title="interface in org.gradle.api.file">Directory</a>&gt;&nbsp;dir&#8203;(<a href="../provider/Provider.html" title="interface in org.gradle.api.provider">Provider</a>&lt;? extends java.lang.CharSequence&gt;&nbsp;path)</pre>
<div class="block">Returns a <a href="../provider/Provider.html" title="interface in org.gradle.api.provider"><code>Provider</code></a> whose value is a <a href="Directory.html" title="interface in org.gradle.api.file"><code>Directory</code></a> whose location is the given path resolved relative to this directory.

 <p>The return value is live and the provided <code>path</code> is queried each time the return value is queried.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>path</code> - The path provider. Can have value that is an absolute path.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The provider.</dd>
</dl>
</li>
</ul>
<a name="file-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>file</h4>
<pre class="methodSignature"><a href="RegularFile.html" title="interface in org.gradle.api.file">RegularFile</a>&nbsp;file&#8203;(java.lang.String&nbsp;path)</pre>
<div class="block">Returns a <a href="RegularFile.html" title="interface in org.gradle.api.file"><code>RegularFile</code></a> whose location is the given path, resolved relative to this directory.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>path</code> - The path. Can be absolute.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The file.</dd>
</dl>
</li>
</ul>
<a name="file-org.gradle.api.provider.Provider-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>file</h4>
<pre class="methodSignature"><a href="../provider/Provider.html" title="interface in org.gradle.api.provider">Provider</a>&lt;<a href="RegularFile.html" title="interface in org.gradle.api.file">RegularFile</a>&gt;&nbsp;file&#8203;(<a href="../provider/Provider.html" title="interface in org.gradle.api.provider">Provider</a>&lt;? extends java.lang.CharSequence&gt;&nbsp;path)</pre>
<div class="block">Returns a <a href="../provider/Provider.html" title="interface in org.gradle.api.provider"><code>Provider</code></a> whose value is a <a href="RegularFile.html" title="interface in org.gradle.api.file"><code>RegularFile</code></a> whose location is the given path resolved relative to this directory.

 <p>The return value is live and the provided <code>path</code> is queried each time the return value is queried.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>path</code> - The path provider. Can have value that is an absolute path.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The file.</dd>
</dl>
</li>
</ul>
<a name="files-java.lang.Object...-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>files</h4>
<pre class="methodSignature"><a href="FileCollection.html" title="interface in org.gradle.api.file">FileCollection</a>&nbsp;files&#8203;(java.lang.Object...&nbsp;paths)</pre>
<div class="block">Returns a <a href="FileCollection.html" title="interface in org.gradle.api.file"><code>FileCollection</code></a> containing the given files,
 whose locations are the given paths resolved relative to this directory,
 as defined by <a href="../Project.html#files-java.lang.Object...-"><code>Project.files(Object...)</code></a>.

 This method can also be used to create an empty collection, but the collection may not be mutated later.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>paths</code> - The paths to the files. May be empty.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The file collection.</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>6.0</dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
