<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc -->
<title>RepositoryHandler (Gradle API 8.4)</title>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<link rel="stylesheet" type="text/css" href="../../../../../javadoc.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../jquery/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
<script type="text/javascript" src="../../../../../jquery/jszip/dist/jszip.min.js"></script>
<script type="text/javascript" src="../../../../../jquery/jszip-utils/dist/jszip-utils.min.js"></script>
<!--[if IE]>
<script type="text/javascript" src="../../../../../jquery/jszip-utils/dist/jszip-utils-ie.min.js"></script>
<![endif]-->
<script type="text/javascript" src="../../../../../jquery/jquery-3.6.0.min.js"></script>
<script type="text/javascript" src="../../../../../jquery/jquery-ui.min.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="RepositoryHandler (Gradle API 8.4)";
        }
    }
    catch(err) {
    }
//-->
var data = {"i0":6,"i1":6,"i2":6,"i3":6,"i4":6,"i5":6,"i6":6,"i7":6,"i8":6,"i9":6,"i10":38,"i11":38,"i12":6,"i13":6,"i14":6,"i15":6,"i16":6,"i17":6,"i18":6};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],4:["t3","Abstract Methods"],32:["t6","Deprecated Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
var pathtoroot = "../../../../../";
var useModuleDirectories = false;
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="fixedNav">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../../allclasses.html">All&nbsp;Classes</a></li>
</ul>
<ul class="navListSearch">
<li><label for="search">SEARCH:</label>
<input type="text" id="search" value="search" disabled="disabled">
<input type="reset" id="reset" value="reset" disabled="disabled">
</li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field.summary">Field</a>&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
</div>
<div class="navPadding">&nbsp;</div>
<script type="text/javascript"><!--
$('.navPadding').css('padding-top', $('.fixedNav').css("height"));
//-->
</script>
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle"><span class="packageLabelInType">Package</span>&nbsp;<a href="package-summary.html">org.gradle.api.artifacts.dsl</a></div>
<h2 title="Interface RepositoryHandler" class="title">Interface RepositoryHandler</h2>
</div>
<div class="contentContainer">
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Superinterfaces:</dt>
<dd><code><a href="../ArtifactRepositoryContainer.html" title="interface in org.gradle.api.artifacts">ArtifactRepositoryContainer</a></code>, <code>java.util.Collection&lt;<a href="../repositories/ArtifactRepository.html" title="interface in org.gradle.api.artifacts.repositories">ArtifactRepository</a>&gt;</code>, <code><a href="../../../util/Configurable.html" title="interface in org.gradle.util">Configurable</a>&lt;<a href="../ArtifactRepositoryContainer.html" title="interface in org.gradle.api.artifacts">ArtifactRepositoryContainer</a>&gt;</code>, <code><a href="../../DomainObjectCollection.html" title="interface in org.gradle.api">DomainObjectCollection</a>&lt;<a href="../repositories/ArtifactRepository.html" title="interface in org.gradle.api.artifacts.repositories">ArtifactRepository</a>&gt;</code>, <code>java.lang.Iterable&lt;<a href="../repositories/ArtifactRepository.html" title="interface in org.gradle.api.artifacts.repositories">ArtifactRepository</a>&gt;</code>, <code>java.util.List&lt;<a href="../repositories/ArtifactRepository.html" title="interface in org.gradle.api.artifacts.repositories">ArtifactRepository</a>&gt;</code>, <code><a href="../../NamedDomainObjectCollection.html" title="interface in org.gradle.api">NamedDomainObjectCollection</a>&lt;<a href="../repositories/ArtifactRepository.html" title="interface in org.gradle.api.artifacts.repositories">ArtifactRepository</a>&gt;</code>, <code><a href="../../NamedDomainObjectList.html" title="interface in org.gradle.api">NamedDomainObjectList</a>&lt;<a href="../repositories/ArtifactRepository.html" title="interface in org.gradle.api.artifacts.repositories">ArtifactRepository</a>&gt;</code></dd>
</dl>
<hr>
<pre>public interface <span class="typeNameLabel">RepositoryHandler</span>
extends <a href="../ArtifactRepositoryContainer.html" title="interface in org.gradle.api.artifacts">ArtifactRepositoryContainer</a></pre>
<div class="block">A <code>RepositoryHandler</code> manages a set of repositories, allowing repositories to be defined and queried.</div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- =========== FIELD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.summary">
<!--   -->
</a>
<h3>Field Summary</h3>
<ul class="blockList">
<li class="blockList"><a name="fields.inherited.from.class.org.gradle.api.artifacts.ArtifactRepositoryContainer">
<!--   -->
</a>
<h3>Fields inherited from interface&nbsp;org.gradle.api.artifacts.<a href="../ArtifactRepositoryContainer.html" title="interface in org.gradle.api.artifacts">ArtifactRepositoryContainer</a></h3>
<code><a href="../ArtifactRepositoryContainer.html#DEFAULT_MAVEN_CENTRAL_REPO_NAME">DEFAULT_MAVEN_CENTRAL_REPO_NAME</a>, <a href="../ArtifactRepositoryContainer.html#DEFAULT_MAVEN_LOCAL_REPO_NAME">DEFAULT_MAVEN_LOCAL_REPO_NAME</a>, <a href="../ArtifactRepositoryContainer.html#GOOGLE_URL">GOOGLE_URL</a>, <a href="../ArtifactRepositoryContainer.html#MAVEN_CENTRAL_URL">MAVEN_CENTRAL_URL</a></code></li>
</ul>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t3" class="tableTab"><span><a href="javascript:show(4);">Abstract Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t6" class="tableTab"><span><a href="javascript:show(32);">Deprecated Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colSecond" scope="col">Method</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#exclusiveContent-org.gradle.api.Action-">exclusiveContent</a></span>&#8203;(<a href="../../Action.html" title="interface in org.gradle.api">Action</a>&lt;? super <a href="../repositories/ExclusiveContentRepository.html" title="interface in org.gradle.api.artifacts.repositories">ExclusiveContentRepository</a>&gt;&nbsp;action)</code></th>
<td class="colLast">
<div class="block">Declares exclusive content repositories.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code><a href="../repositories/FlatDirectoryArtifactRepository.html" title="interface in org.gradle.api.artifacts.repositories">FlatDirectoryArtifactRepository</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#flatDir-groovy.lang.Closure-">flatDir</a></span>&#8203;(<a href="https://docs.groovy-lang.org/docs/groovy-3.0.17/html/gapi/groovy/lang/Closure.html?is-external=true" title="class or interface in groovy.lang" class="externalLink">Closure</a>&nbsp;configureClosure)</code></th>
<td class="colLast">
<div class="block">Adds and configures a repository which will look for dependencies in a number of local directories.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code><a href="../repositories/FlatDirectoryArtifactRepository.html" title="interface in org.gradle.api.artifacts.repositories">FlatDirectoryArtifactRepository</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#flatDir-java.util.Map-">flatDir</a></span>&#8203;(java.util.Map&lt;java.lang.String,&#8203;?&gt;&nbsp;args)</code></th>
<td class="colLast">
<div class="block">Adds a resolver that looks into a number of directories for artifacts.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code><a href="../repositories/FlatDirectoryArtifactRepository.html" title="interface in org.gradle.api.artifacts.repositories">FlatDirectoryArtifactRepository</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#flatDir-org.gradle.api.Action-">flatDir</a></span>&#8203;(<a href="../../Action.html" title="interface in org.gradle.api">Action</a>&lt;? super <a href="../repositories/FlatDirectoryArtifactRepository.html" title="interface in org.gradle.api.artifacts.repositories">FlatDirectoryArtifactRepository</a>&gt;&nbsp;action)</code></th>
<td class="colLast">
<div class="block">Adds and configures a repository which will look for dependencies in a number of local directories.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code><a href="../repositories/MavenArtifactRepository.html" title="interface in org.gradle.api.artifacts.repositories">MavenArtifactRepository</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#google--">google</a></span>()</code></th>
<td class="colLast">
<div class="block">Adds a repository which looks in Google's Maven repository for dependencies.</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code><a href="../repositories/MavenArtifactRepository.html" title="interface in org.gradle.api.artifacts.repositories">MavenArtifactRepository</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#google-org.gradle.api.Action-">google</a></span>&#8203;(<a href="../../Action.html" title="interface in org.gradle.api">Action</a>&lt;? super <a href="../repositories/MavenArtifactRepository.html" title="interface in org.gradle.api.artifacts.repositories">MavenArtifactRepository</a>&gt;&nbsp;action)</code></th>
<td class="colLast">
<div class="block">Adds a repository which looks in Google's Maven repository for dependencies.</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code><a href="../repositories/ArtifactRepository.html" title="interface in org.gradle.api.artifacts.repositories">ArtifactRepository</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#gradlePluginPortal--">gradlePluginPortal</a></span>()</code></th>
<td class="colLast">
<div class="block">Adds a repository which looks in Gradle Central Plugin Repository for dependencies.</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code><a href="../repositories/ArtifactRepository.html" title="interface in org.gradle.api.artifacts.repositories">ArtifactRepository</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#gradlePluginPortal-org.gradle.api.Action-">gradlePluginPortal</a></span>&#8203;(<a href="../../Action.html" title="interface in org.gradle.api">Action</a>&lt;? super <a href="../repositories/ArtifactRepository.html" title="interface in org.gradle.api.artifacts.repositories">ArtifactRepository</a>&gt;&nbsp;action)</code></th>
<td class="colLast">
<div class="block">Adds a repository which looks in Gradle Central Plugin Repository for dependencies.</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code><a href="../repositories/IvyArtifactRepository.html" title="interface in org.gradle.api.artifacts.repositories">IvyArtifactRepository</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#ivy-groovy.lang.Closure-">ivy</a></span>&#8203;(<a href="https://docs.groovy-lang.org/docs/groovy-3.0.17/html/gapi/groovy/lang/Closure.html?is-external=true" title="class or interface in groovy.lang" class="externalLink">Closure</a>&nbsp;closure)</code></th>
<td class="colLast">
<div class="block">Adds and configures an Ivy repository.</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code><a href="../repositories/IvyArtifactRepository.html" title="interface in org.gradle.api.artifacts.repositories">IvyArtifactRepository</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#ivy-org.gradle.api.Action-">ivy</a></span>&#8203;(<a href="../../Action.html" title="interface in org.gradle.api">Action</a>&lt;? super <a href="../repositories/IvyArtifactRepository.html" title="interface in org.gradle.api.artifacts.repositories">IvyArtifactRepository</a>&gt;&nbsp;action)</code></th>
<td class="colLast">
<div class="block">Adds and configures an Ivy repository.</div>
</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code><a href="../repositories/MavenArtifactRepository.html" title="interface in org.gradle.api.artifacts.repositories">MavenArtifactRepository</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#jcenter--">jcenter</a></span>()</code></th>
<td class="colLast">
<div class="block"><span class="deprecatedLabel">Deprecated.</span>
<div class="deprecationComment">JFrog announced JCenter's <a href="https://blog.gradle.org/jcenter-shutdown">sunset</a> in February 2021.</div>
</div>
</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code><a href="../repositories/MavenArtifactRepository.html" title="interface in org.gradle.api.artifacts.repositories">MavenArtifactRepository</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#jcenter-org.gradle.api.Action-">jcenter</a></span>&#8203;(<a href="../../Action.html" title="interface in org.gradle.api">Action</a>&lt;? super <a href="../repositories/MavenArtifactRepository.html" title="interface in org.gradle.api.artifacts.repositories">MavenArtifactRepository</a>&gt;&nbsp;action)</code></th>
<td class="colLast">
<div class="block"><span class="deprecatedLabel">Deprecated.</span>
<div class="deprecationComment">JFrog announced JCenter's <a href="https://blog.gradle.org/jcenter-shutdown">sunset</a> in February 2021.</div>
</div>
</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code><a href="../repositories/MavenArtifactRepository.html" title="interface in org.gradle.api.artifacts.repositories">MavenArtifactRepository</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#maven-groovy.lang.Closure-">maven</a></span>&#8203;(<a href="https://docs.groovy-lang.org/docs/groovy-3.0.17/html/gapi/groovy/lang/Closure.html?is-external=true" title="class or interface in groovy.lang" class="externalLink">Closure</a>&nbsp;closure)</code></th>
<td class="colLast">
<div class="block">Adds and configures a Maven repository.</div>
</td>
</tr>
<tr id="i13" class="rowColor">
<td class="colFirst"><code><a href="../repositories/MavenArtifactRepository.html" title="interface in org.gradle.api.artifacts.repositories">MavenArtifactRepository</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#maven-org.gradle.api.Action-">maven</a></span>&#8203;(<a href="../../Action.html" title="interface in org.gradle.api">Action</a>&lt;? super <a href="../repositories/MavenArtifactRepository.html" title="interface in org.gradle.api.artifacts.repositories">MavenArtifactRepository</a>&gt;&nbsp;action)</code></th>
<td class="colLast">
<div class="block">Adds and configures a Maven repository.</div>
</td>
</tr>
<tr id="i14" class="altColor">
<td class="colFirst"><code><a href="../repositories/MavenArtifactRepository.html" title="interface in org.gradle.api.artifacts.repositories">MavenArtifactRepository</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#mavenCentral--">mavenCentral</a></span>()</code></th>
<td class="colLast">
<div class="block">Adds a repository which looks in the Maven central repository for dependencies.</div>
</td>
</tr>
<tr id="i15" class="rowColor">
<td class="colFirst"><code><a href="../repositories/MavenArtifactRepository.html" title="interface in org.gradle.api.artifacts.repositories">MavenArtifactRepository</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#mavenCentral-java.util.Map-">mavenCentral</a></span>&#8203;(java.util.Map&lt;java.lang.String,&#8203;?&gt;&nbsp;args)</code></th>
<td class="colLast">
<div class="block">Adds a repository which looks in the Maven central repository for dependencies.</div>
</td>
</tr>
<tr id="i16" class="altColor">
<td class="colFirst"><code><a href="../repositories/MavenArtifactRepository.html" title="interface in org.gradle.api.artifacts.repositories">MavenArtifactRepository</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#mavenCentral-org.gradle.api.Action-">mavenCentral</a></span>&#8203;(<a href="../../Action.html" title="interface in org.gradle.api">Action</a>&lt;? super <a href="../repositories/MavenArtifactRepository.html" title="interface in org.gradle.api.artifacts.repositories">MavenArtifactRepository</a>&gt;&nbsp;action)</code></th>
<td class="colLast">
<div class="block">Adds a repository which looks in the Maven central repository for dependencies.</div>
</td>
</tr>
<tr id="i17" class="rowColor">
<td class="colFirst"><code><a href="../repositories/MavenArtifactRepository.html" title="interface in org.gradle.api.artifacts.repositories">MavenArtifactRepository</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#mavenLocal--">mavenLocal</a></span>()</code></th>
<td class="colLast">
<div class="block">Adds a repository which looks in the local Maven cache for dependencies.</div>
</td>
</tr>
<tr id="i18" class="altColor">
<td class="colFirst"><code><a href="../repositories/MavenArtifactRepository.html" title="interface in org.gradle.api.artifacts.repositories">MavenArtifactRepository</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#mavenLocal-org.gradle.api.Action-">mavenLocal</a></span>&#8203;(<a href="../../Action.html" title="interface in org.gradle.api">Action</a>&lt;? super <a href="../repositories/MavenArtifactRepository.html" title="interface in org.gradle.api.artifacts.repositories">MavenArtifactRepository</a>&gt;&nbsp;action)</code></th>
<td class="colLast">
<div class="block">Adds a repository which looks in the local Maven cache for dependencies.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.org.gradle.api.artifacts.ArtifactRepositoryContainer">
<!--   -->
</a>
<h3>Methods inherited from interface&nbsp;org.gradle.api.artifacts.<a href="../ArtifactRepositoryContainer.html" title="interface in org.gradle.api.artifacts">ArtifactRepositoryContainer</a></h3>
<code><a href="../ArtifactRepositoryContainer.html#add-org.gradle.api.artifacts.repositories.ArtifactRepository-">add</a>, <a href="../ArtifactRepositoryContainer.html#addFirst-org.gradle.api.artifacts.repositories.ArtifactRepository-">addFirst</a>, <a href="../ArtifactRepositoryContainer.html#addLast-org.gradle.api.artifacts.repositories.ArtifactRepository-">addLast</a>, <a href="../ArtifactRepositoryContainer.html#getAt-java.lang.String-">getAt</a>, <a href="../ArtifactRepositoryContainer.html#getByName-java.lang.String-">getByName</a>, <a href="../ArtifactRepositoryContainer.html#getByName-java.lang.String-groovy.lang.Closure-">getByName</a>, <a href="../ArtifactRepositoryContainer.html#getByName-java.lang.String-org.gradle.api.Action-">getByName</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.util.Collection">
<!--   -->
</a>
<h3>Methods inherited from interface&nbsp;java.util.Collection</h3>
<code>parallelStream, removeIf, stream, toArray</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.org.gradle.util.Configurable">
<!--   -->
</a>
<h3>Methods inherited from interface&nbsp;org.gradle.util.<a href="../../../util/Configurable.html" title="interface in org.gradle.util">Configurable</a></h3>
<code><a href="../../../util/Configurable.html#configure-groovy.lang.Closure-">configure</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.org.gradle.api.DomainObjectCollection">
<!--   -->
</a>
<h3>Methods inherited from interface&nbsp;org.gradle.api.<a href="../../DomainObjectCollection.html" title="interface in org.gradle.api">DomainObjectCollection</a></h3>
<code><a href="../../DomainObjectCollection.html#addAllLater-org.gradle.api.provider.Provider-">addAllLater</a>, <a href="../../DomainObjectCollection.html#addLater-org.gradle.api.provider.Provider-">addLater</a>, <a href="../../DomainObjectCollection.html#all-groovy.lang.Closure-">all</a>, <a href="../../DomainObjectCollection.html#all-org.gradle.api.Action-">all</a>, <a href="../../DomainObjectCollection.html#configureEach-org.gradle.api.Action-">configureEach</a>, <a href="../../DomainObjectCollection.html#whenObjectAdded-groovy.lang.Closure-">whenObjectAdded</a>, <a href="../../DomainObjectCollection.html#whenObjectAdded-org.gradle.api.Action-">whenObjectAdded</a>, <a href="../../DomainObjectCollection.html#whenObjectRemoved-groovy.lang.Closure-">whenObjectRemoved</a>, <a href="../../DomainObjectCollection.html#whenObjectRemoved-org.gradle.api.Action-">whenObjectRemoved</a>, <a href="../../DomainObjectCollection.html#withType-java.lang.Class-groovy.lang.Closure-">withType</a>, <a href="../../DomainObjectCollection.html#withType-java.lang.Class-org.gradle.api.Action-">withType</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Iterable">
<!--   -->
</a>
<h3>Methods inherited from interface&nbsp;java.lang.Iterable</h3>
<code>forEach</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.util.List">
<!--   -->
</a>
<h3>Methods inherited from interface&nbsp;java.util.List</h3>
<code>add, addAll, addAll, clear, contains, containsAll, equals, get, hashCode, indexOf, isEmpty, iterator, lastIndexOf, listIterator, listIterator, remove, remove, removeAll, replaceAll, retainAll, set, size, sort, spliterator, subList, toArray, toArray</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.org.gradle.api.NamedDomainObjectCollection">
<!--   -->
</a>
<h3>Methods inherited from interface&nbsp;org.gradle.api.<a href="../../NamedDomainObjectCollection.html" title="interface in org.gradle.api">NamedDomainObjectCollection</a></h3>
<code><a href="../../NamedDomainObjectCollection.html#addAll-java.util.Collection-">addAll</a>, <a href="../../NamedDomainObjectCollection.html#addRule-java.lang.String-groovy.lang.Closure-">addRule</a>, <a href="../../NamedDomainObjectCollection.html#addRule-java.lang.String-org.gradle.api.Action-">addRule</a>, <a href="../../NamedDomainObjectCollection.html#addRule-org.gradle.api.Rule-">addRule</a>, <a href="../../NamedDomainObjectCollection.html#findByName-java.lang.String-">findByName</a>, <a href="../../NamedDomainObjectCollection.html#getAsMap--">getAsMap</a>, <a href="../../NamedDomainObjectCollection.html#getCollectionSchema--">getCollectionSchema</a>, <a href="../../NamedDomainObjectCollection.html#getNamer--">getNamer</a>, <a href="../../NamedDomainObjectCollection.html#getNames--">getNames</a>, <a href="../../NamedDomainObjectCollection.html#getRules--">getRules</a>, <a href="../../NamedDomainObjectCollection.html#named-java.lang.String-">named</a>, <a href="../../NamedDomainObjectCollection.html#named-java.lang.String-java.lang.Class-">named</a>, <a href="../../NamedDomainObjectCollection.html#named-java.lang.String-java.lang.Class-org.gradle.api.Action-">named</a>, <a href="../../NamedDomainObjectCollection.html#named-java.lang.String-org.gradle.api.Action-">named</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.org.gradle.api.NamedDomainObjectList">
<!--   -->
</a>
<h3>Methods inherited from interface&nbsp;org.gradle.api.<a href="../../NamedDomainObjectList.html" title="interface in org.gradle.api">NamedDomainObjectList</a></h3>
<code><a href="../../NamedDomainObjectList.html#findAll-groovy.lang.Closure-">findAll</a>, <a href="../../NamedDomainObjectList.html#matching-groovy.lang.Closure-">matching</a>, <a href="../../NamedDomainObjectList.html#matching-org.gradle.api.specs.Spec-">matching</a>, <a href="../../NamedDomainObjectList.html#withType-java.lang.Class-">withType</a></code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="flatDir-java.util.Map-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>flatDir</h4>
<pre class="methodSignature"><a href="../repositories/FlatDirectoryArtifactRepository.html" title="interface in org.gradle.api.artifacts.repositories">FlatDirectoryArtifactRepository</a>&nbsp;flatDir&#8203;(java.util.Map&lt;java.lang.String,&#8203;?&gt;&nbsp;args)</pre>
<div class="block">Adds a resolver that looks into a number of directories for artifacts. The artifacts are expected to be located in the
 root of the specified directories. The resolver ignores any group/organization information specified in the
 dependency section of your build script. If you only use this kind of resolver you might specify your
 dependencies like <code>":junit:4.4"</code> instead of <code>"junit:junit:4.4"</code>.

 The following parameter are accepted as keys for the map:

 <table summary="Shows property keys and associated values">
 <tr><th>Key</th>
     <th>Description of Associated Value</th></tr>
 <tr><td><code>name</code></td>
     <td><em>(optional)</em> The name of the repository.
 The default is a Hash value of the rootdir paths. The name is used in the console output,
 to point to information related to a particular repository. A name must be unique amongst a repository group.</td></tr>
 <tr><td><code>dirs</code></td>
     <td>Specifies a list of rootDirs where to look for dependencies. These are evaluated as per <a href="../../Project.html#files-java.lang.Object...-"><code>Project.files(Object...)</code></a></td></tr>
 </table>

 <p>Examples:</p>
 <pre class='autoTested'>
 repositories {
     flatDir name: 'libs', dirs: "$projectDir/libs"
     flatDir dirs: ["$projectDir/libs1", "$projectDir/libs2"]
 }
 </pre></div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>args</code> - The arguments used to configure the repository.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the added resolver</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code><a href="../../InvalidUserDataException.html" title="class in org.gradle.api">InvalidUserDataException</a></code> - In the case neither rootDir nor rootDirs is specified of if both
 are specified.</dd>
</dl>
</li>
</ul>
<a name="flatDir-groovy.lang.Closure-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>flatDir</h4>
<pre class="methodSignature"><a href="../repositories/FlatDirectoryArtifactRepository.html" title="interface in org.gradle.api.artifacts.repositories">FlatDirectoryArtifactRepository</a>&nbsp;flatDir&#8203;(<a href="https://docs.groovy-lang.org/docs/groovy-3.0.17/html/gapi/groovy/lang/DelegatesTo.html?is-external=true" title="class or interface in groovy.lang" class="externalLink">@DelegatesTo</a>(<a href="../repositories/FlatDirectoryArtifactRepository.html" title="interface in org.gradle.api.artifacts.repositories">FlatDirectoryArtifactRepository.class</a>)
                                        <a href="https://docs.groovy-lang.org/docs/groovy-3.0.17/html/gapi/groovy/lang/Closure.html?is-external=true" title="class or interface in groovy.lang" class="externalLink">Closure</a>&nbsp;configureClosure)</pre>
<div class="block">Adds and configures a repository which will look for dependencies in a number of local directories.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>configureClosure</code> - The closure to execute to configure the repository.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The repository.</dd>
</dl>
</li>
</ul>
<a name="flatDir-org.gradle.api.Action-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>flatDir</h4>
<pre class="methodSignature"><a href="../repositories/FlatDirectoryArtifactRepository.html" title="interface in org.gradle.api.artifacts.repositories">FlatDirectoryArtifactRepository</a>&nbsp;flatDir&#8203;(<a href="../../Action.html" title="interface in org.gradle.api">Action</a>&lt;? super <a href="../repositories/FlatDirectoryArtifactRepository.html" title="interface in org.gradle.api.artifacts.repositories">FlatDirectoryArtifactRepository</a>&gt;&nbsp;action)</pre>
<div class="block">Adds and configures a repository which will look for dependencies in a number of local directories.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>action</code> - The action to execute to configure the repository.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The repository.</dd>
</dl>
</li>
</ul>
<a name="gradlePluginPortal--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>gradlePluginPortal</h4>
<pre class="methodSignature"><a href="../repositories/ArtifactRepository.html" title="interface in org.gradle.api.artifacts.repositories">ArtifactRepository</a>&nbsp;gradlePluginPortal()</pre>
<div class="block">Adds a repository which looks in Gradle Central Plugin Repository for dependencies.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The Gradle Central Plugin Repository</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>4.4</dd>
</dl>
</li>
</ul>
<a name="gradlePluginPortal-org.gradle.api.Action-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>gradlePluginPortal</h4>
<pre class="methodSignature"><a href="../repositories/ArtifactRepository.html" title="interface in org.gradle.api.artifacts.repositories">ArtifactRepository</a>&nbsp;gradlePluginPortal&#8203;(<a href="../../Action.html" title="interface in org.gradle.api">Action</a>&lt;? super <a href="../repositories/ArtifactRepository.html" title="interface in org.gradle.api.artifacts.repositories">ArtifactRepository</a>&gt;&nbsp;action)</pre>
<div class="block">Adds a repository which looks in Gradle Central Plugin Repository for dependencies.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>action</code> - a configuration action</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the added resolver</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>5.4</dd>
</dl>
</li>
</ul>
<a name="jcenter-org.gradle.api.Action-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>jcenter</h4>
<pre class="methodSignature">@Deprecated
<a href="../repositories/MavenArtifactRepository.html" title="interface in org.gradle.api.artifacts.repositories">MavenArtifactRepository</a>&nbsp;jcenter&#8203;(<a href="../../Action.html" title="interface in org.gradle.api">Action</a>&lt;? super <a href="../repositories/MavenArtifactRepository.html" title="interface in org.gradle.api.artifacts.repositories">MavenArtifactRepository</a>&gt;&nbsp;action)</pre>
<div class="deprecationBlock"><span class="deprecatedLabel">Deprecated.</span>
<div class="deprecationComment">JFrog announced JCenter's <a href="https://blog.gradle.org/jcenter-shutdown">sunset</a> in February 2021. Use <a href="#mavenCentral--"><code>mavenCentral()</code></a> instead.</div>
</div>
<div class="block">Adds a repository which looks in Bintray's JCenter repository for dependencies.
 <p>
 The URL used to access this repository is "https://jcenter.bintray.com/".
 The behavior of this repository is otherwise the same as those added by <a href="#maven-org.gradle.api.Action-"><code>maven(org.gradle.api.Action)</code></a>.
 <p>
 Examples:
 <pre class='autoTestedWithDeprecations'>
 repositories {
   jcenter {
     artifactUrls = ["http://www.mycompany.com/artifacts1", "http://www.mycompany.com/artifacts2"]
   }
   jcenter {
     name = "nonDefaultName"
     artifactUrls = ["http://www.mycompany.com/artifacts1"]
   }
 }
 </pre></div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>action</code> - a configuration action</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the added repository</dd>
</dl>
</li>
</ul>
<a name="jcenter--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>jcenter</h4>
<pre class="methodSignature">@Deprecated
<a href="../repositories/MavenArtifactRepository.html" title="interface in org.gradle.api.artifacts.repositories">MavenArtifactRepository</a>&nbsp;jcenter()</pre>
<div class="deprecationBlock"><span class="deprecatedLabel">Deprecated.</span>
<div class="deprecationComment">JFrog announced JCenter's <a href="https://blog.gradle.org/jcenter-shutdown">sunset</a> in February 2021. Use <a href="#mavenCentral--"><code>mavenCentral()</code></a> instead.</div>
</div>
<div class="block">Adds a repository which looks in Bintray's JCenter repository for dependencies.
 <p>
 The URL used to access this repository is "https://jcenter.bintray.com/".
 The behavior of this repository is otherwise the same as those added by <a href="#maven-org.gradle.api.Action-"><code>maven(org.gradle.api.Action)</code></a>.
 <p>
 Examples:
 <pre class='autoTestedWithDeprecations'>
 repositories {
     jcenter()
 }
 </pre></div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the added resolver</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="#jcenter-org.gradle.api.Action-"><code>jcenter(Action)</code></a></dd>
</dl>
</li>
</ul>
<a name="mavenCentral-java.util.Map-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>mavenCentral</h4>
<pre class="methodSignature"><a href="../repositories/MavenArtifactRepository.html" title="interface in org.gradle.api.artifacts.repositories">MavenArtifactRepository</a>&nbsp;mavenCentral&#8203;(java.util.Map&lt;java.lang.String,&#8203;?&gt;&nbsp;args)</pre>
<div class="block">Adds a repository which looks in the Maven central repository for dependencies. The URL used to access this repository is
 <a href="../ArtifactRepositoryContainer.html#MAVEN_CENTRAL_URL">"https://repo.maven.apache.org/maven2/"</a>.

 <p>The following parameter are accepted as keys for the map:

 <table summary="Shows property keys and associated values">
 <tr><th>Key</th>
     <th>Description of Associated Value</th></tr>
 <tr><td><code>name</code></td>
     <td><em>(optional)</em> The name of the repository. The default is
 <a href="../ArtifactRepositoryContainer.html#DEFAULT_MAVEN_CENTRAL_REPO_NAME">"MavenRepo"</a> is used as the name. A name
 must be unique amongst a repository group.
 </td></tr>
 <tr><td><code>artifactUrls</code></td>
     <td>A single jar repository or a collection of jar repositories containing additional artifacts not found in the Maven central repository.
 But be aware that the POM must exist in Maven central.
 The provided values are evaluated as per <a href="../../Project.html#uri-java.lang.Object-"><code>Project.uri(Object)</code></a>.</td></tr>
 </table>

 <p>Examples:</p>
 <pre class='autoTested'>
 repositories {
     mavenCentral artifactUrls: ["http://www.mycompany.com/artifacts1", "http://www.mycompany.com/artifacts2"]
     mavenCentral name: "nonDefaultName", artifactUrls: ["http://www.mycompany.com/artifacts1"]
 }
 </pre></div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>args</code> - A list of urls of repositories to look for artifacts only.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the added repository</dd>
</dl>
</li>
</ul>
<a name="mavenCentral--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>mavenCentral</h4>
<pre class="methodSignature"><a href="../repositories/MavenArtifactRepository.html" title="interface in org.gradle.api.artifacts.repositories">MavenArtifactRepository</a>&nbsp;mavenCentral()</pre>
<div class="block">Adds a repository which looks in the Maven central repository for dependencies. The URL used to access this repository is
 <a href="../ArtifactRepositoryContainer.html#MAVEN_CENTRAL_URL">"https://repo.maven.apache.org/maven2/"</a>. The name of the repository is
 <a href="../ArtifactRepositoryContainer.html#DEFAULT_MAVEN_CENTRAL_REPO_NAME">"MavenRepo"</a>.

 <p>Examples:</p>
 <pre class='autoTested'>
 repositories {
     mavenCentral()
 }
 </pre></div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the added resolver</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="#mavenCentral-java.util.Map-"><code>mavenCentral(java.util.Map)</code></a></dd>
</dl>
</li>
</ul>
<a name="mavenCentral-org.gradle.api.Action-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>mavenCentral</h4>
<pre class="methodSignature"><a href="../repositories/MavenArtifactRepository.html" title="interface in org.gradle.api.artifacts.repositories">MavenArtifactRepository</a>&nbsp;mavenCentral&#8203;(<a href="../../Action.html" title="interface in org.gradle.api">Action</a>&lt;? super <a href="../repositories/MavenArtifactRepository.html" title="interface in org.gradle.api.artifacts.repositories">MavenArtifactRepository</a>&gt;&nbsp;action)</pre>
<div class="block">Adds a repository which looks in the Maven central repository for dependencies. The URL used to access this repository is
 <a href="../ArtifactRepositoryContainer.html#MAVEN_CENTRAL_URL">"https://repo.maven.apache.org/maven2/"</a>. The name of the repository is
 <a href="../ArtifactRepositoryContainer.html#DEFAULT_MAVEN_CENTRAL_REPO_NAME">"MavenRepo"</a>.

 <p>Examples:</p>
 <pre class='autoTested'>
 repositories {
     mavenCentral()
 }
 </pre></div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>action</code> - a configuration action</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the added resolver</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>5.3</dd>
</dl>
</li>
</ul>
<a name="mavenLocal--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>mavenLocal</h4>
<pre class="methodSignature"><a href="../repositories/MavenArtifactRepository.html" title="interface in org.gradle.api.artifacts.repositories">MavenArtifactRepository</a>&nbsp;mavenLocal()</pre>
<div class="block">Adds a repository which looks in the local Maven cache for dependencies. The name of the repository is
 <a href="../ArtifactRepositoryContainer.html#DEFAULT_MAVEN_LOCAL_REPO_NAME">"MavenLocal"</a>.

 <p>Examples:</p>
 <pre class='autoTested'>
 repositories {
     mavenLocal()
 }
 </pre>
 <p>
 The location for the repository is determined as follows (in order of precedence):
 </p>
 <ol>
 <li>The value of system property 'maven.repo.local' if set;</li>
 <li>The value of element &lt;localRepository&gt; of <code>~/.m2/settings.xml</code> if this file exists and element is set;</li>
 <li>The value of element &lt;localRepository&gt; of <code>$M2_HOME/conf/settings.xml</code> (where <code>$M2_HOME</code> is the value of the environment variable with that name) if this file exists and element is set;</li>
 <li>The path <code>~/.m2/repository</code>.</li>
 </ol></div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the added resolver</dd>
</dl>
</li>
</ul>
<a name="mavenLocal-org.gradle.api.Action-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>mavenLocal</h4>
<pre class="methodSignature"><a href="../repositories/MavenArtifactRepository.html" title="interface in org.gradle.api.artifacts.repositories">MavenArtifactRepository</a>&nbsp;mavenLocal&#8203;(<a href="../../Action.html" title="interface in org.gradle.api">Action</a>&lt;? super <a href="../repositories/MavenArtifactRepository.html" title="interface in org.gradle.api.artifacts.repositories">MavenArtifactRepository</a>&gt;&nbsp;action)</pre>
<div class="block">Adds a repository which looks in the local Maven cache for dependencies. The name of the repository is
 <a href="../ArtifactRepositoryContainer.html#DEFAULT_MAVEN_LOCAL_REPO_NAME">"MavenLocal"</a>.

 <p>Examples:</p>
 <pre class='autoTested'>
 repositories {
     mavenLocal()
 }
 </pre>
 <p>
 The location for the repository is determined as follows (in order of precedence):
 </p>
 <ol>
 <li>The value of system property 'maven.repo.local' if set;</li>
 <li>The value of element &lt;localRepository&gt; of <code>~/.m2/settings.xml</code> if this file exists and element is set;</li>
 <li>The value of element &lt;localRepository&gt; of <code>$M2_HOME/conf/settings.xml</code> (where <code>$M2_HOME</code> is the value of the environment variable with that name) if this file exists and element is set;</li>
 <li>The path <code>~/.m2/repository</code>.</li>
 </ol></div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>action</code> - a configuration action</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the added resolver</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>5.3</dd>
</dl>
</li>
</ul>
<a name="google--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>google</h4>
<pre class="methodSignature"><a href="../repositories/MavenArtifactRepository.html" title="interface in org.gradle.api.artifacts.repositories">MavenArtifactRepository</a>&nbsp;google()</pre>
<div class="block">Adds a repository which looks in Google's Maven repository for dependencies.
 <p>
 The URL used to access this repository is "https://dl.google.com/dl/android/maven2/".
 <p>
 Examples:
 <pre class='autoTested'>
 repositories {
     google()
 }
 </pre></div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the added resolver</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>4.0</dd>
</dl>
</li>
</ul>
<a name="google-org.gradle.api.Action-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>google</h4>
<pre class="methodSignature"><a href="../repositories/MavenArtifactRepository.html" title="interface in org.gradle.api.artifacts.repositories">MavenArtifactRepository</a>&nbsp;google&#8203;(<a href="../../Action.html" title="interface in org.gradle.api">Action</a>&lt;? super <a href="../repositories/MavenArtifactRepository.html" title="interface in org.gradle.api.artifacts.repositories">MavenArtifactRepository</a>&gt;&nbsp;action)</pre>
<div class="block">Adds a repository which looks in Google's Maven repository for dependencies.
 <p>
 The URL used to access this repository is "https://dl.google.com/dl/android/maven2/".
 <p>
 Examples:
 <pre class='autoTested'>
 repositories {
     google()
 }
 </pre></div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>action</code> - a configuration action</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the added resolver</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>5.3</dd>
</dl>
</li>
</ul>
<a name="maven-groovy.lang.Closure-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>maven</h4>
<pre class="methodSignature"><a href="../repositories/MavenArtifactRepository.html" title="interface in org.gradle.api.artifacts.repositories">MavenArtifactRepository</a>&nbsp;maven&#8203;(<a href="https://docs.groovy-lang.org/docs/groovy-3.0.17/html/gapi/groovy/lang/DelegatesTo.html?is-external=true" title="class or interface in groovy.lang" class="externalLink">@DelegatesTo</a>(<a href="../repositories/MavenArtifactRepository.html" title="interface in org.gradle.api.artifacts.repositories">MavenArtifactRepository.class</a>)
                              <a href="https://docs.groovy-lang.org/docs/groovy-3.0.17/html/gapi/groovy/lang/Closure.html?is-external=true" title="class or interface in groovy.lang" class="externalLink">Closure</a>&nbsp;closure)</pre>
<div class="block">Adds and configures a Maven repository. Newly created instance of <code>MavenArtifactRepository</code> is passed as an argument to the closure.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>closure</code> - The closure to use to configure the repository.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The added repository.</dd>
</dl>
</li>
</ul>
<a name="maven-org.gradle.api.Action-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>maven</h4>
<pre class="methodSignature"><a href="../repositories/MavenArtifactRepository.html" title="interface in org.gradle.api.artifacts.repositories">MavenArtifactRepository</a>&nbsp;maven&#8203;(<a href="../../Action.html" title="interface in org.gradle.api">Action</a>&lt;? super <a href="../repositories/MavenArtifactRepository.html" title="interface in org.gradle.api.artifacts.repositories">MavenArtifactRepository</a>&gt;&nbsp;action)</pre>
<div class="block">Adds and configures a Maven repository.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>action</code> - The action to use to configure the repository.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The added repository.</dd>
</dl>
</li>
</ul>
<a name="ivy-groovy.lang.Closure-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ivy</h4>
<pre class="methodSignature"><a href="../repositories/IvyArtifactRepository.html" title="interface in org.gradle.api.artifacts.repositories">IvyArtifactRepository</a>&nbsp;ivy&#8203;(<a href="https://docs.groovy-lang.org/docs/groovy-3.0.17/html/gapi/groovy/lang/DelegatesTo.html?is-external=true" title="class or interface in groovy.lang" class="externalLink">@DelegatesTo</a>(<a href="../repositories/IvyArtifactRepository.html" title="interface in org.gradle.api.artifacts.repositories">IvyArtifactRepository.class</a>)
                          <a href="https://docs.groovy-lang.org/docs/groovy-3.0.17/html/gapi/groovy/lang/Closure.html?is-external=true" title="class or interface in groovy.lang" class="externalLink">Closure</a>&nbsp;closure)</pre>
<div class="block">Adds and configures an Ivy repository. Newly created instance of <code>IvyArtifactRepository</code> is passed as an argument to the closure.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>closure</code> - The closure to use to configure the repository.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The added repository.</dd>
</dl>
</li>
</ul>
<a name="ivy-org.gradle.api.Action-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ivy</h4>
<pre class="methodSignature"><a href="../repositories/IvyArtifactRepository.html" title="interface in org.gradle.api.artifacts.repositories">IvyArtifactRepository</a>&nbsp;ivy&#8203;(<a href="../../Action.html" title="interface in org.gradle.api">Action</a>&lt;? super <a href="../repositories/IvyArtifactRepository.html" title="interface in org.gradle.api.artifacts.repositories">IvyArtifactRepository</a>&gt;&nbsp;action)</pre>
<div class="block">Adds and configures an Ivy repository.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>action</code> - The action to use to configure the repository.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The added repository.</dd>
</dl>
</li>
</ul>
<a name="exclusiveContent-org.gradle.api.Action-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>exclusiveContent</h4>
<pre class="methodSignature">void&nbsp;exclusiveContent&#8203;(<a href="../../Action.html" title="interface in org.gradle.api">Action</a>&lt;? super <a href="../repositories/ExclusiveContentRepository.html" title="interface in org.gradle.api.artifacts.repositories">ExclusiveContentRepository</a>&gt;&nbsp;action)</pre>
<div class="block">Declares exclusive content repositories. Exclusive content repositories are
 repositories for which you can declare an inclusive content filter. Artifacts
 matching the filter will then only be searched in the repositories which
 exclusively match it.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>action</code> - the configuration of the repositories</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>6.2</dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../../allclasses.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field.summary">Field</a>&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
