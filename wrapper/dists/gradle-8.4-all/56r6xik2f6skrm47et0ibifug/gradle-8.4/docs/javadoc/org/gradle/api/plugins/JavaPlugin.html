<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc -->
<title>JavaPlugin (Gradle API 8.4)</title>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<link rel="stylesheet" type="text/css" href="../../../../javadoc.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../jquery/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
<script type="text/javascript" src="../../../../jquery/jszip/dist/jszip.min.js"></script>
<script type="text/javascript" src="../../../../jquery/jszip-utils/dist/jszip-utils.min.js"></script>
<!--[if IE]>
<script type="text/javascript" src="../../../../jquery/jszip-utils/dist/jszip-utils-ie.min.js"></script>
<![endif]-->
<script type="text/javascript" src="../../../../jquery/jquery-3.6.0.min.js"></script>
<script type="text/javascript" src="../../../../jquery/jquery-ui.min.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="JavaPlugin (Gradle API 8.4)";
        }
    }
    catch(err) {
    }
//-->
var data = {"i0":10};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
var pathtoroot = "../../../../";
var useModuleDirectories = false;
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="fixedNav">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses.html">All&nbsp;Classes</a></li>
</ul>
<ul class="navListSearch">
<li><label for="search">SEARCH:</label>
<input type="text" id="search" value="search" disabled="disabled">
<input type="reset" id="reset" value="reset" disabled="disabled">
</li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field.summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#field.detail">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
</div>
<div class="navPadding">&nbsp;</div>
<script type="text/javascript"><!--
$('.navPadding').css('padding-top', $('.fixedNav').css("height"));
//-->
</script>
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle"><span class="packageLabelInType">Package</span>&nbsp;<a href="package-summary.html">org.gradle.api.plugins</a></div>
<h2 title="Class JavaPlugin" class="title">Class JavaPlugin</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>org.gradle.api.plugins.JavaPlugin</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Implemented Interfaces:</dt>
<dd><code><a href="../Plugin.html" title="interface in org.gradle.api">Plugin</a>&lt;<a href="../Project.html" title="interface in org.gradle.api">Project</a>&gt;</code></dd>
</dl>
<hr>
<pre>public abstract class <span class="typeNameLabel">JavaPlugin</span>
extends java.lang.Object
implements <a href="../Plugin.html" title="interface in org.gradle.api">Plugin</a>&lt;<a href="../Project.html" title="interface in org.gradle.api">Project</a>&gt;</pre>
<div class="block"><p>A <a href="../Plugin.html" title="interface in org.gradle.api"><code>Plugin</code></a> which compiles and tests Java source, and assembles it into a JAR file.</p>

 This plugin creates a built-in <a href="jvm/JvmTestSuite.html" title="interface in org.gradle.api.plugins.jvm"><code>test suite</code></a> named <code>test</code> that represents the <a href="../tasks/testing/Test.html" title="class in org.gradle.api.tasks.testing"><code>Test</code></a> task for Java projects.</div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="https://docs.gradle.org/current/userguide/java_plugin.html">Java plugin reference</a>, 
<a href="https://docs.gradle.org/current/userguide/jvm_test_suite_plugin.html">JVM test suite plugin reference</a></dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- =========== FIELD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.summary">
<!--   -->
</a>
<h3>Field Summary</h3>
<table class="memberSummary" summary="Field Summary table, listing fields, and an explanation">
<caption><span>Fields</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colSecond" scope="col">Field</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#ANNOTATION_PROCESSOR_CONFIGURATION_NAME">ANNOTATION_PROCESSOR_CONFIGURATION_NAME</a></span></code></th>
<td class="colLast">
<div class="block">The name of the annotation processor configuration.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#API_CONFIGURATION_NAME">API_CONFIGURATION_NAME</a></span></code></th>
<td class="colLast">
<div class="block">The name of the API configuration, where dependencies exported by a component at compile time should
 be declared.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#API_ELEMENTS_CONFIGURATION_NAME">API_ELEMENTS_CONFIGURATION_NAME</a></span></code></th>
<td class="colLast">
<div class="block">The name of the configuration to define the API elements of a component.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#CLASSES_TASK_NAME">CLASSES_TASK_NAME</a></span></code></th>
<td class="colLast">
<div class="block">The name of the lifecycle task which outcome is that all the classes of a component are generated.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#COMPILE_CLASSPATH_CONFIGURATION_NAME">COMPILE_CLASSPATH_CONFIGURATION_NAME</a></span></code></th>
<td class="colLast">
<div class="block">The name of the compile classpath configuration.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#COMPILE_JAVA_TASK_NAME">COMPILE_JAVA_TASK_NAME</a></span></code></th>
<td class="colLast">
<div class="block">The name of the task which compiles Java sources.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#COMPILE_ONLY_API_CONFIGURATION_NAME">COMPILE_ONLY_API_CONFIGURATION_NAME</a></span></code></th>
<td class="colLast">
<div class="block">The name of the configuration to define the API elements of a component that are required to compile a component,
 but not at runtime.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#COMPILE_ONLY_CONFIGURATION_NAME">COMPILE_ONLY_CONFIGURATION_NAME</a></span></code></th>
<td class="colLast">
<div class="block">The name of the configuration that is used to declare dependencies which are only required to compile a component,
 but not at runtime.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#COMPILE_TEST_JAVA_TASK_NAME">COMPILE_TEST_JAVA_TASK_NAME</a></span></code></th>
<td class="colLast">
<div class="block">The name of the task which compiles the test Java sources.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#IMPLEMENTATION_CONFIGURATION_NAME">IMPLEMENTATION_CONFIGURATION_NAME</a></span></code></th>
<td class="colLast">
<div class="block">The name of the implementation configuration, where dependencies that are only used internally by
 a component should be declared.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#JAR_TASK_NAME">JAR_TASK_NAME</a></span></code></th>
<td class="colLast">
<div class="block">The name of the task which generates the component main jar.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#JAVADOC_ELEMENTS_CONFIGURATION_NAME">JAVADOC_ELEMENTS_CONFIGURATION_NAME</a></span></code></th>
<td class="colLast">
<div class="block">The name of the javadoc elements configuration.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#JAVADOC_TASK_NAME">JAVADOC_TASK_NAME</a></span></code></th>
<td class="colLast">
<div class="block">The name of the task which generates the component javadoc.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#PROCESS_RESOURCES_TASK_NAME">PROCESS_RESOURCES_TASK_NAME</a></span></code></th>
<td class="colLast">
<div class="block">The name of the task that processes resources.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#PROCESS_TEST_RESOURCES_TASK_NAME">PROCESS_TEST_RESOURCES_TASK_NAME</a></span></code></th>
<td class="colLast">
<div class="block">The name of the task which processes the test resources.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#RUNTIME_CLASSPATH_CONFIGURATION_NAME">RUNTIME_CLASSPATH_CONFIGURATION_NAME</a></span></code></th>
<td class="colLast">
<div class="block">The name of the runtime classpath configuration, used by a component to query its own runtime classpath.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#RUNTIME_ELEMENTS_CONFIGURATION_NAME">RUNTIME_ELEMENTS_CONFIGURATION_NAME</a></span></code></th>
<td class="colLast">
<div class="block">The name of the runtime elements configuration, that should be used by consumers
 to query the runtime dependencies of a component.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#RUNTIME_ONLY_CONFIGURATION_NAME">RUNTIME_ONLY_CONFIGURATION_NAME</a></span></code></th>
<td class="colLast">
<div class="block">The name of the runtime only dependencies configuration, used to declare dependencies
 that should only be found at runtime.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#SOURCES_ELEMENTS_CONFIGURATION_NAME">SOURCES_ELEMENTS_CONFIGURATION_NAME</a></span></code></th>
<td class="colLast">
<div class="block">The name of the sources elements configuration.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#TEST_ANNOTATION_PROCESSOR_CONFIGURATION_NAME">TEST_ANNOTATION_PROCESSOR_CONFIGURATION_NAME</a></span></code></th>
<td class="colLast">
<div class="block">The name of the test annotation processor configuration.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#TEST_CLASSES_TASK_NAME">TEST_CLASSES_TASK_NAME</a></span></code></th>
<td class="colLast">
<div class="block">The name of the lifecycle task which outcome is that all test classes of a component are generated.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#TEST_COMPILE_CLASSPATH_CONFIGURATION_NAME">TEST_COMPILE_CLASSPATH_CONFIGURATION_NAME</a></span></code></th>
<td class="colLast">
<div class="block">The name of the test compile classpath configuration.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#TEST_COMPILE_ONLY_CONFIGURATION_NAME">TEST_COMPILE_ONLY_CONFIGURATION_NAME</a></span></code></th>
<td class="colLast">
<div class="block">The name of the configuration that should be used to declare dependencies which are only required
 to compile the tests, but not when running them.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#TEST_IMPLEMENTATION_CONFIGURATION_NAME">TEST_IMPLEMENTATION_CONFIGURATION_NAME</a></span></code></th>
<td class="colLast">
<div class="block">The name of the test implementation dependencies configuration.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#TEST_RUNTIME_CLASSPATH_CONFIGURATION_NAME">TEST_RUNTIME_CLASSPATH_CONFIGURATION_NAME</a></span></code></th>
<td class="colLast">
<div class="block">The name of the test runtime classpath configuration.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#TEST_RUNTIME_ONLY_CONFIGURATION_NAME">TEST_RUNTIME_ONLY_CONFIGURATION_NAME</a></span></code></th>
<td class="colLast">
<div class="block">The name of the test runtime only dependencies configuration.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#TEST_TASK_NAME">TEST_TASK_NAME</a></span></code></th>
<td class="colLast">
<div class="block">The name of the task which triggers execution of tests.</div>
</td>
</tr>
</table>
</li>
</ul>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="memberSummary" summary="Constructor Summary table, listing constructors, and an explanation">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Constructor</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tr class="altColor">
<th class="colConstructorName" scope="row"><code><span class="memberNameLink"><a href="#JavaPlugin-org.gradle.api.model.ObjectFactory-">JavaPlugin</a></span>&#8203;(<a href="../model/ObjectFactory.html" title="interface in org.gradle.api.model">ObjectFactory</a>&nbsp;objectFactory)</code></th>
<td class="colLast">&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colSecond" scope="col">Method</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#apply-org.gradle.api.Project-">apply</a></span>&#8203;(<a href="../Project.html" title="interface in org.gradle.api">Project</a>&nbsp;project)</code></th>
<td class="colLast">
<div class="block">Apply this plugin to the given target object.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>clone, equals, finalize, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ FIELD DETAIL =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.detail">
<!--   -->
</a>
<h3>Field Detail</h3>
<a name="PROCESS_RESOURCES_TASK_NAME">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>PROCESS_RESOURCES_TASK_NAME</h4>
<pre>public static final&nbsp;java.lang.String PROCESS_RESOURCES_TASK_NAME</pre>
<div class="block">The name of the task that processes resources.</div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../../constant-values.html#org.gradle.api.plugins.JavaPlugin.PROCESS_RESOURCES_TASK_NAME">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CLASSES_TASK_NAME">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CLASSES_TASK_NAME</h4>
<pre>public static final&nbsp;java.lang.String CLASSES_TASK_NAME</pre>
<div class="block">The name of the lifecycle task which outcome is that all the classes of a component are generated.</div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../../constant-values.html#org.gradle.api.plugins.JavaPlugin.CLASSES_TASK_NAME">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="COMPILE_JAVA_TASK_NAME">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>COMPILE_JAVA_TASK_NAME</h4>
<pre>public static final&nbsp;java.lang.String COMPILE_JAVA_TASK_NAME</pre>
<div class="block">The name of the task which compiles Java sources.</div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../../constant-values.html#org.gradle.api.plugins.JavaPlugin.COMPILE_JAVA_TASK_NAME">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="PROCESS_TEST_RESOURCES_TASK_NAME">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>PROCESS_TEST_RESOURCES_TASK_NAME</h4>
<pre>public static final&nbsp;java.lang.String PROCESS_TEST_RESOURCES_TASK_NAME</pre>
<div class="block">The name of the task which processes the test resources.</div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../../constant-values.html#org.gradle.api.plugins.JavaPlugin.PROCESS_TEST_RESOURCES_TASK_NAME">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="TEST_CLASSES_TASK_NAME">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>TEST_CLASSES_TASK_NAME</h4>
<pre>public static final&nbsp;java.lang.String TEST_CLASSES_TASK_NAME</pre>
<div class="block">The name of the lifecycle task which outcome is that all test classes of a component are generated.</div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../../constant-values.html#org.gradle.api.plugins.JavaPlugin.TEST_CLASSES_TASK_NAME">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="COMPILE_TEST_JAVA_TASK_NAME">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>COMPILE_TEST_JAVA_TASK_NAME</h4>
<pre>public static final&nbsp;java.lang.String COMPILE_TEST_JAVA_TASK_NAME</pre>
<div class="block">The name of the task which compiles the test Java sources.</div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../../constant-values.html#org.gradle.api.plugins.JavaPlugin.COMPILE_TEST_JAVA_TASK_NAME">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="TEST_TASK_NAME">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>TEST_TASK_NAME</h4>
<pre>public static final&nbsp;java.lang.String TEST_TASK_NAME</pre>
<div class="block">The name of the task which triggers execution of tests.</div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../../constant-values.html#org.gradle.api.plugins.JavaPlugin.TEST_TASK_NAME">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="JAR_TASK_NAME">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>JAR_TASK_NAME</h4>
<pre>public static final&nbsp;java.lang.String JAR_TASK_NAME</pre>
<div class="block">The name of the task which generates the component main jar.</div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../../constant-values.html#org.gradle.api.plugins.JavaPlugin.JAR_TASK_NAME">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="JAVADOC_TASK_NAME">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>JAVADOC_TASK_NAME</h4>
<pre>public static final&nbsp;java.lang.String JAVADOC_TASK_NAME</pre>
<div class="block">The name of the task which generates the component javadoc.</div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../../constant-values.html#org.gradle.api.plugins.JavaPlugin.JAVADOC_TASK_NAME">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="API_CONFIGURATION_NAME">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>API_CONFIGURATION_NAME</h4>
<pre>public static final&nbsp;java.lang.String API_CONFIGURATION_NAME</pre>
<div class="block">The name of the API configuration, where dependencies exported by a component at compile time should
 be declared.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>3.4</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../../constant-values.html#org.gradle.api.plugins.JavaPlugin.API_CONFIGURATION_NAME">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="IMPLEMENTATION_CONFIGURATION_NAME">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>IMPLEMENTATION_CONFIGURATION_NAME</h4>
<pre>public static final&nbsp;java.lang.String IMPLEMENTATION_CONFIGURATION_NAME</pre>
<div class="block">The name of the implementation configuration, where dependencies that are only used internally by
 a component should be declared.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>3.4</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../../constant-values.html#org.gradle.api.plugins.JavaPlugin.IMPLEMENTATION_CONFIGURATION_NAME">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="API_ELEMENTS_CONFIGURATION_NAME">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>API_ELEMENTS_CONFIGURATION_NAME</h4>
<pre>public static final&nbsp;java.lang.String API_ELEMENTS_CONFIGURATION_NAME</pre>
<div class="block">The name of the configuration to define the API elements of a component.
 That is, the dependencies which are required to compile against that component.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>3.4</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../../constant-values.html#org.gradle.api.plugins.JavaPlugin.API_ELEMENTS_CONFIGURATION_NAME">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="COMPILE_ONLY_CONFIGURATION_NAME">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>COMPILE_ONLY_CONFIGURATION_NAME</h4>
<pre>public static final&nbsp;java.lang.String COMPILE_ONLY_CONFIGURATION_NAME</pre>
<div class="block">The name of the configuration that is used to declare dependencies which are only required to compile a component,
 but not at runtime.</div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../../constant-values.html#org.gradle.api.plugins.JavaPlugin.COMPILE_ONLY_CONFIGURATION_NAME">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="COMPILE_ONLY_API_CONFIGURATION_NAME">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>COMPILE_ONLY_API_CONFIGURATION_NAME</h4>
<pre>public static final&nbsp;java.lang.String COMPILE_ONLY_API_CONFIGURATION_NAME</pre>
<div class="block">The name of the configuration to define the API elements of a component that are required to compile a component,
 but not at runtime.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>6.7</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../../constant-values.html#org.gradle.api.plugins.JavaPlugin.COMPILE_ONLY_API_CONFIGURATION_NAME">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="RUNTIME_ONLY_CONFIGURATION_NAME">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>RUNTIME_ONLY_CONFIGURATION_NAME</h4>
<pre>public static final&nbsp;java.lang.String RUNTIME_ONLY_CONFIGURATION_NAME</pre>
<div class="block">The name of the runtime only dependencies configuration, used to declare dependencies
 that should only be found at runtime.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>3.4</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../../constant-values.html#org.gradle.api.plugins.JavaPlugin.RUNTIME_ONLY_CONFIGURATION_NAME">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="RUNTIME_CLASSPATH_CONFIGURATION_NAME">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>RUNTIME_CLASSPATH_CONFIGURATION_NAME</h4>
<pre>public static final&nbsp;java.lang.String RUNTIME_CLASSPATH_CONFIGURATION_NAME</pre>
<div class="block">The name of the runtime classpath configuration, used by a component to query its own runtime classpath.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>3.4</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../../constant-values.html#org.gradle.api.plugins.JavaPlugin.RUNTIME_CLASSPATH_CONFIGURATION_NAME">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="RUNTIME_ELEMENTS_CONFIGURATION_NAME">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>RUNTIME_ELEMENTS_CONFIGURATION_NAME</h4>
<pre>public static final&nbsp;java.lang.String RUNTIME_ELEMENTS_CONFIGURATION_NAME</pre>
<div class="block">The name of the runtime elements configuration, that should be used by consumers
 to query the runtime dependencies of a component.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>3.4</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../../constant-values.html#org.gradle.api.plugins.JavaPlugin.RUNTIME_ELEMENTS_CONFIGURATION_NAME">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="JAVADOC_ELEMENTS_CONFIGURATION_NAME">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>JAVADOC_ELEMENTS_CONFIGURATION_NAME</h4>
<pre>public static final&nbsp;java.lang.String JAVADOC_ELEMENTS_CONFIGURATION_NAME</pre>
<div class="block">The name of the javadoc elements configuration.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>6.0</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../../constant-values.html#org.gradle.api.plugins.JavaPlugin.JAVADOC_ELEMENTS_CONFIGURATION_NAME">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="SOURCES_ELEMENTS_CONFIGURATION_NAME">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>SOURCES_ELEMENTS_CONFIGURATION_NAME</h4>
<pre>public static final&nbsp;java.lang.String SOURCES_ELEMENTS_CONFIGURATION_NAME</pre>
<div class="block">The name of the sources elements configuration.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>6.0</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../../constant-values.html#org.gradle.api.plugins.JavaPlugin.SOURCES_ELEMENTS_CONFIGURATION_NAME">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="COMPILE_CLASSPATH_CONFIGURATION_NAME">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>COMPILE_CLASSPATH_CONFIGURATION_NAME</h4>
<pre>public static final&nbsp;java.lang.String COMPILE_CLASSPATH_CONFIGURATION_NAME</pre>
<div class="block">The name of the compile classpath configuration.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>3.4</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../../constant-values.html#org.gradle.api.plugins.JavaPlugin.COMPILE_CLASSPATH_CONFIGURATION_NAME">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="ANNOTATION_PROCESSOR_CONFIGURATION_NAME">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ANNOTATION_PROCESSOR_CONFIGURATION_NAME</h4>
<pre>public static final&nbsp;java.lang.String ANNOTATION_PROCESSOR_CONFIGURATION_NAME</pre>
<div class="block">The name of the annotation processor configuration.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>4.6</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../../constant-values.html#org.gradle.api.plugins.JavaPlugin.ANNOTATION_PROCESSOR_CONFIGURATION_NAME">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="TEST_IMPLEMENTATION_CONFIGURATION_NAME">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>TEST_IMPLEMENTATION_CONFIGURATION_NAME</h4>
<pre>public static final&nbsp;java.lang.String TEST_IMPLEMENTATION_CONFIGURATION_NAME</pre>
<div class="block">The name of the test implementation dependencies configuration.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>3.4</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../../constant-values.html#org.gradle.api.plugins.JavaPlugin.TEST_IMPLEMENTATION_CONFIGURATION_NAME">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="TEST_COMPILE_ONLY_CONFIGURATION_NAME">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>TEST_COMPILE_ONLY_CONFIGURATION_NAME</h4>
<pre>public static final&nbsp;java.lang.String TEST_COMPILE_ONLY_CONFIGURATION_NAME</pre>
<div class="block">The name of the configuration that should be used to declare dependencies which are only required
 to compile the tests, but not when running them.</div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../../constant-values.html#org.gradle.api.plugins.JavaPlugin.TEST_COMPILE_ONLY_CONFIGURATION_NAME">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="TEST_RUNTIME_ONLY_CONFIGURATION_NAME">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>TEST_RUNTIME_ONLY_CONFIGURATION_NAME</h4>
<pre>public static final&nbsp;java.lang.String TEST_RUNTIME_ONLY_CONFIGURATION_NAME</pre>
<div class="block">The name of the test runtime only dependencies configuration.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>3.4</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../../constant-values.html#org.gradle.api.plugins.JavaPlugin.TEST_RUNTIME_ONLY_CONFIGURATION_NAME">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="TEST_COMPILE_CLASSPATH_CONFIGURATION_NAME">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>TEST_COMPILE_CLASSPATH_CONFIGURATION_NAME</h4>
<pre>public static final&nbsp;java.lang.String TEST_COMPILE_CLASSPATH_CONFIGURATION_NAME</pre>
<div class="block">The name of the test compile classpath configuration.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>3.4</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../../constant-values.html#org.gradle.api.plugins.JavaPlugin.TEST_COMPILE_CLASSPATH_CONFIGURATION_NAME">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="TEST_ANNOTATION_PROCESSOR_CONFIGURATION_NAME">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>TEST_ANNOTATION_PROCESSOR_CONFIGURATION_NAME</h4>
<pre>public static final&nbsp;java.lang.String TEST_ANNOTATION_PROCESSOR_CONFIGURATION_NAME</pre>
<div class="block">The name of the test annotation processor configuration.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>4.6</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../../constant-values.html#org.gradle.api.plugins.JavaPlugin.TEST_ANNOTATION_PROCESSOR_CONFIGURATION_NAME">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="TEST_RUNTIME_CLASSPATH_CONFIGURATION_NAME">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>TEST_RUNTIME_CLASSPATH_CONFIGURATION_NAME</h4>
<pre>public static final&nbsp;java.lang.String TEST_RUNTIME_CLASSPATH_CONFIGURATION_NAME</pre>
<div class="block">The name of the test runtime classpath configuration.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>3.4</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../../constant-values.html#org.gradle.api.plugins.JavaPlugin.TEST_RUNTIME_CLASSPATH_CONFIGURATION_NAME">Constant Field Values</a></dd>
</dl>
</li>
</ul>
</li>
</ul>
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a name="JavaPlugin-org.gradle.api.model.ObjectFactory-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>JavaPlugin</h4>
<pre>@Inject
public&nbsp;JavaPlugin&#8203;(<a href="../model/ObjectFactory.html" title="interface in org.gradle.api.model">ObjectFactory</a>&nbsp;objectFactory)</pre>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="apply-org.gradle.api.Project-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>apply</h4>
<pre class="methodSignature">public&nbsp;void&nbsp;apply&#8203;(<a href="../Project.html" title="interface in org.gradle.api">Project</a>&nbsp;project)</pre>
<div class="block"><span class="descfrmTypeLabel">Description copied from interface:&nbsp;<code><a href="../Plugin.html#apply-T-">Plugin</a></code></span></div>
<div class="block">Apply this plugin to the given target object.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../Plugin.html#apply-T-">apply</a></code>&nbsp;in interface&nbsp;<code><a href="../Plugin.html" title="interface in org.gradle.api">Plugin</a>&lt;<a href="../Project.html" title="interface in org.gradle.api">Project</a>&gt;</code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>project</code> - The target object</dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field.summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#field.detail">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
