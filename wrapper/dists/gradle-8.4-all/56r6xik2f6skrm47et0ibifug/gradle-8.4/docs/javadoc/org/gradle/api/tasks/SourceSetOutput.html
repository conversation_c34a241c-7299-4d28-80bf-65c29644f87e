<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc -->
<title>SourceSetOutput (Gradle API 8.4)</title>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<link rel="stylesheet" type="text/css" href="../../../../javadoc.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../jquery/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
<script type="text/javascript" src="../../../../jquery/jszip/dist/jszip.min.js"></script>
<script type="text/javascript" src="../../../../jquery/jszip-utils/dist/jszip-utils.min.js"></script>
<!--[if IE]>
<script type="text/javascript" src="../../../../jquery/jszip-utils/dist/jszip-utils-ie.min.js"></script>
<![endif]-->
<script type="text/javascript" src="../../../../jquery/jquery-3.6.0.min.js"></script>
<script type="text/javascript" src="../../../../jquery/jquery-ui.min.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="SourceSetOutput (Gradle API 8.4)";
        }
    }
    catch(err) {
    }
//-->
var data = {"i0":6,"i1":6,"i2":6,"i3":6,"i4":6,"i5":6,"i6":6,"i7":6};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],4:["t3","Abstract Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
var pathtoroot = "../../../../";
var useModuleDirectories = false;
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="fixedNav">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses.html">All&nbsp;Classes</a></li>
</ul>
<ul class="navListSearch">
<li><label for="search">SEARCH:</label>
<input type="text" id="search" value="search" disabled="disabled">
<input type="reset" id="reset" value="reset" disabled="disabled">
</li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li><a href="#nested.class.summary">Nested</a>&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
</div>
<div class="navPadding">&nbsp;</div>
<script type="text/javascript"><!--
$('.navPadding').css('padding-top', $('.fixedNav').css("height"));
//-->
</script>
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle"><span class="packageLabelInType">Package</span>&nbsp;<a href="package-summary.html">org.gradle.api.tasks</a></div>
<h2 title="Interface SourceSetOutput" class="title">Interface SourceSetOutput</h2>
</div>
<div class="contentContainer">
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Superinterfaces:</dt>
<dd><code><a href="AntBuilderAware.html" title="interface in org.gradle.api.tasks">AntBuilderAware</a></code>, <code><a href="../Buildable.html" title="interface in org.gradle.api">Buildable</a></code>, <code><a href="../file/FileCollection.html" title="interface in org.gradle.api.file">FileCollection</a></code>, <code>java.lang.Iterable&lt;java.io.File&gt;</code></dd>
</dl>
<hr>
<pre>public interface <span class="typeNameLabel">SourceSetOutput</span>
extends <a href="../file/FileCollection.html" title="interface in org.gradle.api.file">FileCollection</a></pre>
<div class="block">A collection of all output directories (compiled classes, processed resources, etc.) - notice that <a href="SourceSetOutput.html" title="interface in org.gradle.api.tasks"><code>SourceSetOutput</code></a> extends <a href="../file/FileCollection.html" title="interface in org.gradle.api.file"><code>FileCollection</code></a>.
 <p>
 Provides output information of the source set. Allows configuring the default output dirs and specify additional output dirs.

 <pre class='autoTested'>
 plugins {
     id 'java'
 }

 sourceSets {
   main {
     //if you truly want to override the defaults:
     output.resourcesDir = file('out/bin')
     // Compiled Java classes should use this directory
     java.destinationDirectory.set(file('out/bin'))
   }
 }
 </pre>

 Working with generated resources.
 <p>
 In general, we recommend generating resources into folders different than the regular resourcesDir and classesDirs.
 Usually, it makes the build easier to understand and maintain. Also it gives some additional benefits
 because other Gradle plugins can take advantage of the output dirs 'registered' in the SourceSet.output.
 For example: Java plugin will use those dirs in calculating class paths and for jarring the content;
 IDEA and Eclipse plugins will put those folders on relevant classpath.
 <p>
 An example how to work with generated resources:

 <pre class='autoTested'>
 plugins {
   id 'java'
 }

 def generateResourcesTask = tasks.register("generate-resources", GenerateResourcesTask) {
   resourcesDir.set(layout.buildDirectory.dir("generated-resources/main"))
 }

 // Include all outputs of the `generate-resources` task as outputs of the main sourceSet.
 sourceSets {
   main {
     output.dir(generateResourcesTask)
   }
 }

 abstract class GenerateResourcesTask extends DefaultTask {
   @OutputDirectory
   abstract DirectoryProperty getResourcesDir()

   @TaskAction
   def generateResources() {
     def generated = resourcesDir.file("myGeneratedResource.properties").get().asFile
     generated.text = "message=Stay happy!"
   }
 }
 </pre>

 Find more information in <a href="#dir-java.lang.Object-"><code>dir(Object)</code></a> and <a href="#getDirs--"><code>getDirs()</code></a></div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== NESTED CLASS SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="nested.class.summary">
<!--   -->
</a>
<h3>Nested Class Summary</h3>
<ul class="blockList">
<li class="blockList"><a name="nested.classes.inherited.from.class.org.gradle.api.file.FileCollection">
<!--   -->
</a>
<h3>Nested classes/interfaces inherited from interface&nbsp;org.gradle.api.file.<a href="../file/FileCollection.html" title="interface in org.gradle.api.file">FileCollection</a></h3>
<code><a href="../file/FileCollection.AntType.html" title="enum in org.gradle.api.file">FileCollection.AntType</a></code></li>
</ul>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t3" class="tableTab"><span><a href="javascript:show(4);">Abstract Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colSecond" scope="col">Method</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#dir-java.lang.Object-">dir</a></span>&#8203;(java.lang.Object&nbsp;dir)</code></th>
<td class="colLast">
<div class="block">Registers an extra output dir.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#dir-java.util.Map-java.lang.Object-">dir</a></span>&#8203;(java.util.Map&lt;java.lang.String,&#8203;java.lang.Object&gt;&nbsp;options,
   java.lang.Object&nbsp;dir)</code></th>
<td class="colLast">
<div class="block">Registers an extra output dir and the builtBy information.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code><a href="../file/FileCollection.html" title="interface in org.gradle.api.file">FileCollection</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getClassesDirs--">getClassesDirs</a></span>()</code></th>
<td class="colLast">
<div class="block">Returns the directories containing compiled classes.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code><a href="../file/FileCollection.html" title="interface in org.gradle.api.file">FileCollection</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getDirs--">getDirs</a></span>()</code></th>
<td class="colLast">
<div class="block">Returns all dirs registered with #dir method.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code><a href="../file/FileCollection.html" title="interface in org.gradle.api.file">FileCollection</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getGeneratedSourcesDirs--">getGeneratedSourcesDirs</a></span>()</code></th>
<td class="colLast">
<div class="block">Returns the directories containing generated source files (e.g.</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>java.io.File</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getResourcesDir--">getResourcesDir</a></span>()</code></th>
<td class="colLast">
<div class="block">Returns the output directory for resources</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setResourcesDir-java.io.File-">setResourcesDir</a></span>&#8203;(java.io.File&nbsp;resourcesDir)</code></th>
<td class="colLast">
<div class="block">Sets the output directory for resources</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setResourcesDir-java.lang.Object-">setResourcesDir</a></span>&#8203;(java.lang.Object&nbsp;resourcesDir)</code></th>
<td class="colLast">
<div class="block">Sets the output directory for resources</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.org.gradle.api.Buildable">
<!--   -->
</a>
<h3>Methods inherited from interface&nbsp;org.gradle.api.<a href="../Buildable.html" title="interface in org.gradle.api">Buildable</a></h3>
<code><a href="../Buildable.html#getBuildDependencies--">getBuildDependencies</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.org.gradle.api.file.FileCollection">
<!--   -->
</a>
<h3>Methods inherited from interface&nbsp;org.gradle.api.file.<a href="../file/FileCollection.html" title="interface in org.gradle.api.file">FileCollection</a></h3>
<code><a href="../file/FileCollection.html#addToAntBuilder-java.lang.Object-java.lang.String-">addToAntBuilder</a>, <a href="../file/FileCollection.html#addToAntBuilder-java.lang.Object-java.lang.String-org.gradle.api.file.FileCollection.AntType-">addToAntBuilder</a>, <a href="../file/FileCollection.html#contains-java.io.File-">contains</a>, <a href="../file/FileCollection.html#filter-groovy.lang.Closure-">filter</a>, <a href="../file/FileCollection.html#filter-org.gradle.api.specs.Spec-">filter</a>, <a href="../file/FileCollection.html#getAsFileTree--">getAsFileTree</a>, <a href="../file/FileCollection.html#getAsPath--">getAsPath</a>, <a href="../file/FileCollection.html#getElements--">getElements</a>, <a href="../file/FileCollection.html#getFiles--">getFiles</a>, <a href="../file/FileCollection.html#getSingleFile--">getSingleFile</a>, <a href="../file/FileCollection.html#isEmpty--">isEmpty</a>, <a href="../file/FileCollection.html#minus-org.gradle.api.file.FileCollection-">minus</a>, <a href="../file/FileCollection.html#plus-org.gradle.api.file.FileCollection-">plus</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Iterable">
<!--   -->
</a>
<h3>Methods inherited from interface&nbsp;java.lang.Iterable</h3>
<code>forEach, iterator, spliterator</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="getClassesDirs--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getClassesDirs</h4>
<pre class="methodSignature"><a href="../file/FileCollection.html" title="interface in org.gradle.api.file">FileCollection</a>&nbsp;getClassesDirs()</pre>
<div class="block">Returns the directories containing compiled classes.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The classes directories. This value may safely be cast to a <a href="../file/ConfigurableFileCollection.html" title="interface in org.gradle.api.file"><code>ConfigurableFileCollection</code></a>.</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>4.0</dd>
</dl>
</li>
</ul>
<a name="getResourcesDir--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getResourcesDir</h4>
<pre class="methodSignature">@Nullable
java.io.File&nbsp;getResourcesDir()</pre>
<div class="block">Returns the output directory for resources
 <p>
 See example at <a href="SourceSetOutput.html" title="interface in org.gradle.api.tasks"><code>SourceSetOutput</code></a></div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The dir resources are copied to.</dd>
</dl>
</li>
</ul>
<a name="setResourcesDir-java.io.File-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setResourcesDir</h4>
<pre class="methodSignature">void&nbsp;setResourcesDir&#8203;(java.io.File&nbsp;resourcesDir)</pre>
<div class="block">Sets the output directory for resources
 <p>
 See example at <a href="SourceSetOutput.html" title="interface in org.gradle.api.tasks"><code>SourceSetOutput</code></a></div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>resourcesDir</code> - the resources dir. Should not be null.</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>4.0</dd>
</dl>
</li>
</ul>
<a name="setResourcesDir-java.lang.Object-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setResourcesDir</h4>
<pre class="methodSignature">void&nbsp;setResourcesDir&#8203;(java.lang.Object&nbsp;resourcesDir)</pre>
<div class="block">Sets the output directory for resources
 <p>
 See example at <a href="SourceSetOutput.html" title="interface in org.gradle.api.tasks"><code>SourceSetOutput</code></a></div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>resourcesDir</code> - the resources dir. Should not be null.</dd>
</dl>
</li>
</ul>
<a name="dir-java.util.Map-java.lang.Object-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>dir</h4>
<pre class="methodSignature">void&nbsp;dir&#8203;(java.util.Map&lt;java.lang.String,&#8203;java.lang.Object&gt;&nbsp;options,
         java.lang.Object&nbsp;dir)</pre>
<div class="block">Registers an extra output dir and the builtBy information. Useful for generated resources.
 <p>
 See example at <a href="SourceSetOutput.html" title="interface in org.gradle.api.tasks"><code>SourceSetOutput</code></a></div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>options</code> - - use 'builtBy' key to configure the 'builtBy' task of the dir</dd>
<dd><code>dir</code> - - will be resolved as <a href="../Project.html#file-java.lang.Object-"><code>Project.file(Object)</code></a></dd>
</dl>
</li>
</ul>
<a name="dir-java.lang.Object-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>dir</h4>
<pre class="methodSignature">void&nbsp;dir&#8203;(java.lang.Object&nbsp;dir)</pre>
<div class="block">Registers an extra output dir. Useful for generated resources.
 <p>
 See example at <a href="SourceSetOutput.html" title="interface in org.gradle.api.tasks"><code>SourceSetOutput</code></a></div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>dir</code> - - will be resolved as <a href="../Project.html#file-java.lang.Object-"><code>Project.file(Object)</code></a></dd>
</dl>
</li>
</ul>
<a name="getDirs--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getDirs</h4>
<pre class="methodSignature"><a href="../file/FileCollection.html" title="interface in org.gradle.api.file">FileCollection</a>&nbsp;getDirs()</pre>
<div class="block">Returns all dirs registered with #dir method.
 Each file is resolved as <a href="../Project.html#file-java.lang.Object-"><code>Project.file(Object)</code></a>
 <p>
 See example at <a href="SourceSetOutput.html" title="interface in org.gradle.api.tasks"><code>SourceSetOutput</code></a></div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>a new instance of registered dirs with resolved files</dd>
</dl>
</li>
</ul>
<a name="getGeneratedSourcesDirs--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>getGeneratedSourcesDirs</h4>
<pre class="methodSignature"><a href="../file/FileCollection.html" title="interface in org.gradle.api.file">FileCollection</a>&nbsp;getGeneratedSourcesDirs()</pre>
<div class="block">Returns the directories containing generated source files (e.g. by annotation processors during compilation).</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The generated sources directories. Never returns null.</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>5.2</dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li><a href="#nested.class.summary">Nested</a>&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
