<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc -->
<title>TestLoggingContainer (Gradle API 8.4)</title>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<link rel="stylesheet" type="text/css" href="../../../../../../javadoc.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../../jquery/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../../jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="../../../../../../script.js"></script>
<script type="text/javascript" src="../../../../../../jquery/jszip/dist/jszip.min.js"></script>
<script type="text/javascript" src="../../../../../../jquery/jszip-utils/dist/jszip-utils.min.js"></script>
<!--[if IE]>
<script type="text/javascript" src="../../../../../../jquery/jszip-utils/dist/jszip-utils-ie.min.js"></script>
<![endif]-->
<script type="text/javascript" src="../../../../../../jquery/jquery-3.6.0.min.js"></script>
<script type="text/javascript" src="../../../../../../jquery/jquery-ui.min.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="TestLoggingContainer (Gradle API 8.4)";
        }
    }
    catch(err) {
    }
//-->
var data = {"i0":6,"i1":6,"i2":6,"i3":6,"i4":6,"i5":6,"i6":6,"i7":6,"i8":6,"i9":6,"i10":6,"i11":6,"i12":6,"i13":6,"i14":6,"i15":6,"i16":6,"i17":6,"i18":6};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],4:["t3","Abstract Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
var pathtoroot = "../../../../../../";
var useModuleDirectories = false;
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="fixedNav">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../../../allclasses.html">All&nbsp;Classes</a></li>
</ul>
<ul class="navListSearch">
<li><label for="search">SEARCH:</label>
<input type="text" id="search" value="search" disabled="disabled">
<input type="reset" id="reset" value="reset" disabled="disabled">
</li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
</div>
<div class="navPadding">&nbsp;</div>
<script type="text/javascript"><!--
$('.navPadding').css('padding-top', $('.fixedNav').css("height"));
//-->
</script>
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle"><span class="packageLabelInType">Package</span>&nbsp;<a href="package-summary.html">org.gradle.api.tasks.testing.logging</a></div>
<h2 title="Interface TestLoggingContainer" class="title">Interface TestLoggingContainer</h2>
</div>
<div class="contentContainer">
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Superinterfaces:</dt>
<dd><code><a href="TestLogging.html" title="interface in org.gradle.api.tasks.testing.logging">TestLogging</a></code></dd>
</dl>
<hr>
<pre>public interface <span class="typeNameLabel">TestLoggingContainer</span>
extends <a href="TestLogging.html" title="interface in org.gradle.api.tasks.testing.logging">TestLogging</a></pre>
<div class="block">Container for all test logging related options. Different options
 can be set for each log level. Options that are set directly (without
 specifying a log level) apply to log level LIFECYCLE. Example:

 <pre class='autoTested'>
 apply plugin: 'java'

 test {
     testLogging {
         // set options for log level LIFECYCLE
         events "failed"
         exceptionFormat "short"

         // set options for log level DEBUG
         debug {
             events "started", "skipped", "failed"
             exceptionFormat "full"
         }

         // remove standard output/error logging from --info builds
         // by assigning only 'failed' and 'skipped' events
         info.events = ["failed", "skipped"]
     }
 }
 </pre>

 The defaults that are in place show progressively more information
 on log levels WARN, LIFECYCLE, INFO, and DEBUG, respectively.</div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t3" class="tableTab"><span><a href="javascript:show(4);">Abstract Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colSecond" scope="col">Method</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#debug-org.gradle.api.Action-">debug</a></span>&#8203;(<a href="../../../Action.html" title="interface in org.gradle.api">Action</a>&lt;<a href="TestLogging.html" title="interface in org.gradle.api.tasks.testing.logging">TestLogging</a>&gt;&nbsp;action)</code></th>
<td class="colLast">
<div class="block">Configures logging options for debug level.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#error-org.gradle.api.Action-">error</a></span>&#8203;(<a href="../../../Action.html" title="interface in org.gradle.api">Action</a>&lt;<a href="TestLogging.html" title="interface in org.gradle.api.tasks.testing.logging">TestLogging</a>&gt;&nbsp;action)</code></th>
<td class="colLast">
<div class="block">Configures logging options for error level.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code><a href="TestLogging.html" title="interface in org.gradle.api.tasks.testing.logging">TestLogging</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#get-org.gradle.api.logging.LogLevel-">get</a></span>&#8203;(<a href="../../../logging/LogLevel.html" title="enum in org.gradle.api.logging">LogLevel</a>&nbsp;level)</code></th>
<td class="colLast">
<div class="block">Returns logging options for the specified level.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code><a href="TestLogging.html" title="interface in org.gradle.api.tasks.testing.logging">TestLogging</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getDebug--">getDebug</a></span>()</code></th>
<td class="colLast">
<div class="block">Returns logging options for debug level.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code><a href="TestLogging.html" title="interface in org.gradle.api.tasks.testing.logging">TestLogging</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getError--">getError</a></span>()</code></th>
<td class="colLast">
<div class="block">Returns logging options for error level.</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code><a href="TestLogging.html" title="interface in org.gradle.api.tasks.testing.logging">TestLogging</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getInfo--">getInfo</a></span>()</code></th>
<td class="colLast">
<div class="block">Gets logging options for info level.</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code><a href="TestLogging.html" title="interface in org.gradle.api.tasks.testing.logging">TestLogging</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getLifecycle--">getLifecycle</a></span>()</code></th>
<td class="colLast">
<div class="block">Returns logging options for lifecycle level.</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code><a href="TestLogging.html" title="interface in org.gradle.api.tasks.testing.logging">TestLogging</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getQuiet--">getQuiet</a></span>()</code></th>
<td class="colLast">
<div class="block">Returns logging options for quiet level.</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code><a href="TestLogging.html" title="interface in org.gradle.api.tasks.testing.logging">TestLogging</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getWarn--">getWarn</a></span>()</code></th>
<td class="colLast">
<div class="block">Gets logging options for warn level.</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#info-org.gradle.api.Action-">info</a></span>&#8203;(<a href="../../../Action.html" title="interface in org.gradle.api">Action</a>&lt;<a href="TestLogging.html" title="interface in org.gradle.api.tasks.testing.logging">TestLogging</a>&gt;&nbsp;action)</code></th>
<td class="colLast">
<div class="block">Configures logging options for info level.</div>
</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#lifecycle-org.gradle.api.Action-">lifecycle</a></span>&#8203;(<a href="../../../Action.html" title="interface in org.gradle.api">Action</a>&lt;<a href="TestLogging.html" title="interface in org.gradle.api.tasks.testing.logging">TestLogging</a>&gt;&nbsp;action)</code></th>
<td class="colLast">
<div class="block">Configures logging options for lifecycle level.</div>
</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#quiet-org.gradle.api.Action-">quiet</a></span>&#8203;(<a href="../../../Action.html" title="interface in org.gradle.api">Action</a>&lt;<a href="TestLogging.html" title="interface in org.gradle.api.tasks.testing.logging">TestLogging</a>&gt;&nbsp;action)</code></th>
<td class="colLast">
<div class="block">Configures logging options for quiet level.</div>
</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setDebug-org.gradle.api.tasks.testing.logging.TestLogging-">setDebug</a></span>&#8203;(<a href="TestLogging.html" title="interface in org.gradle.api.tasks.testing.logging">TestLogging</a>&nbsp;logging)</code></th>
<td class="colLast">
<div class="block">Sets logging options for debug level.</div>
</td>
</tr>
<tr id="i13" class="rowColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setError-org.gradle.api.tasks.testing.logging.TestLogging-">setError</a></span>&#8203;(<a href="TestLogging.html" title="interface in org.gradle.api.tasks.testing.logging">TestLogging</a>&nbsp;logging)</code></th>
<td class="colLast">
<div class="block">Sets logging options for error level.</div>
</td>
</tr>
<tr id="i14" class="altColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setInfo-org.gradle.api.tasks.testing.logging.TestLogging-">setInfo</a></span>&#8203;(<a href="TestLogging.html" title="interface in org.gradle.api.tasks.testing.logging">TestLogging</a>&nbsp;logging)</code></th>
<td class="colLast">
<div class="block">Sets logging options for info level.</div>
</td>
</tr>
<tr id="i15" class="rowColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setLifecycle-org.gradle.api.tasks.testing.logging.TestLogging-">setLifecycle</a></span>&#8203;(<a href="TestLogging.html" title="interface in org.gradle.api.tasks.testing.logging">TestLogging</a>&nbsp;logging)</code></th>
<td class="colLast">
<div class="block">Sets logging options for lifecycle level.</div>
</td>
</tr>
<tr id="i16" class="altColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setQuiet-org.gradle.api.tasks.testing.logging.TestLogging-">setQuiet</a></span>&#8203;(<a href="TestLogging.html" title="interface in org.gradle.api.tasks.testing.logging">TestLogging</a>&nbsp;logging)</code></th>
<td class="colLast">
<div class="block">Sets logging options for quiet level.</div>
</td>
</tr>
<tr id="i17" class="rowColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setWarn-org.gradle.api.tasks.testing.logging.TestLogging-">setWarn</a></span>&#8203;(<a href="TestLogging.html" title="interface in org.gradle.api.tasks.testing.logging">TestLogging</a>&nbsp;logging)</code></th>
<td class="colLast">
<div class="block">Sets logging options for warn level.</div>
</td>
</tr>
<tr id="i18" class="altColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#warn-org.gradle.api.Action-">warn</a></span>&#8203;(<a href="../../../Action.html" title="interface in org.gradle.api">Action</a>&lt;<a href="TestLogging.html" title="interface in org.gradle.api.tasks.testing.logging">TestLogging</a>&gt;&nbsp;action)</code></th>
<td class="colLast">
<div class="block">Configures logging options for warn level.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.org.gradle.api.tasks.testing.logging.TestLogging">
<!--   -->
</a>
<h3>Methods inherited from interface&nbsp;org.gradle.api.tasks.testing.logging.<a href="TestLogging.html" title="interface in org.gradle.api.tasks.testing.logging">TestLogging</a></h3>
<code><a href="TestLogging.html#events-java.lang.Object...-">events</a>, <a href="TestLogging.html#getDisplayGranularity--">getDisplayGranularity</a>, <a href="TestLogging.html#getEvents--">getEvents</a>, <a href="TestLogging.html#getExceptionFormat--">getExceptionFormat</a>, <a href="TestLogging.html#getMaxGranularity--">getMaxGranularity</a>, <a href="TestLogging.html#getMinGranularity--">getMinGranularity</a>, <a href="TestLogging.html#getShowCauses--">getShowCauses</a>, <a href="TestLogging.html#getShowExceptions--">getShowExceptions</a>, <a href="TestLogging.html#getShowStackTraces--">getShowStackTraces</a>, <a href="TestLogging.html#getShowStandardStreams--">getShowStandardStreams</a>, <a href="TestLogging.html#getStackTraceFilters--">getStackTraceFilters</a>, <a href="TestLogging.html#setDisplayGranularity-int-">setDisplayGranularity</a>, <a href="TestLogging.html#setEvents-java.lang.Iterable-">setEvents</a>, <a href="TestLogging.html#setEvents-java.util.Set-">setEvents</a>, <a href="TestLogging.html#setExceptionFormat-java.lang.Object-">setExceptionFormat</a>, <a href="TestLogging.html#setExceptionFormat-org.gradle.api.tasks.testing.logging.TestExceptionFormat-">setExceptionFormat</a>, <a href="TestLogging.html#setMaxGranularity-int-">setMaxGranularity</a>, <a href="TestLogging.html#setMinGranularity-int-">setMinGranularity</a>, <a href="TestLogging.html#setShowCauses-boolean-">setShowCauses</a>, <a href="TestLogging.html#setShowExceptions-boolean-">setShowExceptions</a>, <a href="TestLogging.html#setShowStackTraces-boolean-">setShowStackTraces</a>, <a href="TestLogging.html#setShowStandardStreams-boolean-">setShowStandardStreams</a>, <a href="TestLogging.html#setStackTraceFilters-java.lang.Iterable-">setStackTraceFilters</a>, <a href="TestLogging.html#setStackTraceFilters-java.util.Set-">setStackTraceFilters</a>, <a href="TestLogging.html#stackTraceFilters-java.lang.Object...-">stackTraceFilters</a></code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="getDebug--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getDebug</h4>
<pre class="methodSignature"><a href="TestLogging.html" title="interface in org.gradle.api.tasks.testing.logging">TestLogging</a>&nbsp;getDebug()</pre>
<div class="block">Returns logging options for debug level.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>logging options for debug level</dd>
</dl>
</li>
</ul>
<a name="setDebug-org.gradle.api.tasks.testing.logging.TestLogging-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setDebug</h4>
<pre class="methodSignature">void&nbsp;setDebug&#8203;(<a href="TestLogging.html" title="interface in org.gradle.api.tasks.testing.logging">TestLogging</a>&nbsp;logging)</pre>
<div class="block">Sets logging options for debug level.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>logging</code> - logging options for debug level</dd>
</dl>
</li>
</ul>
<a name="debug-org.gradle.api.Action-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>debug</h4>
<pre class="methodSignature">void&nbsp;debug&#8203;(<a href="../../../Action.html" title="interface in org.gradle.api">Action</a>&lt;<a href="TestLogging.html" title="interface in org.gradle.api.tasks.testing.logging">TestLogging</a>&gt;&nbsp;action)</pre>
<div class="block">Configures logging options for debug level.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>action</code> - logging options for debug level</dd>
</dl>
</li>
</ul>
<a name="getInfo--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getInfo</h4>
<pre class="methodSignature"><a href="TestLogging.html" title="interface in org.gradle.api.tasks.testing.logging">TestLogging</a>&nbsp;getInfo()</pre>
<div class="block">Gets logging options for info level.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>logging options for info level</dd>
</dl>
</li>
</ul>
<a name="setInfo-org.gradle.api.tasks.testing.logging.TestLogging-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setInfo</h4>
<pre class="methodSignature">void&nbsp;setInfo&#8203;(<a href="TestLogging.html" title="interface in org.gradle.api.tasks.testing.logging">TestLogging</a>&nbsp;logging)</pre>
<div class="block">Sets logging options for info level.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>logging</code> - logging options for info level</dd>
</dl>
</li>
</ul>
<a name="info-org.gradle.api.Action-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>info</h4>
<pre class="methodSignature">void&nbsp;info&#8203;(<a href="../../../Action.html" title="interface in org.gradle.api">Action</a>&lt;<a href="TestLogging.html" title="interface in org.gradle.api.tasks.testing.logging">TestLogging</a>&gt;&nbsp;action)</pre>
<div class="block">Configures logging options for info level.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>action</code> - logging options for info level</dd>
</dl>
</li>
</ul>
<a name="getLifecycle--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getLifecycle</h4>
<pre class="methodSignature"><a href="TestLogging.html" title="interface in org.gradle.api.tasks.testing.logging">TestLogging</a>&nbsp;getLifecycle()</pre>
<div class="block">Returns logging options for lifecycle level.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>logging options for lifecycle level</dd>
</dl>
</li>
</ul>
<a name="setLifecycle-org.gradle.api.tasks.testing.logging.TestLogging-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setLifecycle</h4>
<pre class="methodSignature">void&nbsp;setLifecycle&#8203;(<a href="TestLogging.html" title="interface in org.gradle.api.tasks.testing.logging">TestLogging</a>&nbsp;logging)</pre>
<div class="block">Sets logging options for lifecycle level.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>logging</code> - logging options for lifecycle level</dd>
</dl>
</li>
</ul>
<a name="lifecycle-org.gradle.api.Action-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>lifecycle</h4>
<pre class="methodSignature">void&nbsp;lifecycle&#8203;(<a href="../../../Action.html" title="interface in org.gradle.api">Action</a>&lt;<a href="TestLogging.html" title="interface in org.gradle.api.tasks.testing.logging">TestLogging</a>&gt;&nbsp;action)</pre>
<div class="block">Configures logging options for lifecycle level.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>action</code> - logging options for lifecycle level</dd>
</dl>
</li>
</ul>
<a name="getWarn--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getWarn</h4>
<pre class="methodSignature"><a href="TestLogging.html" title="interface in org.gradle.api.tasks.testing.logging">TestLogging</a>&nbsp;getWarn()</pre>
<div class="block">Gets logging options for warn level.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>logging options for warn level</dd>
</dl>
</li>
</ul>
<a name="setWarn-org.gradle.api.tasks.testing.logging.TestLogging-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setWarn</h4>
<pre class="methodSignature">void&nbsp;setWarn&#8203;(<a href="TestLogging.html" title="interface in org.gradle.api.tasks.testing.logging">TestLogging</a>&nbsp;logging)</pre>
<div class="block">Sets logging options for warn level.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>logging</code> - logging options for warn level</dd>
</dl>
</li>
</ul>
<a name="warn-org.gradle.api.Action-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>warn</h4>
<pre class="methodSignature">void&nbsp;warn&#8203;(<a href="../../../Action.html" title="interface in org.gradle.api">Action</a>&lt;<a href="TestLogging.html" title="interface in org.gradle.api.tasks.testing.logging">TestLogging</a>&gt;&nbsp;action)</pre>
<div class="block">Configures logging options for warn level.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>action</code> - logging options for warn level</dd>
</dl>
</li>
</ul>
<a name="getQuiet--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getQuiet</h4>
<pre class="methodSignature"><a href="TestLogging.html" title="interface in org.gradle.api.tasks.testing.logging">TestLogging</a>&nbsp;getQuiet()</pre>
<div class="block">Returns logging options for quiet level.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>logging options for quiet level</dd>
</dl>
</li>
</ul>
<a name="setQuiet-org.gradle.api.tasks.testing.logging.TestLogging-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setQuiet</h4>
<pre class="methodSignature">void&nbsp;setQuiet&#8203;(<a href="TestLogging.html" title="interface in org.gradle.api.tasks.testing.logging">TestLogging</a>&nbsp;logging)</pre>
<div class="block">Sets logging options for quiet level.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>logging</code> - logging options for quiet level</dd>
</dl>
</li>
</ul>
<a name="quiet-org.gradle.api.Action-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>quiet</h4>
<pre class="methodSignature">void&nbsp;quiet&#8203;(<a href="../../../Action.html" title="interface in org.gradle.api">Action</a>&lt;<a href="TestLogging.html" title="interface in org.gradle.api.tasks.testing.logging">TestLogging</a>&gt;&nbsp;action)</pre>
<div class="block">Configures logging options for quiet level.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>action</code> - logging options for quiet level</dd>
</dl>
</li>
</ul>
<a name="getError--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getError</h4>
<pre class="methodSignature"><a href="TestLogging.html" title="interface in org.gradle.api.tasks.testing.logging">TestLogging</a>&nbsp;getError()</pre>
<div class="block">Returns logging options for error level.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>logging options for error level</dd>
</dl>
</li>
</ul>
<a name="setError-org.gradle.api.tasks.testing.logging.TestLogging-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setError</h4>
<pre class="methodSignature">void&nbsp;setError&#8203;(<a href="TestLogging.html" title="interface in org.gradle.api.tasks.testing.logging">TestLogging</a>&nbsp;logging)</pre>
<div class="block">Sets logging options for error level.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>logging</code> - logging options for error level</dd>
</dl>
</li>
</ul>
<a name="error-org.gradle.api.Action-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>error</h4>
<pre class="methodSignature">void&nbsp;error&#8203;(<a href="../../../Action.html" title="interface in org.gradle.api">Action</a>&lt;<a href="TestLogging.html" title="interface in org.gradle.api.tasks.testing.logging">TestLogging</a>&gt;&nbsp;action)</pre>
<div class="block">Configures logging options for error level.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>action</code> - logging options for error level</dd>
</dl>
</li>
</ul>
<a name="get-org.gradle.api.logging.LogLevel-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>get</h4>
<pre class="methodSignature"><a href="TestLogging.html" title="interface in org.gradle.api.tasks.testing.logging">TestLogging</a>&nbsp;get&#8203;(<a href="../../../logging/LogLevel.html" title="enum in org.gradle.api.logging">LogLevel</a>&nbsp;level)</pre>
<div class="block">Returns logging options for the specified level.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>level</code> - the level whose logging options are to be returned</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>logging options for the specified level</dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../../../allclasses.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
