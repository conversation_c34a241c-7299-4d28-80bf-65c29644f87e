<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc -->
<title>IvyModuleDescriptorSpec (Gradle API 8.4)</title>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<link rel="stylesheet" type="text/css" href="../../../../../javadoc.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../jquery/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
<script type="text/javascript" src="../../../../../jquery/jszip/dist/jszip.min.js"></script>
<script type="text/javascript" src="../../../../../jquery/jszip-utils/dist/jszip-utils.min.js"></script>
<!--[if IE]>
<script type="text/javascript" src="../../../../../jquery/jszip-utils/dist/jszip-utils-ie.min.js"></script>
<![endif]-->
<script type="text/javascript" src="../../../../../jquery/jquery-3.6.0.min.js"></script>
<script type="text/javascript" src="../../../../../jquery/jquery-ui.min.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="IvyModuleDescriptorSpec (Gradle API 8.4)";
        }
    }
    catch(err) {
    }
//-->
var data = {"i0":6,"i1":6,"i2":6,"i3":6,"i4":6,"i5":6,"i6":6,"i7":6,"i8":6,"i9":6};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],4:["t3","Abstract Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
var pathtoroot = "../../../../../";
var useModuleDirectories = false;
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="fixedNav">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../../allclasses.html">All&nbsp;Classes</a></li>
</ul>
<ul class="navListSearch">
<li><label for="search">SEARCH:</label>
<input type="text" id="search" value="search" disabled="disabled">
<input type="reset" id="reset" value="reset" disabled="disabled">
</li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
</div>
<div class="navPadding">&nbsp;</div>
<script type="text/javascript"><!--
$('.navPadding').css('padding-top', $('.fixedNav').css("height"));
//-->
</script>
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle"><span class="packageLabelInType">Package</span>&nbsp;<a href="package-summary.html">org.gradle.api.publish.ivy</a></div>
<h2 title="Interface IvyModuleDescriptorSpec" class="title">Interface IvyModuleDescriptorSpec</h2>
</div>
<div class="contentContainer">
<div class="description">
<ul class="blockList">
<li class="blockList">
<hr>
<pre>public interface <span class="typeNameLabel">IvyModuleDescriptorSpec</span></pre>
<div class="block">The descriptor of any Ivy publication.
 <p>
 Corresponds to the <a href="http://ant.apache.org/ivy/history/latest-milestone/ivyfile.html">XML version of the Ivy Module Descriptor</a>.
 <p>
 The <a href="#withXml-org.gradle.api.Action-"><code>withXml(org.gradle.api.Action)</code></a> method can be used to modify the descriptor after it has been generated according to the publication data.
 However, the preferred way to customize the project information to be published is to use the dedicated configuration methods exposed by this class, e.g.
 <a href="#description-org.gradle.api.Action-"><code>description(Action)</code></a>.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>1.3</dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t3" class="tableTab"><span><a href="javascript:show(4);">Abstract Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colSecond" scope="col">Method</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#author-org.gradle.api.Action-">author</a></span>&#8203;(<a href="../../Action.html" title="interface in org.gradle.api">Action</a>&lt;? super <a href="IvyModuleDescriptorAuthor.html" title="interface in org.gradle.api.publish.ivy">IvyModuleDescriptorAuthor</a>&gt;&nbsp;action)</code></th>
<td class="colLast">
<div class="block">Creates, configures and adds an author to this publication.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#description-org.gradle.api.Action-">description</a></span>&#8203;(<a href="../../Action.html" title="interface in org.gradle.api">Action</a>&lt;? super <a href="IvyModuleDescriptorDescription.html" title="interface in org.gradle.api.publish.ivy">IvyModuleDescriptorDescription</a>&gt;&nbsp;action)</code></th>
<td class="colLast">
<div class="block">Configures the description for this publication.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#extraInfo-java.lang.String-java.lang.String-java.lang.String-">extraInfo</a></span>&#8203;(java.lang.String&nbsp;namespace,
         java.lang.String&nbsp;elementName,
         java.lang.String&nbsp;value)</code></th>
<td class="colLast">
<div class="block">Adds a new extra info element to the publication</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>java.lang.String</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getBranch--">getBranch</a></span>()</code></th>
<td class="colLast">
<div class="block">Returns the branch for this publication</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code><a href="IvyExtraInfoSpec.html" title="interface in org.gradle.api.publish.ivy">IvyExtraInfoSpec</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getExtraInfo--">getExtraInfo</a></span>()</code></th>
<td class="colLast">
<div class="block">Returns the extra info element spec for this publication</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>java.lang.String</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getStatus--">getStatus</a></span>()</code></th>
<td class="colLast">
<div class="block">Returns the status for this publication.</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#license-org.gradle.api.Action-">license</a></span>&#8203;(<a href="../../Action.html" title="interface in org.gradle.api">Action</a>&lt;? super <a href="IvyModuleDescriptorLicense.html" title="interface in org.gradle.api.publish.ivy">IvyModuleDescriptorLicense</a>&gt;&nbsp;action)</code></th>
<td class="colLast">
<div class="block">Creates, configures and adds a license to this publication.</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setBranch-java.lang.String-">setBranch</a></span>&#8203;(java.lang.String&nbsp;branch)</code></th>
<td class="colLast">
<div class="block">Sets the branch for this publication</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setStatus-java.lang.String-">setStatus</a></span>&#8203;(java.lang.String&nbsp;status)</code></th>
<td class="colLast">
<div class="block">Sets the status for this publication.</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#withXml-org.gradle.api.Action-">withXml</a></span>&#8203;(<a href="../../Action.html" title="interface in org.gradle.api">Action</a>&lt;? super <a href="../../XmlProvider.html" title="interface in org.gradle.api">XmlProvider</a>&gt;&nbsp;action)</code></th>
<td class="colLast">
<div class="block">Allow configuration of the descriptor, after it has been generated according to the input data.</div>
</td>
</tr>
</table>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="withXml-org.gradle.api.Action-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>withXml</h4>
<pre class="methodSignature">void&nbsp;withXml&#8203;(<a href="../../Action.html" title="interface in org.gradle.api">Action</a>&lt;? super <a href="../../XmlProvider.html" title="interface in org.gradle.api">XmlProvider</a>&gt;&nbsp;action)</pre>
<div class="block">Allow configuration of the descriptor, after it has been generated according to the input data.

 <pre class='autoTested'>
 plugins {
     id 'ivy-publish'
 }

 publishing {
   publications {
     ivy(IvyPublication) {
       descriptor {
         withXml {
           asNode().dependencies.dependency.find { it.@org == "junit" }.@rev = "4.10"
         }
       }
     }
   }
 }
 </pre>

 Note that due to Gradle's internal type conversion system, you can pass a Groovy closure to this method and
 it will be automatically converted to an <code>Action</code>.
 <p>
 Each action/closure passed to this method will be stored as a callback, and executed when the publication
 that this descriptor is attached to is published.
 <p>
 For details on the structure of the XML to be modified, see <a href="http://ant.apache.org/ivy/history/latest-milestone/ivyfile.html">the
 Ivy Module Descriptor reference</a>.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>action</code> - The configuration action.</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="IvyPublication.html" title="interface in org.gradle.api.publish.ivy"><code>IvyPublication</code></a>, 
<a href="../../XmlProvider.html" title="interface in org.gradle.api"><code>XmlProvider</code></a></dd>
</dl>
</li>
</ul>
<a name="getStatus--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getStatus</h4>
<pre class="methodSignature">@Nullable
java.lang.String&nbsp;getStatus()</pre>
<div class="block">Returns the status for this publication.</div>
</li>
</ul>
<a name="setStatus-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setStatus</h4>
<pre class="methodSignature">void&nbsp;setStatus&#8203;(@Nullable
               java.lang.String&nbsp;status)</pre>
<div class="block">Sets the status for this publication.</div>
</li>
</ul>
<a name="getBranch--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getBranch</h4>
<pre class="methodSignature">@Nullable
java.lang.String&nbsp;getBranch()</pre>
<div class="block">Returns the branch for this publication</div>
</li>
</ul>
<a name="setBranch-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setBranch</h4>
<pre class="methodSignature">void&nbsp;setBranch&#8203;(@Nullable
               java.lang.String&nbsp;branch)</pre>
<div class="block">Sets the branch for this publication</div>
</li>
</ul>
<a name="getExtraInfo--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getExtraInfo</h4>
<pre class="methodSignature"><a href="IvyExtraInfoSpec.html" title="interface in org.gradle.api.publish.ivy">IvyExtraInfoSpec</a>&nbsp;getExtraInfo()</pre>
<div class="block">Returns the extra info element spec for this publication</div>
</li>
</ul>
<a name="extraInfo-java.lang.String-java.lang.String-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>extraInfo</h4>
<pre class="methodSignature">void&nbsp;extraInfo&#8203;(java.lang.String&nbsp;namespace,
               java.lang.String&nbsp;elementName,
               java.lang.String&nbsp;value)</pre>
<div class="block">Adds a new extra info element to the publication</div>
</li>
</ul>
<a name="license-org.gradle.api.Action-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>license</h4>
<pre class="methodSignature">void&nbsp;license&#8203;(<a href="../../Action.html" title="interface in org.gradle.api">Action</a>&lt;? super <a href="IvyModuleDescriptorLicense.html" title="interface in org.gradle.api.publish.ivy">IvyModuleDescriptorLicense</a>&gt;&nbsp;action)</pre>
<div class="block">Creates, configures and adds a license to this publication.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>4.8</dd>
</dl>
</li>
</ul>
<a name="author-org.gradle.api.Action-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>author</h4>
<pre class="methodSignature">void&nbsp;author&#8203;(<a href="../../Action.html" title="interface in org.gradle.api">Action</a>&lt;? super <a href="IvyModuleDescriptorAuthor.html" title="interface in org.gradle.api.publish.ivy">IvyModuleDescriptorAuthor</a>&gt;&nbsp;action)</pre>
<div class="block">Creates, configures and adds an author to this publication.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>4.8</dd>
</dl>
</li>
</ul>
<a name="description-org.gradle.api.Action-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>description</h4>
<pre class="methodSignature">void&nbsp;description&#8203;(<a href="../../Action.html" title="interface in org.gradle.api">Action</a>&lt;? super <a href="IvyModuleDescriptorDescription.html" title="interface in org.gradle.api.publish.ivy">IvyModuleDescriptorDescription</a>&gt;&nbsp;action)</pre>
<div class="block">Configures the description for this publication.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>4.8</dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../../allclasses.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
