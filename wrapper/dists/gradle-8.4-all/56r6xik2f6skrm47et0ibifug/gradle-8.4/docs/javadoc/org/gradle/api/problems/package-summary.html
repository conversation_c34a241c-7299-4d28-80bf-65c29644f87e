<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc -->
<title>org.gradle.api.problems (Gradle API 8.4)</title>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<link rel="stylesheet" type="text/css" href="../../../../javadoc.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../jquery/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
<script type="text/javascript" src="../../../../jquery/jszip/dist/jszip.min.js"></script>
<script type="text/javascript" src="../../../../jquery/jszip-utils/dist/jszip-utils.min.js"></script>
<!--[if IE]>
<script type="text/javascript" src="../../../../jquery/jszip-utils/dist/jszip-utils-ie.min.js"></script>
<![endif]-->
<script type="text/javascript" src="../../../../jquery/jquery-3.6.0.min.js"></script>
<script type="text/javascript" src="../../../../jquery/jquery-ui.min.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="org.gradle.api.problems (Gradle API 8.4)";
        }
    }
    catch(err) {
    }
//-->
var pathtoroot = "../../../../";
var useModuleDirectories = false;
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="fixedNav">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../index.html">Overview</a></li>
<li class="navBarCell1Rev">Package</li>
<li>Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses.html">All&nbsp;Classes</a></li>
</ul>
<ul class="navListSearch">
<li><label for="search">SEARCH:</label>
<input type="text" id="search" value="search" disabled="disabled">
<input type="reset" id="reset" value="reset" disabled="disabled">
</li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
</div>
<div class="navPadding">&nbsp;</div>
<script type="text/javascript"><!--
$('.navPadding').css('padding-top', $('.fixedNav').css("height"));
//-->
</script>
<div class="header">
<p><a href="../NonNullApi.html" title="annotation in org.gradle.api">@NonNullApi</a>
</p>
<h1 title="Package" class="title">Package&nbsp;org.gradle.api.problems</h1>
</div>
<div class="contentContainer"><a name="package.description">
<!--   -->
</a>
<div class="block">new Problems API</div>
<ul class="blockList">
<li class="blockList">
<table class="typeSummary" summary="Interface Summary table, listing interfaces, and an explanation">
<caption><span>Interface Summary</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Interface</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tbody>
<tr class="altColor">
<th class="colFirst" scope="row"><a href="DocLink.html" title="interface in org.gradle.api.problems">DocLink</a></th>
<td class="colLast">
<div class="block">A link to a documentation page.</div>
</td>
</tr>
<tr class="rowColor">
<th class="colFirst" scope="row"><a href="Problem.html" title="interface in org.gradle.api.problems">Problem</a></th>
<td class="colLast">
<div class="block">Interface for describing structured information about a problem.</div>
</td>
</tr>
<tr class="altColor">
<th class="colFirst" scope="row"><a href="ProblemBuilder.html" title="interface in org.gradle.api.problems">ProblemBuilder</a></th>
<td class="colLast">
<div class="block"><a href="Problem.html" title="interface in org.gradle.api.problems"><code>Problem</code></a> instance builder allowing the specification of all optional fields.</div>
</td>
</tr>
<tr class="rowColor">
<th class="colFirst" scope="row"><a href="ProblemBuilderDefiningDocumentation.html" title="interface in org.gradle.api.problems">ProblemBuilderDefiningDocumentation</a></th>
<td class="colLast">
<div class="block"><a href="Problem.html" title="interface in org.gradle.api.problems"><code>Problem</code></a> instance builder requiring the specification of documentation.</div>
</td>
</tr>
<tr class="altColor">
<th class="colFirst" scope="row"><a href="ProblemBuilderDefiningGroup.html" title="interface in org.gradle.api.problems">ProblemBuilderDefiningGroup</a></th>
<td class="colLast">
<div class="block"><a href="Problem.html" title="interface in org.gradle.api.problems"><code>Problem</code></a> instance builder requiring the specification of the problem group.</div>
</td>
</tr>
<tr class="rowColor">
<th class="colFirst" scope="row"><a href="ProblemBuilderDefiningLabel.html" title="interface in org.gradle.api.problems">ProblemBuilderDefiningLabel</a></th>
<td class="colLast">
<div class="block"><a href="Problem.html" title="interface in org.gradle.api.problems"><code>Problem</code></a> instance builder requiring the specification of the problem description.</div>
</td>
</tr>
<tr class="altColor">
<th class="colFirst" scope="row"><a href="ProblemBuilderDefiningLocation.html" title="interface in org.gradle.api.problems">ProblemBuilderDefiningLocation</a></th>
<td class="colLast">
<div class="block"><a href="Problem.html" title="interface in org.gradle.api.problems"><code>Problem</code></a> instance builder requiring the specification of the problem location.</div>
</td>
</tr>
<tr class="rowColor">
<th class="colFirst" scope="row"><a href="ProblemBuilderDefiningType.html" title="interface in org.gradle.api.problems">ProblemBuilderDefiningType</a></th>
<td class="colLast">
<div class="block"><a href="Problem.html" title="interface in org.gradle.api.problems"><code>Problem</code></a> instance builder requiring the specification of the problem type.</div>
</td>
</tr>
<tr class="altColor">
<th class="colFirst" scope="row"><a href="ProblemBuilderSpec.html" title="interface in org.gradle.api.problems">ProblemBuilderSpec</a></th>
<td class="colLast">
<div class="block">A function that can be used to specify a <a href="Problem.html" title="interface in org.gradle.api.problems"><code>Problem</code></a> using a <a href="ProblemBuilder.html" title="interface in org.gradle.api.problems"><code>ProblemBuilder</code></a>.</div>
</td>
</tr>
<tr class="rowColor">
<th class="colFirst" scope="row"><a href="ProblemLocation.html" title="interface in org.gradle.api.problems">ProblemLocation</a></th>
<td class="colLast">
<div class="block">A basic problem location pointing to a specific part of a file.</div>
</td>
</tr>
<tr class="altColor">
<th class="colFirst" scope="row"><a href="Problems.html" title="interface in org.gradle.api.problems">Problems</a></th>
<td class="colLast">
<div class="block">Problems API service.</div>
</td>
</tr>
<tr class="rowColor">
<th class="colFirst" scope="row"><a href="ReportableProblem.html" title="interface in org.gradle.api.problems">ReportableProblem</a></th>
<td class="colLast">
<div class="block">Problem that can be submitted for external consumption (e.g.</div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList">
<table class="typeSummary" summary="Class Summary table, listing classes, and an explanation">
<caption><span>Class Summary</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Class</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tbody>
<tr class="altColor">
<th class="colFirst" scope="row"><a href="ProblemGroup.html" title="class in org.gradle.api.problems">ProblemGroup</a></th>
<td class="colLast">
<div class="block">Specifies the main categories of problems.</div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList">
<table class="typeSummary" summary="Enum Summary table, listing enums, and an explanation">
<caption><span>Enum Summary</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Enum</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tbody>
<tr class="altColor">
<th class="colFirst" scope="row"><a href="Severity.html" title="enum in org.gradle.api.problems">Severity</a></th>
<td class="colLast">
<div class="block">Severity.</div>
</td>
</tr>
</tbody>
</table>
</li>
</ul>
</div>
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../index.html">Overview</a></li>
<li class="navBarCell1Rev">Package</li>
<li>Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
