<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc -->
<title>CheckstyleExtension (Gradle API 8.4)</title>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<link rel="stylesheet" type="text/css" href="../../../../../javadoc.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../jquery/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
<script type="text/javascript" src="../../../../../jquery/jszip/dist/jszip.min.js"></script>
<script type="text/javascript" src="../../../../../jquery/jszip-utils/dist/jszip-utils.min.js"></script>
<!--[if IE]>
<script type="text/javascript" src="../../../../../jquery/jszip-utils/dist/jszip-utils-ie.min.js"></script>
<![endif]-->
<script type="text/javascript" src="../../../../../jquery/jquery-3.6.0.min.js"></script>
<script type="text/javascript" src="../../../../../jquery/jquery-ui.min.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="CheckstyleExtension (Gradle API 8.4)";
        }
    }
    catch(err) {
    }
//-->
var data = {"i0":10,"i1":10,"i2":10,"i3":10,"i4":10,"i5":10,"i6":10,"i7":10,"i8":10,"i9":10,"i10":10,"i11":10,"i12":10,"i13":10};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
var pathtoroot = "../../../../../";
var useModuleDirectories = false;
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="fixedNav">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../../allclasses.html">All&nbsp;Classes</a></li>
</ul>
<ul class="navListSearch">
<li><label for="search">SEARCH:</label>
<input type="text" id="search" value="search" disabled="disabled">
<input type="reset" id="reset" value="reset" disabled="disabled">
</li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
</div>
<div class="navPadding">&nbsp;</div>
<script type="text/javascript"><!--
$('.navPadding').css('padding-top', $('.fixedNav').css("height"));
//-->
</script>
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle"><span class="packageLabelInType">Package</span>&nbsp;<a href="package-summary.html">org.gradle.api.plugins.quality</a></div>
<h2 title="Class CheckstyleExtension" class="title">Class CheckstyleExtension</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li><a href="CodeQualityExtension.html" title="class in org.gradle.api.plugins.quality">org.gradle.api.plugins.quality.CodeQualityExtension</a></li>
<li>
<ul class="inheritance">
<li>org.gradle.api.plugins.quality.CheckstyleExtension</li>
</ul>
</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<hr>
<pre>public abstract class <span class="typeNameLabel">CheckstyleExtension</span>
extends <a href="CodeQualityExtension.html" title="class in org.gradle.api.plugins.quality">CodeQualityExtension</a></pre>
<div class="block">Configuration options for the Checkstyle plugin.</div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="CheckstylePlugin.html" title="class in org.gradle.api.plugins.quality"><code>CheckstylePlugin</code></a></dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="memberSummary" summary="Constructor Summary table, listing constructors, and an explanation">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Constructor</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tr class="altColor">
<th class="colConstructorName" scope="row"><code><span class="memberNameLink"><a href="#CheckstyleExtension-org.gradle.api.Project-">CheckstyleExtension</a></span>&#8203;(<a href="../../Project.html" title="interface in org.gradle.api">Project</a>&nbsp;project)</code></th>
<td class="colLast">&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colSecond" scope="col">Method</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code><a href="../../resources/TextResource.html" title="interface in org.gradle.api.resources">TextResource</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getConfig--">getConfig</a></span>()</code></th>
<td class="colLast">
<div class="block">The Checkstyle configuration to use.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code><a href="../../file/DirectoryProperty.html" title="interface in org.gradle.api.file">DirectoryProperty</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getConfigDirectory--">getConfigDirectory</a></span>()</code></th>
<td class="colLast">
<div class="block">Path to other Checkstyle configuration files.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>java.io.File</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getConfigFile--">getConfigFile</a></span>()</code></th>
<td class="colLast">
<div class="block">The Checkstyle configuration file to use.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>java.util.Map&lt;java.lang.String,&#8203;java.lang.Object&gt;</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getConfigProperties--">getConfigProperties</a></span>()</code></th>
<td class="colLast">
<div class="block">The properties available for use in the configuration file.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code><a href="../../provider/Property.html" title="interface in org.gradle.api.provider">Property</a>&lt;java.lang.Boolean&gt;</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getEnableExternalDtdLoad--">getEnableExternalDtdLoad</a></span>()</code></th>
<td class="colLast">
<div class="block">Enable the ability to use custom DTD files in config and load them from some location on all checkstyle tasks in this project.</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>int</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getMaxErrors--">getMaxErrors</a></span>()</code></th>
<td class="colLast">
<div class="block">The maximum number of errors that are tolerated before breaking the build
 or setting the failure property.</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>int</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getMaxWarnings--">getMaxWarnings</a></span>()</code></th>
<td class="colLast">
<div class="block">The maximum number of warnings that are tolerated before breaking the build
 or setting the failure property.</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#isShowViolations--">isShowViolations</a></span>()</code></th>
<td class="colLast">
<div class="block">Whether rule violations are to be displayed on the console.</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setConfig-org.gradle.api.resources.TextResource-">setConfig</a></span>&#8203;(<a href="../../resources/TextResource.html" title="interface in org.gradle.api.resources">TextResource</a>&nbsp;config)</code></th>
<td class="colLast">
<div class="block">The Checkstyle configuration to use.</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setConfigFile-java.io.File-">setConfigFile</a></span>&#8203;(java.io.File&nbsp;configFile)</code></th>
<td class="colLast">
<div class="block">The Checkstyle configuration file to use.</div>
</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setConfigProperties-java.util.Map-">setConfigProperties</a></span>&#8203;(java.util.Map&lt;java.lang.String,&#8203;java.lang.Object&gt;&nbsp;configProperties)</code></th>
<td class="colLast">
<div class="block">The properties available for use in the configuration file.</div>
</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setMaxErrors-int-">setMaxErrors</a></span>&#8203;(int&nbsp;maxErrors)</code></th>
<td class="colLast">
<div class="block">Set the maximum number of errors that are tolerated before breaking the build.</div>
</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setMaxWarnings-int-">setMaxWarnings</a></span>&#8203;(int&nbsp;maxWarnings)</code></th>
<td class="colLast">
<div class="block">Set the maximum number of warnings that are tolerated before breaking the build.</div>
</td>
</tr>
<tr id="i13" class="rowColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setShowViolations-boolean-">setShowViolations</a></span>&#8203;(boolean&nbsp;showViolations)</code></th>
<td class="colLast">
<div class="block">Whether rule violations are to be displayed on the console.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.org.gradle.api.plugins.quality.CodeQualityExtension">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;org.gradle.api.plugins.quality.<a href="CodeQualityExtension.html" title="class in org.gradle.api.plugins.quality">CodeQualityExtension</a></h3>
<code><a href="CodeQualityExtension.html#getReportsDir--">getReportsDir</a>, <a href="CodeQualityExtension.html#getSourceSets--">getSourceSets</a>, <a href="CodeQualityExtension.html#getToolVersion--">getToolVersion</a>, <a href="CodeQualityExtension.html#isIgnoreFailures--">isIgnoreFailures</a>, <a href="CodeQualityExtension.html#setIgnoreFailures-boolean-">setIgnoreFailures</a>, <a href="CodeQualityExtension.html#setReportsDir-java.io.File-">setReportsDir</a>, <a href="CodeQualityExtension.html#setSourceSets-java.util.Collection-">setSourceSets</a>, <a href="CodeQualityExtension.html#setToolVersion-java.lang.String-">setToolVersion</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>clone, equals, finalize, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a name="CheckstyleExtension-org.gradle.api.Project-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>CheckstyleExtension</h4>
<pre>public&nbsp;CheckstyleExtension&#8203;(<a href="../../Project.html" title="interface in org.gradle.api">Project</a>&nbsp;project)</pre>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="getConfigFile--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getConfigFile</h4>
<pre class="methodSignature">public&nbsp;java.io.File&nbsp;getConfigFile()</pre>
<div class="block">The Checkstyle configuration file to use.</div>
</li>
</ul>
<a name="setConfigFile-java.io.File-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setConfigFile</h4>
<pre class="methodSignature">public&nbsp;void&nbsp;setConfigFile&#8203;(java.io.File&nbsp;configFile)</pre>
<div class="block">The Checkstyle configuration file to use.</div>
</li>
</ul>
<a name="getConfig--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getConfig</h4>
<pre class="methodSignature">public&nbsp;<a href="../../resources/TextResource.html" title="interface in org.gradle.api.resources">TextResource</a>&nbsp;getConfig()</pre>
<div class="block">The Checkstyle configuration to use. Replaces the <code>configFile</code> property.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>2.2</dd>
</dl>
</li>
</ul>
<a name="setConfig-org.gradle.api.resources.TextResource-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setConfig</h4>
<pre class="methodSignature">public&nbsp;void&nbsp;setConfig&#8203;(<a href="../../resources/TextResource.html" title="interface in org.gradle.api.resources">TextResource</a>&nbsp;config)</pre>
<div class="block">The Checkstyle configuration to use. Replaces the <code>configFile</code> property.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>2.2</dd>
</dl>
</li>
</ul>
<a name="getConfigProperties--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getConfigProperties</h4>
<pre class="methodSignature">public&nbsp;java.util.Map&lt;java.lang.String,&#8203;java.lang.Object&gt;&nbsp;getConfigProperties()</pre>
<div class="block">The properties available for use in the configuration file. These are substituted into the configuration file.</div>
</li>
</ul>
<a name="setConfigProperties-java.util.Map-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setConfigProperties</h4>
<pre class="methodSignature">public&nbsp;void&nbsp;setConfigProperties&#8203;(java.util.Map&lt;java.lang.String,&#8203;java.lang.Object&gt;&nbsp;configProperties)</pre>
<div class="block">The properties available for use in the configuration file. These are substituted into the configuration file.</div>
</li>
</ul>
<a name="getConfigDirectory--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getConfigDirectory</h4>
<pre class="methodSignature">public&nbsp;<a href="../../file/DirectoryProperty.html" title="interface in org.gradle.api.file">DirectoryProperty</a>&nbsp;getConfigDirectory()</pre>
<div class="block">Path to other Checkstyle configuration files. By default, this path is <code>$rootProject.projectDir/config/checkstyle</code>
 <p>
 This path will be exposed as the variable <code>config_loc</code> in Checkstyle's configuration files.
 </p></div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>path to other Checkstyle configuration files</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>4.7</dd>
</dl>
</li>
</ul>
<a name="getMaxErrors--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getMaxErrors</h4>
<pre class="methodSignature">public&nbsp;int&nbsp;getMaxErrors()</pre>
<div class="block">The maximum number of errors that are tolerated before breaking the build
 or setting the failure property. Defaults to <code>0</code>.
 <p>
 Example: maxErrors = 42</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the maximum number of errors allowed</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>3.4</dd>
</dl>
</li>
</ul>
<a name="setMaxErrors-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setMaxErrors</h4>
<pre class="methodSignature">public&nbsp;void&nbsp;setMaxErrors&#8203;(int&nbsp;maxErrors)</pre>
<div class="block">Set the maximum number of errors that are tolerated before breaking the build.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>maxErrors</code> - number of errors allowed</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>3.4</dd>
</dl>
</li>
</ul>
<a name="getMaxWarnings--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getMaxWarnings</h4>
<pre class="methodSignature">public&nbsp;int&nbsp;getMaxWarnings()</pre>
<div class="block">The maximum number of warnings that are tolerated before breaking the build
 or setting the failure property. Defaults to <code>Integer.MAX_VALUE</code>.
 <p>
 Example: maxWarnings = 1000</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the maximum number of warnings allowed</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>3.4</dd>
</dl>
</li>
</ul>
<a name="setMaxWarnings-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setMaxWarnings</h4>
<pre class="methodSignature">public&nbsp;void&nbsp;setMaxWarnings&#8203;(int&nbsp;maxWarnings)</pre>
<div class="block">Set the maximum number of warnings that are tolerated before breaking the build.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>maxWarnings</code> - number of warnings allowed</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>3.4</dd>
</dl>
</li>
</ul>
<a name="isShowViolations--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isShowViolations</h4>
<pre class="methodSignature">public&nbsp;boolean&nbsp;isShowViolations()</pre>
<div class="block">Whether rule violations are to be displayed on the console. Defaults to <code>true</code>.

 Example: showViolations = false</div>
</li>
</ul>
<a name="setShowViolations-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setShowViolations</h4>
<pre class="methodSignature">public&nbsp;void&nbsp;setShowViolations&#8203;(boolean&nbsp;showViolations)</pre>
<div class="block">Whether rule violations are to be displayed on the console. Defaults to <code>true</code>.

 Example: showViolations = false</div>
</li>
</ul>
<a name="getEnableExternalDtdLoad--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>getEnableExternalDtdLoad</h4>
<pre class="methodSignature"><a href="../../Incubating.html" title="annotation in org.gradle.api">@Incubating</a>
<a href="../../tasks/Optional.html" title="annotation in org.gradle.api.tasks">@Optional</a>
<a href="../../tasks/Input.html" title="annotation in org.gradle.api.tasks">@Input</a>
public&nbsp;<a href="../../provider/Property.html" title="interface in org.gradle.api.provider">Property</a>&lt;java.lang.Boolean&gt;&nbsp;getEnableExternalDtdLoad()</pre>
<div class="block">Enable the ability to use custom DTD files in config and load them from some location on all checkstyle tasks in this project.
 <strong>Disabled by default due to security concerns.</strong>
 See <a href="https://checkstyle.org/config_system_properties.html#Enable_External_DTD_load">Checkstyle documentation</a> for more details.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The property controlling whether to enable the ability to use custom DTD files</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>7.6</dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../../allclasses.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
