<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc -->
<title>org.gradle.api.plugins.quality (Gradle API 8.4)</title>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<link rel="stylesheet" type="text/css" href="../../../../../javadoc.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../jquery/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
<script type="text/javascript" src="../../../../../jquery/jszip/dist/jszip.min.js"></script>
<script type="text/javascript" src="../../../../../jquery/jszip-utils/dist/jszip-utils.min.js"></script>
<!--[if IE]>
<script type="text/javascript" src="../../../../../jquery/jszip-utils/dist/jszip-utils-ie.min.js"></script>
<![endif]-->
<script type="text/javascript" src="../../../../../jquery/jquery-3.6.0.min.js"></script>
<script type="text/javascript" src="../../../../../jquery/jquery-ui.min.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="org.gradle.api.plugins.quality (Gradle API 8.4)";
        }
    }
    catch(err) {
    }
//-->
var pathtoroot = "../../../../../";
var useModuleDirectories = false;
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="fixedNav">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../index.html">Overview</a></li>
<li class="navBarCell1Rev">Package</li>
<li>Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../../allclasses.html">All&nbsp;Classes</a></li>
</ul>
<ul class="navListSearch">
<li><label for="search">SEARCH:</label>
<input type="text" id="search" value="search" disabled="disabled">
<input type="reset" id="reset" value="reset" disabled="disabled">
</li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
</div>
<div class="navPadding">&nbsp;</div>
<script type="text/javascript"><!--
$('.navPadding').css('padding-top', $('.fixedNav').css("height"));
//-->
</script>
<div class="header">
<p><a href="../../NonNullApi.html" title="annotation in org.gradle.api">@NonNullApi</a>
</p>
<h1 title="Package" class="title">Package&nbsp;org.gradle.api.plugins.quality</h1>
</div>
<div class="contentContainer"><a name="package.description">
<!--   -->
</a>
<div class="block">Plugins which measure and enforce code quality.</div>
<ul class="blockList">
<li class="blockList">
<table class="typeSummary" summary="Interface Summary table, listing interfaces, and an explanation">
<caption><span>Interface Summary</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Interface</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tbody>
<tr class="altColor">
<th class="colFirst" scope="row"><a href="CheckstyleReports.html" title="interface in org.gradle.api.plugins.quality">CheckstyleReports</a></th>
<td class="colLast">
<div class="block">The reporting configuration for the <a href="Checkstyle.html" title="class in org.gradle.api.plugins.quality"><code>Checkstyle</code></a> task.</div>
</td>
</tr>
<tr class="rowColor">
<th class="colFirst" scope="row"><a href="CodeNarcReports.html" title="interface in org.gradle.api.plugins.quality">CodeNarcReports</a></th>
<td class="colLast">
<div class="block">The reporting configuration for the <a href="CodeNarc.html" title="class in org.gradle.api.plugins.quality"><code>CodeNarc</code></a> test.</div>
</td>
</tr>
<tr class="altColor">
<th class="colFirst" scope="row"><a href="PmdReports.html" title="interface in org.gradle.api.plugins.quality">PmdReports</a></th>
<td class="colLast">
<div class="block">The reporting configuration for the <a href="Pmd.html" title="class in org.gradle.api.plugins.quality"><code>Pmd</code></a> task.</div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList">
<table class="typeSummary" summary="Class Summary table, listing classes, and an explanation">
<caption><span>Class Summary</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Class</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tbody>
<tr class="altColor">
<th class="colFirst" scope="row"><a href="AbstractCodeQualityTask.html" title="class in org.gradle.api.plugins.quality">AbstractCodeQualityTask</a></th>
<td class="colLast">
<div class="block">Base class for code quality tasks.</div>
</td>
</tr>
<tr class="rowColor">
<th class="colFirst" scope="row"><a href="Checkstyle.html" title="class in org.gradle.api.plugins.quality">Checkstyle</a></th>
<td class="colLast">
<div class="block">Runs Checkstyle against some source files.</div>
</td>
</tr>
<tr class="altColor">
<th class="colFirst" scope="row"><a href="CheckstyleExtension.html" title="class in org.gradle.api.plugins.quality">CheckstyleExtension</a></th>
<td class="colLast">
<div class="block">Configuration options for the Checkstyle plugin.</div>
</td>
</tr>
<tr class="rowColor">
<th class="colFirst" scope="row"><a href="CheckstylePlugin.html" title="class in org.gradle.api.plugins.quality">CheckstylePlugin</a></th>
<td class="colLast">
<div class="block">Checkstyle Plugin.</div>
</td>
</tr>
<tr class="altColor">
<th class="colFirst" scope="row"><a href="CodeNarc.html" title="class in org.gradle.api.plugins.quality">CodeNarc</a></th>
<td class="colLast">
<div class="block">Runs CodeNarc against some source files.</div>
</td>
</tr>
<tr class="rowColor">
<th class="colFirst" scope="row"><a href="CodeNarcExtension.html" title="class in org.gradle.api.plugins.quality">CodeNarcExtension</a></th>
<td class="colLast">
<div class="block">Configuration options for the CodeNarc plugin.</div>
</td>
</tr>
<tr class="altColor">
<th class="colFirst" scope="row"><a href="CodeNarcPlugin.html" title="class in org.gradle.api.plugins.quality">CodeNarcPlugin</a></th>
<td class="colLast">
<div class="block">CodeNarc Plugin.</div>
</td>
</tr>
<tr class="rowColor">
<th class="colFirst" scope="row"><a href="CodeQualityExtension.html" title="class in org.gradle.api.plugins.quality">CodeQualityExtension</a></th>
<td class="colLast">
<div class="block">Base Code Quality Extension.</div>
</td>
</tr>
<tr class="altColor">
<th class="colFirst" scope="row"><a href="Pmd.html" title="class in org.gradle.api.plugins.quality">Pmd</a></th>
<td class="colLast">
<div class="block">Runs a set of static code analysis rules on Java source code files and generates a report of problems found.</div>
</td>
</tr>
<tr class="rowColor">
<th class="colFirst" scope="row"><a href="PmdExtension.html" title="class in org.gradle.api.plugins.quality">PmdExtension</a></th>
<td class="colLast">
<div class="block">Configuration options for the PMD plugin.</div>
</td>
</tr>
<tr class="altColor">
<th class="colFirst" scope="row"><a href="PmdPlugin.html" title="class in org.gradle.api.plugins.quality">PmdPlugin</a></th>
<td class="colLast">
<div class="block">A plugin for the <a href="https://pmd.github.io/">PMD</a> source code analyzer.</div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList">
<table class="typeSummary" summary="Enum Summary table, listing enums, and an explanation">
<caption><span>Enum Summary</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Enum</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tbody>
<tr class="altColor">
<th class="colFirst" scope="row"><a href="TargetJdk.html" title="enum in org.gradle.api.plugins.quality">TargetJdk</a></th>
<td class="colLast">
<div class="block">Represents the PMD targetjdk property available for PMD &lt; 5.0</div>
</td>
</tr>
</tbody>
</table>
</li>
</ul>
</div>
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../index.html">Overview</a></li>
<li class="navBarCell1Rev">Package</li>
<li>Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../../allclasses.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
