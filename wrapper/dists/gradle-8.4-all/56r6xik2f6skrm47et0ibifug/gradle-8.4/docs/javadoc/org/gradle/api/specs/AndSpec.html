<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc -->
<title>AndSpec (Gradle API 8.4)</title>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<link rel="stylesheet" type="text/css" href="../../../../javadoc.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../jquery/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
<script type="text/javascript" src="../../../../jquery/jszip/dist/jszip.min.js"></script>
<script type="text/javascript" src="../../../../jquery/jszip-utils/dist/jszip-utils.min.js"></script>
<!--[if IE]>
<script type="text/javascript" src="../../../../jquery/jszip-utils/dist/jszip-utils-ie.min.js"></script>
<![endif]-->
<script type="text/javascript" src="../../../../jquery/jquery-3.6.0.min.js"></script>
<script type="text/javascript" src="../../../../jquery/jquery-ui.min.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="AndSpec (Gradle API 8.4)";
        }
    }
    catch(err) {
    }
//-->
var data = {"i0":10,"i1":10,"i2":10,"i3":9,"i4":10,"i5":10};
var tabs = {65535:["t0","All Methods"],1:["t1","Static Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
var pathtoroot = "../../../../";
var useModuleDirectories = false;
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="fixedNav">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses.html">All&nbsp;Classes</a></li>
</ul>
<ul class="navListSearch">
<li><label for="search">SEARCH:</label>
<input type="text" id="search" value="search" disabled="disabled">
<input type="reset" id="reset" value="reset" disabled="disabled">
</li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field.summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#field.detail">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
</div>
<div class="navPadding">&nbsp;</div>
<script type="text/javascript"><!--
$('.navPadding').css('padding-top', $('.fixedNav').css("height"));
//-->
</script>
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle"><span class="packageLabelInType">Package</span>&nbsp;<a href="package-summary.html">org.gradle.api.specs</a></div>
<h2 title="Class AndSpec" class="title">Class AndSpec&lt;T&gt;</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li><a href="CompositeSpec.html" title="class in org.gradle.api.specs">org.gradle.api.specs.CompositeSpec</a>&lt;T&gt;</li>
<li>
<ul class="inheritance">
<li>org.gradle.api.specs.AndSpec&lt;T&gt;</li>
</ul>
</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt><span class="paramLabel">Type Parameters:</span></dt>
<dd><code>T</code> - The target type for this Spec</dd>
</dl>
<dl>
<dt>All Implemented Interfaces:</dt>
<dd><code><a href="Spec.html" title="interface in org.gradle.api.specs">Spec</a>&lt;T&gt;</code></dd>
</dl>
<hr>
<pre>public class <span class="typeNameLabel">AndSpec&lt;T&gt;</span>
extends <a href="CompositeSpec.html" title="class in org.gradle.api.specs">CompositeSpec</a>&lt;T&gt;</pre>
<div class="block">A <a href="CompositeSpec.html" title="class in org.gradle.api.specs"><code>CompositeSpec</code></a> which requires all its specs to be true in order to evaluate to true.
 Uses lazy evaluation.</div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- =========== FIELD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.summary">
<!--   -->
</a>
<h3>Field Summary</h3>
<table class="memberSummary" summary="Field Summary table, listing fields, and an explanation">
<caption><span>Fields</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colSecond" scope="col">Field</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="AndSpec.html" title="class in org.gradle.api.specs">AndSpec</a>&lt;?&gt;</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#EMPTY">EMPTY</a></span></code></th>
<td class="colLast">&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="memberSummary" summary="Constructor Summary table, listing constructors, and an explanation">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Constructor</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tr class="altColor">
<th class="colConstructorName" scope="row"><code><span class="memberNameLink"><a href="#AndSpec--">AndSpec</a></span>()</code></th>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="rowColor">
<th class="colConstructorName" scope="row"><code><span class="memberNameLink"><a href="#AndSpec-java.lang.Iterable-">AndSpec</a></span>&#8203;(java.lang.Iterable&lt;? extends <a href="Spec.html" title="interface in org.gradle.api.specs">Spec</a>&lt;? super <a href="AndSpec.html" title="type parameter in AndSpec">T</a>&gt;&gt;&nbsp;specs)</code></th>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="altColor">
<th class="colConstructorName" scope="row"><code><span class="memberNameLink"><a href="#AndSpec-org.gradle.api.specs.Spec...-">AndSpec</a></span>&#8203;(<a href="Spec.html" title="interface in org.gradle.api.specs">Spec</a>&lt;? super <a href="AndSpec.html" title="type parameter in AndSpec">T</a>&gt;...&nbsp;specs)</code></th>
<td class="colLast">&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t1" class="tableTab"><span><a href="javascript:show(1);">Static Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colSecond" scope="col">Method</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code><a href="AndSpec.html" title="class in org.gradle.api.specs">AndSpec</a>&lt;<a href="AndSpec.html" title="type parameter in AndSpec">T</a>&gt;</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#and-groovy.lang.Closure-">and</a></span>&#8203;(<a href="https://docs.groovy-lang.org/docs/groovy-3.0.17/html/gapi/groovy/lang/Closure.html?is-external=true" title="class or interface in groovy.lang" class="externalLink">Closure</a>&nbsp;spec)</code></th>
<td class="colLast">&nbsp;</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code><a href="AndSpec.html" title="class in org.gradle.api.specs">AndSpec</a>&lt;<a href="AndSpec.html" title="type parameter in AndSpec">T</a>&gt;</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#and-org.gradle.api.specs.Spec-">and</a></span>&#8203;(<a href="Spec.html" title="interface in org.gradle.api.specs">Spec</a>&lt;? super <a href="AndSpec.html" title="type parameter in AndSpec">T</a>&gt;&nbsp;spec)</code></th>
<td class="colLast">
<div class="block">Typed and() method for a single <a href="Spec.html" title="interface in org.gradle.api.specs"><code>Spec</code></a>.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code><a href="AndSpec.html" title="class in org.gradle.api.specs">AndSpec</a>&lt;<a href="AndSpec.html" title="type parameter in AndSpec">T</a>&gt;</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#and-org.gradle.api.specs.Spec...-">and</a></span>&#8203;(<a href="Spec.html" title="interface in org.gradle.api.specs">Spec</a>&lt;? super <a href="AndSpec.html" title="type parameter in AndSpec">T</a>&gt;...&nbsp;specs)</code></th>
<td class="colLast">&nbsp;</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>static &lt;T&gt;&nbsp;<a href="AndSpec.html" title="class in org.gradle.api.specs">AndSpec</a>&lt;T&gt;</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#empty--">empty</a></span>()</code></th>
<td class="colLast">&nbsp;</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code><a href="Spec.html" title="interface in org.gradle.api.specs">Spec</a>&lt;? super <a href="AndSpec.html" title="type parameter in AndSpec">T</a>&gt;</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#findUnsatisfiedSpec-T-">findUnsatisfiedSpec</a></span>&#8203;(<a href="AndSpec.html" title="type parameter in AndSpec">T</a>&nbsp;object)</code></th>
<td class="colLast">
<div class="block">Finds the first <a href="Spec.html" title="interface in org.gradle.api.specs"><code>Spec</code></a> that is not satisfied by the object.</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#isSatisfiedBy-T-">isSatisfiedBy</a></span>&#8203;(<a href="AndSpec.html" title="type parameter in AndSpec">T</a>&nbsp;object)</code></th>
<td class="colLast">&nbsp;</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.org.gradle.api.specs.CompositeSpec">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;org.gradle.api.specs.<a href="CompositeSpec.html" title="class in org.gradle.api.specs">CompositeSpec</a></h3>
<code><a href="CompositeSpec.html#equals-java.lang.Object-">equals</a>, <a href="CompositeSpec.html#getSpecs--">getSpecs</a>, <a href="CompositeSpec.html#hashCode--">hashCode</a>, <a href="CompositeSpec.html#isEmpty--">isEmpty</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>clone, finalize, getClass, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ FIELD DETAIL =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.detail">
<!--   -->
</a>
<h3>Field Detail</h3>
<a name="EMPTY">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>EMPTY</h4>
<pre>public static final&nbsp;<a href="AndSpec.html" title="class in org.gradle.api.specs">AndSpec</a>&lt;?&gt; EMPTY</pre>
</li>
</ul>
</li>
</ul>
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a name="AndSpec--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>AndSpec</h4>
<pre>public&nbsp;AndSpec()</pre>
</li>
</ul>
<a name="AndSpec-org.gradle.api.specs.Spec...-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>AndSpec</h4>
<pre>@SafeVarargs
public&nbsp;AndSpec&#8203;(<a href="Spec.html" title="interface in org.gradle.api.specs">Spec</a>&lt;? super <a href="AndSpec.html" title="type parameter in AndSpec">T</a>&gt;...&nbsp;specs)</pre>
</li>
</ul>
<a name="AndSpec-java.lang.Iterable-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>AndSpec</h4>
<pre>public&nbsp;AndSpec&#8203;(java.lang.Iterable&lt;? extends <a href="Spec.html" title="interface in org.gradle.api.specs">Spec</a>&lt;? super <a href="AndSpec.html" title="type parameter in AndSpec">T</a>&gt;&gt;&nbsp;specs)</pre>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="isSatisfiedBy-java.lang.Object-">
<!--   -->
</a><a name="isSatisfiedBy-T-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isSatisfiedBy</h4>
<pre class="methodSignature">public&nbsp;boolean&nbsp;isSatisfiedBy&#8203;(<a href="AndSpec.html" title="type parameter in AndSpec">T</a>&nbsp;object)</pre>
</li>
</ul>
<a name="findUnsatisfiedSpec-java.lang.Object-">
<!--   -->
</a><a name="findUnsatisfiedSpec-T-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>findUnsatisfiedSpec</h4>
<pre class="methodSignature">@Nullable
<a href="../Incubating.html" title="annotation in org.gradle.api">@Incubating</a>
public&nbsp;<a href="Spec.html" title="interface in org.gradle.api.specs">Spec</a>&lt;? super <a href="AndSpec.html" title="type parameter in AndSpec">T</a>&gt;&nbsp;findUnsatisfiedSpec&#8203;(<a href="AndSpec.html" title="type parameter in AndSpec">T</a>&nbsp;object)</pre>
<div class="block">Finds the first <a href="Spec.html" title="interface in org.gradle.api.specs"><code>Spec</code></a> that is not satisfied by the object.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>object</code> - to check specs against</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>an unsatisfied spec or null</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>7.6</dd>
</dl>
</li>
</ul>
<a name="and-org.gradle.api.specs.Spec...-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>and</h4>
<pre class="methodSignature">public&nbsp;<a href="AndSpec.html" title="class in org.gradle.api.specs">AndSpec</a>&lt;<a href="AndSpec.html" title="type parameter in AndSpec">T</a>&gt;&nbsp;and&#8203;(<a href="Spec.html" title="interface in org.gradle.api.specs">Spec</a>&lt;? super <a href="AndSpec.html" title="type parameter in AndSpec">T</a>&gt;...&nbsp;specs)</pre>
</li>
</ul>
<a name="and-org.gradle.api.specs.Spec-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>and</h4>
<pre class="methodSignature">public&nbsp;<a href="AndSpec.html" title="class in org.gradle.api.specs">AndSpec</a>&lt;<a href="AndSpec.html" title="type parameter in AndSpec">T</a>&gt;&nbsp;and&#8203;(<a href="Spec.html" title="interface in org.gradle.api.specs">Spec</a>&lt;? super <a href="AndSpec.html" title="type parameter in AndSpec">T</a>&gt;&nbsp;spec)</pre>
<div class="block">Typed and() method for a single <a href="Spec.html" title="interface in org.gradle.api.specs"><code>Spec</code></a>.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>4.3</dd>
</dl>
</li>
</ul>
<a name="and-groovy.lang.Closure-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>and</h4>
<pre class="methodSignature">public&nbsp;<a href="AndSpec.html" title="class in org.gradle.api.specs">AndSpec</a>&lt;<a href="AndSpec.html" title="type parameter in AndSpec">T</a>&gt;&nbsp;and&#8203;(<a href="https://docs.groovy-lang.org/docs/groovy-3.0.17/html/gapi/groovy/lang/Closure.html?is-external=true" title="class or interface in groovy.lang" class="externalLink">Closure</a>&nbsp;spec)</pre>
</li>
</ul>
<a name="empty--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>empty</h4>
<pre class="methodSignature">public static&nbsp;&lt;T&gt;&nbsp;<a href="AndSpec.html" title="class in org.gradle.api.specs">AndSpec</a>&lt;T&gt;&nbsp;empty()</pre>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field.summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#field.detail">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
