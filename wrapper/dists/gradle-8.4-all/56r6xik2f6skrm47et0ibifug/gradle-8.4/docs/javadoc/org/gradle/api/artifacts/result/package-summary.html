<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc -->
<title>org.gradle.api.artifacts.result (Gradle API 8.4)</title>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<link rel="stylesheet" type="text/css" href="../../../../../javadoc.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../jquery/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
<script type="text/javascript" src="../../../../../jquery/jszip/dist/jszip.min.js"></script>
<script type="text/javascript" src="../../../../../jquery/jszip-utils/dist/jszip-utils.min.js"></script>
<!--[if IE]>
<script type="text/javascript" src="../../../../../jquery/jszip-utils/dist/jszip-utils-ie.min.js"></script>
<![endif]-->
<script type="text/javascript" src="../../../../../jquery/jquery-3.6.0.min.js"></script>
<script type="text/javascript" src="../../../../../jquery/jquery-ui.min.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="org.gradle.api.artifacts.result (Gradle API 8.4)";
        }
    }
    catch(err) {
    }
//-->
var pathtoroot = "../../../../../";
var useModuleDirectories = false;
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="fixedNav">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../index.html">Overview</a></li>
<li class="navBarCell1Rev">Package</li>
<li>Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../../allclasses.html">All&nbsp;Classes</a></li>
</ul>
<ul class="navListSearch">
<li><label for="search">SEARCH:</label>
<input type="text" id="search" value="search" disabled="disabled">
<input type="reset" id="reset" value="reset" disabled="disabled">
</li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
</div>
<div class="navPadding">&nbsp;</div>
<script type="text/javascript"><!--
$('.navPadding').css('padding-top', $('.fixedNav').css("height"));
//-->
</script>
<div class="header">
<p><a href="../../NonNullApi.html" title="annotation in org.gradle.api">@NonNullApi</a>
</p>
<h1 title="Package" class="title">Package&nbsp;org.gradle.api.artifacts.result</h1>
</div>
<div class="contentContainer"><a name="package.description">
<!--   -->
</a>
<div class="block">Classes that compose the resolution result</div>
<ul class="blockList">
<li class="blockList">
<table class="typeSummary" summary="Interface Summary table, listing interfaces, and an explanation">
<caption><span>Interface Summary</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Interface</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tbody>
<tr class="altColor">
<th class="colFirst" scope="row"><a href="ArtifactResolutionResult.html" title="interface in org.gradle.api.artifacts.result">ArtifactResolutionResult</a></th>
<td class="colLast">
<div class="block">The result of executing an artifact resolution query.</div>
</td>
</tr>
<tr class="rowColor">
<th class="colFirst" scope="row"><a href="ArtifactResult.html" title="interface in org.gradle.api.artifacts.result">ArtifactResult</a></th>
<td class="colLast">
<div class="block">The result of resolving an artifact.</div>
</td>
</tr>
<tr class="altColor">
<th class="colFirst" scope="row"><a href="ComponentArtifactsResult.html" title="interface in org.gradle.api.artifacts.result">ComponentArtifactsResult</a></th>
<td class="colLast">
<div class="block">The result of successfully resolving a component with a set of artifacts.</div>
</td>
</tr>
<tr class="rowColor">
<th class="colFirst" scope="row"><a href="ComponentResult.html" title="interface in org.gradle.api.artifacts.result">ComponentResult</a></th>
<td class="colLast">
<div class="block">The result of resolving a component.</div>
</td>
</tr>
<tr class="altColor">
<th class="colFirst" scope="row"><a href="ComponentSelectionDescriptor.html" title="interface in org.gradle.api.artifacts.result">ComponentSelectionDescriptor</a></th>
<td class="colLast">
<div class="block">A component selection description, which wraps a cause with an optional custom description.</div>
</td>
</tr>
<tr class="rowColor">
<th class="colFirst" scope="row"><a href="ComponentSelectionReason.html" title="interface in org.gradle.api.artifacts.result">ComponentSelectionReason</a></th>
<td class="colLast">
<div class="block">Answers the question why a component was selected during the dependency resolution.</div>
</td>
</tr>
<tr class="altColor">
<th class="colFirst" scope="row"><a href="DependencyResult.html" title="interface in org.gradle.api.artifacts.result">DependencyResult</a></th>
<td class="colLast">
<div class="block">An edge in the dependency graph.</div>
</td>
</tr>
<tr class="rowColor">
<th class="colFirst" scope="row"><a href="ResolutionResult.html" title="interface in org.gradle.api.artifacts.result">ResolutionResult</a></th>
<td class="colLast">
<div class="block">Contains the information about the result of dependency resolution.</div>
</td>
</tr>
<tr class="altColor">
<th class="colFirst" scope="row"><a href="ResolvedArtifactResult.html" title="interface in org.gradle.api.artifacts.result">ResolvedArtifactResult</a></th>
<td class="colLast">
<div class="block">The result of successfully resolving an artifact.</div>
</td>
</tr>
<tr class="rowColor">
<th class="colFirst" scope="row"><a href="ResolvedComponentResult.html" title="interface in org.gradle.api.artifacts.result">ResolvedComponentResult</a></th>
<td class="colLast">
<div class="block">Represents a component instance in the resolved dependency graph.</div>
</td>
</tr>
<tr class="altColor">
<th class="colFirst" scope="row"><a href="ResolvedDependencyResult.html" title="interface in org.gradle.api.artifacts.result">ResolvedDependencyResult</a></th>
<td class="colLast">
<div class="block">A dependency that was resolved successfully.</div>
</td>
</tr>
<tr class="rowColor">
<th class="colFirst" scope="row"><a href="ResolvedVariantResult.html" title="interface in org.gradle.api.artifacts.result">ResolvedVariantResult</a></th>
<td class="colLast">
<div class="block">The result of successfully resolving a component variant.</div>
</td>
</tr>
<tr class="altColor">
<th class="colFirst" scope="row"><a href="UnresolvedArtifactResult.html" title="interface in org.gradle.api.artifacts.result">UnresolvedArtifactResult</a></th>
<td class="colLast">
<div class="block">An artifact that could not be resolved.</div>
</td>
</tr>
<tr class="rowColor">
<th class="colFirst" scope="row"><a href="UnresolvedComponentResult.html" title="interface in org.gradle.api.artifacts.result">UnresolvedComponentResult</a></th>
<td class="colLast">
<div class="block">A component that could not be resolved.</div>
</td>
</tr>
<tr class="altColor">
<th class="colFirst" scope="row"><a href="UnresolvedDependencyResult.html" title="interface in org.gradle.api.artifacts.result">UnresolvedDependencyResult</a></th>
<td class="colLast">
<div class="block">A dependency that could not be resolved.</div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList">
<table class="typeSummary" summary="Enum Summary table, listing enums, and an explanation">
<caption><span>Enum Summary</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Enum</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tbody>
<tr class="altColor">
<th class="colFirst" scope="row"><a href="ComponentSelectionCause.html" title="enum in org.gradle.api.artifacts.result">ComponentSelectionCause</a></th>
<td class="colLast">
<div class="block">The possible component selection causes.</div>
</td>
</tr>
</tbody>
</table>
</li>
</ul>
</div>
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../index.html">Overview</a></li>
<li class="navBarCell1Rev">Package</li>
<li>Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../../allclasses.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
