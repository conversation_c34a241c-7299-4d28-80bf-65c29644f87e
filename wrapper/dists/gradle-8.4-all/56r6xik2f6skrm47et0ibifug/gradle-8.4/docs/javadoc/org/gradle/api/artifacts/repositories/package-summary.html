<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc -->
<title>org.gradle.api.artifacts.repositories (Gradle API 8.4)</title>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<link rel="stylesheet" type="text/css" href="../../../../../javadoc.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../jquery/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
<script type="text/javascript" src="../../../../../jquery/jszip/dist/jszip.min.js"></script>
<script type="text/javascript" src="../../../../../jquery/jszip-utils/dist/jszip-utils.min.js"></script>
<!--[if IE]>
<script type="text/javascript" src="../../../../../jquery/jszip-utils/dist/jszip-utils-ie.min.js"></script>
<![endif]-->
<script type="text/javascript" src="../../../../../jquery/jquery-3.6.0.min.js"></script>
<script type="text/javascript" src="../../../../../jquery/jquery-ui.min.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="org.gradle.api.artifacts.repositories (Gradle API 8.4)";
        }
    }
    catch(err) {
    }
//-->
var pathtoroot = "../../../../../";
var useModuleDirectories = false;
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="fixedNav">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../index.html">Overview</a></li>
<li class="navBarCell1Rev">Package</li>
<li>Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../../allclasses.html">All&nbsp;Classes</a></li>
</ul>
<ul class="navListSearch">
<li><label for="search">SEARCH:</label>
<input type="text" id="search" value="search" disabled="disabled">
<input type="reset" id="reset" value="reset" disabled="disabled">
</li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
</div>
<div class="navPadding">&nbsp;</div>
<script type="text/javascript"><!--
$('.navPadding').css('padding-top', $('.fixedNav').css("height"));
//-->
</script>
<div class="header">
<p><a href="../../NonNullApi.html" title="annotation in org.gradle.api">@NonNullApi</a>
</p>
<h1 title="Package" class="title">Package&nbsp;org.gradle.api.artifacts.repositories</h1>
</div>
<div class="contentContainer"><a name="package.description">
<!--   -->
</a>
<div class="block">Classes for declaring and using artifact repositories.</div>
<ul class="blockList">
<li class="blockList">
<table class="typeSummary" summary="Interface Summary table, listing interfaces, and an explanation">
<caption><span>Interface Summary</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Interface</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tbody>
<tr class="altColor">
<th class="colFirst" scope="row"><a href="ArtifactRepository.html" title="interface in org.gradle.api.artifacts.repositories">ArtifactRepository</a></th>
<td class="colLast">
<div class="block">A repository for resolving and publishing artifacts.</div>
</td>
</tr>
<tr class="rowColor">
<th class="colFirst" scope="row"><a href="AuthenticationContainer.html" title="interface in org.gradle.api.artifacts.repositories">AuthenticationContainer</a></th>
<td class="colLast">
<div class="block">Container for configuring repository authentication schemes of type <a href="../../../authentication/Authentication.html" title="interface in org.gradle.authentication"><code>Authentication</code></a>.</div>
</td>
</tr>
<tr class="altColor">
<th class="colFirst" scope="row"><a href="AuthenticationSupported.html" title="interface in org.gradle.api.artifacts.repositories">AuthenticationSupported</a></th>
<td class="colLast">
<div class="block">An artifact repository which supports username/password authentication.</div>
</td>
</tr>
<tr class="rowColor">
<th class="colFirst" scope="row"><a href="ExclusiveContentRepository.html" title="interface in org.gradle.api.artifacts.repositories">ExclusiveContentRepository</a></th>
<td class="colLast">
<div class="block">Describes one or more repositories which together constitute the only possible
 source for an artifact, independently of the others.</div>
</td>
</tr>
<tr class="altColor">
<th class="colFirst" scope="row"><a href="FlatDirectoryArtifactRepository.html" title="interface in org.gradle.api.artifacts.repositories">FlatDirectoryArtifactRepository</a></th>
<td class="colLast">
<div class="block">A repository that looks into a number of directories for artifacts.</div>
</td>
</tr>
<tr class="rowColor">
<th class="colFirst" scope="row"><a href="InclusiveRepositoryContentDescriptor.html" title="interface in org.gradle.api.artifacts.repositories">InclusiveRepositoryContentDescriptor</a></th>
<td class="colLast">
<div class="block">Descriptor of a repository content, used to avoid reaching to
 an external repository when not needed.</div>
</td>
</tr>
<tr class="altColor">
<th class="colFirst" scope="row"><a href="IvyArtifactRepository.html" title="interface in org.gradle.api.artifacts.repositories">IvyArtifactRepository</a></th>
<td class="colLast">
<div class="block">An artifact repository which uses an Ivy format to store artifacts and meta-data.</div>
</td>
</tr>
<tr class="rowColor">
<th class="colFirst" scope="row"><a href="IvyArtifactRepository.MetadataSources.html" title="interface in org.gradle.api.artifacts.repositories">IvyArtifactRepository.MetadataSources</a></th>
<td class="colLast">
<div class="block">Allows configuring the sources of metadata for a specific repository.</div>
</td>
</tr>
<tr class="altColor">
<th class="colFirst" scope="row"><a href="IvyArtifactRepositoryMetaDataProvider.html" title="interface in org.gradle.api.artifacts.repositories">IvyArtifactRepositoryMetaDataProvider</a></th>
<td class="colLast">
<div class="block">The meta-data provider for an Ivy repository.</div>
</td>
</tr>
<tr class="rowColor">
<th class="colFirst" scope="row"><a href="IvyPatternRepositoryLayout.html" title="interface in org.gradle.api.artifacts.repositories">IvyPatternRepositoryLayout</a></th>
<td class="colLast">
<div class="block">A repository layout that uses user-supplied patterns.</div>
</td>
</tr>
<tr class="altColor">
<th class="colFirst" scope="row"><a href="MavenArtifactRepository.html" title="interface in org.gradle.api.artifacts.repositories">MavenArtifactRepository</a></th>
<td class="colLast">
<div class="block">An artifact repository which uses a Maven format to store artifacts and meta-data.</div>
</td>
</tr>
<tr class="rowColor">
<th class="colFirst" scope="row"><a href="MavenArtifactRepository.MetadataSources.html" title="interface in org.gradle.api.artifacts.repositories">MavenArtifactRepository.MetadataSources</a></th>
<td class="colLast">
<div class="block">Allows configuring the sources of metadata for a specific repository.</div>
</td>
</tr>
<tr class="altColor">
<th class="colFirst" scope="row"><a href="MavenRepositoryContentDescriptor.html" title="interface in org.gradle.api.artifacts.repositories">MavenRepositoryContentDescriptor</a></th>
<td class="colLast">
<div class="block">Extends the repository content descriptor with Maven repositories specific options.</div>
</td>
</tr>
<tr class="rowColor">
<th class="colFirst" scope="row"><a href="MetadataSupplierAware.html" title="interface in org.gradle.api.artifacts.repositories">MetadataSupplierAware</a></th>
<td class="colLast">
<div class="block">Interface for repositories which support custom metadata suppliers and/or version listers.</div>
</td>
</tr>
<tr class="altColor">
<th class="colFirst" scope="row"><a href="PasswordCredentials.html" title="interface in org.gradle.api.artifacts.repositories">PasswordCredentials</a></th>
<td class="colLast">
<div class="block">A username/password credentials that can be used to login to password-protected remote repository.</div>
</td>
</tr>
<tr class="rowColor">
<th class="colFirst" scope="row"><a href="RepositoryContentDescriptor.html" title="interface in org.gradle.api.artifacts.repositories">RepositoryContentDescriptor</a></th>
<td class="colLast">
<div class="block">Descriptor of a repository content, used to avoid reaching to
 an external repository when not needed.</div>
</td>
</tr>
<tr class="altColor">
<th class="colFirst" scope="row"><a href="RepositoryLayout.html" title="interface in org.gradle.api.artifacts.repositories">RepositoryLayout</a></th>
<td class="colLast">
<div class="block">Represents the directory structure for a repository.</div>
</td>
</tr>
<tr class="rowColor">
<th class="colFirst" scope="row"><a href="RepositoryResourceAccessor.html" title="interface in org.gradle.api.artifacts.repositories">RepositoryResourceAccessor</a></th>
<td class="colLast">
<div class="block">Provides access to resources on an artifact repository.</div>
</td>
</tr>
<tr class="altColor">
<th class="colFirst" scope="row"><a href="UrlArtifactRepository.html" title="interface in org.gradle.api.artifacts.repositories">UrlArtifactRepository</a></th>
<td class="colLast">
<div class="block">A repository that supports resolving artifacts from a URL.</div>
</td>
</tr>
</tbody>
</table>
</li>
</ul>
</div>
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../index.html">Overview</a></li>
<li class="navBarCell1Rev">Package</li>
<li>Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../../allclasses.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
