<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc -->
<title>org.gradle.api.tasks Class Hierarchy (Gradle API 8.4)</title>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<link rel="stylesheet" type="text/css" href="../../../../javadoc.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../jquery/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
<script type="text/javascript" src="../../../../jquery/jszip/dist/jszip.min.js"></script>
<script type="text/javascript" src="../../../../jquery/jszip-utils/dist/jszip-utils.min.js"></script>
<!--[if IE]>
<script type="text/javascript" src="../../../../jquery/jszip-utils/dist/jszip-utils-ie.min.js"></script>
<![endif]-->
<script type="text/javascript" src="../../../../jquery/jquery-3.6.0.min.js"></script>
<script type="text/javascript" src="../../../../jquery/jquery-ui.min.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="org.gradle.api.tasks Class Hierarchy (Gradle API 8.4)";
        }
    }
    catch(err) {
    }
//-->
var pathtoroot = "../../../../";
var useModuleDirectories = false;
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="fixedNav">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li>Class</li>
<li class="navBarCell1Rev">Tree</li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses.html">All&nbsp;Classes</a></li>
</ul>
<ul class="navListSearch">
<li><label for="search">SEARCH:</label>
<input type="text" id="search" value="search" disabled="disabled">
<input type="reset" id="reset" value="reset" disabled="disabled">
</li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
</div>
<div class="navPadding">&nbsp;</div>
<script type="text/javascript"><!--
$('.navPadding').css('padding-top', $('.fixedNav').css("height"));
//-->
</script>
<div class="header">
<h1 class="title">Hierarchy For Package org.gradle.api.tasks</h1>
<span class="packageHierarchyLabel">Package Hierarchies:</span>
<ul class="horizontal">
<li><a href="../../../../overview-tree.html">All Packages</a></li>
</ul>
</div>
<div class="contentContainer">
<h2 title="Class Hierarchy">Class Hierarchy</h2>
<ul>
<li class="circle">java.lang.Object
<ul>
<li class="circle">org.gradle.api.internal.AbstractTask (implements org.gradle.api.internal.DynamicObjectAware, org.gradle.api.internal.TaskInternal)
<ul>
<li class="circle">org.gradle.api.<a href="../DefaultTask.html" title="class in org.gradle.api"><span class="typeNameLink">DefaultTask</span></a> (implements org.gradle.api.<a href="../Task.html" title="interface in org.gradle.api">Task</a>)
<ul>
<li class="circle">org.gradle.api.internal.ConventionTask (implements org.gradle.api.internal.IConventionAware)
<ul>
<li class="circle">org.gradle.api.tasks.<a href="AbstractCopyTask.html" title="class in org.gradle.api.tasks"><span class="typeNameLink">AbstractCopyTask</span></a> (implements org.gradle.api.file.<a href="../file/CopySpec.html" title="interface in org.gradle.api.file">CopySpec</a>, org.gradle.api.internal.file.copy.CopySpecSource)
<ul>
<li class="circle">org.gradle.api.tasks.<a href="Copy.html" title="class in org.gradle.api.tasks"><span class="typeNameLink">Copy</span></a></li>
<li class="circle">org.gradle.api.tasks.<a href="Sync.html" title="class in org.gradle.api.tasks"><span class="typeNameLink">Sync</span></a></li>
</ul>
</li>
<li class="circle">org.gradle.api.tasks.<a href="AbstractExecTask.html" title="class in org.gradle.api.tasks"><span class="typeNameLink">AbstractExecTask</span></a>&lt;T&gt; (implements org.gradle.process.<a href="../../process/ExecSpec.html" title="interface in org.gradle.process">ExecSpec</a>)
<ul>
<li class="circle">org.gradle.api.tasks.<a href="Exec.html" title="class in org.gradle.api.tasks"><span class="typeNameLink">Exec</span></a></li>
</ul>
</li>
<li class="circle">org.gradle.api.tasks.<a href="Delete.html" title="class in org.gradle.api.tasks"><span class="typeNameLink">Delete</span></a> (implements org.gradle.api.file.<a href="../file/DeleteSpec.html" title="interface in org.gradle.api.file">DeleteSpec</a>)</li>
<li class="circle">org.gradle.api.tasks.<a href="GradleBuild.html" title="class in org.gradle.api.tasks"><span class="typeNameLink">GradleBuild</span></a></li>
<li class="circle">org.gradle.api.tasks.<a href="JavaExec.html" title="class in org.gradle.api.tasks"><span class="typeNameLink">JavaExec</span></a> (implements org.gradle.process.<a href="../../process/JavaExecSpec.html" title="interface in org.gradle.process">JavaExecSpec</a>)</li>
<li class="circle">org.gradle.api.tasks.<a href="SourceTask.html" title="class in org.gradle.api.tasks"><span class="typeNameLink">SourceTask</span></a> (implements org.gradle.api.tasks.util.<a href="util/PatternFilterable.html" title="interface in org.gradle.api.tasks.util">PatternFilterable</a>)</li>
<li class="circle">org.gradle.api.tasks.<a href="Upload.html" title="class in org.gradle.api.tasks"><span class="typeNameLink">Upload</span></a></li>
</ul>
</li>
<li class="circle">org.gradle.api.tasks.<a href="WriteProperties.html" title="class in org.gradle.api.tasks"><span class="typeNameLink">WriteProperties</span></a></li>
</ul>
</li>
</ul>
</li>
<li class="circle">org.gradle.api.tasks.<a href="GroovyRuntime.html" title="class in org.gradle.api.tasks"><span class="typeNameLink">GroovyRuntime</span></a></li>
<li class="circle">org.gradle.api.tasks.<a href="ScalaRuntime.html" title="class in org.gradle.api.tasks"><span class="typeNameLink">ScalaRuntime</span></a></li>
<li class="circle">java.lang.Throwable (implements java.io.Serializable)
<ul>
<li class="circle">java.lang.Exception
<ul>
<li class="circle">java.lang.RuntimeException
<ul>
<li class="circle">org.gradle.api.<a href="../GradleException.html" title="class in org.gradle.api"><span class="typeNameLink">GradleException</span></a>
<ul>
<li class="circle">org.gradle.internal.exceptions.DefaultMultiCauseException (implements org.gradle.internal.exceptions.MultiCauseException, org.gradle.internal.exceptions.NonGradleCauseExceptionsHolder)
<ul>
<li class="circle">org.gradle.api.tasks.<a href="TaskExecutionException.html" title="class in org.gradle.api.tasks"><span class="typeNameLink">TaskExecutionException</span></a></li>
</ul>
</li>
<li class="circle">org.gradle.api.tasks.<a href="StopActionException.html" title="class in org.gradle.api.tasks"><span class="typeNameLink">StopActionException</span></a></li>
<li class="circle">org.gradle.api.tasks.<a href="TaskInstantiationException.html" title="class in org.gradle.api.tasks"><span class="typeNameLink">TaskInstantiationException</span></a></li>
<li class="circle">org.gradle.api.tasks.<a href="VerificationException.html" title="class in org.gradle.api.tasks"><span class="typeNameLink">VerificationException</span></a></li>
</ul>
</li>
<li class="circle">org.gradle.api.tasks.<a href="StopExecutionException.html" title="class in org.gradle.api.tasks"><span class="typeNameLink">StopExecutionException</span></a></li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
<li class="circle">org.gradle.api.tasks.<a href="WorkResults.html" title="class in org.gradle.api.tasks"><span class="typeNameLink">WorkResults</span></a></li>
</ul>
</li>
</ul>
<h2 title="Interface Hierarchy">Interface Hierarchy</h2>
<ul>
<li class="circle">org.gradle.api.tasks.<a href="AntBuilderAware.html" title="interface in org.gradle.api.tasks"><span class="typeNameLink">AntBuilderAware</span></a>
<ul>
<li class="circle">org.gradle.api.file.<a href="../file/FileCollection.html" title="interface in org.gradle.api.file"><span class="typeNameLink">FileCollection</span></a> (also extends org.gradle.api.<a href="../Buildable.html" title="interface in org.gradle.api">Buildable</a>, java.lang.Iterable&lt;T&gt;)
<ul>
<li class="circle">org.gradle.api.file.<a href="../file/FileTree.html" title="interface in org.gradle.api.file"><span class="typeNameLink">FileTree</span></a>
<ul>
<li class="circle">org.gradle.api.file.<a href="../file/SourceDirectorySet.html" title="interface in org.gradle.api.file"><span class="typeNameLink">SourceDirectorySet</span></a> (also extends org.gradle.api.<a href="../Describable.html" title="interface in org.gradle.api">Describable</a>, org.gradle.api.<a href="../Named.html" title="interface in org.gradle.api">Named</a>, org.gradle.api.tasks.util.<a href="util/PatternFilterable.html" title="interface in org.gradle.api.tasks.util">PatternFilterable</a>)
<ul>
<li class="circle">org.gradle.api.tasks.<a href="GroovySourceDirectorySet.html" title="interface in org.gradle.api.tasks"><span class="typeNameLink">GroovySourceDirectorySet</span></a></li>
<li class="circle">org.gradle.api.tasks.<a href="ScalaSourceDirectorySet.html" title="interface in org.gradle.api.tasks"><span class="typeNameLink">ScalaSourceDirectorySet</span></a></li>
</ul>
</li>
</ul>
</li>
<li class="circle">org.gradle.api.tasks.<a href="SourceSetOutput.html" title="interface in org.gradle.api.tasks"><span class="typeNameLink">SourceSetOutput</span></a></li>
</ul>
</li>
</ul>
</li>
<li class="circle">org.gradle.api.<a href="../Buildable.html" title="interface in org.gradle.api"><span class="typeNameLink">Buildable</span></a>
<ul>
<li class="circle">org.gradle.api.file.<a href="../file/FileCollection.html" title="interface in org.gradle.api.file"><span class="typeNameLink">FileCollection</span></a> (also extends org.gradle.api.tasks.<a href="AntBuilderAware.html" title="interface in org.gradle.api.tasks">AntBuilderAware</a>, java.lang.Iterable&lt;T&gt;)
<ul>
<li class="circle">org.gradle.api.file.<a href="../file/FileTree.html" title="interface in org.gradle.api.file"><span class="typeNameLink">FileTree</span></a>
<ul>
<li class="circle">org.gradle.api.file.<a href="../file/SourceDirectorySet.html" title="interface in org.gradle.api.file"><span class="typeNameLink">SourceDirectorySet</span></a> (also extends org.gradle.api.<a href="../Describable.html" title="interface in org.gradle.api">Describable</a>, org.gradle.api.<a href="../Named.html" title="interface in org.gradle.api">Named</a>, org.gradle.api.tasks.util.<a href="util/PatternFilterable.html" title="interface in org.gradle.api.tasks.util">PatternFilterable</a>)
<ul>
<li class="circle">org.gradle.api.tasks.<a href="GroovySourceDirectorySet.html" title="interface in org.gradle.api.tasks"><span class="typeNameLink">GroovySourceDirectorySet</span></a></li>
<li class="circle">org.gradle.api.tasks.<a href="ScalaSourceDirectorySet.html" title="interface in org.gradle.api.tasks"><span class="typeNameLink">ScalaSourceDirectorySet</span></a></li>
</ul>
</li>
</ul>
</li>
<li class="circle">org.gradle.api.tasks.<a href="SourceSetOutput.html" title="interface in org.gradle.api.tasks"><span class="typeNameLink">SourceSetOutput</span></a></li>
</ul>
</li>
</ul>
</li>
<li class="circle">org.gradle.util.<a href="../../util/Configurable.html" title="interface in org.gradle.util"><span class="typeNameLink">Configurable</span></a>&lt;T&gt;
<ul>
<li class="circle">org.gradle.api.<a href="../NamedDomainObjectContainer.html" title="interface in org.gradle.api"><span class="typeNameLink">NamedDomainObjectContainer</span></a>&lt;T&gt; (also extends org.gradle.api.<a href="../NamedDomainObjectSet.html" title="interface in org.gradle.api">NamedDomainObjectSet</a>&lt;T&gt;)
<ul>
<li class="circle">org.gradle.api.<a href="../PolymorphicDomainObjectContainer.html" title="interface in org.gradle.api"><span class="typeNameLink">PolymorphicDomainObjectContainer</span></a>&lt;T&gt;
<ul>
<li class="circle">org.gradle.api.tasks.<a href="TaskContainer.html" title="interface in org.gradle.api.tasks"><span class="typeNameLink">TaskContainer</span></a> (also extends org.gradle.api.tasks.<a href="TaskCollection.html" title="interface in org.gradle.api.tasks">TaskCollection</a>&lt;T&gt;)</li>
</ul>
</li>
<li class="circle">org.gradle.api.tasks.<a href="SourceSetContainer.html" title="interface in org.gradle.api.tasks"><span class="typeNameLink">SourceSetContainer</span></a></li>
</ul>
</li>
</ul>
</li>
<li class="circle">org.gradle.api.<a href="../Describable.html" title="interface in org.gradle.api"><span class="typeNameLink">Describable</span></a>
<ul>
<li class="circle">org.gradle.api.file.<a href="../file/SourceDirectorySet.html" title="interface in org.gradle.api.file"><span class="typeNameLink">SourceDirectorySet</span></a> (also extends org.gradle.api.file.<a href="../file/FileTree.html" title="interface in org.gradle.api.file">FileTree</a>, org.gradle.api.<a href="../Named.html" title="interface in org.gradle.api">Named</a>, org.gradle.api.tasks.util.<a href="util/PatternFilterable.html" title="interface in org.gradle.api.tasks.util">PatternFilterable</a>)
<ul>
<li class="circle">org.gradle.api.tasks.<a href="GroovySourceDirectorySet.html" title="interface in org.gradle.api.tasks"><span class="typeNameLink">GroovySourceDirectorySet</span></a></li>
<li class="circle">org.gradle.api.tasks.<a href="ScalaSourceDirectorySet.html" title="interface in org.gradle.api.tasks"><span class="typeNameLink">ScalaSourceDirectorySet</span></a></li>
</ul>
</li>
</ul>
</li>
<li class="circle">org.gradle.api.plugins.<a href="../plugins/ExtensionAware.html" title="interface in org.gradle.api.plugins"><span class="typeNameLink">ExtensionAware</span></a>
<ul>
<li class="circle">org.gradle.api.tasks.<a href="SourceSet.html" title="interface in org.gradle.api.tasks"><span class="typeNameLink">SourceSet</span></a></li>
</ul>
</li>
<li class="circle">org.gradle.api.tasks.<a href="FileNormalizer.html" title="interface in org.gradle.api.tasks"><span class="typeNameLink">FileNormalizer</span></a>
<ul>
<li class="circle">org.gradle.api.tasks.<a href="ClasspathNormalizer.html" title="interface in org.gradle.api.tasks"><span class="typeNameLink">ClasspathNormalizer</span></a></li>
<li class="circle">org.gradle.api.tasks.<a href="CompileClasspathNormalizer.html" title="interface in org.gradle.api.tasks"><span class="typeNameLink">CompileClasspathNormalizer</span></a></li>
</ul>
</li>
<li class="circle">org.gradle.api.tasks.<a href="GroovySourceSet.html" title="interface in org.gradle.api.tasks"><span class="typeNameLink">GroovySourceSet</span></a></li>
<li class="circle">java.lang.Iterable&lt;T&gt;
<ul>
<li class="circle">java.util.Collection&lt;E&gt;
<ul>
<li class="circle">org.gradle.api.<a href="../DomainObjectCollection.html" title="interface in org.gradle.api"><span class="typeNameLink">DomainObjectCollection</span></a>&lt;T&gt;
<ul>
<li class="circle">org.gradle.api.<a href="../DomainObjectSet.html" title="interface in org.gradle.api"><span class="typeNameLink">DomainObjectSet</span></a>&lt;T&gt; (also extends java.util.Set&lt;E&gt;)
<ul>
<li class="circle">org.gradle.api.<a href="../NamedDomainObjectSet.html" title="interface in org.gradle.api"><span class="typeNameLink">NamedDomainObjectSet</span></a>&lt;T&gt; (also extends org.gradle.api.<a href="../NamedDomainObjectCollection.html" title="interface in org.gradle.api">NamedDomainObjectCollection</a>&lt;T&gt;)
<ul>
<li class="circle">org.gradle.api.<a href="../NamedDomainObjectContainer.html" title="interface in org.gradle.api"><span class="typeNameLink">NamedDomainObjectContainer</span></a>&lt;T&gt; (also extends org.gradle.util.<a href="../../util/Configurable.html" title="interface in org.gradle.util">Configurable</a>&lt;T&gt;)
<ul>
<li class="circle">org.gradle.api.<a href="../PolymorphicDomainObjectContainer.html" title="interface in org.gradle.api"><span class="typeNameLink">PolymorphicDomainObjectContainer</span></a>&lt;T&gt;
<ul>
<li class="circle">org.gradle.api.tasks.<a href="TaskContainer.html" title="interface in org.gradle.api.tasks"><span class="typeNameLink">TaskContainer</span></a> (also extends org.gradle.api.tasks.<a href="TaskCollection.html" title="interface in org.gradle.api.tasks">TaskCollection</a>&lt;T&gt;)</li>
</ul>
</li>
<li class="circle">org.gradle.api.tasks.<a href="SourceSetContainer.html" title="interface in org.gradle.api.tasks"><span class="typeNameLink">SourceSetContainer</span></a></li>
</ul>
</li>
<li class="circle">org.gradle.api.tasks.<a href="TaskCollection.html" title="interface in org.gradle.api.tasks"><span class="typeNameLink">TaskCollection</span></a>&lt;T&gt;
<ul>
<li class="circle">org.gradle.api.tasks.<a href="TaskContainer.html" title="interface in org.gradle.api.tasks"><span class="typeNameLink">TaskContainer</span></a> (also extends org.gradle.api.<a href="../PolymorphicDomainObjectContainer.html" title="interface in org.gradle.api">PolymorphicDomainObjectContainer</a>&lt;T&gt;)</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
<li class="circle">org.gradle.api.<a href="../NamedDomainObjectCollection.html" title="interface in org.gradle.api"><span class="typeNameLink">NamedDomainObjectCollection</span></a>&lt;T&gt;
<ul>
<li class="circle">org.gradle.api.<a href="../NamedDomainObjectSet.html" title="interface in org.gradle.api"><span class="typeNameLink">NamedDomainObjectSet</span></a>&lt;T&gt; (also extends org.gradle.api.<a href="../DomainObjectSet.html" title="interface in org.gradle.api">DomainObjectSet</a>&lt;T&gt;)
<ul>
<li class="circle">org.gradle.api.<a href="../NamedDomainObjectContainer.html" title="interface in org.gradle.api"><span class="typeNameLink">NamedDomainObjectContainer</span></a>&lt;T&gt; (also extends org.gradle.util.<a href="../../util/Configurable.html" title="interface in org.gradle.util">Configurable</a>&lt;T&gt;)
<ul>
<li class="circle">org.gradle.api.<a href="../PolymorphicDomainObjectContainer.html" title="interface in org.gradle.api"><span class="typeNameLink">PolymorphicDomainObjectContainer</span></a>&lt;T&gt;
<ul>
<li class="circle">org.gradle.api.tasks.<a href="TaskContainer.html" title="interface in org.gradle.api.tasks"><span class="typeNameLink">TaskContainer</span></a> (also extends org.gradle.api.tasks.<a href="TaskCollection.html" title="interface in org.gradle.api.tasks">TaskCollection</a>&lt;T&gt;)</li>
</ul>
</li>
<li class="circle">org.gradle.api.tasks.<a href="SourceSetContainer.html" title="interface in org.gradle.api.tasks"><span class="typeNameLink">SourceSetContainer</span></a></li>
</ul>
</li>
<li class="circle">org.gradle.api.tasks.<a href="TaskCollection.html" title="interface in org.gradle.api.tasks"><span class="typeNameLink">TaskCollection</span></a>&lt;T&gt;
<ul>
<li class="circle">org.gradle.api.tasks.<a href="TaskContainer.html" title="interface in org.gradle.api.tasks"><span class="typeNameLink">TaskContainer</span></a> (also extends org.gradle.api.<a href="../PolymorphicDomainObjectContainer.html" title="interface in org.gradle.api">PolymorphicDomainObjectContainer</a>&lt;T&gt;)</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
<li class="circle">java.util.Set&lt;E&gt;
<ul>
<li class="circle">org.gradle.api.<a href="../DomainObjectSet.html" title="interface in org.gradle.api"><span class="typeNameLink">DomainObjectSet</span></a>&lt;T&gt; (also extends org.gradle.api.<a href="../DomainObjectCollection.html" title="interface in org.gradle.api">DomainObjectCollection</a>&lt;T&gt;)
<ul>
<li class="circle">org.gradle.api.<a href="../NamedDomainObjectSet.html" title="interface in org.gradle.api"><span class="typeNameLink">NamedDomainObjectSet</span></a>&lt;T&gt; (also extends org.gradle.api.<a href="../NamedDomainObjectCollection.html" title="interface in org.gradle.api">NamedDomainObjectCollection</a>&lt;T&gt;)
<ul>
<li class="circle">org.gradle.api.<a href="../NamedDomainObjectContainer.html" title="interface in org.gradle.api"><span class="typeNameLink">NamedDomainObjectContainer</span></a>&lt;T&gt; (also extends org.gradle.util.<a href="../../util/Configurable.html" title="interface in org.gradle.util">Configurable</a>&lt;T&gt;)
<ul>
<li class="circle">org.gradle.api.<a href="../PolymorphicDomainObjectContainer.html" title="interface in org.gradle.api"><span class="typeNameLink">PolymorphicDomainObjectContainer</span></a>&lt;T&gt;
<ul>
<li class="circle">org.gradle.api.tasks.<a href="TaskContainer.html" title="interface in org.gradle.api.tasks"><span class="typeNameLink">TaskContainer</span></a> (also extends org.gradle.api.tasks.<a href="TaskCollection.html" title="interface in org.gradle.api.tasks">TaskCollection</a>&lt;T&gt;)</li>
</ul>
</li>
<li class="circle">org.gradle.api.tasks.<a href="SourceSetContainer.html" title="interface in org.gradle.api.tasks"><span class="typeNameLink">SourceSetContainer</span></a></li>
</ul>
</li>
<li class="circle">org.gradle.api.tasks.<a href="TaskCollection.html" title="interface in org.gradle.api.tasks"><span class="typeNameLink">TaskCollection</span></a>&lt;T&gt;
<ul>
<li class="circle">org.gradle.api.tasks.<a href="TaskContainer.html" title="interface in org.gradle.api.tasks"><span class="typeNameLink">TaskContainer</span></a> (also extends org.gradle.api.<a href="../PolymorphicDomainObjectContainer.html" title="interface in org.gradle.api">PolymorphicDomainObjectContainer</a>&lt;T&gt;)</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
<li class="circle">org.gradle.api.file.<a href="../file/FileCollection.html" title="interface in org.gradle.api.file"><span class="typeNameLink">FileCollection</span></a> (also extends org.gradle.api.tasks.<a href="AntBuilderAware.html" title="interface in org.gradle.api.tasks">AntBuilderAware</a>, org.gradle.api.<a href="../Buildable.html" title="interface in org.gradle.api">Buildable</a>)
<ul>
<li class="circle">org.gradle.api.file.<a href="../file/FileTree.html" title="interface in org.gradle.api.file"><span class="typeNameLink">FileTree</span></a>
<ul>
<li class="circle">org.gradle.api.file.<a href="../file/SourceDirectorySet.html" title="interface in org.gradle.api.file"><span class="typeNameLink">SourceDirectorySet</span></a> (also extends org.gradle.api.<a href="../Describable.html" title="interface in org.gradle.api">Describable</a>, org.gradle.api.<a href="../Named.html" title="interface in org.gradle.api">Named</a>, org.gradle.api.tasks.util.<a href="util/PatternFilterable.html" title="interface in org.gradle.api.tasks.util">PatternFilterable</a>)
<ul>
<li class="circle">org.gradle.api.tasks.<a href="GroovySourceDirectorySet.html" title="interface in org.gradle.api.tasks"><span class="typeNameLink">GroovySourceDirectorySet</span></a></li>
<li class="circle">org.gradle.api.tasks.<a href="ScalaSourceDirectorySet.html" title="interface in org.gradle.api.tasks"><span class="typeNameLink">ScalaSourceDirectorySet</span></a></li>
</ul>
</li>
</ul>
</li>
<li class="circle">org.gradle.api.tasks.<a href="SourceSetOutput.html" title="interface in org.gradle.api.tasks"><span class="typeNameLink">SourceSetOutput</span></a></li>
</ul>
</li>
</ul>
</li>
<li class="circle">org.gradle.api.<a href="../Named.html" title="interface in org.gradle.api"><span class="typeNameLink">Named</span></a>
<ul>
<li class="circle">org.gradle.api.file.<a href="../file/SourceDirectorySet.html" title="interface in org.gradle.api.file"><span class="typeNameLink">SourceDirectorySet</span></a> (also extends org.gradle.api.<a href="../Describable.html" title="interface in org.gradle.api">Describable</a>, org.gradle.api.file.<a href="../file/FileTree.html" title="interface in org.gradle.api.file">FileTree</a>, org.gradle.api.tasks.util.<a href="util/PatternFilterable.html" title="interface in org.gradle.api.tasks.util">PatternFilterable</a>)
<ul>
<li class="circle">org.gradle.api.tasks.<a href="GroovySourceDirectorySet.html" title="interface in org.gradle.api.tasks"><span class="typeNameLink">GroovySourceDirectorySet</span></a></li>
<li class="circle">org.gradle.api.tasks.<a href="ScalaSourceDirectorySet.html" title="interface in org.gradle.api.tasks"><span class="typeNameLink">ScalaSourceDirectorySet</span></a></li>
</ul>
</li>
<li class="circle">org.gradle.api.tasks.<a href="TaskReference.html" title="interface in org.gradle.api.tasks"><span class="typeNameLink">TaskReference</span></a></li>
</ul>
</li>
<li class="circle">org.gradle.api.tasks.util.<a href="util/PatternFilterable.html" title="interface in org.gradle.api.tasks.util"><span class="typeNameLink">PatternFilterable</span></a>
<ul>
<li class="circle">org.gradle.api.file.<a href="../file/SourceDirectorySet.html" title="interface in org.gradle.api.file"><span class="typeNameLink">SourceDirectorySet</span></a> (also extends org.gradle.api.<a href="../Describable.html" title="interface in org.gradle.api">Describable</a>, org.gradle.api.file.<a href="../file/FileTree.html" title="interface in org.gradle.api.file">FileTree</a>, org.gradle.api.<a href="../Named.html" title="interface in org.gradle.api">Named</a>)
<ul>
<li class="circle">org.gradle.api.tasks.<a href="GroovySourceDirectorySet.html" title="interface in org.gradle.api.tasks"><span class="typeNameLink">GroovySourceDirectorySet</span></a></li>
<li class="circle">org.gradle.api.tasks.<a href="ScalaSourceDirectorySet.html" title="interface in org.gradle.api.tasks"><span class="typeNameLink">ScalaSourceDirectorySet</span></a></li>
</ul>
</li>
</ul>
</li>
<li class="circle">org.gradle.api.provider.<a href="../provider/Provider.html" title="interface in org.gradle.api.provider"><span class="typeNameLink">Provider</span></a>&lt;T&gt;
<ul>
<li class="circle">org.gradle.api.<a href="../NamedDomainObjectProvider.html" title="interface in org.gradle.api"><span class="typeNameLink">NamedDomainObjectProvider</span></a>&lt;T&gt;
<ul>
<li class="circle">org.gradle.api.tasks.<a href="TaskProvider.html" title="interface in org.gradle.api.tasks"><span class="typeNameLink">TaskProvider</span></a>&lt;T&gt;</li>
</ul>
</li>
</ul>
</li>
<li class="circle">org.gradle.api.tasks.<a href="ScalaSourceSet.html" title="interface in org.gradle.api.tasks"><span class="typeNameLink">ScalaSourceSet</span></a></li>
<li class="circle">org.gradle.api.tasks.<a href="TaskDependency.html" title="interface in org.gradle.api.tasks"><span class="typeNameLink">TaskDependency</span></a></li>
<li class="circle">org.gradle.api.tasks.<a href="TaskDestroyables.html" title="interface in org.gradle.api.tasks"><span class="typeNameLink">TaskDestroyables</span></a></li>
<li class="circle">org.gradle.api.tasks.<a href="TaskInputs.html" title="interface in org.gradle.api.tasks"><span class="typeNameLink">TaskInputs</span></a></li>
<li class="circle">org.gradle.api.tasks.<a href="TaskLocalState.html" title="interface in org.gradle.api.tasks"><span class="typeNameLink">TaskLocalState</span></a></li>
<li class="circle">org.gradle.api.tasks.<a href="TaskOutputs.html" title="interface in org.gradle.api.tasks"><span class="typeNameLink">TaskOutputs</span></a></li>
<li class="circle">org.gradle.api.tasks.<a href="TaskPropertyBuilder.html" title="interface in org.gradle.api.tasks"><span class="typeNameLink">TaskPropertyBuilder</span></a>
<ul>
<li class="circle">org.gradle.api.tasks.<a href="TaskFilePropertyBuilder.html" title="interface in org.gradle.api.tasks"><span class="typeNameLink">TaskFilePropertyBuilder</span></a>
<ul>
<li class="circle">org.gradle.api.tasks.<a href="TaskInputFilePropertyBuilder.html" title="interface in org.gradle.api.tasks"><span class="typeNameLink">TaskInputFilePropertyBuilder</span></a></li>
<li class="circle">org.gradle.api.tasks.<a href="TaskOutputFilePropertyBuilder.html" title="interface in org.gradle.api.tasks"><span class="typeNameLink">TaskOutputFilePropertyBuilder</span></a></li>
</ul>
</li>
<li class="circle">org.gradle.api.tasks.<a href="TaskInputPropertyBuilder.html" title="interface in org.gradle.api.tasks"><span class="typeNameLink">TaskInputPropertyBuilder</span></a></li>
</ul>
</li>
<li class="circle">org.gradle.api.tasks.<a href="TaskState.html" title="interface in org.gradle.api.tasks"><span class="typeNameLink">TaskState</span></a></li>
<li class="circle">org.gradle.api.tasks.<a href="VerificationTask.html" title="interface in org.gradle.api.tasks"><span class="typeNameLink">VerificationTask</span></a></li>
<li class="circle">org.gradle.api.tasks.<a href="WorkResult.html" title="interface in org.gradle.api.tasks"><span class="typeNameLink">WorkResult</span></a></li>
</ul>
<h2 title="Annotation Type Hierarchy">Annotation Type Hierarchy</h2>
<ul>
<li class="circle">org.gradle.api.tasks.<a href="CacheableTask.html" title="annotation in org.gradle.api.tasks"><span class="typeNameLink">CacheableTask</span></a> (implements java.lang.annotation.Annotation)</li>
<li class="circle">org.gradle.api.tasks.<a href="Classpath.html" title="annotation in org.gradle.api.tasks"><span class="typeNameLink">Classpath</span></a> (implements java.lang.annotation.Annotation)</li>
<li class="circle">org.gradle.api.tasks.<a href="CompileClasspath.html" title="annotation in org.gradle.api.tasks"><span class="typeNameLink">CompileClasspath</span></a> (implements java.lang.annotation.Annotation)</li>
<li class="circle">org.gradle.api.tasks.<a href="Console.html" title="annotation in org.gradle.api.tasks"><span class="typeNameLink">Console</span></a> (implements java.lang.annotation.Annotation)</li>
<li class="circle">org.gradle.api.tasks.<a href="Destroys.html" title="annotation in org.gradle.api.tasks"><span class="typeNameLink">Destroys</span></a> (implements java.lang.annotation.Annotation)</li>
<li class="circle">org.gradle.api.tasks.<a href="IgnoreEmptyDirectories.html" title="annotation in org.gradle.api.tasks"><span class="typeNameLink">IgnoreEmptyDirectories</span></a> (implements java.lang.annotation.Annotation)</li>
<li class="circle">org.gradle.api.tasks.<a href="Input.html" title="annotation in org.gradle.api.tasks"><span class="typeNameLink">Input</span></a> (implements java.lang.annotation.Annotation)</li>
<li class="circle">org.gradle.api.tasks.<a href="InputDirectory.html" title="annotation in org.gradle.api.tasks"><span class="typeNameLink">InputDirectory</span></a> (implements java.lang.annotation.Annotation)</li>
<li class="circle">org.gradle.api.tasks.<a href="InputFile.html" title="annotation in org.gradle.api.tasks"><span class="typeNameLink">InputFile</span></a> (implements java.lang.annotation.Annotation)</li>
<li class="circle">org.gradle.api.tasks.<a href="InputFiles.html" title="annotation in org.gradle.api.tasks"><span class="typeNameLink">InputFiles</span></a> (implements java.lang.annotation.Annotation)</li>
<li class="circle">org.gradle.api.tasks.<a href="Internal.html" title="annotation in org.gradle.api.tasks"><span class="typeNameLink">Internal</span></a> (implements java.lang.annotation.Annotation)</li>
<li class="circle">org.gradle.api.tasks.<a href="LocalState.html" title="annotation in org.gradle.api.tasks"><span class="typeNameLink">LocalState</span></a> (implements java.lang.annotation.Annotation)</li>
<li class="circle">org.gradle.api.tasks.<a href="Nested.html" title="annotation in org.gradle.api.tasks"><span class="typeNameLink">Nested</span></a> (implements java.lang.annotation.Annotation)</li>
<li class="circle">org.gradle.api.tasks.<a href="Optional.html" title="annotation in org.gradle.api.tasks"><span class="typeNameLink">Optional</span></a> (implements java.lang.annotation.Annotation)</li>
<li class="circle">org.gradle.api.tasks.<a href="OutputDirectories.html" title="annotation in org.gradle.api.tasks"><span class="typeNameLink">OutputDirectories</span></a> (implements java.lang.annotation.Annotation)</li>
<li class="circle">org.gradle.api.tasks.<a href="OutputDirectory.html" title="annotation in org.gradle.api.tasks"><span class="typeNameLink">OutputDirectory</span></a> (implements java.lang.annotation.Annotation)</li>
<li class="circle">org.gradle.api.tasks.<a href="OutputFile.html" title="annotation in org.gradle.api.tasks"><span class="typeNameLink">OutputFile</span></a> (implements java.lang.annotation.Annotation)</li>
<li class="circle">org.gradle.api.tasks.<a href="OutputFiles.html" title="annotation in org.gradle.api.tasks"><span class="typeNameLink">OutputFiles</span></a> (implements java.lang.annotation.Annotation)</li>
<li class="circle">org.gradle.api.tasks.<a href="PathSensitive.html" title="annotation in org.gradle.api.tasks"><span class="typeNameLink">PathSensitive</span></a> (implements java.lang.annotation.Annotation)</li>
<li class="circle">org.gradle.api.tasks.<a href="SkipWhenEmpty.html" title="annotation in org.gradle.api.tasks"><span class="typeNameLink">SkipWhenEmpty</span></a> (implements java.lang.annotation.Annotation)</li>
<li class="circle">org.gradle.api.tasks.<a href="TaskAction.html" title="annotation in org.gradle.api.tasks"><span class="typeNameLink">TaskAction</span></a> (implements java.lang.annotation.Annotation)</li>
<li class="circle">org.gradle.api.tasks.<a href="UntrackedTask.html" title="annotation in org.gradle.api.tasks"><span class="typeNameLink">UntrackedTask</span></a> (implements java.lang.annotation.Annotation)</li>
</ul>
<h2 title="Enum Hierarchy">Enum Hierarchy</h2>
<ul>
<li class="circle">java.lang.Object
<ul>
<li class="circle">java.lang.Enum&lt;E&gt; (implements java.lang.Comparable&lt;T&gt;, java.io.Serializable)
<ul>
<li class="circle">org.gradle.api.tasks.<a href="PathSensitivity.html" title="enum in org.gradle.api.tasks"><span class="typeNameLink">PathSensitivity</span></a></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li>Class</li>
<li class="navBarCell1Rev">Tree</li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
