<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc -->
<title>CopySpec (Gradle API 8.4)</title>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<link rel="stylesheet" type="text/css" href="../../../../javadoc.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../jquery/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
<script type="text/javascript" src="../../../../jquery/jszip/dist/jszip.min.js"></script>
<script type="text/javascript" src="../../../../jquery/jszip-utils/dist/jszip-utils.min.js"></script>
<!--[if IE]>
<script type="text/javascript" src="../../../../jquery/jszip-utils/dist/jszip-utils-ie.min.js"></script>
<![endif]-->
<script type="text/javascript" src="../../../../jquery/jquery-3.6.0.min.js"></script>
<script type="text/javascript" src="../../../../jquery/jquery-ui.min.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="CopySpec (Gradle API 8.4)";
        }
    }
    catch(err) {
    }
//-->
var data = {"i0":6,"i1":6,"i2":6,"i3":6,"i4":6,"i5":6,"i6":6,"i7":6,"i8":6,"i9":6,"i10":6,"i11":6,"i12":6,"i13":6,"i14":6,"i15":6,"i16":6,"i17":6,"i18":6,"i19":6,"i20":6,"i21":6,"i22":6,"i23":6,"i24":6,"i25":6,"i26":6,"i27":6,"i28":6,"i29":6,"i30":6,"i31":6,"i32":6,"i33":6,"i34":6,"i35":6,"i36":6,"i37":6,"i38":6,"i39":6,"i40":6};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],4:["t3","Abstract Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
var pathtoroot = "../../../../";
var useModuleDirectories = false;
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="fixedNav">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses.html">All&nbsp;Classes</a></li>
</ul>
<ul class="navListSearch">
<li><label for="search">SEARCH:</label>
<input type="text" id="search" value="search" disabled="disabled">
<input type="reset" id="reset" value="reset" disabled="disabled">
</li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
</div>
<div class="navPadding">&nbsp;</div>
<script type="text/javascript"><!--
$('.navPadding').css('padding-top', $('.fixedNav').css("height"));
//-->
</script>
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle"><span class="packageLabelInType">Package</span>&nbsp;<a href="package-summary.html">org.gradle.api.file</a></div>
<h2 title="Interface CopySpec" class="title">Interface CopySpec</h2>
</div>
<div class="contentContainer">
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Superinterfaces:</dt>
<dd><code><a href="ContentFilterable.html" title="interface in org.gradle.api.file">ContentFilterable</a></code>, <code><a href="CopyProcessingSpec.html" title="interface in org.gradle.api.file">CopyProcessingSpec</a></code>, <code><a href="CopySourceSpec.html" title="interface in org.gradle.api.file">CopySourceSpec</a></code>, <code><a href="../tasks/util/PatternFilterable.html" title="interface in org.gradle.api.tasks.util">PatternFilterable</a></code></dd>
</dl>
<dl>
<dt>All Known Subinterfaces:</dt>
<dd><code><a href="SyncSpec.html" title="interface in org.gradle.api.file">SyncSpec</a></code></dd>
</dl>
<dl>
<dt>All Known Implementing Classes:</dt>
<dd><code><a href="../tasks/bundling/AbstractArchiveTask.html" title="class in org.gradle.api.tasks.bundling">AbstractArchiveTask</a></code>, <code><a href="../tasks/AbstractCopyTask.html" title="class in org.gradle.api.tasks">AbstractCopyTask</a></code>, <code><a href="../tasks/Copy.html" title="class in org.gradle.api.tasks">Copy</a></code>, <code><a href="../../plugins/ear/Ear.html" title="class in org.gradle.plugins.ear">Ear</a></code>, <code><a href="../tasks/bundling/Jar.html" title="class in org.gradle.api.tasks.bundling">Jar</a></code>, <code><a href="../../jvm/tasks/Jar.html" title="class in org.gradle.jvm.tasks">Jar</a></code>, <code><a href="../../language/jvm/tasks/ProcessResources.html" title="class in org.gradle.language.jvm.tasks">ProcessResources</a></code>, <code><a href="../tasks/Sync.html" title="class in org.gradle.api.tasks">Sync</a></code>, <code><a href="../tasks/bundling/Tar.html" title="class in org.gradle.api.tasks.bundling">Tar</a></code>, <code><a href="../tasks/bundling/War.html" title="class in org.gradle.api.tasks.bundling">War</a></code>, <code><a href="../tasks/bundling/Zip.html" title="class in org.gradle.api.tasks.bundling">Zip</a></code></dd>
</dl>
<hr>
<pre>public interface <span class="typeNameLabel">CopySpec</span>
extends <a href="CopySourceSpec.html" title="interface in org.gradle.api.file">CopySourceSpec</a>, <a href="CopyProcessingSpec.html" title="interface in org.gradle.api.file">CopyProcessingSpec</a>, <a href="../tasks/util/PatternFilterable.html" title="interface in org.gradle.api.tasks.util">PatternFilterable</a></pre>
<div class="block">A set of specifications for copying files.  This includes:

 <ul>

 <li>source directories (multiples allowed)

 <li>destination directory

 <li>ANT like include patterns

 <li>ANT like exclude patterns

 <li>File relocating rules

 <li>renaming rules

 <li>content filters

 </ul>

 CopySpecs may be nested by passing a closure to one of the from methods.  The closure creates a child CopySpec and
 delegates methods in the closure to the child. Child CopySpecs inherit any values specified in the parent. This
 allows constructs like:
 <pre class='autoTested'>
 def myCopySpec = project.copySpec {
   into('webroot')
   exclude('**&#47;.data/**')
   from('src/main/webapp') {
     include '**&#47;*.jsp'
   }
   from('src/main/js') {
     include '**&#47;*.js'
   }
 }
 </pre>

 In this example, the <code>into</code> and <code>exclude</code> specifications at the root level are inherited by the
 two child CopySpecs.

 Copy specs can be reused in other copy specs via <a href="#with-org.gradle.api.file.CopySpec...-"><code>with(CopySpec...)</code></a> method. This enables reuse of the copy spec instances.

 <pre class='autoTested'>
 def contentSpec = copySpec {
   from("content") {
     include "**&#47;*.txt"
   }
 }

 task copy(type: Copy) {
   into "$buildDir/copy"
   with contentSpec
 }
 </pre></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../tasks/Copy.html" title="class in org.gradle.api.tasks"><code>Copy Task</code></a>, 
<a href="../Project.html#copy-groovy.lang.Closure-"><code>Project.copy()</code></a></dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t3" class="tableTab"><span><a href="javascript:show(4);">Abstract Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colSecond" scope="col">Method</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code><a href="CopySpec.html" title="interface in org.gradle.api.file">CopySpec</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#eachFile-groovy.lang.Closure-">eachFile</a></span>&#8203;(<a href="https://docs.groovy-lang.org/docs/groovy-3.0.17/html/gapi/groovy/lang/Closure.html?is-external=true" title="class or interface in groovy.lang" class="externalLink">Closure</a>&nbsp;closure)</code></th>
<td class="colLast">
<div class="block">Adds an action to be applied to each file as it about to be copied into its destination.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code><a href="CopySpec.html" title="interface in org.gradle.api.file">CopySpec</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#eachFile-org.gradle.api.Action-">eachFile</a></span>&#8203;(<a href="../Action.html" title="interface in org.gradle.api">Action</a>&lt;? super <a href="FileCopyDetails.html" title="interface in org.gradle.api.file">FileCopyDetails</a>&gt;&nbsp;action)</code></th>
<td class="colLast">
<div class="block">Adds an action to be applied to each file as it is about to be copied into its destination.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code><a href="CopySpec.html" title="interface in org.gradle.api.file">CopySpec</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#exclude-groovy.lang.Closure-">exclude</a></span>&#8203;(<a href="https://docs.groovy-lang.org/docs/groovy-3.0.17/html/gapi/groovy/lang/Closure.html?is-external=true" title="class or interface in groovy.lang" class="externalLink">Closure</a>&nbsp;excludeSpec)</code></th>
<td class="colLast">
<div class="block">Adds an exclude spec.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code><a href="CopySpec.html" title="interface in org.gradle.api.file">CopySpec</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#exclude-java.lang.Iterable-">exclude</a></span>&#8203;(java.lang.Iterable&lt;java.lang.String&gt;&nbsp;excludes)</code></th>
<td class="colLast">
<div class="block">Adds an ANT style exclude pattern.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code><a href="CopySpec.html" title="interface in org.gradle.api.file">CopySpec</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#exclude-java.lang.String...-">exclude</a></span>&#8203;(java.lang.String...&nbsp;excludes)</code></th>
<td class="colLast">
<div class="block">Adds an ANT style exclude pattern.</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code><a href="CopySpec.html" title="interface in org.gradle.api.file">CopySpec</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#exclude-org.gradle.api.specs.Spec-">exclude</a></span>&#8203;(<a href="../specs/Spec.html" title="interface in org.gradle.api.specs">Spec</a>&lt;<a href="FileTreeElement.html" title="interface in org.gradle.api.file">FileTreeElement</a>&gt;&nbsp;excludeSpec)</code></th>
<td class="colLast">
<div class="block">Adds an exclude spec.</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code><a href="CopySpec.html" title="interface in org.gradle.api.file">CopySpec</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#expand-java.util.Map-">expand</a></span>&#8203;(java.util.Map&lt;java.lang.String,&#8203;?&gt;&nbsp;properties)</code></th>
<td class="colLast">
<div class="block">Expands property references in each file as it is copied.</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code><a href="CopySpec.html" title="interface in org.gradle.api.file">CopySpec</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#expand-java.util.Map-org.gradle.api.Action-">expand</a></span>&#8203;(java.util.Map&lt;java.lang.String,&#8203;?&gt;&nbsp;properties,
      <a href="../Action.html" title="interface in org.gradle.api">Action</a>&lt;? super <a href="ExpandDetails.html" title="interface in org.gradle.api.file">ExpandDetails</a>&gt;&nbsp;action)</code></th>
<td class="colLast">
<div class="block">Expands property references in each file as it is copied.</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code><a href="CopySpec.html" title="interface in org.gradle.api.file">CopySpec</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#filesMatching-java.lang.Iterable-org.gradle.api.Action-">filesMatching</a></span>&#8203;(java.lang.Iterable&lt;java.lang.String&gt;&nbsp;patterns,
             <a href="../Action.html" title="interface in org.gradle.api">Action</a>&lt;? super <a href="FileCopyDetails.html" title="interface in org.gradle.api.file">FileCopyDetails</a>&gt;&nbsp;action)</code></th>
<td class="colLast">
<div class="block">Configure the <a href="FileCopyDetails.html" title="interface in org.gradle.api.file"><code>FileCopyDetails</code></a> for each file whose path matches any of the specified Ant-style patterns.</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code><a href="CopySpec.html" title="interface in org.gradle.api.file">CopySpec</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#filesMatching-java.lang.String-org.gradle.api.Action-">filesMatching</a></span>&#8203;(java.lang.String&nbsp;pattern,
             <a href="../Action.html" title="interface in org.gradle.api">Action</a>&lt;? super <a href="FileCopyDetails.html" title="interface in org.gradle.api.file">FileCopyDetails</a>&gt;&nbsp;action)</code></th>
<td class="colLast">
<div class="block">Configure the <a href="FileCopyDetails.html" title="interface in org.gradle.api.file"><code>FileCopyDetails</code></a> for each file whose path matches the specified Ant-style pattern.</div>
</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code><a href="CopySpec.html" title="interface in org.gradle.api.file">CopySpec</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#filesNotMatching-java.lang.Iterable-org.gradle.api.Action-">filesNotMatching</a></span>&#8203;(java.lang.Iterable&lt;java.lang.String&gt;&nbsp;patterns,
                <a href="../Action.html" title="interface in org.gradle.api">Action</a>&lt;? super <a href="FileCopyDetails.html" title="interface in org.gradle.api.file">FileCopyDetails</a>&gt;&nbsp;action)</code></th>
<td class="colLast">
<div class="block">Configure the <a href="FileCopyDetails.html" title="interface in org.gradle.api.file"><code>FileCopyDetails</code></a> for each file whose path does not match any of the specified
 Ant-style patterns.</div>
</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code><a href="CopySpec.html" title="interface in org.gradle.api.file">CopySpec</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#filesNotMatching-java.lang.String-org.gradle.api.Action-">filesNotMatching</a></span>&#8203;(java.lang.String&nbsp;pattern,
                <a href="../Action.html" title="interface in org.gradle.api">Action</a>&lt;? super <a href="FileCopyDetails.html" title="interface in org.gradle.api.file">FileCopyDetails</a>&gt;&nbsp;action)</code></th>
<td class="colLast">
<div class="block">Configure the <a href="FileCopyDetails.html" title="interface in org.gradle.api.file"><code>FileCopyDetails</code></a> for each file whose path does not match the specified
 Ant-style pattern.</div>
</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code><a href="CopySpec.html" title="interface in org.gradle.api.file">CopySpec</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#filter-groovy.lang.Closure-">filter</a></span>&#8203;(<a href="https://docs.groovy-lang.org/docs/groovy-3.0.17/html/gapi/groovy/lang/Closure.html?is-external=true" title="class or interface in groovy.lang" class="externalLink">Closure</a>&nbsp;closure)</code></th>
<td class="colLast">
<div class="block">Adds a content filter based on the provided closure.</div>
</td>
</tr>
<tr id="i13" class="rowColor">
<td class="colFirst"><code><a href="CopySpec.html" title="interface in org.gradle.api.file">CopySpec</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#filter-java.lang.Class-">filter</a></span>&#8203;(java.lang.Class&lt;? extends java.io.FilterReader&gt;&nbsp;filterType)</code></th>
<td class="colLast">
<div class="block">Adds a content filter to be used during the copy.</div>
</td>
</tr>
<tr id="i14" class="altColor">
<td class="colFirst"><code><a href="CopySpec.html" title="interface in org.gradle.api.file">CopySpec</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#filter-java.util.Map-java.lang.Class-">filter</a></span>&#8203;(java.util.Map&lt;java.lang.String,&#8203;?&gt;&nbsp;properties,
      java.lang.Class&lt;? extends java.io.FilterReader&gt;&nbsp;filterType)</code></th>
<td class="colLast">
<div class="block">Adds a content filter to be used during the copy.</div>
</td>
</tr>
<tr id="i15" class="rowColor">
<td class="colFirst"><code><a href="CopySpec.html" title="interface in org.gradle.api.file">CopySpec</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#filter-org.gradle.api.Transformer-">filter</a></span>&#8203;(<a href="../Transformer.html" title="interface in org.gradle.api">Transformer</a>&lt;@Nullable java.lang.String,&#8203;java.lang.String&gt;&nbsp;transformer)</code></th>
<td class="colLast">
<div class="block">Adds a content filter based on the provided transformer.</div>
</td>
</tr>
<tr id="i16" class="altColor">
<td class="colFirst"><code><a href="CopySpec.html" title="interface in org.gradle.api.file">CopySpec</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#from-java.lang.Object...-">from</a></span>&#8203;(java.lang.Object...&nbsp;sourcePaths)</code></th>
<td class="colLast">
<div class="block">Specifies source files or directories for a copy.</div>
</td>
</tr>
<tr id="i17" class="rowColor">
<td class="colFirst"><code><a href="CopySpec.html" title="interface in org.gradle.api.file">CopySpec</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#from-java.lang.Object-groovy.lang.Closure-">from</a></span>&#8203;(java.lang.Object&nbsp;sourcePath,
    <a href="https://docs.groovy-lang.org/docs/groovy-3.0.17/html/gapi/groovy/lang/Closure.html?is-external=true" title="class or interface in groovy.lang" class="externalLink">Closure</a>&nbsp;c)</code></th>
<td class="colLast">
<div class="block">Specifies the source files or directories for a copy and creates a child <code>CopySourceSpec</code>.</div>
</td>
</tr>
<tr id="i18" class="altColor">
<td class="colFirst"><code><a href="CopySpec.html" title="interface in org.gradle.api.file">CopySpec</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#from-java.lang.Object-org.gradle.api.Action-">from</a></span>&#8203;(java.lang.Object&nbsp;sourcePath,
    <a href="../Action.html" title="interface in org.gradle.api">Action</a>&lt;? super <a href="CopySpec.html" title="interface in org.gradle.api.file">CopySpec</a>&gt;&nbsp;configureAction)</code></th>
<td class="colLast">
<div class="block">Specifies the source files or directories for a copy and creates a child <code>CopySpec</code>.</div>
</td>
</tr>
<tr id="i19" class="rowColor">
<td class="colFirst"><code><a href="DuplicatesStrategy.html" title="enum in org.gradle.api.file">DuplicatesStrategy</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getDuplicatesStrategy--">getDuplicatesStrategy</a></span>()</code></th>
<td class="colLast">
<div class="block">Returns the strategy to use when trying to copy more than one file to the same destination.</div>
</td>
</tr>
<tr id="i20" class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getFilteringCharset--">getFilteringCharset</a></span>()</code></th>
<td class="colLast">
<div class="block">Gets the charset used to read and write files when filtering.</div>
</td>
</tr>
<tr id="i21" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getIncludeEmptyDirs--">getIncludeEmptyDirs</a></span>()</code></th>
<td class="colLast">
<div class="block">Tells if empty target directories will be included in the copy.</div>
</td>
</tr>
<tr id="i22" class="altColor">
<td class="colFirst"><code><a href="CopySpec.html" title="interface in org.gradle.api.file">CopySpec</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#include-groovy.lang.Closure-">include</a></span>&#8203;(<a href="https://docs.groovy-lang.org/docs/groovy-3.0.17/html/gapi/groovy/lang/Closure.html?is-external=true" title="class or interface in groovy.lang" class="externalLink">Closure</a>&nbsp;includeSpec)</code></th>
<td class="colLast">
<div class="block">Adds an include spec.</div>
</td>
</tr>
<tr id="i23" class="rowColor">
<td class="colFirst"><code><a href="CopySpec.html" title="interface in org.gradle.api.file">CopySpec</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#include-java.lang.Iterable-">include</a></span>&#8203;(java.lang.Iterable&lt;java.lang.String&gt;&nbsp;includes)</code></th>
<td class="colLast">
<div class="block">Adds an ANT style include pattern.</div>
</td>
</tr>
<tr id="i24" class="altColor">
<td class="colFirst"><code><a href="CopySpec.html" title="interface in org.gradle.api.file">CopySpec</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#include-java.lang.String...-">include</a></span>&#8203;(java.lang.String...&nbsp;includes)</code></th>
<td class="colLast">
<div class="block">Adds an ANT style include pattern.</div>
</td>
</tr>
<tr id="i25" class="rowColor">
<td class="colFirst"><code><a href="CopySpec.html" title="interface in org.gradle.api.file">CopySpec</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#include-org.gradle.api.specs.Spec-">include</a></span>&#8203;(<a href="../specs/Spec.html" title="interface in org.gradle.api.specs">Spec</a>&lt;<a href="FileTreeElement.html" title="interface in org.gradle.api.file">FileTreeElement</a>&gt;&nbsp;includeSpec)</code></th>
<td class="colLast">
<div class="block">Adds an include spec.</div>
</td>
</tr>
<tr id="i26" class="altColor">
<td class="colFirst"><code><a href="CopySpec.html" title="interface in org.gradle.api.file">CopySpec</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#into-java.lang.Object-">into</a></span>&#8203;(java.lang.Object&nbsp;destPath)</code></th>
<td class="colLast">
<div class="block">Specifies the destination directory for a copy.</div>
</td>
</tr>
<tr id="i27" class="rowColor">
<td class="colFirst"><code><a href="CopySpec.html" title="interface in org.gradle.api.file">CopySpec</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#into-java.lang.Object-groovy.lang.Closure-">into</a></span>&#8203;(java.lang.Object&nbsp;destPath,
    <a href="https://docs.groovy-lang.org/docs/groovy-3.0.17/html/gapi/groovy/lang/Closure.html?is-external=true" title="class or interface in groovy.lang" class="externalLink">Closure</a>&nbsp;configureClosure)</code></th>
<td class="colLast">
<div class="block">Creates and configures a child <code>CopySpec</code> with the given destination path.</div>
</td>
</tr>
<tr id="i28" class="altColor">
<td class="colFirst"><code><a href="CopySpec.html" title="interface in org.gradle.api.file">CopySpec</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#into-java.lang.Object-org.gradle.api.Action-">into</a></span>&#8203;(java.lang.Object&nbsp;destPath,
    <a href="../Action.html" title="interface in org.gradle.api">Action</a>&lt;? super <a href="CopySpec.html" title="interface in org.gradle.api.file">CopySpec</a>&gt;&nbsp;copySpec)</code></th>
<td class="colLast">
<div class="block">Creates and configures a child <code>CopySpec</code> with the given destination path.</div>
</td>
</tr>
<tr id="i29" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#isCaseSensitive--">isCaseSensitive</a></span>()</code></th>
<td class="colLast">
<div class="block">Specifies whether case-sensitive pattern matching should be used.</div>
</td>
</tr>
<tr id="i30" class="altColor">
<td class="colFirst"><code><a href="CopySpec.html" title="interface in org.gradle.api.file">CopySpec</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#rename-groovy.lang.Closure-">rename</a></span>&#8203;(<a href="https://docs.groovy-lang.org/docs/groovy-3.0.17/html/gapi/groovy/lang/Closure.html?is-external=true" title="class or interface in groovy.lang" class="externalLink">Closure</a>&nbsp;closure)</code></th>
<td class="colLast">
<div class="block">Renames a source file.</div>
</td>
</tr>
<tr id="i31" class="rowColor">
<td class="colFirst"><code><a href="CopySpec.html" title="interface in org.gradle.api.file">CopySpec</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#rename-java.lang.String-java.lang.String-">rename</a></span>&#8203;(java.lang.String&nbsp;sourceRegEx,
      java.lang.String&nbsp;replaceWith)</code></th>
<td class="colLast">
<div class="block">Renames files based on a regular expression.</div>
</td>
</tr>
<tr id="i32" class="altColor">
<td class="colFirst"><code><a href="CopyProcessingSpec.html" title="interface in org.gradle.api.file">CopyProcessingSpec</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#rename-java.util.regex.Pattern-java.lang.String-">rename</a></span>&#8203;(java.util.regex.Pattern&nbsp;sourceRegEx,
      java.lang.String&nbsp;replaceWith)</code></th>
<td class="colLast">
<div class="block">Renames files based on a regular expression.</div>
</td>
</tr>
<tr id="i33" class="rowColor">
<td class="colFirst"><code><a href="CopySpec.html" title="interface in org.gradle.api.file">CopySpec</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#rename-org.gradle.api.Transformer-">rename</a></span>&#8203;(<a href="../Transformer.html" title="interface in org.gradle.api">Transformer</a>&lt;@Nullable java.lang.String,&#8203;java.lang.String&gt;&nbsp;renamer)</code></th>
<td class="colLast">
<div class="block">Renames a source file.</div>
</td>
</tr>
<tr id="i34" class="altColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setCaseSensitive-boolean-">setCaseSensitive</a></span>&#8203;(boolean&nbsp;caseSensitive)</code></th>
<td class="colLast">
<div class="block">Specifies whether case-sensitive pattern matching should be used for this CopySpec.</div>
</td>
</tr>
<tr id="i35" class="rowColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setDuplicatesStrategy-org.gradle.api.file.DuplicatesStrategy-">setDuplicatesStrategy</a></span>&#8203;(<a href="DuplicatesStrategy.html" title="enum in org.gradle.api.file">DuplicatesStrategy</a>&nbsp;strategy)</code></th>
<td class="colLast">
<div class="block">The strategy to use when trying to copy more than one file to the same destination.</div>
</td>
</tr>
<tr id="i36" class="altColor">
<td class="colFirst"><code><a href="CopySpec.html" title="interface in org.gradle.api.file">CopySpec</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setExcludes-java.lang.Iterable-">setExcludes</a></span>&#8203;(java.lang.Iterable&lt;java.lang.String&gt;&nbsp;excludes)</code></th>
<td class="colLast">
<div class="block">Set the allowable exclude patterns.</div>
</td>
</tr>
<tr id="i37" class="rowColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setFilteringCharset-java.lang.String-">setFilteringCharset</a></span>&#8203;(java.lang.String&nbsp;charset)</code></th>
<td class="colLast">
<div class="block">Specifies the charset used to read and write files when filtering.</div>
</td>
</tr>
<tr id="i38" class="altColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setIncludeEmptyDirs-boolean-">setIncludeEmptyDirs</a></span>&#8203;(boolean&nbsp;includeEmptyDirs)</code></th>
<td class="colLast">
<div class="block">Controls if empty target directories should be included in the copy.</div>
</td>
</tr>
<tr id="i39" class="rowColor">
<td class="colFirst"><code><a href="CopySpec.html" title="interface in org.gradle.api.file">CopySpec</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setIncludes-java.lang.Iterable-">setIncludes</a></span>&#8203;(java.lang.Iterable&lt;java.lang.String&gt;&nbsp;includes)</code></th>
<td class="colLast">
<div class="block">Set the allowable include patterns.</div>
</td>
</tr>
<tr id="i40" class="altColor">
<td class="colFirst"><code><a href="CopySpec.html" title="interface in org.gradle.api.file">CopySpec</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#with-org.gradle.api.file.CopySpec...-">with</a></span>&#8203;(<a href="CopySpec.html" title="interface in org.gradle.api.file">CopySpec</a>...&nbsp;sourceSpecs)</code></th>
<td class="colLast">
<div class="block">Adds the given specs as a child of this spec.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.org.gradle.api.file.CopyProcessingSpec">
<!--   -->
</a>
<h3>Methods inherited from interface&nbsp;org.gradle.api.file.<a href="CopyProcessingSpec.html" title="interface in org.gradle.api.file">CopyProcessingSpec</a></h3>
<code><a href="CopyProcessingSpec.html#dirPermissions-org.gradle.api.Action-">dirPermissions</a>, <a href="CopyProcessingSpec.html#filePermissions-org.gradle.api.Action-">filePermissions</a>, <a href="CopyProcessingSpec.html#getDirMode--">getDirMode</a>, <a href="CopyProcessingSpec.html#getDirPermissions--">getDirPermissions</a>, <a href="CopyProcessingSpec.html#getFileMode--">getFileMode</a>, <a href="CopyProcessingSpec.html#getFilePermissions--">getFilePermissions</a>, <a href="CopyProcessingSpec.html#setDirMode-java.lang.Integer-">setDirMode</a>, <a href="CopyProcessingSpec.html#setFileMode-java.lang.Integer-">setFileMode</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.org.gradle.api.tasks.util.PatternFilterable">
<!--   -->
</a>
<h3>Methods inherited from interface&nbsp;org.gradle.api.tasks.util.<a href="../tasks/util/PatternFilterable.html" title="interface in org.gradle.api.tasks.util">PatternFilterable</a></h3>
<code><a href="../tasks/util/PatternFilterable.html#getExcludes--">getExcludes</a>, <a href="../tasks/util/PatternFilterable.html#getIncludes--">getIncludes</a></code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="isCaseSensitive--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isCaseSensitive</h4>
<pre class="methodSignature">boolean&nbsp;isCaseSensitive()</pre>
<div class="block">Specifies whether case-sensitive pattern matching should be used.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>true for case-sensitive matching.</dd>
</dl>
</li>
</ul>
<a name="setCaseSensitive-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCaseSensitive</h4>
<pre class="methodSignature">void&nbsp;setCaseSensitive&#8203;(boolean&nbsp;caseSensitive)</pre>
<div class="block">Specifies whether case-sensitive pattern matching should be used for this CopySpec.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>caseSensitive</code> - true for case-sensitive matching.</dd>
</dl>
</li>
</ul>
<a name="getIncludeEmptyDirs--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getIncludeEmptyDirs</h4>
<pre class="methodSignature">boolean&nbsp;getIncludeEmptyDirs()</pre>
<div class="block">Tells if empty target directories will be included in the copy.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd><code>true</code> if empty target directories will be included in the copy, <code>false</code> otherwise</dd>
</dl>
</li>
</ul>
<a name="setIncludeEmptyDirs-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setIncludeEmptyDirs</h4>
<pre class="methodSignature">void&nbsp;setIncludeEmptyDirs&#8203;(boolean&nbsp;includeEmptyDirs)</pre>
<div class="block">Controls if empty target directories should be included in the copy.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>includeEmptyDirs</code> - <code>true</code> if empty target directories should be included in the copy, <code>false</code> otherwise</dd>
</dl>
</li>
</ul>
<a name="getDuplicatesStrategy--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getDuplicatesStrategy</h4>
<pre class="methodSignature"><a href="DuplicatesStrategy.html" title="enum in org.gradle.api.file">DuplicatesStrategy</a>&nbsp;getDuplicatesStrategy()</pre>
<div class="block">Returns the strategy to use when trying to copy more than one file to the same destination.
 <p>
 The value can be set with a case insensitive string of the enum value (e.g. <code>'exclude'</code> for <a href="DuplicatesStrategy.html#EXCLUDE"><code>DuplicatesStrategy.EXCLUDE</code></a>).
 <p>
 This strategy can be overridden for individual files by using <a href="#eachFile-org.gradle.api.Action-"><code>eachFile(org.gradle.api.Action)</code></a> or <a href="#filesMatching-java.lang.String-org.gradle.api.Action-"><code>filesMatching(String, org.gradle.api.Action)</code></a>.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the strategy to use for files included by this copy spec.</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="DuplicatesStrategy.html" title="enum in org.gradle.api.file"><code>DuplicatesStrategy</code></a></dd>
</dl>
</li>
</ul>
<a name="setDuplicatesStrategy-org.gradle.api.file.DuplicatesStrategy-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setDuplicatesStrategy</h4>
<pre class="methodSignature">void&nbsp;setDuplicatesStrategy&#8203;(<a href="DuplicatesStrategy.html" title="enum in org.gradle.api.file">DuplicatesStrategy</a>&nbsp;strategy)</pre>
<div class="block">The strategy to use when trying to copy more than one file to the same destination. Set to <a href="DuplicatesStrategy.html#INHERIT"><code>DuplicatesStrategy.INHERIT</code></a>, the default strategy, to use
 the strategy inherited from the parent copy spec, if any, or <a href="DuplicatesStrategy.html#INCLUDE"><code>DuplicatesStrategy.INCLUDE</code></a> if this copy spec has no parent.</div>
</li>
</ul>
<a name="filesMatching-java.lang.String-org.gradle.api.Action-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>filesMatching</h4>
<pre class="methodSignature"><a href="CopySpec.html" title="interface in org.gradle.api.file">CopySpec</a>&nbsp;filesMatching&#8203;(java.lang.String&nbsp;pattern,
                       <a href="../Action.html" title="interface in org.gradle.api">Action</a>&lt;? super <a href="FileCopyDetails.html" title="interface in org.gradle.api.file">FileCopyDetails</a>&gt;&nbsp;action)</pre>
<div class="block">Configure the <a href="FileCopyDetails.html" title="interface in org.gradle.api.file"><code>FileCopyDetails</code></a> for each file whose path matches the specified Ant-style pattern.
 This is equivalent to using eachFile() and selectively applying a configuration based on the file's path.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>pattern</code> - Ant-style pattern used to match against files' relative paths</dd>
<dd><code>action</code> - action called for the FileCopyDetails of each file matching pattern</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>this</dd>
</dl>
</li>
</ul>
<a name="filesMatching-java.lang.Iterable-org.gradle.api.Action-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>filesMatching</h4>
<pre class="methodSignature"><a href="CopySpec.html" title="interface in org.gradle.api.file">CopySpec</a>&nbsp;filesMatching&#8203;(java.lang.Iterable&lt;java.lang.String&gt;&nbsp;patterns,
                       <a href="../Action.html" title="interface in org.gradle.api">Action</a>&lt;? super <a href="FileCopyDetails.html" title="interface in org.gradle.api.file">FileCopyDetails</a>&gt;&nbsp;action)</pre>
<div class="block">Configure the <a href="FileCopyDetails.html" title="interface in org.gradle.api.file"><code>FileCopyDetails</code></a> for each file whose path matches any of the specified Ant-style patterns.
 This is equivalent to using eachFile() and selectively applying a configuration based on the file's path.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>patterns</code> - Ant-style patterns used to match against files' relative paths</dd>
<dd><code>action</code> - action called for the FileCopyDetails of each file matching pattern</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>this</dd>
</dl>
</li>
</ul>
<a name="filesNotMatching-java.lang.String-org.gradle.api.Action-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>filesNotMatching</h4>
<pre class="methodSignature"><a href="CopySpec.html" title="interface in org.gradle.api.file">CopySpec</a>&nbsp;filesNotMatching&#8203;(java.lang.String&nbsp;pattern,
                          <a href="../Action.html" title="interface in org.gradle.api">Action</a>&lt;? super <a href="FileCopyDetails.html" title="interface in org.gradle.api.file">FileCopyDetails</a>&gt;&nbsp;action)</pre>
<div class="block">Configure the <a href="FileCopyDetails.html" title="interface in org.gradle.api.file"><code>FileCopyDetails</code></a> for each file whose path does not match the specified
 Ant-style pattern. This is equivalent to using eachFile() and selectively applying a configuration based on the
 file's path.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>pattern</code> - Ant-style pattern used to match against files' relative paths</dd>
<dd><code>action</code> - action called for the FileCopyDetails of each file that does not match pattern</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>this</dd>
</dl>
</li>
</ul>
<a name="filesNotMatching-java.lang.Iterable-org.gradle.api.Action-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>filesNotMatching</h4>
<pre class="methodSignature"><a href="CopySpec.html" title="interface in org.gradle.api.file">CopySpec</a>&nbsp;filesNotMatching&#8203;(java.lang.Iterable&lt;java.lang.String&gt;&nbsp;patterns,
                          <a href="../Action.html" title="interface in org.gradle.api">Action</a>&lt;? super <a href="FileCopyDetails.html" title="interface in org.gradle.api.file">FileCopyDetails</a>&gt;&nbsp;action)</pre>
<div class="block">Configure the <a href="FileCopyDetails.html" title="interface in org.gradle.api.file"><code>FileCopyDetails</code></a> for each file whose path does not match any of the specified
 Ant-style patterns. This is equivalent to using eachFile() and selectively applying a configuration based on the
 file's path.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>patterns</code> - Ant-style patterns used to match against files' relative paths</dd>
<dd><code>action</code> - action called for the FileCopyDetails of each file that does not match any pattern</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>this</dd>
</dl>
</li>
</ul>
<a name="with-org.gradle.api.file.CopySpec...-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>with</h4>
<pre class="methodSignature"><a href="CopySpec.html" title="interface in org.gradle.api.file">CopySpec</a>&nbsp;with&#8203;(<a href="CopySpec.html" title="interface in org.gradle.api.file">CopySpec</a>...&nbsp;sourceSpecs)</pre>
<div class="block">Adds the given specs as a child of this spec.

 <pre class='autoTested'>
 def contentSpec = copySpec {
   from("content") {
     include "**&#47;*.txt"
   }
 }

 task copy(type: Copy) {
   into "$buildDir/copy"
   with contentSpec
 }
 </pre></div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>sourceSpecs</code> - The specs to add</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>this</dd>
</dl>
</li>
</ul>
<a name="from-java.lang.Object...-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>from</h4>
<pre class="methodSignature"><a href="CopySpec.html" title="interface in org.gradle.api.file">CopySpec</a>&nbsp;from&#8203;(java.lang.Object...&nbsp;sourcePaths)</pre>
<div class="block">Specifies source files or directories for a copy. The given paths are evaluated as per <a href="../Project.html#files-java.lang.Object...-"><code>Project.files(Object...)</code></a>.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="CopySourceSpec.html#from-java.lang.Object...-">from</a></code>&nbsp;in interface&nbsp;<code><a href="CopySourceSpec.html" title="interface in org.gradle.api.file">CopySourceSpec</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>sourcePaths</code> - Paths to source files for the copy</dd>
</dl>
</li>
</ul>
<a name="from-java.lang.Object-groovy.lang.Closure-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>from</h4>
<pre class="methodSignature"><a href="CopySpec.html" title="interface in org.gradle.api.file">CopySpec</a>&nbsp;from&#8203;(java.lang.Object&nbsp;sourcePath,
              <a href="https://docs.groovy-lang.org/docs/groovy-3.0.17/html/gapi/groovy/lang/DelegatesTo.html?is-external=true" title="class or interface in groovy.lang" class="externalLink">@DelegatesTo</a>(<a href="CopySpec.html" title="interface in org.gradle.api.file">CopySpec.class</a>)
              <a href="https://docs.groovy-lang.org/docs/groovy-3.0.17/html/gapi/groovy/lang/Closure.html?is-external=true" title="class or interface in groovy.lang" class="externalLink">Closure</a>&nbsp;c)</pre>
<div class="block">Specifies the source files or directories for a copy and creates a child <code>CopySourceSpec</code>. The given source
 path is evaluated as per <a href="../Project.html#files-java.lang.Object...-"><code>Project.files(Object...)</code></a> .</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="CopySourceSpec.html#from-java.lang.Object-groovy.lang.Closure-">from</a></code>&nbsp;in interface&nbsp;<code><a href="CopySourceSpec.html" title="interface in org.gradle.api.file">CopySourceSpec</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>sourcePath</code> - Path to source for the copy</dd>
<dd><code>c</code> - closure for configuring the child CopySourceSpec</dd>
</dl>
</li>
</ul>
<a name="from-java.lang.Object-org.gradle.api.Action-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>from</h4>
<pre class="methodSignature"><a href="CopySpec.html" title="interface in org.gradle.api.file">CopySpec</a>&nbsp;from&#8203;(java.lang.Object&nbsp;sourcePath,
              <a href="../Action.html" title="interface in org.gradle.api">Action</a>&lt;? super <a href="CopySpec.html" title="interface in org.gradle.api.file">CopySpec</a>&gt;&nbsp;configureAction)</pre>
<div class="block">Specifies the source files or directories for a copy and creates a child <code>CopySpec</code>. The given source
 path is evaluated as per <a href="../Project.html#files-java.lang.Object...-"><code>Project.files(Object...)</code></a> .</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="CopySourceSpec.html#from-java.lang.Object-org.gradle.api.Action-">from</a></code>&nbsp;in interface&nbsp;<code><a href="CopySourceSpec.html" title="interface in org.gradle.api.file">CopySourceSpec</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>sourcePath</code> - Path to source for the copy</dd>
<dd><code>configureAction</code> - action for configuring the child CopySpec</dd>
</dl>
</li>
</ul>
<a name="setIncludes-java.lang.Iterable-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setIncludes</h4>
<pre class="methodSignature"><a href="CopySpec.html" title="interface in org.gradle.api.file">CopySpec</a>&nbsp;setIncludes&#8203;(java.lang.Iterable&lt;java.lang.String&gt;&nbsp;includes)</pre>
<div class="block">Set the allowable include patterns.  Note that unlike <a href="../tasks/util/PatternFilterable.html#include-java.lang.Iterable-"><code>PatternFilterable.include(Iterable)</code></a> this replaces any previously
 defined includes.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../tasks/util/PatternFilterable.html#setIncludes-java.lang.Iterable-">setIncludes</a></code>&nbsp;in interface&nbsp;<code><a href="../tasks/util/PatternFilterable.html" title="interface in org.gradle.api.tasks.util">PatternFilterable</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>includes</code> - an Iterable providing new include patterns</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>this</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../tasks/util/PatternFilterable.html" title="interface in org.gradle.api.tasks.util"><code>Pattern Format</code></a></dd>
</dl>
</li>
</ul>
<a name="setExcludes-java.lang.Iterable-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setExcludes</h4>
<pre class="methodSignature"><a href="CopySpec.html" title="interface in org.gradle.api.file">CopySpec</a>&nbsp;setExcludes&#8203;(java.lang.Iterable&lt;java.lang.String&gt;&nbsp;excludes)</pre>
<div class="block">Set the allowable exclude patterns.  Note that unlike <a href="../tasks/util/PatternFilterable.html#exclude-java.lang.Iterable-"><code>PatternFilterable.exclude(Iterable)</code></a> this replaces any previously
 defined excludes.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../tasks/util/PatternFilterable.html#setExcludes-java.lang.Iterable-">setExcludes</a></code>&nbsp;in interface&nbsp;<code><a href="../tasks/util/PatternFilterable.html" title="interface in org.gradle.api.tasks.util">PatternFilterable</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>excludes</code> - an Iterable providing new exclude patterns</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>this</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../tasks/util/PatternFilterable.html" title="interface in org.gradle.api.tasks.util"><code>Pattern Format</code></a></dd>
</dl>
</li>
</ul>
<a name="include-java.lang.String...-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>include</h4>
<pre class="methodSignature"><a href="CopySpec.html" title="interface in org.gradle.api.file">CopySpec</a>&nbsp;include&#8203;(java.lang.String...&nbsp;includes)</pre>
<div class="block">Adds an ANT style include pattern. This method may be called multiple times to append new patterns and multiple
 patterns may be specified in a single call.

 If includes are not provided, then all files in this container will be included. If includes are provided, then a
 file must match at least one of the include patterns to be processed.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../tasks/util/PatternFilterable.html#include-java.lang.String...-">include</a></code>&nbsp;in interface&nbsp;<code><a href="../tasks/util/PatternFilterable.html" title="interface in org.gradle.api.tasks.util">PatternFilterable</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>includes</code> - a vararg list of include patterns</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>this</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../tasks/util/PatternFilterable.html" title="interface in org.gradle.api.tasks.util"><code>Pattern Format</code></a></dd>
</dl>
</li>
</ul>
<a name="include-java.lang.Iterable-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>include</h4>
<pre class="methodSignature"><a href="CopySpec.html" title="interface in org.gradle.api.file">CopySpec</a>&nbsp;include&#8203;(java.lang.Iterable&lt;java.lang.String&gt;&nbsp;includes)</pre>
<div class="block">Adds an ANT style include pattern. This method may be called multiple times to append new patterns and multiple
 patterns may be specified in a single call.

 If includes are not provided, then all files in this container will be included. If includes are provided, then a
 file must match at least one of the include patterns to be processed.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../tasks/util/PatternFilterable.html#include-java.lang.Iterable-">include</a></code>&nbsp;in interface&nbsp;<code><a href="../tasks/util/PatternFilterable.html" title="interface in org.gradle.api.tasks.util">PatternFilterable</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>includes</code> - a Iterable providing more include patterns</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>this</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../tasks/util/PatternFilterable.html" title="interface in org.gradle.api.tasks.util"><code>Pattern Format</code></a></dd>
</dl>
</li>
</ul>
<a name="include-org.gradle.api.specs.Spec-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>include</h4>
<pre class="methodSignature"><a href="CopySpec.html" title="interface in org.gradle.api.file">CopySpec</a>&nbsp;include&#8203;(<a href="../specs/Spec.html" title="interface in org.gradle.api.specs">Spec</a>&lt;<a href="FileTreeElement.html" title="interface in org.gradle.api.file">FileTreeElement</a>&gt;&nbsp;includeSpec)</pre>
<div class="block">Adds an include spec. This method may be called multiple times to append new specs.

 If includes are not provided, then all files in this container will be included. If includes are provided, then a
 file must match at least one of the include patterns or specs to be included.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../tasks/util/PatternFilterable.html#include-org.gradle.api.specs.Spec-">include</a></code>&nbsp;in interface&nbsp;<code><a href="../tasks/util/PatternFilterable.html" title="interface in org.gradle.api.tasks.util">PatternFilterable</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>includeSpec</code> - the spec to add</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>this</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../tasks/util/PatternFilterable.html" title="interface in org.gradle.api.tasks.util"><code>Pattern Format</code></a></dd>
</dl>
</li>
</ul>
<a name="include-groovy.lang.Closure-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>include</h4>
<pre class="methodSignature"><a href="CopySpec.html" title="interface in org.gradle.api.file">CopySpec</a>&nbsp;include&#8203;(<a href="https://docs.groovy-lang.org/docs/groovy-3.0.17/html/gapi/groovy/lang/Closure.html?is-external=true" title="class or interface in groovy.lang" class="externalLink">Closure</a>&nbsp;includeSpec)</pre>
<div class="block">Adds an include spec. This method may be called multiple times to append new specs. The given closure is passed a
 <a href="FileTreeElement.html" title="interface in org.gradle.api.file"><code>FileTreeElement</code></a> as its parameter.

 If includes are not provided, then all files in this container will be included. If includes are provided, then a
 file must match at least one of the include patterns or specs to be included.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../tasks/util/PatternFilterable.html#include-groovy.lang.Closure-">include</a></code>&nbsp;in interface&nbsp;<code><a href="../tasks/util/PatternFilterable.html" title="interface in org.gradle.api.tasks.util">PatternFilterable</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>includeSpec</code> - the spec to add</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>this</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../tasks/util/PatternFilterable.html" title="interface in org.gradle.api.tasks.util"><code>Pattern Format</code></a></dd>
</dl>
</li>
</ul>
<a name="exclude-java.lang.String...-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>exclude</h4>
<pre class="methodSignature"><a href="CopySpec.html" title="interface in org.gradle.api.file">CopySpec</a>&nbsp;exclude&#8203;(java.lang.String...&nbsp;excludes)</pre>
<div class="block">Adds an ANT style exclude pattern. This method may be called multiple times to append new patterns and multiple
 patterns may be specified in a single call.

 If excludes are not provided, then no files will be excluded. If excludes are provided, then files must not match
 any exclude pattern to be processed.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../tasks/util/PatternFilterable.html#exclude-java.lang.String...-">exclude</a></code>&nbsp;in interface&nbsp;<code><a href="../tasks/util/PatternFilterable.html" title="interface in org.gradle.api.tasks.util">PatternFilterable</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>excludes</code> - a vararg list of exclude patterns</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>this</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../tasks/util/PatternFilterable.html" title="interface in org.gradle.api.tasks.util"><code>Pattern Format</code></a></dd>
</dl>
</li>
</ul>
<a name="exclude-java.lang.Iterable-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>exclude</h4>
<pre class="methodSignature"><a href="CopySpec.html" title="interface in org.gradle.api.file">CopySpec</a>&nbsp;exclude&#8203;(java.lang.Iterable&lt;java.lang.String&gt;&nbsp;excludes)</pre>
<div class="block">Adds an ANT style exclude pattern. This method may be called multiple times to append new patterns and multiple
 patterns may be specified in a single call.

 If excludes are not provided, then no files will be excluded. If excludes are provided, then files must not match
 any exclude pattern to be processed.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../tasks/util/PatternFilterable.html#exclude-java.lang.Iterable-">exclude</a></code>&nbsp;in interface&nbsp;<code><a href="../tasks/util/PatternFilterable.html" title="interface in org.gradle.api.tasks.util">PatternFilterable</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>excludes</code> - a Iterable providing new exclude patterns</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>this</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../tasks/util/PatternFilterable.html" title="interface in org.gradle.api.tasks.util"><code>Pattern Format</code></a></dd>
</dl>
</li>
</ul>
<a name="exclude-org.gradle.api.specs.Spec-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>exclude</h4>
<pre class="methodSignature"><a href="CopySpec.html" title="interface in org.gradle.api.file">CopySpec</a>&nbsp;exclude&#8203;(<a href="../specs/Spec.html" title="interface in org.gradle.api.specs">Spec</a>&lt;<a href="FileTreeElement.html" title="interface in org.gradle.api.file">FileTreeElement</a>&gt;&nbsp;excludeSpec)</pre>
<div class="block">Adds an exclude spec. This method may be called multiple times to append new specs.

 If excludes are not provided, then no files will be excluded. If excludes are provided, then files must not match
 any exclude pattern to be processed.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../tasks/util/PatternFilterable.html#exclude-org.gradle.api.specs.Spec-">exclude</a></code>&nbsp;in interface&nbsp;<code><a href="../tasks/util/PatternFilterable.html" title="interface in org.gradle.api.tasks.util">PatternFilterable</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>excludeSpec</code> - the spec to add</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>this</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../tasks/util/PatternFilterable.html" title="interface in org.gradle.api.tasks.util"><code>Pattern Format</code></a></dd>
</dl>
</li>
</ul>
<a name="exclude-groovy.lang.Closure-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>exclude</h4>
<pre class="methodSignature"><a href="CopySpec.html" title="interface in org.gradle.api.file">CopySpec</a>&nbsp;exclude&#8203;(<a href="https://docs.groovy-lang.org/docs/groovy-3.0.17/html/gapi/groovy/lang/Closure.html?is-external=true" title="class or interface in groovy.lang" class="externalLink">Closure</a>&nbsp;excludeSpec)</pre>
<div class="block">Adds an exclude spec. This method may be called multiple times to append new specs.The given closure is passed a
 <a href="FileTreeElement.html" title="interface in org.gradle.api.file"><code>FileTreeElement</code></a> as its parameter. The closure should return true or false. Example:

 <pre class='autoTested'>
 copySpec {
   from 'source'
   into 'destination'
   //an example of excluding files from certain configuration:
   exclude { it.file in configurations.someConf.files }
 }
 </pre>

 If excludes are not provided, then no files will be excluded. If excludes are provided, then files must not match
 any exclude pattern to be processed.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../tasks/util/PatternFilterable.html#exclude-groovy.lang.Closure-">exclude</a></code>&nbsp;in interface&nbsp;<code><a href="../tasks/util/PatternFilterable.html" title="interface in org.gradle.api.tasks.util">PatternFilterable</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>excludeSpec</code> - the spec to add</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>this</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../tasks/util/PatternFilterable.html" title="interface in org.gradle.api.tasks.util"><code>Pattern Format</code></a></dd>
</dl>
</li>
</ul>
<a name="into-java.lang.Object-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>into</h4>
<pre class="methodSignature"><a href="CopySpec.html" title="interface in org.gradle.api.file">CopySpec</a>&nbsp;into&#8203;(java.lang.Object&nbsp;destPath)</pre>
<div class="block">Specifies the destination directory for a copy. The destination is evaluated as per <a href="../Project.html#file-java.lang.Object-"><code>Project.file(Object)</code></a>.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="CopyProcessingSpec.html#into-java.lang.Object-">into</a></code>&nbsp;in interface&nbsp;<code><a href="CopyProcessingSpec.html" title="interface in org.gradle.api.file">CopyProcessingSpec</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>destPath</code> - Path to the destination directory for a Copy</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>this</dd>
</dl>
</li>
</ul>
<a name="into-java.lang.Object-groovy.lang.Closure-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>into</h4>
<pre class="methodSignature"><a href="CopySpec.html" title="interface in org.gradle.api.file">CopySpec</a>&nbsp;into&#8203;(java.lang.Object&nbsp;destPath,
              <a href="https://docs.groovy-lang.org/docs/groovy-3.0.17/html/gapi/groovy/lang/DelegatesTo.html?is-external=true" title="class or interface in groovy.lang" class="externalLink">@DelegatesTo</a>(<a href="CopySpec.html" title="interface in org.gradle.api.file">CopySpec.class</a>)
              <a href="https://docs.groovy-lang.org/docs/groovy-3.0.17/html/gapi/groovy/lang/Closure.html?is-external=true" title="class or interface in groovy.lang" class="externalLink">Closure</a>&nbsp;configureClosure)</pre>
<div class="block">Creates and configures a child <code>CopySpec</code> with the given destination path.
 The destination is evaluated as per <a href="../Project.html#file-java.lang.Object-"><code>Project.file(Object)</code></a>.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>destPath</code> - Path to the destination directory for a Copy</dd>
<dd><code>configureClosure</code> - The closure to use to configure the child <code>CopySpec</code>.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>this</dd>
</dl>
</li>
</ul>
<a name="into-java.lang.Object-org.gradle.api.Action-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>into</h4>
<pre class="methodSignature"><a href="CopySpec.html" title="interface in org.gradle.api.file">CopySpec</a>&nbsp;into&#8203;(java.lang.Object&nbsp;destPath,
              <a href="../Action.html" title="interface in org.gradle.api">Action</a>&lt;? super <a href="CopySpec.html" title="interface in org.gradle.api.file">CopySpec</a>&gt;&nbsp;copySpec)</pre>
<div class="block">Creates and configures a child <code>CopySpec</code> with the given destination path.
 The destination is evaluated as per <a href="../Project.html#file-java.lang.Object-"><code>Project.file(Object)</code></a>.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>destPath</code> - Path to the destination directory for a Copy</dd>
<dd><code>copySpec</code> - The action to use to configure the child <code>CopySpec</code>.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>this</dd>
</dl>
</li>
</ul>
<a name="rename-groovy.lang.Closure-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>rename</h4>
<pre class="methodSignature"><a href="CopySpec.html" title="interface in org.gradle.api.file">CopySpec</a>&nbsp;rename&#8203;(<a href="https://docs.groovy-lang.org/docs/groovy-3.0.17/html/gapi/groovy/lang/Closure.html?is-external=true" title="class or interface in groovy.lang" class="externalLink">Closure</a>&nbsp;closure)</pre>
<div class="block">Renames a source file. The closure will be called with a single parameter, the name of the file.
 The closure should return a String object with a new target name. The closure may return null,
 in which case the original name will be used.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="CopyProcessingSpec.html#rename-groovy.lang.Closure-">rename</a></code>&nbsp;in interface&nbsp;<code><a href="CopyProcessingSpec.html" title="interface in org.gradle.api.file">CopyProcessingSpec</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>closure</code> - rename closure</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>this</dd>
</dl>
</li>
</ul>
<a name="rename-org.gradle.api.Transformer-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>rename</h4>
<pre class="methodSignature"><a href="CopySpec.html" title="interface in org.gradle.api.file">CopySpec</a>&nbsp;rename&#8203;(<a href="../Transformer.html" title="interface in org.gradle.api">Transformer</a>&lt;@Nullable java.lang.String,&#8203;java.lang.String&gt;&nbsp;renamer)</pre>
<div class="block">Renames a source file. The function will be called with a single parameter, the name of the file.
 The function should return a new target name. The function may return null,
 in which case the original name will be used.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="CopyProcessingSpec.html#rename-org.gradle.api.Transformer-">rename</a></code>&nbsp;in interface&nbsp;<code><a href="CopyProcessingSpec.html" title="interface in org.gradle.api.file">CopyProcessingSpec</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>renamer</code> - rename function</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>this</dd>
</dl>
</li>
</ul>
<a name="rename-java.lang.String-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>rename</h4>
<pre class="methodSignature"><a href="CopySpec.html" title="interface in org.gradle.api.file">CopySpec</a>&nbsp;rename&#8203;(java.lang.String&nbsp;sourceRegEx,
                java.lang.String&nbsp;replaceWith)</pre>
<div class="block">Renames files based on a regular expression.  Uses java.util.regex type of regular expressions.  Note that the
 replace string should use the '$1' syntax to refer to capture groups in the source regular expression.  Files
 that do not match the source regular expression will be copied with the original name.

 <p> Example:
 <pre>
 rename '(.*)_OEM_BLUE_(.*)', '$1$2'
 </pre>
 would map the file 'style_OEM_BLUE_.css' to 'style.css'</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="CopyProcessingSpec.html#rename-java.lang.String-java.lang.String-">rename</a></code>&nbsp;in interface&nbsp;<code><a href="CopyProcessingSpec.html" title="interface in org.gradle.api.file">CopyProcessingSpec</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>sourceRegEx</code> - Source regular expression</dd>
<dd><code>replaceWith</code> - Replacement string (use $ syntax for capture groups)</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>this</dd>
</dl>
</li>
</ul>
<a name="rename-java.util.regex.Pattern-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>rename</h4>
<pre class="methodSignature"><a href="CopyProcessingSpec.html" title="interface in org.gradle.api.file">CopyProcessingSpec</a>&nbsp;rename&#8203;(java.util.regex.Pattern&nbsp;sourceRegEx,
                          java.lang.String&nbsp;replaceWith)</pre>
<div class="block">Renames files based on a regular expression. See <a href="CopyProcessingSpec.html#rename-java.lang.String-java.lang.String-"><code>CopyProcessingSpec.rename(String, String)</code></a>.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="CopyProcessingSpec.html#rename-java.util.regex.Pattern-java.lang.String-">rename</a></code>&nbsp;in interface&nbsp;<code><a href="CopyProcessingSpec.html" title="interface in org.gradle.api.file">CopyProcessingSpec</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>sourceRegEx</code> - Source regular expression</dd>
<dd><code>replaceWith</code> - Replacement string (use $ syntax for capture groups)</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>this</dd>
</dl>
</li>
</ul>
<a name="filter-java.util.Map-java.lang.Class-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>filter</h4>
<pre class="methodSignature"><a href="CopySpec.html" title="interface in org.gradle.api.file">CopySpec</a>&nbsp;filter&#8203;(java.util.Map&lt;java.lang.String,&#8203;?&gt;&nbsp;properties,
                java.lang.Class&lt;? extends java.io.FilterReader&gt;&nbsp;filterType)</pre>
<div class="block"><p>Adds a content filter to be used during the copy.  Multiple calls to filter, add additional filters to the
 filter chain.  Each filter should implement <code>java.io.FilterReader</code>. Include <code>
 org.apache.tools.ant.filters.*</code> for access to all the standard Ant filters.</p>

 <p>Filter properties may be specified using groovy map syntax.</p>

 <p> Examples:
 <pre>
    filter(HeadFilter, lines:25, skip:2)
    filter(ReplaceTokens, tokens:[copyright:'2009', version:'2.3.1'])
 </pre></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="ContentFilterable.html#filter-java.util.Map-java.lang.Class-">filter</a></code>&nbsp;in interface&nbsp;<code><a href="ContentFilterable.html" title="interface in org.gradle.api.file">ContentFilterable</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>properties</code> - map of filter properties</dd>
<dd><code>filterType</code> - Class of filter to add</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>this</dd>
</dl>
</li>
</ul>
<a name="filter-java.lang.Class-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>filter</h4>
<pre class="methodSignature"><a href="CopySpec.html" title="interface in org.gradle.api.file">CopySpec</a>&nbsp;filter&#8203;(java.lang.Class&lt;? extends java.io.FilterReader&gt;&nbsp;filterType)</pre>
<div class="block"><p>Adds a content filter to be used during the copy.  Multiple calls to filter, add additional filters to the
 filter chain.  Each filter should implement <code>java.io.FilterReader</code>. Include <code>
 org.apache.tools.ant.filters.*</code> for access to all the standard Ant filters.</p>

 <p> Examples:
 <pre>
    filter(StripJavaComments)
    filter(com.mycompany.project.CustomFilter)
 </pre></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="ContentFilterable.html#filter-java.lang.Class-">filter</a></code>&nbsp;in interface&nbsp;<code><a href="ContentFilterable.html" title="interface in org.gradle.api.file">ContentFilterable</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>filterType</code> - Class of filter to add</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>this</dd>
</dl>
</li>
</ul>
<a name="filter-groovy.lang.Closure-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>filter</h4>
<pre class="methodSignature"><a href="CopySpec.html" title="interface in org.gradle.api.file">CopySpec</a>&nbsp;filter&#8203;(<a href="https://docs.groovy-lang.org/docs/groovy-3.0.17/html/gapi/groovy/lang/Closure.html?is-external=true" title="class or interface in groovy.lang" class="externalLink">Closure</a>&nbsp;closure)</pre>
<div class="block">Adds a content filter based on the provided closure.  The Closure will be called with each line (stripped of line
 endings) and should return a String to replace the line or <code>null</code> to remove the line.  If every line is
 removed, the result will be an empty file, not an absent one.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="ContentFilterable.html#filter-groovy.lang.Closure-">filter</a></code>&nbsp;in interface&nbsp;<code><a href="ContentFilterable.html" title="interface in org.gradle.api.file">ContentFilterable</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>closure</code> - to implement line based filtering</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>this</dd>
</dl>
</li>
</ul>
<a name="filter-org.gradle.api.Transformer-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>filter</h4>
<pre class="methodSignature"><a href="CopySpec.html" title="interface in org.gradle.api.file">CopySpec</a>&nbsp;filter&#8203;(<a href="../Transformer.html" title="interface in org.gradle.api">Transformer</a>&lt;@Nullable java.lang.String,&#8203;java.lang.String&gt;&nbsp;transformer)</pre>
<div class="block">Adds a content filter based on the provided transformer.  The Closure will be called with each line (stripped of line
 endings) and should return a String to replace the line or <code>null</code> to remove the line.  If every line is
 removed, the result will be an empty file, not an absent one.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="ContentFilterable.html#filter-org.gradle.api.Transformer-">filter</a></code>&nbsp;in interface&nbsp;<code><a href="ContentFilterable.html" title="interface in org.gradle.api.file">ContentFilterable</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>transformer</code> - to implement line based filtering</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>this</dd>
</dl>
</li>
</ul>
<a name="expand-java.util.Map-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>expand</h4>
<pre class="methodSignature"><a href="CopySpec.html" title="interface in org.gradle.api.file">CopySpec</a>&nbsp;expand&#8203;(java.util.Map&lt;java.lang.String,&#8203;?&gt;&nbsp;properties)</pre>
<div class="block"><p>Expands property references in each file as it is copied. More specifically, each file is transformed using
 Groovy's <a href="https://docs.groovy-lang.org/docs/groovy-3.0.17/html/gapi/groovy/text/SimpleTemplateEngine.html?is-external=true" title="class or interface in groovy.text" class="externalLink"><code>SimpleTemplateEngine</code></a>. This means you can use simple property references, such as
 <code>$property</code> or <code>${property}</code> in the file. You can also include arbitrary Groovy code in the
 file, such as <code>${version ?: 'unknown'}</code> or <code>${classpath*.name.join(' ')}</code>
 <p>
 Note that all escape sequences (<code>\n</code>, <code>\t</code>, <code>\\</code>, etc) are converted to the symbols
 they represent, so, for example, <code>\n</code> becomes newline. If this is undesirable then <a href="ContentFilterable.html#expand-java.util.Map-org.gradle.api.Action-"><code>ContentFilterable.expand(Map, Action)</code></a>
 should be used to disable this behavior.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="ContentFilterable.html#expand-java.util.Map-">expand</a></code>&nbsp;in interface&nbsp;<code><a href="ContentFilterable.html" title="interface in org.gradle.api.file">ContentFilterable</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>properties</code> - reference-to-value map for substitution</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>this</dd>
</dl>
</li>
</ul>
<a name="expand-java.util.Map-org.gradle.api.Action-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>expand</h4>
<pre class="methodSignature"><a href="CopySpec.html" title="interface in org.gradle.api.file">CopySpec</a>&nbsp;expand&#8203;(java.util.Map&lt;java.lang.String,&#8203;?&gt;&nbsp;properties,
                <a href="../Action.html" title="interface in org.gradle.api">Action</a>&lt;? super <a href="ExpandDetails.html" title="interface in org.gradle.api.file">ExpandDetails</a>&gt;&nbsp;action)</pre>
<div class="block"><p>Expands property references in each file as it is copied. More specifically, each file is transformed using
 Groovy's <a href="https://docs.groovy-lang.org/docs/groovy-3.0.17/html/gapi/groovy/text/SimpleTemplateEngine.html?is-external=true" title="class or interface in groovy.text" class="externalLink"><code>SimpleTemplateEngine</code></a>. This means you can use simple property references, such as
 <code>$property</code> or <code>${property}</code> in the file. You can also include arbitrary Groovy code in the
 file, such as <code>${version ?: 'unknown'}</code> or <code>${classpath*.name.join(' ')}</code>. The template
 engine can be configured with the provided action.
 <p>
 Note that by default all escape sequences (<code>\n</code>, <code>\t</code>, <code>\\</code>, etc) are converted to the symbols
 they represent, so, for example, <code>\n</code> becomes newline. This behavior is controlled by
 <a href="ExpandDetails.html#getEscapeBackslash--"><code>ExpandDetails.getEscapeBackslash()</code></a> property. It should be set to <code>true</code> to disable escape sequences
 conversion:
 <pre>
  expand(one: '1', two: 2) {
      escapeBackslash = true
  }
 </pre></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="ContentFilterable.html#expand-java.util.Map-org.gradle.api.Action-">expand</a></code>&nbsp;in interface&nbsp;<code><a href="ContentFilterable.html" title="interface in org.gradle.api.file">ContentFilterable</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>properties</code> - reference-to-value map for substitution</dd>
<dd><code>action</code> - action to perform additional configuration of the underlying template engine</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>this</dd>
</dl>
</li>
</ul>
<a name="eachFile-org.gradle.api.Action-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>eachFile</h4>
<pre class="methodSignature"><a href="CopySpec.html" title="interface in org.gradle.api.file">CopySpec</a>&nbsp;eachFile&#8203;(<a href="../Action.html" title="interface in org.gradle.api">Action</a>&lt;? super <a href="FileCopyDetails.html" title="interface in org.gradle.api.file">FileCopyDetails</a>&gt;&nbsp;action)</pre>
<div class="block">Adds an action to be applied to each file as it is about to be copied into its destination. The action can change
 the destination path of the file, filter the contents of the file, or exclude the file from the result entirely.
 Actions are executed in the order added, and are inherited from the parent spec.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="CopyProcessingSpec.html#eachFile-org.gradle.api.Action-">eachFile</a></code>&nbsp;in interface&nbsp;<code><a href="CopyProcessingSpec.html" title="interface in org.gradle.api.file">CopyProcessingSpec</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>action</code> - The action to execute.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>this</dd>
</dl>
</li>
</ul>
<a name="eachFile-groovy.lang.Closure-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>eachFile</h4>
<pre class="methodSignature"><a href="CopySpec.html" title="interface in org.gradle.api.file">CopySpec</a>&nbsp;eachFile&#8203;(<a href="https://docs.groovy-lang.org/docs/groovy-3.0.17/html/gapi/groovy/lang/DelegatesTo.html?is-external=true" title="class or interface in groovy.lang" class="externalLink">@DelegatesTo</a>(<a href="FileCopyDetails.html" title="interface in org.gradle.api.file">FileCopyDetails.class</a>)
                  <a href="https://docs.groovy-lang.org/docs/groovy-3.0.17/html/gapi/groovy/lang/Closure.html?is-external=true" title="class or interface in groovy.lang" class="externalLink">Closure</a>&nbsp;closure)</pre>
<div class="block">Adds an action to be applied to each file as it about to be copied into its destination. The given closure is
 called with a <a href="FileCopyDetails.html" title="interface in org.gradle.api.file"><code>FileCopyDetails</code></a> as its parameter. Actions are executed in the order
 added, and are inherited from the parent spec.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="CopyProcessingSpec.html#eachFile-groovy.lang.Closure-">eachFile</a></code>&nbsp;in interface&nbsp;<code><a href="CopyProcessingSpec.html" title="interface in org.gradle.api.file">CopyProcessingSpec</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>closure</code> - The action to execute.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>this</dd>
</dl>
</li>
</ul>
<a name="getFilteringCharset--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getFilteringCharset</h4>
<pre class="methodSignature">java.lang.String&nbsp;getFilteringCharset()</pre>
<div class="block">Gets the charset used to read and write files when filtering.
 By default, the JVM default charset is used.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the charset used to read and write files when filtering</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>2.14</dd>
</dl>
</li>
</ul>
<a name="setFilteringCharset-java.lang.String-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>setFilteringCharset</h4>
<pre class="methodSignature">void&nbsp;setFilteringCharset&#8203;(java.lang.String&nbsp;charset)</pre>
<div class="block">Specifies the charset used to read and write files when filtering.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>charset</code> - the name of the charset to use when filtering files</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>2.14</dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
