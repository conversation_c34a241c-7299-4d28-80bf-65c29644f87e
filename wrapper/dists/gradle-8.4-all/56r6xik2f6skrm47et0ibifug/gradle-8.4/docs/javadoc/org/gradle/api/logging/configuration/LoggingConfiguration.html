<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc -->
<title>LoggingConfiguration (Gradle API 8.4)</title>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<link rel="stylesheet" type="text/css" href="../../../../../javadoc.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../jquery/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
<script type="text/javascript" src="../../../../../jquery/jszip/dist/jszip.min.js"></script>
<script type="text/javascript" src="../../../../../jquery/jszip-utils/dist/jszip-utils.min.js"></script>
<!--[if IE]>
<script type="text/javascript" src="../../../../../jquery/jszip-utils/dist/jszip-utils-ie.min.js"></script>
<![endif]-->
<script type="text/javascript" src="../../../../../jquery/jquery-3.6.0.min.js"></script>
<script type="text/javascript" src="../../../../../jquery/jquery-ui.min.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="LoggingConfiguration (Gradle API 8.4)";
        }
    }
    catch(err) {
    }
//-->
var data = {"i0":6,"i1":6,"i2":6,"i3":6,"i4":6,"i5":6,"i6":6,"i7":6};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],4:["t3","Abstract Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
var pathtoroot = "../../../../../";
var useModuleDirectories = false;
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="fixedNav">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../../allclasses.html">All&nbsp;Classes</a></li>
</ul>
<ul class="navListSearch">
<li><label for="search">SEARCH:</label>
<input type="text" id="search" value="search" disabled="disabled">
<input type="reset" id="reset" value="reset" disabled="disabled">
</li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
</div>
<div class="navPadding">&nbsp;</div>
<script type="text/javascript"><!--
$('.navPadding').css('padding-top', $('.fixedNav').css("height"));
//-->
</script>
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle"><span class="packageLabelInType">Package</span>&nbsp;<a href="package-summary.html">org.gradle.api.logging.configuration</a></div>
<h2 title="Interface LoggingConfiguration" class="title">Interface LoggingConfiguration</h2>
</div>
<div class="contentContainer">
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Known Implementing Classes:</dt>
<dd><code><a href="../../../StartParameter.html" title="class in org.gradle">StartParameter</a></code></dd>
</dl>
<hr>
<pre>public interface <span class="typeNameLabel">LoggingConfiguration</span></pre>
<div class="block">A <code>LoggingConfiguration</code> defines the logging settings for a Gradle build.</div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t3" class="tableTab"><span><a href="javascript:show(4);">Abstract Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colSecond" scope="col">Method</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code><a href="ConsoleOutput.html" title="enum in org.gradle.api.logging.configuration">ConsoleOutput</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getConsoleOutput--">getConsoleOutput</a></span>()</code></th>
<td class="colLast">
<div class="block">Returns the style of logging output that should be written to the console.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code><a href="../LogLevel.html" title="enum in org.gradle.api.logging">LogLevel</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getLogLevel--">getLogLevel</a></span>()</code></th>
<td class="colLast">
<div class="block">Returns the minimum logging level to use.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code><a href="ShowStacktrace.html" title="enum in org.gradle.api.logging.configuration">ShowStacktrace</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getShowStacktrace--">getShowStacktrace</a></span>()</code></th>
<td class="colLast">
<div class="block">Returns the detail that should be included in stacktraces.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code><a href="WarningMode.html" title="enum in org.gradle.api.logging.configuration">WarningMode</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getWarningMode--">getWarningMode</a></span>()</code></th>
<td class="colLast">
<div class="block">Specifies which type of warnings should be written to the console.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setConsoleOutput-org.gradle.api.logging.configuration.ConsoleOutput-">setConsoleOutput</a></span>&#8203;(<a href="ConsoleOutput.html" title="enum in org.gradle.api.logging.configuration">ConsoleOutput</a>&nbsp;consoleOutput)</code></th>
<td class="colLast">
<div class="block">Specifies the style of logging output that should be written to the console.</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setLogLevel-org.gradle.api.logging.LogLevel-">setLogLevel</a></span>&#8203;(<a href="../LogLevel.html" title="enum in org.gradle.api.logging">LogLevel</a>&nbsp;logLevel)</code></th>
<td class="colLast">
<div class="block">Specifies the minimum logging level to use.</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setShowStacktrace-org.gradle.api.logging.configuration.ShowStacktrace-">setShowStacktrace</a></span>&#8203;(<a href="ShowStacktrace.html" title="enum in org.gradle.api.logging.configuration">ShowStacktrace</a>&nbsp;showStacktrace)</code></th>
<td class="colLast">
<div class="block">Sets the detail that should be included in stacktraces.</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setWarningMode-org.gradle.api.logging.configuration.WarningMode-">setWarningMode</a></span>&#8203;(<a href="WarningMode.html" title="enum in org.gradle.api.logging.configuration">WarningMode</a>&nbsp;warningMode)</code></th>
<td class="colLast">
<div class="block">Specifies which type of warnings should be written to the console.</div>
</td>
</tr>
</table>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="getLogLevel--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getLogLevel</h4>
<pre class="methodSignature"><a href="../LogLevel.html" title="enum in org.gradle.api.logging">LogLevel</a>&nbsp;getLogLevel()</pre>
<div class="block">Returns the minimum logging level to use. All log messages with a lower log level are ignored.
 Defaults to <a href="../LogLevel.html#LIFECYCLE"><code>LogLevel.LIFECYCLE</code></a>.</div>
</li>
</ul>
<a name="setLogLevel-org.gradle.api.logging.LogLevel-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setLogLevel</h4>
<pre class="methodSignature">void&nbsp;setLogLevel&#8203;(<a href="../LogLevel.html" title="enum in org.gradle.api.logging">LogLevel</a>&nbsp;logLevel)</pre>
<div class="block">Specifies the minimum logging level to use. All log messages with a lower log level are ignored.</div>
</li>
</ul>
<a name="getConsoleOutput--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getConsoleOutput</h4>
<pre class="methodSignature"><a href="ConsoleOutput.html" title="enum in org.gradle.api.logging.configuration">ConsoleOutput</a>&nbsp;getConsoleOutput()</pre>
<div class="block">Returns the style of logging output that should be written to the console.
 Defaults to <a href="ConsoleOutput.html#Auto"><code>ConsoleOutput.Auto</code></a></div>
</li>
</ul>
<a name="setConsoleOutput-org.gradle.api.logging.configuration.ConsoleOutput-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setConsoleOutput</h4>
<pre class="methodSignature">void&nbsp;setConsoleOutput&#8203;(<a href="ConsoleOutput.html" title="enum in org.gradle.api.logging.configuration">ConsoleOutput</a>&nbsp;consoleOutput)</pre>
<div class="block">Specifies the style of logging output that should be written to the console.</div>
</li>
</ul>
<a name="getWarningMode--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getWarningMode</h4>
<pre class="methodSignature"><a href="WarningMode.html" title="enum in org.gradle.api.logging.configuration">WarningMode</a>&nbsp;getWarningMode()</pre>
<div class="block">Specifies which type of warnings should be written to the console.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>4.5</dd>
</dl>
</li>
</ul>
<a name="setWarningMode-org.gradle.api.logging.configuration.WarningMode-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setWarningMode</h4>
<pre class="methodSignature">void&nbsp;setWarningMode&#8203;(<a href="WarningMode.html" title="enum in org.gradle.api.logging.configuration">WarningMode</a>&nbsp;warningMode)</pre>
<div class="block">Specifies which type of warnings should be written to the console.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>4.5</dd>
</dl>
</li>
</ul>
<a name="getShowStacktrace--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getShowStacktrace</h4>
<pre class="methodSignature"><a href="ShowStacktrace.html" title="enum in org.gradle.api.logging.configuration">ShowStacktrace</a>&nbsp;getShowStacktrace()</pre>
<div class="block">Returns the detail that should be included in stacktraces. Defaults to <a href="ShowStacktrace.html#INTERNAL_EXCEPTIONS"><code>ShowStacktrace.INTERNAL_EXCEPTIONS</code></a>.</div>
</li>
</ul>
<a name="setShowStacktrace-org.gradle.api.logging.configuration.ShowStacktrace-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>setShowStacktrace</h4>
<pre class="methodSignature">void&nbsp;setShowStacktrace&#8203;(<a href="ShowStacktrace.html" title="enum in org.gradle.api.logging.configuration">ShowStacktrace</a>&nbsp;showStacktrace)</pre>
<div class="block">Sets the detail that should be included in stacktraces.</div>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../../allclasses.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
