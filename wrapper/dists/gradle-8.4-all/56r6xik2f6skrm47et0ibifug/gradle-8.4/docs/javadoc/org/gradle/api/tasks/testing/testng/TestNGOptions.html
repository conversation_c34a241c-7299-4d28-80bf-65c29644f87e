<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc -->
<title>TestNGOptions (Gradle API 8.4)</title>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<link rel="stylesheet" type="text/css" href="../../../../../../javadoc.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../../jquery/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../../jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="../../../../../../script.js"></script>
<script type="text/javascript" src="../../../../../../jquery/jszip/dist/jszip.min.js"></script>
<script type="text/javascript" src="../../../../../../jquery/jszip-utils/dist/jszip-utils.min.js"></script>
<!--[if IE]>
<script type="text/javascript" src="../../../../../../jquery/jszip-utils/dist/jszip-utils-ie.min.js"></script>
<![endif]-->
<script type="text/javascript" src="../../../../../../jquery/jquery-3.6.0.min.js"></script>
<script type="text/javascript" src="../../../../../../jquery/jquery-ui.min.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="TestNGOptions (Gradle API 8.4)";
        }
    }
    catch(err) {
    }
//-->
var data = {"i0":10,"i1":10,"i2":10,"i3":10,"i4":10,"i5":10,"i6":10,"i7":10,"i8":10,"i9":10,"i10":10,"i11":10,"i12":10,"i13":10,"i14":10,"i15":10,"i16":10,"i17":10,"i18":10,"i19":10,"i20":10,"i21":10,"i22":10,"i23":10,"i24":10,"i25":10,"i26":10,"i27":10,"i28":10,"i29":10,"i30":10,"i31":10,"i32":10,"i33":10,"i34":10,"i35":10,"i36":10,"i37":10,"i38":10,"i39":10,"i40":10,"i41":10,"i42":10,"i43":10,"i44":10,"i45":10};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
var pathtoroot = "../../../../../../";
var useModuleDirectories = false;
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="fixedNav">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../../../allclasses.html">All&nbsp;Classes</a></li>
</ul>
<ul class="navListSearch">
<li><label for="search">SEARCH:</label>
<input type="text" id="search" value="search" disabled="disabled">
<input type="reset" id="reset" value="reset" disabled="disabled">
</li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field.summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#field.detail">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
</div>
<div class="navPadding">&nbsp;</div>
<script type="text/javascript"><!--
$('.navPadding').css('padding-top', $('.fixedNav').css("height"));
//-->
</script>
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle"><span class="packageLabelInType">Package</span>&nbsp;<a href="package-summary.html">org.gradle.api.tasks.testing.testng</a></div>
<h2 title="Class TestNGOptions" class="title">Class TestNGOptions</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li><a href="../TestFrameworkOptions.html" title="class in org.gradle.api.tasks.testing">org.gradle.api.tasks.testing.TestFrameworkOptions</a></li>
<li>
<ul class="inheritance">
<li>org.gradle.api.tasks.testing.testng.TestNGOptions</li>
</ul>
</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<hr>
<pre>public abstract class <span class="typeNameLabel">TestNGOptions</span>
extends <a href="../TestFrameworkOptions.html" title="class in org.gradle.api.tasks.testing">TestFrameworkOptions</a></pre>
<div class="block">The TestNG specific test options.</div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- =========== FIELD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.summary">
<!--   -->
</a>
<h3>Field Summary</h3>
<table class="memberSummary" summary="Field Summary table, listing fields, and an explanation">
<caption><span>Fields</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colSecond" scope="col">Field</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#DEFAULT_CONFIG_FAILURE_POLICY">DEFAULT_CONFIG_FAILURE_POLICY</a></span></code></th>
<td class="colLast">&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="memberSummary" summary="Constructor Summary table, listing constructors, and an explanation">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Constructor</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tr class="altColor">
<th class="colConstructorName" scope="row"><code><span class="memberNameLink"><a href="#TestNGOptions-org.gradle.api.file.ProjectLayout-">TestNGOptions</a></span>&#8203;(<a href="../../../file/ProjectLayout.html" title="interface in org.gradle.api.file">ProjectLayout</a>&nbsp;projectLayout)</code></th>
<td class="colLast">&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colSecond" scope="col">Method</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#copyFrom-org.gradle.api.tasks.testing.testng.TestNGOptions-">copyFrom</a></span>&#8203;(<a href="TestNGOptions.html" title="class in org.gradle.api.tasks.testing.testng">TestNGOptions</a>&nbsp;other)</code></th>
<td class="colLast">
<div class="block">Copies the options from the source options into the current one.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code><a href="TestNGOptions.html" title="class in org.gradle.api.tasks.testing.testng">TestNGOptions</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#excludeGroups-java.lang.String...-">excludeGroups</a></span>&#8203;(java.lang.String...&nbsp;excludeGroups)</code></th>
<td class="colLast">&nbsp;</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getConfigFailurePolicy--">getConfigFailurePolicy</a></span>()</code></th>
<td class="colLast">
<div class="block">Option for what to do for other tests that use a configuration step when that step fails.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>java.util.Set&lt;java.lang.String&gt;</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getExcludeGroups--">getExcludeGroups</a></span>()</code></th>
<td class="colLast">
<div class="block">The set of groups to exclude.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getGroupByInstances--">getGroupByInstances</a></span>()</code></th>
<td class="colLast">&nbsp;</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>java.util.Set&lt;java.lang.String&gt;</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getIncludeGroups--">getIncludeGroups</a></span>()</code></th>
<td class="colLast">
<div class="block">The set of groups to run.</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>java.util.Set&lt;java.lang.String&gt;</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getListeners--">getListeners</a></span>()</code></th>
<td class="colLast">
<div class="block">Fully qualified classes that are TestNG listeners (instances of org.testng.ITestListener or org.testng.IReporter).</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>java.io.File</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getOutputDirectory--">getOutputDirectory</a></span>()</code></th>
<td class="colLast">
<div class="block">The location to write TestNG's output.</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getParallel--">getParallel</a></span>()</code></th>
<td class="colLast">
<div class="block">The parallel mode to use for running the tests - one of the following modes: methods, tests, classes or instances.</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getPreserveOrder--">getPreserveOrder</a></span>()</code></th>
<td class="colLast">&nbsp;</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code>protected java.io.File</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getProjectDir--">getProjectDir</a></span>()</code></th>
<td class="colLast">&nbsp;</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code>java.lang.String</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getSuiteName--">getSuiteName</a></span>()</code></th>
<td class="colLast">
<div class="block">Sets the default name of the test suite, if one is not specified in a suite XML file or in the source code.</div>
</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code>java.util.List&lt;java.io.File&gt;</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getSuites-java.io.File-">getSuites</a></span>&#8203;(java.io.File&nbsp;testSuitesDir)</code></th>
<td class="colLast">&nbsp;</td>
</tr>
<tr id="i13" class="rowColor">
<td class="colFirst"><code>protected java.lang.String</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getSuiteXml--">getSuiteXml</a></span>()</code></th>
<td class="colLast">
<div class="block">Returns the XML generated using <a href="#suiteXmlBuilder--"><code>suiteXmlBuilder()</code></a>, if any.</div>
</td>
</tr>
<tr id="i14" class="altColor">
<td class="colFirst"><code><a href="https://docs.groovy-lang.org/docs/groovy-3.0.17/html/gapi/groovy/xml/MarkupBuilder.html?is-external=true" title="class or interface in groovy.xml" class="externalLink">MarkupBuilder</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getSuiteXmlBuilder--">getSuiteXmlBuilder</a></span>()</code></th>
<td class="colLast">&nbsp;</td>
</tr>
<tr id="i15" class="rowColor">
<td class="colFirst"><code>java.util.List&lt;java.io.File&gt;</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getSuiteXmlFiles--">getSuiteXmlFiles</a></span>()</code></th>
<td class="colLast">
<div class="block">The suiteXmlFiles to use for running TestNG.</div>
</td>
</tr>
<tr id="i16" class="altColor">
<td class="colFirst"><code>java.io.StringWriter</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getSuiteXmlWriter--">getSuiteXmlWriter</a></span>()</code></th>
<td class="colLast">&nbsp;</td>
</tr>
<tr id="i17" class="rowColor">
<td class="colFirst"><code>java.lang.String</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getTestName--">getTestName</a></span>()</code></th>
<td class="colLast">
<div class="block">Sets the default name of the test, if one is not specified in a suite XML file or in the source code.</div>
</td>
</tr>
<tr id="i18" class="altColor">
<td class="colFirst"><code>int</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getThreadCount--">getThreadCount</a></span>()</code></th>
<td class="colLast">
<div class="block">The number of threads to use for this run.</div>
</td>
</tr>
<tr id="i19" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getUseDefaultListeners--">getUseDefaultListeners</a></span>()</code></th>
<td class="colLast">&nbsp;</td>
</tr>
<tr id="i20" class="altColor">
<td class="colFirst"><code><a href="TestNGOptions.html" title="class in org.gradle.api.tasks.testing.testng">TestNGOptions</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#includeGroups-java.lang.String...-">includeGroups</a></span>&#8203;(java.lang.String...&nbsp;includeGroups)</code></th>
<td class="colLast">&nbsp;</td>
</tr>
<tr id="i21" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#isGroupByInstances--">isGroupByInstances</a></span>()</code></th>
<td class="colLast">
<div class="block">Indicates whether the tests should be grouped by instances.</div>
</td>
</tr>
<tr id="i22" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#isPreserveOrder--">isPreserveOrder</a></span>()</code></th>
<td class="colLast">
<div class="block">Indicates whether the tests should be run in deterministic order.</div>
</td>
</tr>
<tr id="i23" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#isUseDefaultListeners--">isUseDefaultListeners</a></span>()</code></th>
<td class="colLast">
<div class="block">Whether the default listeners and reporters should be used.</div>
</td>
</tr>
<tr id="i24" class="altColor">
<td class="colFirst"><code>java.lang.Object</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#methodMissing-java.lang.String-java.lang.Object-">methodMissing</a></span>&#8203;(java.lang.String&nbsp;name,
             java.lang.Object&nbsp;args)</code></th>
<td class="colLast">&nbsp;</td>
</tr>
<tr id="i25" class="rowColor">
<td class="colFirst"><code>java.lang.Object</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#propertyMissing-java.lang.String-">propertyMissing</a></span>&#8203;(java.lang.String&nbsp;name)</code></th>
<td class="colLast">&nbsp;</td>
</tr>
<tr id="i26" class="altColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setConfigFailurePolicy-java.lang.String-">setConfigFailurePolicy</a></span>&#8203;(java.lang.String&nbsp;configFailurePolicy)</code></th>
<td class="colLast">&nbsp;</td>
</tr>
<tr id="i27" class="rowColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setExcludeGroups-java.util.Set-">setExcludeGroups</a></span>&#8203;(java.util.Set&lt;java.lang.String&gt;&nbsp;excludeGroups)</code></th>
<td class="colLast">&nbsp;</td>
</tr>
<tr id="i28" class="altColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setGroupByInstances-boolean-">setGroupByInstances</a></span>&#8203;(boolean&nbsp;groupByInstances)</code></th>
<td class="colLast">&nbsp;</td>
</tr>
<tr id="i29" class="rowColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setIncludeGroups-java.util.Set-">setIncludeGroups</a></span>&#8203;(java.util.Set&lt;java.lang.String&gt;&nbsp;includeGroups)</code></th>
<td class="colLast">&nbsp;</td>
</tr>
<tr id="i30" class="altColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setListeners-java.util.Set-">setListeners</a></span>&#8203;(java.util.Set&lt;java.lang.String&gt;&nbsp;listeners)</code></th>
<td class="colLast">&nbsp;</td>
</tr>
<tr id="i31" class="rowColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setOutputDirectory-java.io.File-">setOutputDirectory</a></span>&#8203;(java.io.File&nbsp;outputDirectory)</code></th>
<td class="colLast">&nbsp;</td>
</tr>
<tr id="i32" class="altColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setParallel-java.lang.String-">setParallel</a></span>&#8203;(java.lang.String&nbsp;parallel)</code></th>
<td class="colLast">&nbsp;</td>
</tr>
<tr id="i33" class="rowColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setPreserveOrder-boolean-">setPreserveOrder</a></span>&#8203;(boolean&nbsp;preserveOrder)</code></th>
<td class="colLast">&nbsp;</td>
</tr>
<tr id="i34" class="altColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setSuiteName-java.lang.String-">setSuiteName</a></span>&#8203;(java.lang.String&nbsp;suiteName)</code></th>
<td class="colLast">&nbsp;</td>
</tr>
<tr id="i35" class="rowColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setSuiteXmlBuilder-groovy.xml.MarkupBuilder-">setSuiteXmlBuilder</a></span>&#8203;(<a href="https://docs.groovy-lang.org/docs/groovy-3.0.17/html/gapi/groovy/xml/MarkupBuilder.html?is-external=true" title="class or interface in groovy.xml" class="externalLink">MarkupBuilder</a>&nbsp;suiteXmlBuilder)</code></th>
<td class="colLast">&nbsp;</td>
</tr>
<tr id="i36" class="altColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setSuiteXmlFiles-java.util.List-">setSuiteXmlFiles</a></span>&#8203;(java.util.List&lt;java.io.File&gt;&nbsp;suiteXmlFiles)</code></th>
<td class="colLast">&nbsp;</td>
</tr>
<tr id="i37" class="rowColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setSuiteXmlWriter-java.io.StringWriter-">setSuiteXmlWriter</a></span>&#8203;(java.io.StringWriter&nbsp;suiteXmlWriter)</code></th>
<td class="colLast">&nbsp;</td>
</tr>
<tr id="i38" class="altColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setTestName-java.lang.String-">setTestName</a></span>&#8203;(java.lang.String&nbsp;testName)</code></th>
<td class="colLast">&nbsp;</td>
</tr>
<tr id="i39" class="rowColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setThreadCount-int-">setThreadCount</a></span>&#8203;(int&nbsp;threadCount)</code></th>
<td class="colLast">&nbsp;</td>
</tr>
<tr id="i40" class="altColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setUseDefaultListeners-boolean-">setUseDefaultListeners</a></span>&#8203;(boolean&nbsp;useDefaultListeners)</code></th>
<td class="colLast">&nbsp;</td>
</tr>
<tr id="i41" class="rowColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#suites-java.io.File...-">suites</a></span>&#8203;(java.io.File...&nbsp;suiteFiles)</code></th>
<td class="colLast">
<div class="block">Add suite files by File objects.</div>
</td>
</tr>
<tr id="i42" class="altColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#suites-java.lang.String...-">suites</a></span>&#8203;(java.lang.String...&nbsp;suiteFiles)</code></th>
<td class="colLast">
<div class="block">Add suite files by Strings.</div>
</td>
</tr>
<tr id="i43" class="rowColor">
<td class="colFirst"><code><a href="https://docs.groovy-lang.org/docs/groovy-3.0.17/html/gapi/groovy/xml/MarkupBuilder.html?is-external=true" title="class or interface in groovy.xml" class="externalLink">MarkupBuilder</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#suiteXmlBuilder--">suiteXmlBuilder</a></span>()</code></th>
<td class="colLast">&nbsp;</td>
</tr>
<tr id="i44" class="altColor">
<td class="colFirst"><code><a href="TestNGOptions.html" title="class in org.gradle.api.tasks.testing.testng">TestNGOptions</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#useDefaultListeners--">useDefaultListeners</a></span>()</code></th>
<td class="colLast">&nbsp;</td>
</tr>
<tr id="i45" class="rowColor">
<td class="colFirst"><code><a href="TestNGOptions.html" title="class in org.gradle.api.tasks.testing.testng">TestNGOptions</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#useDefaultListeners-boolean-">useDefaultListeners</a></span>&#8203;(boolean&nbsp;useDefaultListeners)</code></th>
<td class="colLast">&nbsp;</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>clone, equals, finalize, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ FIELD DETAIL =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.detail">
<!--   -->
</a>
<h3>Field Detail</h3>
<a name="DEFAULT_CONFIG_FAILURE_POLICY">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>DEFAULT_CONFIG_FAILURE_POLICY</h4>
<pre>public static final&nbsp;java.lang.String DEFAULT_CONFIG_FAILURE_POLICY</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../../../../constant-values.html#org.gradle.api.tasks.testing.testng.TestNGOptions.DEFAULT_CONFIG_FAILURE_POLICY">Constant Field Values</a></dd>
</dl>
</li>
</ul>
</li>
</ul>
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a name="TestNGOptions-org.gradle.api.file.ProjectLayout-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>TestNGOptions</h4>
<pre>@Inject
public&nbsp;TestNGOptions&#8203;(<a href="../../../file/ProjectLayout.html" title="interface in org.gradle.api.file">ProjectLayout</a>&nbsp;projectLayout)</pre>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="copyFrom-org.gradle.api.tasks.testing.testng.TestNGOptions-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>copyFrom</h4>
<pre class="methodSignature">public&nbsp;void&nbsp;copyFrom&#8203;(<a href="TestNGOptions.html" title="class in org.gradle.api.tasks.testing.testng">TestNGOptions</a>&nbsp;other)</pre>
<div class="block">Copies the options from the source options into the current one.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>8.0</dd>
</dl>
</li>
</ul>
<a name="suiteXmlBuilder--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>suiteXmlBuilder</h4>
<pre class="methodSignature">public&nbsp;<a href="https://docs.groovy-lang.org/docs/groovy-3.0.17/html/gapi/groovy/xml/MarkupBuilder.html?is-external=true" title="class or interface in groovy.xml" class="externalLink">MarkupBuilder</a>&nbsp;suiteXmlBuilder()</pre>
</li>
</ul>
<a name="suites-java.lang.String...-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>suites</h4>
<pre class="methodSignature">public&nbsp;void&nbsp;suites&#8203;(java.lang.String...&nbsp;suiteFiles)</pre>
<div class="block">Add suite files by Strings. Each suiteFile String should be a path relative to the project root.</div>
</li>
</ul>
<a name="getProjectDir--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getProjectDir</h4>
<pre class="methodSignature"><a href="../../Internal.html" title="annotation in org.gradle.api.tasks">@Internal</a>
protected&nbsp;java.io.File&nbsp;getProjectDir()</pre>
</li>
</ul>
<a name="suites-java.io.File...-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>suites</h4>
<pre class="methodSignature">public&nbsp;void&nbsp;suites&#8203;(java.io.File...&nbsp;suiteFiles)</pre>
<div class="block">Add suite files by File objects.</div>
</li>
</ul>
<a name="getSuites-java.io.File-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getSuites</h4>
<pre class="methodSignature">public&nbsp;java.util.List&lt;java.io.File&gt;&nbsp;getSuites&#8203;(java.io.File&nbsp;testSuitesDir)</pre>
</li>
</ul>
<a name="includeGroups-java.lang.String...-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>includeGroups</h4>
<pre class="methodSignature">public&nbsp;<a href="TestNGOptions.html" title="class in org.gradle.api.tasks.testing.testng">TestNGOptions</a>&nbsp;includeGroups&#8203;(java.lang.String...&nbsp;includeGroups)</pre>
</li>
</ul>
<a name="excludeGroups-java.lang.String...-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>excludeGroups</h4>
<pre class="methodSignature">public&nbsp;<a href="TestNGOptions.html" title="class in org.gradle.api.tasks.testing.testng">TestNGOptions</a>&nbsp;excludeGroups&#8203;(java.lang.String...&nbsp;excludeGroups)</pre>
</li>
</ul>
<a name="useDefaultListeners--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>useDefaultListeners</h4>
<pre class="methodSignature">public&nbsp;<a href="TestNGOptions.html" title="class in org.gradle.api.tasks.testing.testng">TestNGOptions</a>&nbsp;useDefaultListeners()</pre>
</li>
</ul>
<a name="useDefaultListeners-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>useDefaultListeners</h4>
<pre class="methodSignature">public&nbsp;<a href="TestNGOptions.html" title="class in org.gradle.api.tasks.testing.testng">TestNGOptions</a>&nbsp;useDefaultListeners&#8203;(boolean&nbsp;useDefaultListeners)</pre>
</li>
</ul>
<a name="propertyMissing-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>propertyMissing</h4>
<pre class="methodSignature">public&nbsp;java.lang.Object&nbsp;propertyMissing&#8203;(java.lang.String&nbsp;name)</pre>
</li>
</ul>
<a name="methodMissing-java.lang.String-java.lang.Object-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>methodMissing</h4>
<pre class="methodSignature">public&nbsp;java.lang.Object&nbsp;methodMissing&#8203;(java.lang.String&nbsp;name,
                                      java.lang.Object&nbsp;args)</pre>
</li>
</ul>
<a name="getOutputDirectory--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getOutputDirectory</h4>
<pre class="methodSignature"><a href="../../OutputDirectory.html" title="annotation in org.gradle.api.tasks">@OutputDirectory</a>
public&nbsp;java.io.File&nbsp;getOutputDirectory()</pre>
<div class="block">The location to write TestNG's output. <p> Defaults to the owning test task's location for writing the HTML report.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>1.11</dd>
</dl>
</li>
</ul>
<a name="setOutputDirectory-java.io.File-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setOutputDirectory</h4>
<pre class="methodSignature">public&nbsp;void&nbsp;setOutputDirectory&#8203;(java.io.File&nbsp;outputDirectory)</pre>
</li>
</ul>
<a name="getIncludeGroups--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getIncludeGroups</h4>
<pre class="methodSignature"><a href="../../Input.html" title="annotation in org.gradle.api.tasks">@Input</a>
public&nbsp;java.util.Set&lt;java.lang.String&gt;&nbsp;getIncludeGroups()</pre>
<div class="block">The set of groups to run.</div>
</li>
</ul>
<a name="setIncludeGroups-java.util.Set-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setIncludeGroups</h4>
<pre class="methodSignature">public&nbsp;void&nbsp;setIncludeGroups&#8203;(java.util.Set&lt;java.lang.String&gt;&nbsp;includeGroups)</pre>
</li>
</ul>
<a name="getExcludeGroups--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getExcludeGroups</h4>
<pre class="methodSignature"><a href="../../Input.html" title="annotation in org.gradle.api.tasks">@Input</a>
public&nbsp;java.util.Set&lt;java.lang.String&gt;&nbsp;getExcludeGroups()</pre>
<div class="block">The set of groups to exclude.</div>
</li>
</ul>
<a name="setExcludeGroups-java.util.Set-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setExcludeGroups</h4>
<pre class="methodSignature">public&nbsp;void&nbsp;setExcludeGroups&#8203;(java.util.Set&lt;java.lang.String&gt;&nbsp;excludeGroups)</pre>
</li>
</ul>
<a name="getConfigFailurePolicy--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getConfigFailurePolicy</h4>
<pre class="methodSignature"><a href="../../Internal.html" title="annotation in org.gradle.api.tasks">@Internal</a>
public&nbsp;java.lang.String&nbsp;getConfigFailurePolicy()</pre>
<div class="block">Option for what to do for other tests that use a configuration step when that step fails. Can be "skip" or "continue", defaults to "skip".</div>
</li>
</ul>
<a name="setConfigFailurePolicy-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setConfigFailurePolicy</h4>
<pre class="methodSignature">public&nbsp;void&nbsp;setConfigFailurePolicy&#8203;(java.lang.String&nbsp;configFailurePolicy)</pre>
</li>
</ul>
<a name="getListeners--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getListeners</h4>
<pre class="methodSignature"><a href="../../Internal.html" title="annotation in org.gradle.api.tasks">@Internal</a>
public&nbsp;java.util.Set&lt;java.lang.String&gt;&nbsp;getListeners()</pre>
<div class="block">Fully qualified classes that are TestNG listeners (instances of org.testng.ITestListener or org.testng.IReporter). By default, the listeners set is empty.

 Configuring extra listener:
 <pre class='autoTested'>
 plugins {
     id 'java'
 }

 test {
     useTestNG() {
         // creates emailable HTML file
         // this reporter typically ships with TestNG library
         listeners &lt;&lt; 'org.testng.reporters.EmailableReporter'
     }
 }
 </pre></div>
</li>
</ul>
<a name="setListeners-java.util.Set-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setListeners</h4>
<pre class="methodSignature">public&nbsp;void&nbsp;setListeners&#8203;(java.util.Set&lt;java.lang.String&gt;&nbsp;listeners)</pre>
</li>
</ul>
<a name="getParallel--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getParallel</h4>
<pre class="methodSignature">@Nullable
<a href="../../Internal.html" title="annotation in org.gradle.api.tasks">@Internal</a>
public&nbsp;java.lang.String&nbsp;getParallel()</pre>
<div class="block">The parallel mode to use for running the tests - one of the following modes: methods, tests, classes or instances.

 Not required.

 If not present, parallel mode will not be selected</div>
</li>
</ul>
<a name="setParallel-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setParallel</h4>
<pre class="methodSignature">public&nbsp;void&nbsp;setParallel&#8203;(java.lang.String&nbsp;parallel)</pre>
</li>
</ul>
<a name="getThreadCount--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getThreadCount</h4>
<pre class="methodSignature"><a href="../../Internal.html" title="annotation in org.gradle.api.tasks">@Internal</a>
public&nbsp;int&nbsp;getThreadCount()</pre>
<div class="block">The number of threads to use for this run. Ignored unless the parallel mode is also specified</div>
</li>
</ul>
<a name="setThreadCount-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setThreadCount</h4>
<pre class="methodSignature">public&nbsp;void&nbsp;setThreadCount&#8203;(int&nbsp;threadCount)</pre>
</li>
</ul>
<a name="getUseDefaultListeners--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getUseDefaultListeners</h4>
<pre class="methodSignature"><a href="../../Internal.html" title="annotation in org.gradle.api.tasks">@Internal</a>
public&nbsp;boolean&nbsp;getUseDefaultListeners()</pre>
</li>
</ul>
<a name="isUseDefaultListeners--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isUseDefaultListeners</h4>
<pre class="methodSignature"><a href="../../Internal.html" title="annotation in org.gradle.api.tasks">@Internal</a>
public&nbsp;boolean&nbsp;isUseDefaultListeners()</pre>
<div class="block">Whether the default listeners and reporters should be used. Since Gradle 1.4 it defaults to 'false' so that Gradle can own the reports generation and provide various improvements. This option
 might be useful for advanced TestNG users who prefer the reports generated by the TestNG library. If you cannot live without some specific TestNG reporter please use <a href="#listeners"><code>listeners</code></a>
 property. If you really want to use all default TestNG reporters (e.g. generate the old reports):

 <pre class='autoTested'>
 plugins {
     id 'java'
 }

 test {
     useTestNG() {
         // report generation delegated to TestNG library:
         useDefaultListeners = true
     }

     // turn off Gradle's HTML report to avoid replacing the
     // reports generated by TestNG library:
     reports.html.required = false
 }
 </pre>

 Please refer to the documentation of your version of TestNG what are the default listeners. At the moment of writing this documentation, the default listeners are a set of reporters that
 generate: TestNG variant of HTML results, TestNG variant of XML results in JUnit format, emailable HTML test report, XML results in TestNG format.</div>
</li>
</ul>
<a name="setUseDefaultListeners-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setUseDefaultListeners</h4>
<pre class="methodSignature">public&nbsp;void&nbsp;setUseDefaultListeners&#8203;(boolean&nbsp;useDefaultListeners)</pre>
</li>
</ul>
<a name="getSuiteName--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getSuiteName</h4>
<pre class="methodSignature"><a href="../../Internal.html" title="annotation in org.gradle.api.tasks">@Internal</a>
public&nbsp;java.lang.String&nbsp;getSuiteName()</pre>
<div class="block">Sets the default name of the test suite, if one is not specified in a suite XML file or in the source code.</div>
</li>
</ul>
<a name="setSuiteName-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setSuiteName</h4>
<pre class="methodSignature">public&nbsp;void&nbsp;setSuiteName&#8203;(java.lang.String&nbsp;suiteName)</pre>
</li>
</ul>
<a name="getTestName--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getTestName</h4>
<pre class="methodSignature"><a href="../../Internal.html" title="annotation in org.gradle.api.tasks">@Internal</a>
public&nbsp;java.lang.String&nbsp;getTestName()</pre>
<div class="block">Sets the default name of the test, if one is not specified in a suite XML file or in the source code.</div>
</li>
</ul>
<a name="setTestName-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setTestName</h4>
<pre class="methodSignature">public&nbsp;void&nbsp;setTestName&#8203;(java.lang.String&nbsp;testName)</pre>
</li>
</ul>
<a name="getSuiteXmlFiles--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getSuiteXmlFiles</h4>
<pre class="methodSignature"><a href="../../InputFiles.html" title="annotation in org.gradle.api.tasks">@InputFiles</a>
<a href="../../PathSensitive.html" title="annotation in org.gradle.api.tasks">@PathSensitive</a>(<a href="../../PathSensitivity.html#NONE">NONE</a>)
public&nbsp;java.util.List&lt;java.io.File&gt;&nbsp;getSuiteXmlFiles()</pre>
<div class="block">The suiteXmlFiles to use for running TestNG.

 Note: The suiteXmlFiles can be used in conjunction with the suiteXmlBuilder.</div>
</li>
</ul>
<a name="setSuiteXmlFiles-java.util.List-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setSuiteXmlFiles</h4>
<pre class="methodSignature">public&nbsp;void&nbsp;setSuiteXmlFiles&#8203;(java.util.List&lt;java.io.File&gt;&nbsp;suiteXmlFiles)</pre>
</li>
</ul>
<a name="getPreserveOrder--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getPreserveOrder</h4>
<pre class="methodSignature"><a href="../../Internal.html" title="annotation in org.gradle.api.tasks">@Internal</a>
public&nbsp;boolean&nbsp;getPreserveOrder()</pre>
</li>
</ul>
<a name="isPreserveOrder--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isPreserveOrder</h4>
<pre class="methodSignature"><a href="../../Internal.html" title="annotation in org.gradle.api.tasks">@Internal</a>
public&nbsp;boolean&nbsp;isPreserveOrder()</pre>
<div class="block">Indicates whether the tests should be run in deterministic order. Preserving the order guarantees that the complete test
 (including @BeforeXXX and @AfterXXX) is run in a test thread before the next test is run.

 Not required.

 If not present, the order will not be preserved.</div>
</li>
</ul>
<a name="setPreserveOrder-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setPreserveOrder</h4>
<pre class="methodSignature">public&nbsp;void&nbsp;setPreserveOrder&#8203;(boolean&nbsp;preserveOrder)</pre>
</li>
</ul>
<a name="getGroupByInstances--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getGroupByInstances</h4>
<pre class="methodSignature"><a href="../../Internal.html" title="annotation in org.gradle.api.tasks">@Internal</a>
public&nbsp;boolean&nbsp;getGroupByInstances()</pre>
</li>
</ul>
<a name="isGroupByInstances--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isGroupByInstances</h4>
<pre class="methodSignature"><a href="../../Internal.html" title="annotation in org.gradle.api.tasks">@Internal</a>
public&nbsp;boolean&nbsp;isGroupByInstances()</pre>
<div class="block">Indicates whether the tests should be grouped by instances. Grouping by instances will result in resolving test method dependencies for each instance instead of running the dependees of all
 instances before running the dependants.

 Not required.

 If not present, the tests will not be grouped by instances.</div>
</li>
</ul>
<a name="setGroupByInstances-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setGroupByInstances</h4>
<pre class="methodSignature">public&nbsp;void&nbsp;setGroupByInstances&#8203;(boolean&nbsp;groupByInstances)</pre>
</li>
</ul>
<a name="getSuiteXml--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getSuiteXml</h4>
<pre class="methodSignature"><a href="../../Input.html" title="annotation in org.gradle.api.tasks">@Input</a>
<a href="../../Optional.html" title="annotation in org.gradle.api.tasks">@Optional</a>
protected&nbsp;java.lang.String&nbsp;getSuiteXml()</pre>
<div class="block">Returns the XML generated using <a href="#suiteXmlBuilder--"><code>suiteXmlBuilder()</code></a>, if any.

 <p>This property is read-only and exists merely for up-to-date checking.</div>
</li>
</ul>
<a name="getSuiteXmlWriter--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getSuiteXmlWriter</h4>
<pre class="methodSignature"><a href="../../Internal.html" title="annotation in org.gradle.api.tasks">@Internal</a>
public&nbsp;java.io.StringWriter&nbsp;getSuiteXmlWriter()</pre>
</li>
</ul>
<a name="setSuiteXmlWriter-java.io.StringWriter-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setSuiteXmlWriter</h4>
<pre class="methodSignature">public&nbsp;void&nbsp;setSuiteXmlWriter&#8203;(java.io.StringWriter&nbsp;suiteXmlWriter)</pre>
</li>
</ul>
<a name="getSuiteXmlBuilder--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getSuiteXmlBuilder</h4>
<pre class="methodSignature"><a href="../../Internal.html" title="annotation in org.gradle.api.tasks">@Internal</a>
public&nbsp;<a href="https://docs.groovy-lang.org/docs/groovy-3.0.17/html/gapi/groovy/xml/MarkupBuilder.html?is-external=true" title="class or interface in groovy.xml" class="externalLink">MarkupBuilder</a>&nbsp;getSuiteXmlBuilder()</pre>
</li>
</ul>
<a name="setSuiteXmlBuilder-groovy.xml.MarkupBuilder-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>setSuiteXmlBuilder</h4>
<pre class="methodSignature">public&nbsp;void&nbsp;setSuiteXmlBuilder&#8203;(<a href="https://docs.groovy-lang.org/docs/groovy-3.0.17/html/gapi/groovy/xml/MarkupBuilder.html?is-external=true" title="class or interface in groovy.xml" class="externalLink">MarkupBuilder</a>&nbsp;suiteXmlBuilder)</pre>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../../../allclasses.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field.summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#field.detail">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
