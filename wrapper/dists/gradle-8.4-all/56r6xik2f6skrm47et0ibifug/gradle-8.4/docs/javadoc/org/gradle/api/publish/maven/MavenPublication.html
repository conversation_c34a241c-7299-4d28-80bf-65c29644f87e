<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc -->
<title>MavenPublication (Gradle API 8.4)</title>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<link rel="stylesheet" type="text/css" href="../../../../../javadoc.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../jquery/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
<script type="text/javascript" src="../../../../../jquery/jszip/dist/jszip.min.js"></script>
<script type="text/javascript" src="../../../../../jquery/jszip-utils/dist/jszip-utils.min.js"></script>
<!--[if IE]>
<script type="text/javascript" src="../../../../../jquery/jszip-utils/dist/jszip-utils-ie.min.js"></script>
<![endif]-->
<script type="text/javascript" src="../../../../../jquery/jquery-3.6.0.min.js"></script>
<script type="text/javascript" src="../../../../../jquery/jquery-ui.min.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="MavenPublication (Gradle API 8.4)";
        }
    }
    catch(err) {
    }
//-->
var data = {"i0":6,"i1":6,"i2":6,"i3":6,"i4":6,"i5":6,"i6":6,"i7":6,"i8":6,"i9":6,"i10":6,"i11":6,"i12":6,"i13":6,"i14":6,"i15":6};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],4:["t3","Abstract Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
var pathtoroot = "../../../../../";
var useModuleDirectories = false;
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="fixedNav">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../../allclasses.html">All&nbsp;Classes</a></li>
</ul>
<ul class="navListSearch">
<li><label for="search">SEARCH:</label>
<input type="text" id="search" value="search" disabled="disabled">
<input type="reset" id="reset" value="reset" disabled="disabled">
</li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li><a href="#nested.class.summary">Nested</a>&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
</div>
<div class="navPadding">&nbsp;</div>
<script type="text/javascript"><!--
$('.navPadding').css('padding-top', $('.fixedNav').css("height"));
//-->
</script>
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle"><span class="packageLabelInType">Package</span>&nbsp;<a href="package-summary.html">org.gradle.api.publish.maven</a></div>
<h2 title="Interface MavenPublication" class="title">Interface MavenPublication</h2>
</div>
<div class="contentContainer">
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Superinterfaces:</dt>
<dd><code><a href="../../Named.html" title="interface in org.gradle.api">Named</a></code>, <code><a href="../Publication.html" title="interface in org.gradle.api.publish">Publication</a></code></dd>
</dl>
<hr>
<pre>public interface <span class="typeNameLabel">MavenPublication</span>
extends <a href="../Publication.html" title="interface in org.gradle.api.publish">Publication</a></pre>
<div class="block">A <code>MavenPublication</code> is the representation/configuration of how Gradle should publish something in Maven format.

 You directly add a named Maven publication the project's <code>publishing.publications</code> container by providing <a href="MavenPublication.html" title="interface in org.gradle.api.publish.maven"><code>MavenPublication</code></a> as the type.
 <pre>
 publishing {
   publications {
     myPublicationName(MavenPublication) {
       // Configure the publication here
     }
   }
 }
 </pre>

 The default Maven POM identifying attributes are mapped as follows:
 <ul>
 <li><code>groupId</code> - <code>project.group</code></li>
 <li><code>artifactId</code> - <code>project.name</code></li>
 <li><code>version</code> - <code>project.version</code></li>
 </ul>

 <p>
 For certain common use cases, it's often sufficient to specify the component to publish, and nothing more (<a href="#from-org.gradle.api.component.SoftwareComponent-"><code>from(org.gradle.api.component.SoftwareComponent)</code></a>.
 The published component is used to determine which artifacts to publish, and which dependencies should be listed in the generated POM file.
 </p><p>
 To add additional artifacts to the set published, use the <a href="#artifact-java.lang.Object-"><code>artifact(Object)</code></a> and <a href="#artifact-java.lang.Object-org.gradle.api.Action-"><code>artifact(Object, org.gradle.api.Action)</code></a> methods.
 You can also completely replace the set of published artifacts using <a href="#setArtifacts-java.lang.Iterable-"><code>setArtifacts(Iterable)</code></a>.
 Together, these methods give you full control over what artifacts will be published.
 </p><p>
 To customize the metadata published in the generated POM, set properties, e.g. <a href="MavenPom.html#getDescription--"><code>MavenPom.getDescription()</code></a>, on the POM returned via the <a href="#getPom--"><code>getPom()</code></a>
 method or directly by an action (or closure) passed into <a href="#pom-org.gradle.api.Action-"><code>pom(org.gradle.api.Action)</code></a>.
 As a last resort, it is possible to modify the generated POM using the <a href="MavenPom.html#withXml-org.gradle.api.Action-"><code>MavenPom.withXml(org.gradle.api.Action)</code></a> method.
 </p>
 <h4>Example of publishing a Java module with a source artifact and a customized POM</h4>
 <pre class='autoTested'>
 plugins {
     id 'java'
     id 'maven-publish'
 }

 task sourceJar(type: Jar) {
   from sourceSets.main.allJava
   archiveClassifier = "sources"
 }

 publishing {
   publications {
     myPublication(MavenPublication) {
       from components.java
       artifact sourceJar
       pom {
         name = "Demo"
         description = "A demonstration of Maven POM customization"
         url = "http://www.example.com/project"
         licenses {
           license {
             name = "The Apache License, Version 2.0"
             url = "http://www.apache.org/licenses/LICENSE-2.0.txt"
           }
         }
         developers {
           developer {
             id = "johnd"
             name = "John Doe"
             email = "<EMAIL>"
           }
         }
         scm {
           connection = "scm:svn:http://subversion.example.com/svn/project/trunk/"
           developerConnection = "scm:svn:https://subversion.example.com/svn/project/trunk/"
           url = "http://subversion.example.com/svn/project/trunk/"
         }
       }
     }
   }
 }
 </pre></div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>1.4</dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== NESTED CLASS SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="nested.class.summary">
<!--   -->
</a>
<h3>Nested Class Summary</h3>
<ul class="blockList">
<li class="blockList"><a name="nested.classes.inherited.from.class.org.gradle.api.Named">
<!--   -->
</a>
<h3>Nested classes/interfaces inherited from interface&nbsp;org.gradle.api.<a href="../../Named.html" title="interface in org.gradle.api">Named</a></h3>
<code><a href="../../Named.Namer.html" title="class in org.gradle.api">Named.Namer</a></code></li>
</ul>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t3" class="tableTab"><span><a href="javascript:show(4);">Abstract Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colSecond" scope="col">Method</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code><a href="MavenArtifact.html" title="interface in org.gradle.api.publish.maven">MavenArtifact</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#artifact-java.lang.Object-">artifact</a></span>&#8203;(java.lang.Object&nbsp;source)</code></th>
<td class="colLast">
<div class="block">Creates a custom <a href="MavenArtifact.html" title="interface in org.gradle.api.publish.maven"><code>MavenArtifact</code></a> to be included in the publication.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code><a href="MavenArtifact.html" title="interface in org.gradle.api.publish.maven">MavenArtifact</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#artifact-java.lang.Object-org.gradle.api.Action-">artifact</a></span>&#8203;(java.lang.Object&nbsp;source,
        <a href="../../Action.html" title="interface in org.gradle.api">Action</a>&lt;? super <a href="MavenArtifact.html" title="interface in org.gradle.api.publish.maven">MavenArtifact</a>&gt;&nbsp;config)</code></th>
<td class="colLast">
<div class="block">Creates an <a href="MavenArtifact.html" title="interface in org.gradle.api.publish.maven"><code>MavenArtifact</code></a> to be included in the publication, which is configured by the associated action.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#from-org.gradle.api.component.SoftwareComponent-">from</a></span>&#8203;(<a href="../../component/SoftwareComponent.html" title="interface in org.gradle.api.component">SoftwareComponent</a>&nbsp;component)</code></th>
<td class="colLast">
<div class="block">Provides the software component that should be published.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>java.lang.String</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getArtifactId--">getArtifactId</a></span>()</code></th>
<td class="colLast">
<div class="block">Returns the artifactId for this publication.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code><a href="MavenArtifactSet.html" title="interface in org.gradle.api.publish.maven">MavenArtifactSet</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getArtifacts--">getArtifacts</a></span>()</code></th>
<td class="colLast">
<div class="block">Returns the complete set of artifacts for this publication.</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>java.lang.String</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getGroupId--">getGroupId</a></span>()</code></th>
<td class="colLast">
<div class="block">Returns the groupId for this publication.</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code><a href="MavenPom.html" title="interface in org.gradle.api.publish.maven">MavenPom</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getPom--">getPom</a></span>()</code></th>
<td class="colLast">
<div class="block">The POM that will be published.</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>java.lang.String</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getVersion--">getVersion</a></span>()</code></th>
<td class="colLast">
<div class="block">Returns the version for this publication.</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#pom-org.gradle.api.Action-">pom</a></span>&#8203;(<a href="../../Action.html" title="interface in org.gradle.api">Action</a>&lt;? super <a href="MavenPom.html" title="interface in org.gradle.api.publish.maven">MavenPom</a>&gt;&nbsp;configure)</code></th>
<td class="colLast">
<div class="block">Configures the POM that will be published.</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setArtifactId-java.lang.String-">setArtifactId</a></span>&#8203;(java.lang.String&nbsp;artifactId)</code></th>
<td class="colLast">
<div class="block">Sets the artifactId for this publication.</div>
</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setArtifacts-java.lang.Iterable-">setArtifacts</a></span>&#8203;(java.lang.Iterable&lt;?&gt;&nbsp;sources)</code></th>
<td class="colLast">
<div class="block">Clears any previously added artifacts from <a href="#getArtifacts--"><code>getArtifacts()</code></a> and creates artifacts from the specified sources.</div>
</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setGroupId-java.lang.String-">setGroupId</a></span>&#8203;(java.lang.String&nbsp;groupId)</code></th>
<td class="colLast">
<div class="block">Sets the groupId for this publication.</div>
</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setVersion-java.lang.String-">setVersion</a></span>&#8203;(java.lang.String&nbsp;version)</code></th>
<td class="colLast">
<div class="block">Sets the version for this publication.</div>
</td>
</tr>
<tr id="i13" class="rowColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#suppressAllPomMetadataWarnings--">suppressAllPomMetadataWarnings</a></span>()</code></th>
<td class="colLast">
<div class="block">Silences all the compatibility warnings for the Maven publication.</div>
</td>
</tr>
<tr id="i14" class="altColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#suppressPomMetadataWarningsFor-java.lang.String-">suppressPomMetadataWarningsFor</a></span>&#8203;(java.lang.String&nbsp;variantName)</code></th>
<td class="colLast">
<div class="block">Silences the compatibility warnings for the Maven publication for the specified variant.</div>
</td>
</tr>
<tr id="i15" class="rowColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#versionMapping-org.gradle.api.Action-">versionMapping</a></span>&#8203;(<a href="../../Action.html" title="interface in org.gradle.api">Action</a>&lt;? super <a href="../VersionMappingStrategy.html" title="interface in org.gradle.api.publish">VersionMappingStrategy</a>&gt;&nbsp;configureAction)</code></th>
<td class="colLast">
<div class="block">Configures the version mapping strategy.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.org.gradle.api.Named">
<!--   -->
</a>
<h3>Methods inherited from interface&nbsp;org.gradle.api.<a href="../../Named.html" title="interface in org.gradle.api">Named</a></h3>
<code><a href="../../Named.html#getName--">getName</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.org.gradle.api.publish.Publication">
<!--   -->
</a>
<h3>Methods inherited from interface&nbsp;org.gradle.api.publish.<a href="../Publication.html" title="interface in org.gradle.api.publish">Publication</a></h3>
<code><a href="../Publication.html#withBuildIdentifier--">withBuildIdentifier</a>, <a href="../Publication.html#withoutBuildIdentifier--">withoutBuildIdentifier</a></code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="getPom--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getPom</h4>
<pre class="methodSignature"><a href="MavenPom.html" title="interface in org.gradle.api.publish.maven">MavenPom</a>&nbsp;getPom()</pre>
<div class="block">The POM that will be published.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The POM that will be published.</dd>
</dl>
</li>
</ul>
<a name="pom-org.gradle.api.Action-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>pom</h4>
<pre class="methodSignature">void&nbsp;pom&#8203;(<a href="../../Action.html" title="interface in org.gradle.api">Action</a>&lt;? super <a href="MavenPom.html" title="interface in org.gradle.api.publish.maven">MavenPom</a>&gt;&nbsp;configure)</pre>
<div class="block">Configures the POM that will be published.

 The supplied action will be executed against the <a href="#getPom--"><code>getPom()</code></a> result. This method also accepts a closure argument, by type coercion.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>configure</code> - The configuration action.</dd>
</dl>
</li>
</ul>
<a name="from-org.gradle.api.component.SoftwareComponent-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>from</h4>
<pre class="methodSignature">void&nbsp;from&#8203;(<a href="../../component/SoftwareComponent.html" title="interface in org.gradle.api.component">SoftwareComponent</a>&nbsp;component)</pre>
<div class="block">Provides the software component that should be published.

 <ul>
     <li>Any artifacts declared by the component will be included in the publication.</li>
     <li>The dependencies declared by the component will be included in the published meta-data.</li>
 </ul>

 Currently 3 types of component are supported: 'components.java' (added by the JavaPlugin), 'components.web' (added by the WarPlugin)
 and `components.javaPlatform` (added by the JavaPlatformPlugin).

 For any individual MavenPublication, only a single component can be provided in this way.

 The following example demonstrates how to publish the 'java' component to a Maven repository.
 <pre class='autoTested'>
 plugins {
     id 'java'
     id 'maven-publish'
 }

 publishing {
   publications {
     maven(MavenPublication) {
       from components.java
     }
   }
 }
 </pre></div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>component</code> - The software component to publish.</dd>
</dl>
</li>
</ul>
<a name="artifact-java.lang.Object-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>artifact</h4>
<pre class="methodSignature"><a href="MavenArtifact.html" title="interface in org.gradle.api.publish.maven">MavenArtifact</a>&nbsp;artifact&#8203;(java.lang.Object&nbsp;source)</pre>
<div class="block">Creates a custom <a href="MavenArtifact.html" title="interface in org.gradle.api.publish.maven"><code>MavenArtifact</code></a> to be included in the publication.

 The <code>artifact</code> method can take a variety of input:
 <ul>
     <li>A <a href="../../artifacts/PublishArtifact.html" title="interface in org.gradle.api.artifacts"><code>PublishArtifact</code></a> instance. Extension and classifier values are taken from the wrapped instance.</li>
     <li>An <a href="../../tasks/bundling/AbstractArchiveTask.html" title="class in org.gradle.api.tasks.bundling"><code>AbstractArchiveTask</code></a> instance. Extension and classifier values are taken from the wrapped instance.</li>
     <li>Anything that can be resolved to a <code>File</code> via the <a href="../../Project.html#file-java.lang.Object-"><code>Project.file(Object)</code></a> method.
          Extension and classifier values are interpolated from the file name.</li>
     <li>A <code>Map</code> that contains a 'source' entry that can be resolved as any of the other input types, including file.
         This map can contain a 'classifier' and an 'extension' entry to further configure the constructed artifact.</li>
 </ul>

 The following example demonstrates the addition of various custom artifacts.
 <pre class='autoTested'>
 plugins {
     id 'maven-publish'
 }

 task sourceJar(type: Jar) {
   archiveClassifier = "sources"
 }

 publishing {
   publications {
     maven(MavenPublication) {
       artifact sourceJar // Publish the output of the sourceJar task
       artifact 'my-file-name.jar' // Publish a file created outside of the build
       artifact source: sourceJar, classifier: 'src', extension: 'zip'
     }
   }
 }
 </pre></div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>source</code> - The source of the artifact content.</dd>
</dl>
</li>
</ul>
<a name="artifact-java.lang.Object-org.gradle.api.Action-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>artifact</h4>
<pre class="methodSignature"><a href="MavenArtifact.html" title="interface in org.gradle.api.publish.maven">MavenArtifact</a>&nbsp;artifact&#8203;(java.lang.Object&nbsp;source,
                       <a href="../../Action.html" title="interface in org.gradle.api">Action</a>&lt;? super <a href="MavenArtifact.html" title="interface in org.gradle.api.publish.maven">MavenArtifact</a>&gt;&nbsp;config)</pre>
<div class="block">Creates an <a href="MavenArtifact.html" title="interface in org.gradle.api.publish.maven"><code>MavenArtifact</code></a> to be included in the publication, which is configured by the associated action.

 The first parameter is used to create a custom artifact and add it to the publication, as per <a href="#artifact-java.lang.Object-"><code>artifact(Object)</code></a>.
 The created <a href="MavenArtifact.html" title="interface in org.gradle.api.publish.maven"><code>MavenArtifact</code></a> is then configured using the supplied action, which can override the extension or classifier of the artifact.
 This method also accepts the configure action as a closure argument, by type coercion.

 <pre class='autoTested'>
 plugins {
     id 'maven-publish'
 }

 task sourceJar(type: Jar) {
   archiveClassifier = "sources"
 }

 publishing {
   publications {
     maven(MavenPublication) {
       artifact(sourceJar) {
         // These values will be used instead of the values from the task. The task values will not be updated.
         classifier "src"
         extension "zip"
       }
       artifact("my-docs-file.htm") {
         classifier "documentation"
         extension "html"
       }
     }
   }
 }
 </pre></div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>source</code> - The source of the artifact.</dd>
<dd><code>config</code> - An action to configure the values of the constructed <a href="MavenArtifact.html" title="interface in org.gradle.api.publish.maven"><code>MavenArtifact</code></a>.</dd>
</dl>
</li>
</ul>
<a name="setArtifacts-java.lang.Iterable-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setArtifacts</h4>
<pre class="methodSignature">void&nbsp;setArtifacts&#8203;(java.lang.Iterable&lt;?&gt;&nbsp;sources)</pre>
<div class="block">Clears any previously added artifacts from <a href="#getArtifacts--"><code>getArtifacts()</code></a> and creates artifacts from the specified sources.
 Each supplied source is interpreted as per <a href="#artifact-java.lang.Object-"><code>artifact(Object)</code></a>.

 For example, to exclude the dependencies declared by a component and instead use a custom set of artifacts:
 <pre class='autoTested'>
 plugins {
     id 'java'
     id 'maven-publish'
 }

 task sourceJar(type: Jar) {
   archiveClassifier = "sources"
 }

 publishing {
   publications {
     maven(MavenPublication) {
       from components.java
       artifacts = ["my-custom-jar.jar", sourceJar]
     }
   }
 }
 </pre></div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>sources</code> - The set of artifacts for this publication.</dd>
</dl>
</li>
</ul>
<a name="getArtifacts--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getArtifacts</h4>
<pre class="methodSignature"><a href="MavenArtifactSet.html" title="interface in org.gradle.api.publish.maven">MavenArtifactSet</a>&nbsp;getArtifacts()</pre>
<div class="block">Returns the complete set of artifacts for this publication.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the artifacts.</dd>
</dl>
</li>
</ul>
<a name="getGroupId--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getGroupId</h4>
<pre class="methodSignature">java.lang.String&nbsp;getGroupId()</pre>
<div class="block">Returns the groupId for this publication.</div>
</li>
</ul>
<a name="setGroupId-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setGroupId</h4>
<pre class="methodSignature">void&nbsp;setGroupId&#8203;(java.lang.String&nbsp;groupId)</pre>
<div class="block">Sets the groupId for this publication.</div>
</li>
</ul>
<a name="getArtifactId--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getArtifactId</h4>
<pre class="methodSignature">java.lang.String&nbsp;getArtifactId()</pre>
<div class="block">Returns the artifactId for this publication.</div>
</li>
</ul>
<a name="setArtifactId-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setArtifactId</h4>
<pre class="methodSignature">void&nbsp;setArtifactId&#8203;(java.lang.String&nbsp;artifactId)</pre>
<div class="block">Sets the artifactId for this publication.</div>
</li>
</ul>
<a name="getVersion--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getVersion</h4>
<pre class="methodSignature">java.lang.String&nbsp;getVersion()</pre>
<div class="block">Returns the version for this publication.</div>
</li>
</ul>
<a name="setVersion-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setVersion</h4>
<pre class="methodSignature">void&nbsp;setVersion&#8203;(java.lang.String&nbsp;version)</pre>
<div class="block">Sets the version for this publication.</div>
</li>
</ul>
<a name="versionMapping-org.gradle.api.Action-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>versionMapping</h4>
<pre class="methodSignature">void&nbsp;versionMapping&#8203;(<a href="../../Action.html" title="interface in org.gradle.api">Action</a>&lt;? super <a href="../VersionMappingStrategy.html" title="interface in org.gradle.api.publish">VersionMappingStrategy</a>&gt;&nbsp;configureAction)</pre>
<div class="block">Configures the version mapping strategy.

 For example, to use resolved versions for runtime dependencies:
 <pre class='autoTested'>
 plugins {
     id 'java'
     id 'maven-publish'
 }

 publishing {
   publications {
     maven(MavenPublication) {
       from components.java
       versionMapping {
         usage('java-runtime'){
           fromResolutionResult()
         }
       }
     }
   }
 }
 </pre></div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>configureAction</code> - the configuration</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>5.2</dd>
</dl>
</li>
</ul>
<a name="suppressPomMetadataWarningsFor-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>suppressPomMetadataWarningsFor</h4>
<pre class="methodSignature">void&nbsp;suppressPomMetadataWarningsFor&#8203;(java.lang.String&nbsp;variantName)</pre>
<div class="block">Silences the compatibility warnings for the Maven publication for the specified variant.

 Warnings are emitted when Gradle features are used that cannot be mapped completely to Maven POM.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>variantName</code> - the variant to silence warning for</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>6.0</dd>
</dl>
</li>
</ul>
<a name="suppressAllPomMetadataWarnings--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>suppressAllPomMetadataWarnings</h4>
<pre class="methodSignature">void&nbsp;suppressAllPomMetadataWarnings()</pre>
<div class="block">Silences all the compatibility warnings for the Maven publication.

 Warnings are emitted when Gradle features are used that cannot be mapped completely to Maven POM.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>6.0</dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../../allclasses.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li><a href="#nested.class.summary">Nested</a>&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
