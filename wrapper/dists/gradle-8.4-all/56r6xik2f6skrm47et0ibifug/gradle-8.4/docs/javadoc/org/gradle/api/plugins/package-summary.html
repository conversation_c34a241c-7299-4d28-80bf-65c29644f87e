<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc -->
<title>org.gradle.api.plugins (Gradle API 8.4)</title>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<link rel="stylesheet" type="text/css" href="../../../../javadoc.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../jquery/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
<script type="text/javascript" src="../../../../jquery/jszip/dist/jszip.min.js"></script>
<script type="text/javascript" src="../../../../jquery/jszip-utils/dist/jszip-utils.min.js"></script>
<!--[if IE]>
<script type="text/javascript" src="../../../../jquery/jszip-utils/dist/jszip-utils-ie.min.js"></script>
<![endif]-->
<script type="text/javascript" src="../../../../jquery/jquery-3.6.0.min.js"></script>
<script type="text/javascript" src="../../../../jquery/jquery-ui.min.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="org.gradle.api.plugins (Gradle API 8.4)";
        }
    }
    catch(err) {
    }
//-->
var pathtoroot = "../../../../";
var useModuleDirectories = false;
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="fixedNav">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../index.html">Overview</a></li>
<li class="navBarCell1Rev">Package</li>
<li>Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses.html">All&nbsp;Classes</a></li>
</ul>
<ul class="navListSearch">
<li><label for="search">SEARCH:</label>
<input type="text" id="search" value="search" disabled="disabled">
<input type="reset" id="reset" value="reset" disabled="disabled">
</li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
</div>
<div class="navPadding">&nbsp;</div>
<script type="text/javascript"><!--
$('.navPadding').css('padding-top', $('.fixedNav').css("height"));
//-->
</script>
<div class="header">
<p><a href="../NonNullApi.html" title="annotation in org.gradle.api">@NonNullApi</a>
</p>
<h1 title="Package" class="title">Package&nbsp;org.gradle.api.plugins</h1>
</div>
<div class="contentContainer"><a name="package.description">
<!--   -->
</a>
<div class="block">The standard <a href="../Plugin.html" title="interface in org.gradle.api"><code>Plugin</code></a> implementations.</div>
<ul class="blockList">
<li class="blockList">
<table class="typeSummary" summary="Interface Summary table, listing interfaces, and an explanation">
<caption><span>Interface Summary</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Interface</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tbody>
<tr class="altColor">
<th class="colFirst" scope="row"><a href="AppliedPlugin.html" title="interface in org.gradle.api.plugins">AppliedPlugin</a></th>
<td class="colLast">
<div class="block">Represents a plugin that has been applied.</div>
</td>
</tr>
<tr class="rowColor">
<th class="colFirst" scope="row"><a href="BasePluginExtension.html" title="interface in org.gradle.api.plugins">BasePluginExtension</a></th>
<td class="colLast">
<div class="block">An extension used for <a href="BasePlugin.html" title="class in org.gradle.api.plugins"><code>BasePlugin</code></a>.</div>
</td>
</tr>
<tr class="altColor">
<th class="colFirst" scope="row"><a href="Convention.html" title="interface in org.gradle.api.plugins">Convention</a></th>
<td class="colLast">Deprecated.
<div class="deprecationComment">Use extensions instead.</div>
</td>
</tr>
<tr class="rowColor">
<th class="colFirst" scope="row"><a href="ExtensionAware.html" title="interface in org.gradle.api.plugins">ExtensionAware</a></th>
<td class="colLast">
<div class="block">Objects that can be extended at runtime with other objects.</div>
</td>
</tr>
<tr class="altColor">
<th class="colFirst" scope="row"><a href="ExtensionContainer.html" title="interface in org.gradle.api.plugins">ExtensionContainer</a></th>
<td class="colLast">
<div class="block">Allows adding 'namespaced' DSL extensions to a target object.</div>
</td>
</tr>
<tr class="rowColor">
<th class="colFirst" scope="row"><a href="ExtensionsSchema.html" title="interface in org.gradle.api.plugins">ExtensionsSchema</a></th>
<td class="colLast">
<div class="block">Schema of extensions.</div>
</td>
</tr>
<tr class="altColor">
<th class="colFirst" scope="row"><a href="ExtensionsSchema.ExtensionSchema.html" title="interface in org.gradle.api.plugins">ExtensionsSchema.ExtensionSchema</a></th>
<td class="colLast">
<div class="block">Schema of an extension.</div>
</td>
</tr>
<tr class="rowColor">
<th class="colFirst" scope="row"><a href="ExtraPropertiesExtension.html" title="interface in org.gradle.api.plugins">ExtraPropertiesExtension</a></th>
<td class="colLast">
<div class="block">Additional, ad-hoc, properties for Gradle domain objects.</div>
</td>
</tr>
<tr class="altColor">
<th class="colFirst" scope="row"><a href="FeatureSpec.html" title="interface in org.gradle.api.plugins">FeatureSpec</a></th>
<td class="colLast">
<div class="block">Handler for configuring features, which may contribute additional
 configurations, publications, dependencies, ...</div>
</td>
</tr>
<tr class="rowColor">
<th class="colFirst" scope="row"><a href="JavaApplication.html" title="interface in org.gradle.api.plugins">JavaApplication</a></th>
<td class="colLast">
<div class="block">Configuration for a Java application, defining how to assemble the application.</div>
</td>
</tr>
<tr class="altColor">
<th class="colFirst" scope="row"><a href="JavaPlatformExtension.html" title="interface in org.gradle.api.plugins">JavaPlatformExtension</a></th>
<td class="colLast">
<div class="block">The extension to configure a Java platform project.</div>
</td>
</tr>
<tr class="rowColor">
<th class="colFirst" scope="row"><a href="JavaPluginExtension.html" title="interface in org.gradle.api.plugins">JavaPluginExtension</a></th>
<td class="colLast">
<div class="block">Common configuration for Java based projects.</div>
</td>
</tr>
<tr class="altColor">
<th class="colFirst" scope="row"><a href="JavaResolutionConsistency.html" title="interface in org.gradle.api.plugins">JavaResolutionConsistency</a></th>
<td class="colLast">
<div class="block">Dependency resolution consistency configuration for
 the Java derived plugins.</div>
</td>
</tr>
<tr class="rowColor">
<th class="colFirst" scope="row"><a href="ObjectConfigurationAction.html" title="interface in org.gradle.api.plugins">ObjectConfigurationAction</a></th>
<td class="colLast">
<div class="block">An <code>ObjectConfigurationAction</code> allows you to apply <a href="../Plugin.html" title="interface in org.gradle.api"><code>Plugin</code></a>s and scripts to an object
 or objects.</div>
</td>
</tr>
<tr class="altColor">
<th class="colFirst" scope="row"><a href="PluginAware.html" title="interface in org.gradle.api.plugins">PluginAware</a></th>
<td class="colLast">
<div class="block">Something that can have plugins applied to it.</div>
</td>
</tr>
<tr class="rowColor">
<th class="colFirst" scope="row"><a href="PluginCollection.html" title="interface in org.gradle.api.plugins">PluginCollection</a>&lt;T extends <a href="../Plugin.html" title="interface in org.gradle.api">Plugin</a>&gt;</th>
<td class="colLast">
<div class="block">A <code>PluginCollection</code> represents a collection of <a href="../Plugin.html" title="interface in org.gradle.api"><code>Plugin</code></a> instances.</div>
</td>
</tr>
<tr class="altColor">
<th class="colFirst" scope="row"><a href="PluginContainer.html" title="interface in org.gradle.api.plugins">PluginContainer</a></th>
<td class="colLast">
<div class="block">A <code>PluginContainer</code> is used to manage a set of <a href="../Plugin.html" title="interface in org.gradle.api"><code>Plugin</code></a> instances applied to a
 particular project.</div>
</td>
</tr>
<tr class="rowColor">
<th class="colFirst" scope="row"><a href="PluginManager.html" title="interface in org.gradle.api.plugins">PluginManager</a></th>
<td class="colLast">
<div class="block">Facilitates applying plugins and determining which plugins have been applied to a <a href="PluginAware.html" title="interface in org.gradle.api.plugins"><code>PluginAware</code></a> object.</div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList">
<table class="typeSummary" summary="Class Summary table, listing classes, and an explanation">
<caption><span>Class Summary</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Class</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tbody>
<tr class="altColor">
<th class="colFirst" scope="row"><a href="ApplicationPlugin.html" title="class in org.gradle.api.plugins">ApplicationPlugin</a></th>
<td class="colLast">
<div class="block">A <a href="../Plugin.html" title="interface in org.gradle.api"><code>Plugin</code></a> which packages and runs a project as a Java Application.</div>
</td>
</tr>
<tr class="rowColor">
<th class="colFirst" scope="row"><a href="ApplicationPluginConvention.html" title="class in org.gradle.api.plugins">ApplicationPluginConvention</a></th>
<td class="colLast">Deprecated.
<div class="deprecationComment">Use <a href="JavaApplication.html" title="interface in org.gradle.api.plugins"><code>JavaApplication</code></a> instead.</div>
</td>
</tr>
<tr class="altColor">
<th class="colFirst" scope="row"><a href="BasePlugin.html" title="class in org.gradle.api.plugins">BasePlugin</a></th>
<td class="colLast">
<div class="block">A <a href="../Plugin.html" title="interface in org.gradle.api"><code>Plugin</code></a> which defines a basic project lifecycle and some common convention properties.</div>
</td>
</tr>
<tr class="rowColor">
<th class="colFirst" scope="row"><a href="BasePluginConvention.html" title="class in org.gradle.api.plugins">BasePluginConvention</a></th>
<td class="colLast">Deprecated.
<div class="deprecationComment">Use <a href="BasePluginExtension.html" title="interface in org.gradle.api.plugins"><code>BasePluginExtension</code></a> instead.</div>
</td>
</tr>
<tr class="altColor">
<th class="colFirst" scope="row"><a href="GroovyBasePlugin.html" title="class in org.gradle.api.plugins">GroovyBasePlugin</a></th>
<td class="colLast">
<div class="block">Extends <a href="JavaBasePlugin.html" title="class in org.gradle.api.plugins"><code>JavaBasePlugin</code></a> to provide support for compiling and documenting Groovy
 source files.</div>
</td>
</tr>
<tr class="rowColor">
<th class="colFirst" scope="row"><a href="GroovyPlugin.html" title="class in org.gradle.api.plugins">GroovyPlugin</a></th>
<td class="colLast">
<div class="block">A <a href="../Plugin.html" title="interface in org.gradle.api"><code>Plugin</code></a> which extends the <a href="JavaPlugin.html" title="class in org.gradle.api.plugins"><code>JavaPlugin</code></a> to provide support for compiling and documenting Groovy
 source files.</div>
</td>
</tr>
<tr class="altColor">
<th class="colFirst" scope="row"><a href="HelpTasksPlugin.html" title="class in org.gradle.api.plugins">HelpTasksPlugin</a></th>
<td class="colLast">
<div class="block">Adds various reporting tasks that provide information about the project.</div>
</td>
</tr>
<tr class="rowColor">
<th class="colFirst" scope="row"><a href="JavaBasePlugin.html" title="class in org.gradle.api.plugins">JavaBasePlugin</a></th>
<td class="colLast">
<div class="block">A <a href="../Plugin.html" title="interface in org.gradle.api"><code>Plugin</code></a> which compiles and tests Java source, and assembles it into a JAR file.</div>
</td>
</tr>
<tr class="altColor">
<th class="colFirst" scope="row"><a href="JavaLibraryDistributionPlugin.html" title="class in org.gradle.api.plugins">JavaLibraryDistributionPlugin</a></th>
<td class="colLast">
<div class="block">A <a href="../Plugin.html" title="interface in org.gradle.api"><code>Plugin</code></a> which package a Java project as a distribution including the JAR and runtime dependencies.</div>
</td>
</tr>
<tr class="rowColor">
<th class="colFirst" scope="row"><a href="JavaLibraryPlugin.html" title="class in org.gradle.api.plugins">JavaLibraryPlugin</a></th>
<td class="colLast">
<div class="block">A <a href="../Plugin.html" title="interface in org.gradle.api"><code>Plugin</code></a> which extends the capabilities of the <a href="JavaPlugin.html" title="class in org.gradle.api.plugins"><code>Java plugin</code></a> by cleanly separating
 the API and implementation dependencies of a library.</div>
</td>
</tr>
<tr class="altColor">
<th class="colFirst" scope="row"><a href="JavaPlatformPlugin.html" title="class in org.gradle.api.plugins">JavaPlatformPlugin</a></th>
<td class="colLast">
<div class="block">The Java platform plugin allows building platform components
 for Java, which are usually published as BOM files (for Maven)
 or Gradle platforms (Gradle metadata).</div>
</td>
</tr>
<tr class="rowColor">
<th class="colFirst" scope="row"><a href="JavaPlugin.html" title="class in org.gradle.api.plugins">JavaPlugin</a></th>
<td class="colLast">
<div class="block">A <a href="../Plugin.html" title="interface in org.gradle.api"><code>Plugin</code></a> which compiles and tests Java source, and assembles it into a JAR file.</div>
</td>
</tr>
<tr class="altColor">
<th class="colFirst" scope="row"><a href="JavaPluginConvention.html" title="class in org.gradle.api.plugins">JavaPluginConvention</a></th>
<td class="colLast">Deprecated.
<div class="deprecationComment">Replaced by <a href="JavaPluginExtension.html" title="interface in org.gradle.api.plugins"><code>JavaPluginExtension</code></a>.</div>
</td>
</tr>
<tr class="rowColor">
<th class="colFirst" scope="row"><a href="JavaTestFixturesPlugin.html" title="class in org.gradle.api.plugins">JavaTestFixturesPlugin</a></th>
<td class="colLast">
<div class="block">Adds support for producing test fixtures.</div>
</td>
</tr>
<tr class="altColor">
<th class="colFirst" scope="row"><a href="JvmEcosystemPlugin.html" title="class in org.gradle.api.plugins">JvmEcosystemPlugin</a></th>
<td class="colLast">
<div class="block">A base plugin for projects working in a JVM world.</div>
</td>
</tr>
<tr class="rowColor">
<th class="colFirst" scope="row"><a href="JvmTestSuitePlugin.html" title="class in org.gradle.api.plugins">JvmTestSuitePlugin</a></th>
<td class="colLast">
<div class="block">A <a href="../Plugin.html" title="interface in org.gradle.api"><code>Plugin</code></a> that adds extensions for declaring, compiling and running <a href="jvm/JvmTestSuite.html" title="interface in org.gradle.api.plugins.jvm"><code>JvmTestSuite</code></a>s.</div>
</td>
</tr>
<tr class="altColor">
<th class="colFirst" scope="row"><a href="JvmToolchainManagementPlugin.html" title="class in org.gradle.api.plugins">JvmToolchainManagementPlugin</a></th>
<td class="colLast">
<div class="block">A plugin that provides JVM specific <a href="../toolchain/management/ToolchainManagement.html" title="interface in org.gradle.api.toolchain.management"><code>ToolchainManagement</code></a> configuration.</div>
</td>
</tr>
<tr class="rowColor">
<th class="colFirst" scope="row"><a href="JvmToolchainsPlugin.html" title="class in org.gradle.api.plugins">JvmToolchainsPlugin</a></th>
<td class="colLast">
<div class="block">A plugin that provides JVM toolchains for projects that need to execute Java from local JVM installations or run the tools included in a JDK.</div>
</td>
</tr>
<tr class="altColor">
<th class="colFirst" scope="row"><a href="ProjectReportsPlugin.html" title="class in org.gradle.api.plugins">ProjectReportsPlugin</a></th>
<td class="colLast">
<div class="block">A <a href="../Plugin.html" title="interface in org.gradle.api"><code>Plugin</code></a> which adds some project visualization report tasks to a project.</div>
</td>
</tr>
<tr class="rowColor">
<th class="colFirst" scope="row"><a href="ProjectReportsPluginConvention.html" title="class in org.gradle.api.plugins">ProjectReportsPluginConvention</a></th>
<td class="colLast">Deprecated.
<div class="deprecationComment">Please configure the tasks directly.</div>
</td>
</tr>
<tr class="altColor">
<th class="colFirst" scope="row"><a href="ReportingBasePlugin.html" title="class in org.gradle.api.plugins">ReportingBasePlugin</a></th>
<td class="colLast">
<div class="block">A <a href="../Plugin.html" title="interface in org.gradle.api"><code>Plugin</code></a> which provides the basic skeleton for reporting.</div>
</td>
</tr>
<tr class="rowColor">
<th class="colFirst" scope="row"><a href="TestReportAggregationPlugin.html" title="class in org.gradle.api.plugins">TestReportAggregationPlugin</a></th>
<td class="colLast">
<div class="block">Adds configurations to for resolving variants containing test execution results, which may span multiple subprojects.</div>
</td>
</tr>
<tr class="altColor">
<th class="colFirst" scope="row"><a href="WarPlugin.html" title="class in org.gradle.api.plugins">WarPlugin</a></th>
<td class="colLast">
<div class="block">A <a href="../Plugin.html" title="interface in org.gradle.api"><code>Plugin</code></a> which extends the <a href="JavaPlugin.html" title="class in org.gradle.api.plugins"><code>JavaPlugin</code></a> to add tasks which assemble a web application into a WAR
 file.</div>
</td>
</tr>
<tr class="rowColor">
<th class="colFirst" scope="row"><a href="WarPluginConvention.html" title="class in org.gradle.api.plugins">WarPluginConvention</a></th>
<td class="colLast">Deprecated.
<div class="deprecationComment">Please configure the tasks directly.</div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList">
<table class="typeSummary" summary="Exception Summary table, listing exceptions, and an explanation">
<caption><span>Exception Summary</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Exception</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tbody>
<tr class="altColor">
<th class="colFirst" scope="row"><a href="ExtraPropertiesExtension.UnknownPropertyException.html" title="class in org.gradle.api.plugins">ExtraPropertiesExtension.UnknownPropertyException</a></th>
<td class="colLast">
<div class="block">The exception that will be thrown when an attempt is made to read a property that is not set.</div>
</td>
</tr>
<tr class="rowColor">
<th class="colFirst" scope="row"><a href="InvalidPluginException.html" title="class in org.gradle.api.plugins">InvalidPluginException</a></th>
<td class="colLast">
<div class="block">Thrown when a plugin is found to be invalid when it is loaded.</div>
</td>
</tr>
<tr class="altColor">
<th class="colFirst" scope="row"><a href="PluginInstantiationException.html" title="class in org.gradle.api.plugins">PluginInstantiationException</a></th>
<td class="colLast">
<div class="block">A <code>PluginInstantiationException</code> is thrown when a plugin cannot be instantiated.</div>
</td>
</tr>
<tr class="rowColor">
<th class="colFirst" scope="row"><a href="UnknownPluginException.html" title="class in org.gradle.api.plugins">UnknownPluginException</a></th>
<td class="colLast">
<div class="block">A <code>UnknownPluginException</code> is thrown when an unknown plugin id is provided.</div>
</td>
</tr>
</tbody>
</table>
</li>
</ul>
</div>
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../index.html">Overview</a></li>
<li class="navBarCell1Rev">Package</li>
<li>Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
