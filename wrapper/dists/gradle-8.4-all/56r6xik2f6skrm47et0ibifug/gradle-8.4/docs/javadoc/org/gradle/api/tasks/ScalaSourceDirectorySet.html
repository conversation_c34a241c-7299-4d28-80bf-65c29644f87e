<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc -->
<title>ScalaSourceDirectorySet (Gradle API 8.4)</title>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<link rel="stylesheet" type="text/css" href="../../../../javadoc.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../jquery/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
<script type="text/javascript" src="../../../../jquery/jszip/dist/jszip.min.js"></script>
<script type="text/javascript" src="../../../../jquery/jszip-utils/dist/jszip-utils.min.js"></script>
<!--[if IE]>
<script type="text/javascript" src="../../../../jquery/jszip-utils/dist/jszip-utils-ie.min.js"></script>
<![endif]-->
<script type="text/javascript" src="../../../../jquery/jquery-3.6.0.min.js"></script>
<script type="text/javascript" src="../../../../jquery/jquery-ui.min.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="ScalaSourceDirectorySet (Gradle API 8.4)";
        }
    }
    catch(err) {
    }
//-->
var pathtoroot = "../../../../";
var useModuleDirectories = false;
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="fixedNav">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses.html">All&nbsp;Classes</a></li>
</ul>
<ul class="navListSearch">
<li><label for="search">SEARCH:</label>
<input type="text" id="search" value="search" disabled="disabled">
<input type="reset" id="reset" value="reset" disabled="disabled">
</li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li><a href="#nested.class.summary">Nested</a>&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li>Method</li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
</div>
<div class="navPadding">&nbsp;</div>
<script type="text/javascript"><!--
$('.navPadding').css('padding-top', $('.fixedNav').css("height"));
//-->
</script>
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle"><span class="packageLabelInType">Package</span>&nbsp;<a href="package-summary.html">org.gradle.api.tasks</a></div>
<h2 title="Interface ScalaSourceDirectorySet" class="title">Interface ScalaSourceDirectorySet</h2>
</div>
<div class="contentContainer">
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Superinterfaces:</dt>
<dd><code><a href="AntBuilderAware.html" title="interface in org.gradle.api.tasks">AntBuilderAware</a></code>, <code><a href="../Buildable.html" title="interface in org.gradle.api">Buildable</a></code>, <code><a href="../Describable.html" title="interface in org.gradle.api">Describable</a></code>, <code><a href="../file/FileCollection.html" title="interface in org.gradle.api.file">FileCollection</a></code>, <code><a href="../file/FileTree.html" title="interface in org.gradle.api.file">FileTree</a></code>, <code>java.lang.Iterable&lt;java.io.File&gt;</code>, <code><a href="../Named.html" title="interface in org.gradle.api">Named</a></code>, <code><a href="util/PatternFilterable.html" title="interface in org.gradle.api.tasks.util">PatternFilterable</a></code>, <code><a href="../file/SourceDirectorySet.html" title="interface in org.gradle.api.file">SourceDirectorySet</a></code></dd>
</dl>
<hr>
<pre>public interface <span class="typeNameLabel">ScalaSourceDirectorySet</span>
extends <a href="../file/SourceDirectorySet.html" title="interface in org.gradle.api.file">SourceDirectorySet</a></pre>
<div class="block">A <code>ScalaSourceDirectorySet</code> defines the properties and methods added to a <a href="SourceSet.html" title="interface in org.gradle.api.tasks"><code>SourceSet</code></a> by the <code>ScalaPlugin</code>.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>7.1</dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== NESTED CLASS SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="nested.class.summary">
<!--   -->
</a>
<h3>Nested Class Summary</h3>
<ul class="blockList">
<li class="blockList"><a name="nested.classes.inherited.from.class.org.gradle.api.file.FileCollection">
<!--   -->
</a>
<h3>Nested classes/interfaces inherited from interface&nbsp;org.gradle.api.file.<a href="../file/FileCollection.html" title="interface in org.gradle.api.file">FileCollection</a></h3>
<code><a href="../file/FileCollection.AntType.html" title="enum in org.gradle.api.file">FileCollection.AntType</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="nested.classes.inherited.from.class.org.gradle.api.Named">
<!--   -->
</a>
<h3>Nested classes/interfaces inherited from interface&nbsp;org.gradle.api.<a href="../Named.html" title="interface in org.gradle.api">Named</a></h3>
<code><a href="../Named.Namer.html" title="class in org.gradle.api">Named.Namer</a></code></li>
</ul>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.org.gradle.api.Buildable">
<!--   -->
</a>
<h3>Methods inherited from interface&nbsp;org.gradle.api.<a href="../Buildable.html" title="interface in org.gradle.api">Buildable</a></h3>
<code><a href="../Buildable.html#getBuildDependencies--">getBuildDependencies</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.org.gradle.api.Describable">
<!--   -->
</a>
<h3>Methods inherited from interface&nbsp;org.gradle.api.<a href="../Describable.html" title="interface in org.gradle.api">Describable</a></h3>
<code><a href="../Describable.html#getDisplayName--">getDisplayName</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.org.gradle.api.file.FileCollection">
<!--   -->
</a>
<h3>Methods inherited from interface&nbsp;org.gradle.api.file.<a href="../file/FileCollection.html" title="interface in org.gradle.api.file">FileCollection</a></h3>
<code><a href="../file/FileCollection.html#addToAntBuilder-java.lang.Object-java.lang.String-">addToAntBuilder</a>, <a href="../file/FileCollection.html#addToAntBuilder-java.lang.Object-java.lang.String-org.gradle.api.file.FileCollection.AntType-">addToAntBuilder</a>, <a href="../file/FileCollection.html#contains-java.io.File-">contains</a>, <a href="../file/FileCollection.html#filter-groovy.lang.Closure-">filter</a>, <a href="../file/FileCollection.html#filter-org.gradle.api.specs.Spec-">filter</a>, <a href="../file/FileCollection.html#getAsPath--">getAsPath</a>, <a href="../file/FileCollection.html#getElements--">getElements</a>, <a href="../file/FileCollection.html#getSingleFile--">getSingleFile</a>, <a href="../file/FileCollection.html#isEmpty--">isEmpty</a>, <a href="../file/FileCollection.html#minus-org.gradle.api.file.FileCollection-">minus</a>, <a href="../file/FileCollection.html#plus-org.gradle.api.file.FileCollection-">plus</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.org.gradle.api.file.FileTree">
<!--   -->
</a>
<h3>Methods inherited from interface&nbsp;org.gradle.api.file.<a href="../file/FileTree.html" title="interface in org.gradle.api.file">FileTree</a></h3>
<code><a href="../file/FileTree.html#getAsFileTree--">getAsFileTree</a>, <a href="../file/FileTree.html#getFiles--">getFiles</a>, <a href="../file/FileTree.html#matching-groovy.lang.Closure-">matching</a>, <a href="../file/FileTree.html#matching-org.gradle.api.Action-">matching</a>, <a href="../file/FileTree.html#matching-org.gradle.api.tasks.util.PatternFilterable-">matching</a>, <a href="../file/FileTree.html#plus-org.gradle.api.file.FileTree-">plus</a>, <a href="../file/FileTree.html#visit-groovy.lang.Closure-">visit</a>, <a href="../file/FileTree.html#visit-org.gradle.api.Action-">visit</a>, <a href="../file/FileTree.html#visit-org.gradle.api.file.FileVisitor-">visit</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Iterable">
<!--   -->
</a>
<h3>Methods inherited from interface&nbsp;java.lang.Iterable</h3>
<code>forEach, iterator, spliterator</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.org.gradle.api.tasks.util.PatternFilterable">
<!--   -->
</a>
<h3>Methods inherited from interface&nbsp;org.gradle.api.tasks.util.<a href="util/PatternFilterable.html" title="interface in org.gradle.api.tasks.util">PatternFilterable</a></h3>
<code><a href="util/PatternFilterable.html#exclude-groovy.lang.Closure-">exclude</a>, <a href="util/PatternFilterable.html#exclude-java.lang.Iterable-">exclude</a>, <a href="util/PatternFilterable.html#exclude-java.lang.String...-">exclude</a>, <a href="util/PatternFilterable.html#exclude-org.gradle.api.specs.Spec-">exclude</a>, <a href="util/PatternFilterable.html#getExcludes--">getExcludes</a>, <a href="util/PatternFilterable.html#getIncludes--">getIncludes</a>, <a href="util/PatternFilterable.html#include-groovy.lang.Closure-">include</a>, <a href="util/PatternFilterable.html#include-java.lang.Iterable-">include</a>, <a href="util/PatternFilterable.html#include-java.lang.String...-">include</a>, <a href="util/PatternFilterable.html#include-org.gradle.api.specs.Spec-">include</a>, <a href="util/PatternFilterable.html#setExcludes-java.lang.Iterable-">setExcludes</a>, <a href="util/PatternFilterable.html#setIncludes-java.lang.Iterable-">setIncludes</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.org.gradle.api.file.SourceDirectorySet">
<!--   -->
</a>
<h3>Methods inherited from interface&nbsp;org.gradle.api.file.<a href="../file/SourceDirectorySet.html" title="interface in org.gradle.api.file">SourceDirectorySet</a></h3>
<code><a href="../file/SourceDirectorySet.html#compiledBy-org.gradle.api.tasks.TaskProvider-java.util.function.Function-">compiledBy</a>, <a href="../file/SourceDirectorySet.html#getClassesDirectory--">getClassesDirectory</a>, <a href="../file/SourceDirectorySet.html#getDestinationDirectory--">getDestinationDirectory</a>, <a href="../file/SourceDirectorySet.html#getFilter--">getFilter</a>, <a href="../file/SourceDirectorySet.html#getName--">getName</a>, <a href="../file/SourceDirectorySet.html#getSourceDirectories--">getSourceDirectories</a>, <a href="../file/SourceDirectorySet.html#getSrcDirs--">getSrcDirs</a>, <a href="../file/SourceDirectorySet.html#getSrcDirTrees--">getSrcDirTrees</a>, <a href="../file/SourceDirectorySet.html#setSrcDirs-java.lang.Iterable-">setSrcDirs</a>, <a href="../file/SourceDirectorySet.html#source-org.gradle.api.file.SourceDirectorySet-">source</a>, <a href="../file/SourceDirectorySet.html#srcDir-java.lang.Object-">srcDir</a>, <a href="../file/SourceDirectorySet.html#srcDirs-java.lang.Object...-">srcDirs</a></code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li><a href="#nested.class.summary">Nested</a>&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li>Method</li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
