<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc -->
<title>ExtensionContainer (Gradle API 8.4)</title>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<link rel="stylesheet" type="text/css" href="../../../../javadoc.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../jquery/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
<script type="text/javascript" src="../../../../jquery/jszip/dist/jszip.min.js"></script>
<script type="text/javascript" src="../../../../jquery/jszip-utils/dist/jszip-utils.min.js"></script>
<!--[if IE]>
<script type="text/javascript" src="../../../../jquery/jszip-utils/dist/jszip-utils-ie.min.js"></script>
<![endif]-->
<script type="text/javascript" src="../../../../jquery/jquery-3.6.0.min.js"></script>
<script type="text/javascript" src="../../../../jquery/jquery-ui.min.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="ExtensionContainer (Gradle API 8.4)";
        }
    }
    catch(err) {
    }
//-->
var data = {"i0":6,"i1":6,"i2":6,"i3":6,"i4":6,"i5":6,"i6":6,"i7":6,"i8":6,"i9":6,"i10":6,"i11":6,"i12":6,"i13":6,"i14":6,"i15":6,"i16":6};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],4:["t3","Abstract Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
var pathtoroot = "../../../../";
var useModuleDirectories = false;
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="fixedNav">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses.html">All&nbsp;Classes</a></li>
</ul>
<ul class="navListSearch">
<li><label for="search">SEARCH:</label>
<input type="text" id="search" value="search" disabled="disabled">
<input type="reset" id="reset" value="reset" disabled="disabled">
</li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
</div>
<div class="navPadding">&nbsp;</div>
<script type="text/javascript"><!--
$('.navPadding').css('padding-top', $('.fixedNav').css("height"));
//-->
</script>
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle"><span class="packageLabelInType">Package</span>&nbsp;<a href="package-summary.html">org.gradle.api.plugins</a></div>
<h2 title="Interface ExtensionContainer" class="title">Interface ExtensionContainer</h2>
</div>
<div class="contentContainer">
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Known Subinterfaces:</dt>
<dd><code><a href="Convention.html" title="interface in org.gradle.api.plugins">Convention</a></code></dd>
</dl>
<hr>
<pre>public interface <span class="typeNameLabel">ExtensionContainer</span></pre>
<div class="block">Allows adding 'namespaced' DSL extensions to a target object.</div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t3" class="tableTab"><span><a href="javascript:show(4);">Abstract Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colSecond" scope="col">Method</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>&lt;T&gt;&nbsp;void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#add-java.lang.Class-java.lang.String-T-">add</a></span>&#8203;(java.lang.Class&lt;T&gt;&nbsp;publicType,
   java.lang.String&nbsp;name,
   T&nbsp;extension)</code></th>
<td class="colLast">
<div class="block">Adds a new extension to this container.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#add-java.lang.String-java.lang.Object-">add</a></span>&#8203;(java.lang.String&nbsp;name,
   java.lang.Object&nbsp;extension)</code></th>
<td class="colLast">
<div class="block">Adds a new extension to this container.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>&lt;T&gt;&nbsp;void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#add-org.gradle.api.reflect.TypeOf-java.lang.String-T-">add</a></span>&#8203;(<a href="../reflect/TypeOf.html" title="class in org.gradle.api.reflect">TypeOf</a>&lt;T&gt;&nbsp;publicType,
   java.lang.String&nbsp;name,
   T&nbsp;extension)</code></th>
<td class="colLast">
<div class="block">Adds a new extension to this container.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>&lt;T&gt;&nbsp;void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#configure-java.lang.Class-org.gradle.api.Action-">configure</a></span>&#8203;(java.lang.Class&lt;T&gt;&nbsp;type,
         <a href="../Action.html" title="interface in org.gradle.api">Action</a>&lt;? super T&gt;&nbsp;action)</code></th>
<td class="colLast">
<div class="block">Looks for the extension of the specified type and configures it with the supplied action.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>&lt;T&gt;&nbsp;void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#configure-java.lang.String-org.gradle.api.Action-">configure</a></span>&#8203;(java.lang.String&nbsp;name,
         <a href="../Action.html" title="interface in org.gradle.api">Action</a>&lt;? super T&gt;&nbsp;action)</code></th>
<td class="colLast">
<div class="block">Looks for the extension with the specified name and configures it with the supplied action.</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>&lt;T&gt;&nbsp;void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#configure-org.gradle.api.reflect.TypeOf-org.gradle.api.Action-">configure</a></span>&#8203;(<a href="../reflect/TypeOf.html" title="class in org.gradle.api.reflect">TypeOf</a>&lt;T&gt;&nbsp;type,
         <a href="../Action.html" title="interface in org.gradle.api">Action</a>&lt;? super T&gt;&nbsp;action)</code></th>
<td class="colLast">
<div class="block">Looks for the extension of the specified type and configures it with the supplied action.</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>&lt;T&gt;&nbsp;T</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#create-java.lang.Class-java.lang.String-java.lang.Class-java.lang.Object...-">create</a></span>&#8203;(java.lang.Class&lt;T&gt;&nbsp;publicType,
      java.lang.String&nbsp;name,
      java.lang.Class&lt;? extends T&gt;&nbsp;instanceType,
      java.lang.Object...&nbsp;constructionArguments)</code></th>
<td class="colLast">
<div class="block">Creates and adds a new extension to this container.</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>&lt;T&gt;&nbsp;T</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#create-java.lang.String-java.lang.Class-java.lang.Object...-">create</a></span>&#8203;(java.lang.String&nbsp;name,
      java.lang.Class&lt;T&gt;&nbsp;type,
      java.lang.Object...&nbsp;constructionArguments)</code></th>
<td class="colLast">
<div class="block">Creates and adds a new extension to this container.</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>&lt;T&gt;&nbsp;T</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#create-org.gradle.api.reflect.TypeOf-java.lang.String-java.lang.Class-java.lang.Object...-">create</a></span>&#8203;(<a href="../reflect/TypeOf.html" title="class in org.gradle.api.reflect">TypeOf</a>&lt;T&gt;&nbsp;publicType,
      java.lang.String&nbsp;name,
      java.lang.Class&lt;? extends T&gt;&nbsp;instanceType,
      java.lang.Object...&nbsp;constructionArguments)</code></th>
<td class="colLast">
<div class="block">Creates and adds a new extension to this container.</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>java.lang.Object</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#findByName-java.lang.String-">findByName</a></span>&#8203;(java.lang.String&nbsp;name)</code></th>
<td class="colLast">
<div class="block">Looks for the extension of a given name.</div>
</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code>&lt;T&gt;&nbsp;T</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#findByType-java.lang.Class-">findByType</a></span>&#8203;(java.lang.Class&lt;T&gt;&nbsp;type)</code></th>
<td class="colLast">
<div class="block">Looks for the extension of a given type (useful to avoid casting).</div>
</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code>&lt;T&gt;&nbsp;T</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#findByType-org.gradle.api.reflect.TypeOf-">findByType</a></span>&#8203;(<a href="../reflect/TypeOf.html" title="class in org.gradle.api.reflect">TypeOf</a>&lt;T&gt;&nbsp;type)</code></th>
<td class="colLast">
<div class="block">Looks for the extension of a given type (useful to avoid casting).</div>
</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code>java.lang.Object</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getByName-java.lang.String-">getByName</a></span>&#8203;(java.lang.String&nbsp;name)</code></th>
<td class="colLast">
<div class="block">Looks for the extension of a given name.</div>
</td>
</tr>
<tr id="i13" class="rowColor">
<td class="colFirst"><code>&lt;T&gt;&nbsp;T</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getByType-java.lang.Class-">getByType</a></span>&#8203;(java.lang.Class&lt;T&gt;&nbsp;type)</code></th>
<td class="colLast">
<div class="block">Looks for the extension of a given type (useful to avoid casting).</div>
</td>
</tr>
<tr id="i14" class="altColor">
<td class="colFirst"><code>&lt;T&gt;&nbsp;T</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getByType-org.gradle.api.reflect.TypeOf-">getByType</a></span>&#8203;(<a href="../reflect/TypeOf.html" title="class in org.gradle.api.reflect">TypeOf</a>&lt;T&gt;&nbsp;type)</code></th>
<td class="colLast">
<div class="block">Looks for the extension of a given type (useful to avoid casting).</div>
</td>
</tr>
<tr id="i15" class="rowColor">
<td class="colFirst"><code><a href="ExtensionsSchema.html" title="interface in org.gradle.api.plugins">ExtensionsSchema</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getExtensionsSchema--">getExtensionsSchema</a></span>()</code></th>
<td class="colLast">
<div class="block">Provides access to the schema of all known extensions.</div>
</td>
</tr>
<tr id="i16" class="altColor">
<td class="colFirst"><code><a href="ExtraPropertiesExtension.html" title="interface in org.gradle.api.plugins">ExtraPropertiesExtension</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getExtraProperties--">getExtraProperties</a></span>()</code></th>
<td class="colLast">
<div class="block">The extra properties extension in this extension container.</div>
</td>
</tr>
</table>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="add-java.lang.Class-java.lang.String-java.lang.Object-">
<!--   -->
</a><a name="add-java.lang.Class-java.lang.String-T-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>add</h4>
<pre class="methodSignature">&lt;T&gt;&nbsp;void&nbsp;add&#8203;(java.lang.Class&lt;T&gt;&nbsp;publicType,
             java.lang.String&nbsp;name,
             T&nbsp;extension)</pre>
<div class="block">Adds a new extension to this container.

 Adding an extension of name 'foo' will:
 <ul>
 <li> add 'foo' dynamic property
 <li> add 'foo' dynamic method that accepts a closure that is a configuration script block
 </ul>

 The extension will be exposed as <code>publicType</code>.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>publicType</code> - The extension public type</dd>
<dd><code>name</code> - The name for the extension</dd>
<dd><code>extension</code> - Any object implementing <code>publicType</code></dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code>java.lang.IllegalArgumentException</code> - When an extension with the given name already exists.</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>3.5</dd>
</dl>
</li>
</ul>
<a name="add-org.gradle.api.reflect.TypeOf-java.lang.String-java.lang.Object-">
<!--   -->
</a><a name="add-org.gradle.api.reflect.TypeOf-java.lang.String-T-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>add</h4>
<pre class="methodSignature">&lt;T&gt;&nbsp;void&nbsp;add&#8203;(<a href="../reflect/TypeOf.html" title="class in org.gradle.api.reflect">TypeOf</a>&lt;T&gt;&nbsp;publicType,
             java.lang.String&nbsp;name,
             T&nbsp;extension)</pre>
<div class="block">Adds a new extension to this container.

 Adding an extension of name 'foo' will:
 <ul>
 <li> add 'foo' dynamic property
 <li> add 'foo' dynamic method that accepts a closure that is a configuration script block
 </ul>

 The extension will be exposed as <code>publicType</code>.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>publicType</code> - The extension public type</dd>
<dd><code>name</code> - The name for the extension</dd>
<dd><code>extension</code> - Any object implementing <code>publicType</code></dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code>java.lang.IllegalArgumentException</code> - When an extension with the given name already exists.</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>3.5</dd>
</dl>
</li>
</ul>
<a name="add-java.lang.String-java.lang.Object-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>add</h4>
<pre class="methodSignature">void&nbsp;add&#8203;(java.lang.String&nbsp;name,
         java.lang.Object&nbsp;extension)</pre>
<div class="block">Adds a new extension to this container.

 Adding an extension of name 'foo' will:
 <ul>
 <li> add 'foo' dynamic property
 <li> add 'foo' dynamic method that accepts a closure that is a configuration script block
 </ul>

 The extension will be exposed as <code>extension.getClass()</code> unless the extension itself declares a preferred public type via the <a href="../reflect/HasPublicType.html" title="interface in org.gradle.api.reflect"><code>HasPublicType</code></a> protocol.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>name</code> - The name for the extension</dd>
<dd><code>extension</code> - Any object</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code>java.lang.IllegalArgumentException</code> - When an extension with the given name already exists</dd>
</dl>
</li>
</ul>
<a name="create-java.lang.Class-java.lang.String-java.lang.Class-java.lang.Object...-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>create</h4>
<pre class="methodSignature">&lt;T&gt;&nbsp;T&nbsp;create&#8203;(java.lang.Class&lt;T&gt;&nbsp;publicType,
             java.lang.String&nbsp;name,
             java.lang.Class&lt;? extends T&gt;&nbsp;instanceType,
             java.lang.Object...&nbsp;constructionArguments)</pre>
<div class="block">Creates and adds a new extension to this container.

 A new instance of the given <code>instanceType</code> will be created using the given <code>constructionArguments</code>.
 The extension will be exposed as <code>publicType</code>.
 The new instance will have been dynamically made <a href="ExtensionAware.html" title="interface in org.gradle.api.plugins"><code>ExtensionAware</code></a>, which means that you can cast it to <a href="ExtensionAware.html" title="interface in org.gradle.api.plugins"><code>ExtensionAware</code></a>.</div>
<dl>
<dt><span class="paramLabel">Type Parameters:</span></dt>
<dd><code>T</code> - the extension public type</dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>publicType</code> - The extension public type</dd>
<dd><code>name</code> - The name for the extension</dd>
<dd><code>instanceType</code> - The extension instance type</dd>
<dd><code>constructionArguments</code> - The arguments to be used to construct the extension instance</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The created instance</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code>java.lang.IllegalArgumentException</code> - When an extension with the given name already exists.</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>3.5</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="#add-java.lang.Class-java.lang.String-T-"><code>add(Class, String, Object)</code></a></dd>
</dl>
</li>
</ul>
<a name="create-org.gradle.api.reflect.TypeOf-java.lang.String-java.lang.Class-java.lang.Object...-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>create</h4>
<pre class="methodSignature">&lt;T&gt;&nbsp;T&nbsp;create&#8203;(<a href="../reflect/TypeOf.html" title="class in org.gradle.api.reflect">TypeOf</a>&lt;T&gt;&nbsp;publicType,
             java.lang.String&nbsp;name,
             java.lang.Class&lt;? extends T&gt;&nbsp;instanceType,
             java.lang.Object...&nbsp;constructionArguments)</pre>
<div class="block">Creates and adds a new extension to this container.

 A new instance of the given <code>instanceType</code> will be created using the given <code>constructionArguments</code>.
 The extension will be exposed as <code>publicType</code>.
 The new instance will have been dynamically made <a href="ExtensionAware.html" title="interface in org.gradle.api.plugins"><code>ExtensionAware</code></a>, which means that you can cast it to <a href="ExtensionAware.html" title="interface in org.gradle.api.plugins"><code>ExtensionAware</code></a>.</div>
<dl>
<dt><span class="paramLabel">Type Parameters:</span></dt>
<dd><code>T</code> - the extension public type</dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>publicType</code> - The extension public type</dd>
<dd><code>name</code> - The name for the extension</dd>
<dd><code>instanceType</code> - The extension instance type</dd>
<dd><code>constructionArguments</code> - The arguments to be used to construct the extension instance</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The created instance</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code>java.lang.IllegalArgumentException</code> - When an extension with the given name already exists.</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>3.5</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="#add-java.lang.Class-java.lang.String-T-"><code>add(Class, String, Object)</code></a></dd>
</dl>
</li>
</ul>
<a name="create-java.lang.String-java.lang.Class-java.lang.Object...-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>create</h4>
<pre class="methodSignature">&lt;T&gt;&nbsp;T&nbsp;create&#8203;(java.lang.String&nbsp;name,
             java.lang.Class&lt;T&gt;&nbsp;type,
             java.lang.Object...&nbsp;constructionArguments)</pre>
<div class="block">Creates and adds a new extension to this container.

 A new instance of the given <code>type</code> will be created using the given <code>constructionArguments</code>.
 The extension will be exposed as <code>type</code> unless the extension itself declares a preferred public type via the <a href="../reflect/HasPublicType.html" title="interface in org.gradle.api.reflect"><code>HasPublicType</code></a> protocol.
 The new instance will have been dynamically made <a href="ExtensionAware.html" title="interface in org.gradle.api.plugins"><code>ExtensionAware</code></a>, which means that you can cast it to <a href="ExtensionAware.html" title="interface in org.gradle.api.plugins"><code>ExtensionAware</code></a>.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>name</code> - The name for the extension</dd>
<dd><code>type</code> - The type of the extension</dd>
<dd><code>constructionArguments</code> - The arguments to be used to construct the extension instance</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The created instance</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code>java.lang.IllegalArgumentException</code> - When an extension with the given name already exists.</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="#add-java.lang.String-java.lang.Object-"><code>add(String, Object)</code></a></dd>
</dl>
</li>
</ul>
<a name="getExtensionsSchema--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getExtensionsSchema</h4>
<pre class="methodSignature"><a href="ExtensionsSchema.html" title="interface in org.gradle.api.plugins">ExtensionsSchema</a>&nbsp;getExtensionsSchema()</pre>
<div class="block">Provides access to the schema of all known extensions.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>4.5</dd>
</dl>
</li>
</ul>
<a name="getByType-java.lang.Class-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getByType</h4>
<pre class="methodSignature">&lt;T&gt;&nbsp;T&nbsp;getByType&#8203;(java.lang.Class&lt;T&gt;&nbsp;type)
         throws <a href="../UnknownDomainObjectException.html" title="class in org.gradle.api">UnknownDomainObjectException</a></pre>
<div class="block">Looks for the extension of a given type (useful to avoid casting). If none found it will throw an exception.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>type</code> - extension type</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>extension, never null</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code><a href="../UnknownDomainObjectException.html" title="class in org.gradle.api">UnknownDomainObjectException</a></code> - When the given extension is not found.</dd>
</dl>
</li>
</ul>
<a name="getByType-org.gradle.api.reflect.TypeOf-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getByType</h4>
<pre class="methodSignature">&lt;T&gt;&nbsp;T&nbsp;getByType&#8203;(<a href="../reflect/TypeOf.html" title="class in org.gradle.api.reflect">TypeOf</a>&lt;T&gt;&nbsp;type)
         throws <a href="../UnknownDomainObjectException.html" title="class in org.gradle.api">UnknownDomainObjectException</a></pre>
<div class="block">Looks for the extension of a given type (useful to avoid casting). If none found it will throw an exception.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>type</code> - extension type</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>extension, never null</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code><a href="../UnknownDomainObjectException.html" title="class in org.gradle.api">UnknownDomainObjectException</a></code> - When the given extension is not found.</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>3.5</dd>
</dl>
</li>
</ul>
<a name="findByType-java.lang.Class-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>findByType</h4>
<pre class="methodSignature">@Nullable
&lt;T&gt;&nbsp;T&nbsp;findByType&#8203;(java.lang.Class&lt;T&gt;&nbsp;type)</pre>
<div class="block">Looks for the extension of a given type (useful to avoid casting). If none found null is returned.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>type</code> - extension type</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>extension or null</dd>
</dl>
</li>
</ul>
<a name="findByType-org.gradle.api.reflect.TypeOf-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>findByType</h4>
<pre class="methodSignature">@Nullable
&lt;T&gt;&nbsp;T&nbsp;findByType&#8203;(<a href="../reflect/TypeOf.html" title="class in org.gradle.api.reflect">TypeOf</a>&lt;T&gt;&nbsp;type)</pre>
<div class="block">Looks for the extension of a given type (useful to avoid casting). If none found null is returned.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>type</code> - extension type</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>extension or null</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>3.5</dd>
</dl>
</li>
</ul>
<a name="getByName-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getByName</h4>
<pre class="methodSignature">java.lang.Object&nbsp;getByName&#8203;(java.lang.String&nbsp;name)
                    throws <a href="../UnknownDomainObjectException.html" title="class in org.gradle.api">UnknownDomainObjectException</a></pre>
<div class="block">Looks for the extension of a given name. If none found it will throw an exception.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>name</code> - extension name</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>extension, never null</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code><a href="../UnknownDomainObjectException.html" title="class in org.gradle.api">UnknownDomainObjectException</a></code> - When the given extension is not found.</dd>
</dl>
</li>
</ul>
<a name="findByName-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>findByName</h4>
<pre class="methodSignature">@Nullable
java.lang.Object&nbsp;findByName&#8203;(java.lang.String&nbsp;name)</pre>
<div class="block">Looks for the extension of a given name. If none found null is returned.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>name</code> - extension name</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>extension or null</dd>
</dl>
</li>
</ul>
<a name="configure-java.lang.Class-org.gradle.api.Action-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>configure</h4>
<pre class="methodSignature">&lt;T&gt;&nbsp;void&nbsp;configure&#8203;(java.lang.Class&lt;T&gt;&nbsp;type,
                   <a href="../Action.html" title="interface in org.gradle.api">Action</a>&lt;? super T&gt;&nbsp;action)</pre>
<div class="block">Looks for the extension of the specified type and configures it with the supplied action.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>type</code> - extension type</dd>
<dd><code>action</code> - the configure action</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code><a href="../UnknownDomainObjectException.html" title="class in org.gradle.api">UnknownDomainObjectException</a></code> - if no extension is found.</dd>
</dl>
</li>
</ul>
<a name="configure-org.gradle.api.reflect.TypeOf-org.gradle.api.Action-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>configure</h4>
<pre class="methodSignature">&lt;T&gt;&nbsp;void&nbsp;configure&#8203;(<a href="../reflect/TypeOf.html" title="class in org.gradle.api.reflect">TypeOf</a>&lt;T&gt;&nbsp;type,
                   <a href="../Action.html" title="interface in org.gradle.api">Action</a>&lt;? super T&gt;&nbsp;action)</pre>
<div class="block">Looks for the extension of the specified type and configures it with the supplied action.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>type</code> - extension type</dd>
<dd><code>action</code> - the configure action</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code><a href="../UnknownDomainObjectException.html" title="class in org.gradle.api">UnknownDomainObjectException</a></code> - if no extension is found.</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>3.5</dd>
</dl>
</li>
</ul>
<a name="configure-java.lang.String-org.gradle.api.Action-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>configure</h4>
<pre class="methodSignature">&lt;T&gt;&nbsp;void&nbsp;configure&#8203;(java.lang.String&nbsp;name,
                   <a href="../Action.html" title="interface in org.gradle.api">Action</a>&lt;? super T&gt;&nbsp;action)</pre>
<div class="block">Looks for the extension with the specified name and configures it with the supplied action.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>name</code> - extension name</dd>
<dd><code>action</code> - the configure action</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code><a href="../UnknownDomainObjectException.html" title="class in org.gradle.api">UnknownDomainObjectException</a></code> - if no extension is found.</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>4.0</dd>
</dl>
</li>
</ul>
<a name="getExtraProperties--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>getExtraProperties</h4>
<pre class="methodSignature"><a href="ExtraPropertiesExtension.html" title="interface in org.gradle.api.plugins">ExtraPropertiesExtension</a>&nbsp;getExtraProperties()</pre>
<div class="block">The extra properties extension in this extension container.

 This extension is always present in the container, with the name “ext”.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The extra properties extension in this extension container.</dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
