<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc -->
<title>HasConfigurableValue (Gradle API 8.4)</title>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<link rel="stylesheet" type="text/css" href="../../../../javadoc.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../jquery/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
<script type="text/javascript" src="../../../../jquery/jszip/dist/jszip.min.js"></script>
<script type="text/javascript" src="../../../../jquery/jszip-utils/dist/jszip-utils.min.js"></script>
<!--[if IE]>
<script type="text/javascript" src="../../../../jquery/jszip-utils/dist/jszip-utils-ie.min.js"></script>
<![endif]-->
<script type="text/javascript" src="../../../../jquery/jquery-3.6.0.min.js"></script>
<script type="text/javascript" src="../../../../jquery/jquery-ui.min.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="HasConfigurableValue (Gradle API 8.4)";
        }
    }
    catch(err) {
    }
//-->
var data = {"i0":6,"i1":6,"i2":6,"i3":6};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],4:["t3","Abstract Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
var pathtoroot = "../../../../";
var useModuleDirectories = false;
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="fixedNav">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses.html">All&nbsp;Classes</a></li>
</ul>
<ul class="navListSearch">
<li><label for="search">SEARCH:</label>
<input type="text" id="search" value="search" disabled="disabled">
<input type="reset" id="reset" value="reset" disabled="disabled">
</li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
</div>
<div class="navPadding">&nbsp;</div>
<script type="text/javascript"><!--
$('.navPadding').css('padding-top', $('.fixedNav').css("height"));
//-->
</script>
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle"><span class="packageLabelInType">Package</span>&nbsp;<a href="package-summary.html">org.gradle.api.provider</a></div>
<h2 title="Interface HasConfigurableValue" class="title">Interface HasConfigurableValue</h2>
</div>
<div class="contentContainer">
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Known Subinterfaces:</dt>
<dd><code><a href="../file/ConfigurableFileCollection.html" title="interface in org.gradle.api.file">ConfigurableFileCollection</a></code>, <code><a href="../file/DirectoryProperty.html" title="interface in org.gradle.api.file">DirectoryProperty</a></code>, <code><a href="../file/FileSystemLocationProperty.html" title="interface in org.gradle.api.file">FileSystemLocationProperty</a>&lt;T&gt;</code>, <code><a href="HasMultipleValues.html" title="interface in org.gradle.api.provider">HasMultipleValues</a>&lt;T&gt;</code>, <code><a href="ListProperty.html" title="interface in org.gradle.api.provider">ListProperty</a>&lt;T&gt;</code>, <code><a href="MapProperty.html" title="interface in org.gradle.api.provider">MapProperty</a>&lt;K,&#8203;V&gt;</code>, <code><a href="Property.html" title="interface in org.gradle.api.provider">Property</a>&lt;T&gt;</code>, <code><a href="../file/RegularFileProperty.html" title="interface in org.gradle.api.file">RegularFileProperty</a></code>, <code><a href="SetProperty.html" title="interface in org.gradle.api.provider">SetProperty</a>&lt;T&gt;</code></dd>
</dl>
<hr>
<pre>public interface <span class="typeNameLabel">HasConfigurableValue</span></pre>
<div class="block">Represents an object that holds a value that is configurable, meaning that the value or some source for the value, such as a <a href="Provider.html" title="interface in org.gradle.api.provider"><code>Provider</code></a>,
 can be specified directly on the object.

 <p>This interface provides methods to manage the lifecycle of the value. Using these methods, the value of the object can be <em>finalized</em>, which means that the value will
 no longer change. Note that this is not the same as an <em>immutable</em> value. You can think of a finalized value as similar to a <code>final</code> variable in Java,
 so while the value of the variable does not change, the value itself may still be mutable.</p>

 <p>When a task property has a value of this type, it will be implicitly finalized when the task starts execution, to prevent accidental changes to the task parameters as the task runs.</p>

 <p><b>Note:</b> This interface is not intended for implementation by build script or plugin authors.</p></div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>5.6</dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t3" class="tableTab"><span><a href="javascript:show(4);">Abstract Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colSecond" scope="col">Method</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#disallowChanges--">disallowChanges</a></span>()</code></th>
<td class="colLast">
<div class="block">Disallows further direct changes to this object.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#disallowUnsafeRead--">disallowUnsafeRead</a></span>()</code></th>
<td class="colLast">
<div class="block">Disallows reading the value of this object when its value may not yet be available or may still change.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#finalizeValue--">finalizeValue</a></span>()</code></th>
<td class="colLast">
<div class="block">Calculates the final value of this object and disallows further changes to this object.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#finalizeValueOnRead--">finalizeValueOnRead</a></span>()</code></th>
<td class="colLast">
<div class="block">Requests that the final value of this object be calculated on the next read of the value, if not already known.</div>
</td>
</tr>
</table>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="finalizeValue--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>finalizeValue</h4>
<pre class="methodSignature">void&nbsp;finalizeValue()</pre>
<div class="block">Calculates the final value of this object and disallows further changes to this object.

 <p>To calculate the final value of this object any source for the value is queried and the result used as the final value for this object. The source is discarded
 so that this object no longer tracks the value of the source.</p>

 <p>Subsequent attempts to change the value of this object or to replace the source from which the value is derived will fail with an exception.

 <p>Note that although the value of this object will no longer change, the value may itself be mutable.
 Calling this method does not guarantee that the value will become immutable, though some implementations may support this.</p>

 <p>If the value of this object is already final, this method does nothing.</p></div>
</li>
</ul>
<a name="finalizeValueOnRead--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>finalizeValueOnRead</h4>
<pre class="methodSignature">void&nbsp;finalizeValueOnRead()</pre>
<div class="block">Requests that the final value of this object be calculated on the next read of the value, if not already known.

 <p>Changes to the value of this object or to the source for the value are still permitted until the final value is calculated, after which time attempts to make changes will fail with an exception.</p>

 <p>You can use this method along with <a href="#disallowChanges--"><code>disallowChanges()</code></a> to indicate that the value has been configured and that the final value is ready to calculate,
 without actually calculating the final value until it is required. This can be a useful alternative to <a href="#finalizeValue--"><code>finalizeValue()</code></a> for values that are expensive to calculate.
 </p>

 <p>If the value of this object is already final, this method does nothing.</p></div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>6.1</dd>
</dl>
</li>
</ul>
<a name="disallowChanges--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>disallowChanges</h4>
<pre class="methodSignature">void&nbsp;disallowChanges()</pre>
<div class="block">Disallows further direct changes to this object.

 <p>This differs from <a href="#finalizeValue--"><code>finalizeValue()</code></a> in that it does not calculate the final value of this object, and so any source for the value will continue to be used until
 the value is finalized.</p>

 <p>If the value of this object is already final, this method does nothing.</p></div>
</li>
</ul>
<a name="disallowUnsafeRead--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>disallowUnsafeRead</h4>
<pre class="methodSignature">void&nbsp;disallowUnsafeRead()</pre>
<div class="block">Disallows reading the value of this object when its value may not yet be available or may still change.

 <p>The value of this property cannot be read during project configuration, to allow all plugins an opportunity to configure the value. After a project's configuration has completed,
 the value may be read. The property is also finalized on read.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>6.4</dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
