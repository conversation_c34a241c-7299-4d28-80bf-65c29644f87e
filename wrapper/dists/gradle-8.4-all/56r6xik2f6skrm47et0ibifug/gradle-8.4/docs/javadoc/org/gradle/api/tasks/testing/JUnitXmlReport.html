<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc -->
<title>JUnitXmlReport (Gradle API 8.4)</title>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<link rel="stylesheet" type="text/css" href="../../../../../javadoc.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../jquery/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
<script type="text/javascript" src="../../../../../jquery/jszip/dist/jszip.min.js"></script>
<script type="text/javascript" src="../../../../../jquery/jszip-utils/dist/jszip-utils.min.js"></script>
<!--[if IE]>
<script type="text/javascript" src="../../../../../jquery/jszip-utils/dist/jszip-utils-ie.min.js"></script>
<![endif]-->
<script type="text/javascript" src="../../../../../jquery/jquery-3.6.0.min.js"></script>
<script type="text/javascript" src="../../../../../jquery/jquery-ui.min.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="JUnitXmlReport (Gradle API 8.4)";
        }
    }
    catch(err) {
    }
//-->
var data = {"i0":6,"i1":6,"i2":6};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],4:["t3","Abstract Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
var pathtoroot = "../../../../../";
var useModuleDirectories = false;
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="fixedNav">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../../allclasses.html">All&nbsp;Classes</a></li>
</ul>
<ul class="navListSearch">
<li><label for="search">SEARCH:</label>
<input type="text" id="search" value="search" disabled="disabled">
<input type="reset" id="reset" value="reset" disabled="disabled">
</li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li><a href="#nested.class.summary">Nested</a>&nbsp;|&nbsp;</li>
<li><a href="#field.summary">Field</a>&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
</div>
<div class="navPadding">&nbsp;</div>
<script type="text/javascript"><!--
$('.navPadding').css('padding-top', $('.fixedNav').css("height"));
//-->
</script>
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle"><span class="packageLabelInType">Package</span>&nbsp;<a href="package-summary.html">org.gradle.api.tasks.testing</a></div>
<h2 title="Interface JUnitXmlReport" class="title">Interface JUnitXmlReport</h2>
</div>
<div class="contentContainer">
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Superinterfaces:</dt>
<dd><code><a href="../../../util/Configurable.html" title="interface in org.gradle.util">Configurable</a>&lt;<a href="../../reporting/Report.html" title="interface in org.gradle.api.reporting">Report</a>&gt;</code>, <code><a href="../../reporting/ConfigurableReport.html" title="interface in org.gradle.api.reporting">ConfigurableReport</a></code>, <code><a href="../../reporting/DirectoryReport.html" title="interface in org.gradle.api.reporting">DirectoryReport</a></code>, <code><a href="../../reporting/Report.html" title="interface in org.gradle.api.reporting">Report</a></code></dd>
</dl>
<hr>
<pre>public interface <span class="typeNameLabel">JUnitXmlReport</span>
extends <a href="../../reporting/DirectoryReport.html" title="interface in org.gradle.api.reporting">DirectoryReport</a></pre>
<div class="block">The JUnit XML files, commonly used to communicate results to CI servers.</div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="TestTaskReports.html#getJunitXml--"><code>TestTaskReports.getJunitXml()</code></a></dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== NESTED CLASS SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="nested.class.summary">
<!--   -->
</a>
<h3>Nested Class Summary</h3>
<ul class="blockList">
<li class="blockList"><a name="nested.classes.inherited.from.class.org.gradle.api.reporting.Report">
<!--   -->
</a>
<h3>Nested classes/interfaces inherited from interface&nbsp;org.gradle.api.reporting.<a href="../../reporting/Report.html" title="interface in org.gradle.api.reporting">Report</a></h3>
<code><a href="../../reporting/Report.OutputType.html" title="enum in org.gradle.api.reporting">Report.OutputType</a></code></li>
</ul>
</li>
</ul>
<!-- =========== FIELD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.summary">
<!--   -->
</a>
<h3>Field Summary</h3>
<ul class="blockList">
<li class="blockList"><a name="fields.inherited.from.class.org.gradle.api.reporting.Report">
<!--   -->
</a>
<h3>Fields inherited from interface&nbsp;org.gradle.api.reporting.<a href="../../reporting/Report.html" title="interface in org.gradle.api.reporting">Report</a></h3>
<code><a href="../../reporting/Report.html#NAMER">NAMER</a></code></li>
</ul>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t3" class="tableTab"><span><a href="javascript:show(4);">Abstract Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colSecond" scope="col">Method</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code><a href="../../provider/Property.html" title="interface in org.gradle.api.provider">Property</a>&lt;java.lang.Boolean&gt;</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getMergeReruns--">getMergeReruns</a></span>()</code></th>
<td class="colLast">
<div class="block">Whether reruns or retries of a test should be merged into a combined testcase.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#isOutputPerTestCase--">isOutputPerTestCase</a></span>()</code></th>
<td class="colLast">
<div class="block">Should the output be associated with individual test cases instead of at the suite level.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setOutputPerTestCase-boolean-">setOutputPerTestCase</a></span>&#8203;(boolean&nbsp;outputPerTestCase)</code></th>
<td class="colLast">
<div class="block">Should the output be associated with individual test cases instead of at the suite level.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.org.gradle.util.Configurable">
<!--   -->
</a>
<h3>Methods inherited from interface&nbsp;org.gradle.util.<a href="../../../util/Configurable.html" title="interface in org.gradle.util">Configurable</a></h3>
<code><a href="../../../util/Configurable.html#configure-groovy.lang.Closure-">configure</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.org.gradle.api.reporting.ConfigurableReport">
<!--   -->
</a>
<h3>Methods inherited from interface&nbsp;org.gradle.api.reporting.<a href="../../reporting/ConfigurableReport.html" title="interface in org.gradle.api.reporting">ConfigurableReport</a></h3>
<code><a href="../../reporting/ConfigurableReport.html#setDestination-java.io.File-">setDestination</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.org.gradle.api.reporting.DirectoryReport">
<!--   -->
</a>
<h3>Methods inherited from interface&nbsp;org.gradle.api.reporting.<a href="../../reporting/DirectoryReport.html" title="interface in org.gradle.api.reporting">DirectoryReport</a></h3>
<code><a href="../../reporting/DirectoryReport.html#getEntryPoint--">getEntryPoint</a>, <a href="../../reporting/DirectoryReport.html#getOutputLocation--">getOutputLocation</a>, <a href="../../reporting/DirectoryReport.html#getOutputType--">getOutputType</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.org.gradle.api.reporting.Report">
<!--   -->
</a>
<h3>Methods inherited from interface&nbsp;org.gradle.api.reporting.<a href="../../reporting/Report.html" title="interface in org.gradle.api.reporting">Report</a></h3>
<code><a href="../../reporting/Report.html#getDisplayName--">getDisplayName</a>, <a href="../../reporting/Report.html#getName--">getName</a>, <a href="../../reporting/Report.html#getRequired--">getRequired</a></code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="isOutputPerTestCase--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isOutputPerTestCase</h4>
<pre class="methodSignature"><a href="../Input.html" title="annotation in org.gradle.api.tasks">@Input</a>
boolean&nbsp;isOutputPerTestCase()</pre>
<div class="block">Should the output be associated with individual test cases instead of at the suite level.</div>
</li>
</ul>
<a name="setOutputPerTestCase-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setOutputPerTestCase</h4>
<pre class="methodSignature">void&nbsp;setOutputPerTestCase&#8203;(boolean&nbsp;outputPerTestCase)</pre>
<div class="block">Should the output be associated with individual test cases instead of at the suite level.</div>
</li>
</ul>
<a name="getMergeReruns--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>getMergeReruns</h4>
<pre class="methodSignature"><a href="../Input.html" title="annotation in org.gradle.api.tasks">@Input</a>
<a href="../../provider/Property.html" title="interface in org.gradle.api.provider">Property</a>&lt;java.lang.Boolean&gt;&nbsp;getMergeReruns()</pre>
<div class="block">Whether reruns or retries of a test should be merged into a combined testcase.

 When enabled, the XML output will be very similar to the surefire plugin of Apache Maven™ when enabling reruns.
 If a test fails but is then retried and succeeds, its failures will be recorded as <code>&lt;flakyFailure&gt;</code>
 instead of <code>&lt;failure&gt;</code>, within one <code>&lt;testcase&gt;</code>.
 This can be important for build tooling that uses this XML to understand test results,
 and where distinguishing such passed-on-retry outcomes is important.
 This is the case for the Jenkins CI server and its Flaky Test Handler plugin.

 This value defaults to <code>false</code>, causing each test execution to be a discrete <code>&lt;testcase&gt;</code>.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>6.8</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="https://maven.apache.org/components/surefire/maven-surefire-plugin/examples/rerun-failing-tests.html">https://maven.apache.org/components/surefire/maven-surefire-plugin/examples/rerun-failing-tests.html</a>, 
<a href="https://plugins.jenkins.io/flaky-test-handler">https://plugins.jenkins.io/flaky-test-handler</a></dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../../allclasses.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li><a href="#nested.class.summary">Nested</a>&nbsp;|&nbsp;</li>
<li><a href="#field.summary">Field</a>&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
