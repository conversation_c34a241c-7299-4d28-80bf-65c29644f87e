<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc -->
<title>CopyProcessingSpec (Gradle API 8.4)</title>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<link rel="stylesheet" type="text/css" href="../../../../javadoc.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../jquery/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
<script type="text/javascript" src="../../../../jquery/jszip/dist/jszip.min.js"></script>
<script type="text/javascript" src="../../../../jquery/jszip-utils/dist/jszip-utils.min.js"></script>
<!--[if IE]>
<script type="text/javascript" src="../../../../jquery/jszip-utils/dist/jszip-utils-ie.min.js"></script>
<![endif]-->
<script type="text/javascript" src="../../../../jquery/jquery-3.6.0.min.js"></script>
<script type="text/javascript" src="../../../../jquery/jquery-ui.min.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="CopyProcessingSpec (Gradle API 8.4)";
        }
    }
    catch(err) {
    }
//-->
var data = {"i0":6,"i1":6,"i2":6,"i3":6,"i4":6,"i5":6,"i6":6,"i7":6,"i8":6,"i9":6,"i10":6,"i11":6,"i12":6,"i13":6,"i14":6};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],4:["t3","Abstract Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
var pathtoroot = "../../../../";
var useModuleDirectories = false;
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="fixedNav">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses.html">All&nbsp;Classes</a></li>
</ul>
<ul class="navListSearch">
<li><label for="search">SEARCH:</label>
<input type="text" id="search" value="search" disabled="disabled">
<input type="reset" id="reset" value="reset" disabled="disabled">
</li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
</div>
<div class="navPadding">&nbsp;</div>
<script type="text/javascript"><!--
$('.navPadding').css('padding-top', $('.fixedNav').css("height"));
//-->
</script>
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle"><span class="packageLabelInType">Package</span>&nbsp;<a href="package-summary.html">org.gradle.api.file</a></div>
<h2 title="Interface CopyProcessingSpec" class="title">Interface CopyProcessingSpec</h2>
</div>
<div class="contentContainer">
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Superinterfaces:</dt>
<dd><code><a href="ContentFilterable.html" title="interface in org.gradle.api.file">ContentFilterable</a></code></dd>
</dl>
<dl>
<dt>All Known Subinterfaces:</dt>
<dd><code><a href="CopySpec.html" title="interface in org.gradle.api.file">CopySpec</a></code>, <code><a href="SyncSpec.html" title="interface in org.gradle.api.file">SyncSpec</a></code></dd>
</dl>
<dl>
<dt>All Known Implementing Classes:</dt>
<dd><code><a href="../tasks/bundling/AbstractArchiveTask.html" title="class in org.gradle.api.tasks.bundling">AbstractArchiveTask</a></code>, <code><a href="../tasks/AbstractCopyTask.html" title="class in org.gradle.api.tasks">AbstractCopyTask</a></code>, <code><a href="../tasks/Copy.html" title="class in org.gradle.api.tasks">Copy</a></code>, <code><a href="../../plugins/ear/Ear.html" title="class in org.gradle.plugins.ear">Ear</a></code>, <code><a href="../tasks/bundling/Jar.html" title="class in org.gradle.api.tasks.bundling">Jar</a></code>, <code><a href="../../jvm/tasks/Jar.html" title="class in org.gradle.jvm.tasks">Jar</a></code>, <code><a href="../../language/jvm/tasks/ProcessResources.html" title="class in org.gradle.language.jvm.tasks">ProcessResources</a></code>, <code><a href="../tasks/Sync.html" title="class in org.gradle.api.tasks">Sync</a></code>, <code><a href="../tasks/bundling/Tar.html" title="class in org.gradle.api.tasks.bundling">Tar</a></code>, <code><a href="../tasks/bundling/War.html" title="class in org.gradle.api.tasks.bundling">War</a></code>, <code><a href="../tasks/bundling/Zip.html" title="class in org.gradle.api.tasks.bundling">Zip</a></code></dd>
</dl>
<hr>
<pre>public interface <span class="typeNameLabel">CopyProcessingSpec</span>
extends <a href="ContentFilterable.html" title="interface in org.gradle.api.file">ContentFilterable</a></pre>
<div class="block">Specifies the destination of a copy.</div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t3" class="tableTab"><span><a href="javascript:show(4);">Abstract Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colSecond" scope="col">Method</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code><a href="CopyProcessingSpec.html" title="interface in org.gradle.api.file">CopyProcessingSpec</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#dirPermissions-org.gradle.api.Action-">dirPermissions</a></span>&#8203;(<a href="../Action.html" title="interface in org.gradle.api">Action</a>&lt;? super <a href="ConfigurableFilePermissions.html" title="interface in org.gradle.api.file">ConfigurableFilePermissions</a>&gt;&nbsp;configureAction)</code></th>
<td class="colLast">
<div class="block">Configuration action for specifying directory access permissions.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code><a href="CopyProcessingSpec.html" title="interface in org.gradle.api.file">CopyProcessingSpec</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#eachFile-groovy.lang.Closure-">eachFile</a></span>&#8203;(<a href="https://docs.groovy-lang.org/docs/groovy-3.0.17/html/gapi/groovy/lang/Closure.html?is-external=true" title="class or interface in groovy.lang" class="externalLink">Closure</a>&nbsp;closure)</code></th>
<td class="colLast">
<div class="block">Adds an action to be applied to each file as it about to be copied into its destination.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code><a href="CopyProcessingSpec.html" title="interface in org.gradle.api.file">CopyProcessingSpec</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#eachFile-org.gradle.api.Action-">eachFile</a></span>&#8203;(<a href="../Action.html" title="interface in org.gradle.api">Action</a>&lt;? super <a href="FileCopyDetails.html" title="interface in org.gradle.api.file">FileCopyDetails</a>&gt;&nbsp;action)</code></th>
<td class="colLast">
<div class="block">Adds an action to be applied to each file as it is about to be copied into its destination.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code><a href="CopyProcessingSpec.html" title="interface in org.gradle.api.file">CopyProcessingSpec</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#filePermissions-org.gradle.api.Action-">filePermissions</a></span>&#8203;(<a href="../Action.html" title="interface in org.gradle.api">Action</a>&lt;? super <a href="ConfigurableFilePermissions.html" title="interface in org.gradle.api.file">ConfigurableFilePermissions</a>&gt;&nbsp;configureAction)</code></th>
<td class="colLast">
<div class="block">Configuration action for specifying file access permissions.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>java.lang.Integer</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getDirMode--">getDirMode</a></span>()</code></th>
<td class="colLast">
<div class="block">Returns the Unix permissions to use for the target directories.</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code><a href="../provider/Property.html" title="interface in org.gradle.api.provider">Property</a>&lt;<a href="ConfigurableFilePermissions.html" title="interface in org.gradle.api.file">ConfigurableFilePermissions</a>&gt;</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getDirPermissions--">getDirPermissions</a></span>()</code></th>
<td class="colLast">
<div class="block">Property for configuring directory access permissions.</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>java.lang.Integer</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getFileMode--">getFileMode</a></span>()</code></th>
<td class="colLast">
<div class="block">Returns the Unix permissions to use for the target files.</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code><a href="../provider/Property.html" title="interface in org.gradle.api.provider">Property</a>&lt;<a href="ConfigurableFilePermissions.html" title="interface in org.gradle.api.file">ConfigurableFilePermissions</a>&gt;</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getFilePermissions--">getFilePermissions</a></span>()</code></th>
<td class="colLast">
<div class="block">Property for configuring file access permissions.</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code><a href="CopyProcessingSpec.html" title="interface in org.gradle.api.file">CopyProcessingSpec</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#into-java.lang.Object-">into</a></span>&#8203;(java.lang.Object&nbsp;destPath)</code></th>
<td class="colLast">
<div class="block">Specifies the destination directory for a copy.</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code><a href="CopyProcessingSpec.html" title="interface in org.gradle.api.file">CopyProcessingSpec</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#rename-groovy.lang.Closure-">rename</a></span>&#8203;(<a href="https://docs.groovy-lang.org/docs/groovy-3.0.17/html/gapi/groovy/lang/Closure.html?is-external=true" title="class or interface in groovy.lang" class="externalLink">Closure</a>&nbsp;closure)</code></th>
<td class="colLast">
<div class="block">Renames a source file.</div>
</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code><a href="CopyProcessingSpec.html" title="interface in org.gradle.api.file">CopyProcessingSpec</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#rename-java.lang.String-java.lang.String-">rename</a></span>&#8203;(java.lang.String&nbsp;sourceRegEx,
      java.lang.String&nbsp;replaceWith)</code></th>
<td class="colLast">
<div class="block">Renames files based on a regular expression.</div>
</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code><a href="CopyProcessingSpec.html" title="interface in org.gradle.api.file">CopyProcessingSpec</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#rename-java.util.regex.Pattern-java.lang.String-">rename</a></span>&#8203;(java.util.regex.Pattern&nbsp;sourceRegEx,
      java.lang.String&nbsp;replaceWith)</code></th>
<td class="colLast">
<div class="block">Renames files based on a regular expression.</div>
</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code><a href="CopyProcessingSpec.html" title="interface in org.gradle.api.file">CopyProcessingSpec</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#rename-org.gradle.api.Transformer-">rename</a></span>&#8203;(<a href="../Transformer.html" title="interface in org.gradle.api">Transformer</a>&lt;@Nullable java.lang.String,&#8203;java.lang.String&gt;&nbsp;renamer)</code></th>
<td class="colLast">
<div class="block">Renames a source file.</div>
</td>
</tr>
<tr id="i13" class="rowColor">
<td class="colFirst"><code><a href="CopyProcessingSpec.html" title="interface in org.gradle.api.file">CopyProcessingSpec</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setDirMode-java.lang.Integer-">setDirMode</a></span>&#8203;(java.lang.Integer&nbsp;mode)</code></th>
<td class="colLast">
<div class="block">Sets the Unix permissions to use for the target directories.</div>
</td>
</tr>
<tr id="i14" class="altColor">
<td class="colFirst"><code><a href="CopyProcessingSpec.html" title="interface in org.gradle.api.file">CopyProcessingSpec</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setFileMode-java.lang.Integer-">setFileMode</a></span>&#8203;(java.lang.Integer&nbsp;mode)</code></th>
<td class="colLast">
<div class="block">Sets the Unix permissions to use for the target files.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.org.gradle.api.file.ContentFilterable">
<!--   -->
</a>
<h3>Methods inherited from interface&nbsp;org.gradle.api.file.<a href="ContentFilterable.html" title="interface in org.gradle.api.file">ContentFilterable</a></h3>
<code><a href="ContentFilterable.html#expand-java.util.Map-">expand</a>, <a href="ContentFilterable.html#expand-java.util.Map-org.gradle.api.Action-">expand</a>, <a href="ContentFilterable.html#filter-groovy.lang.Closure-">filter</a>, <a href="ContentFilterable.html#filter-java.lang.Class-">filter</a>, <a href="ContentFilterable.html#filter-java.util.Map-java.lang.Class-">filter</a>, <a href="ContentFilterable.html#filter-org.gradle.api.Transformer-">filter</a></code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="into-java.lang.Object-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>into</h4>
<pre class="methodSignature"><a href="CopyProcessingSpec.html" title="interface in org.gradle.api.file">CopyProcessingSpec</a>&nbsp;into&#8203;(java.lang.Object&nbsp;destPath)</pre>
<div class="block">Specifies the destination directory for a copy. The destination is evaluated as per <a href="../Project.html#file-java.lang.Object-"><code>Project.file(Object)</code></a>.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>destPath</code> - Path to the destination directory for a Copy</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>this</dd>
</dl>
</li>
</ul>
<a name="rename-groovy.lang.Closure-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>rename</h4>
<pre class="methodSignature"><a href="CopyProcessingSpec.html" title="interface in org.gradle.api.file">CopyProcessingSpec</a>&nbsp;rename&#8203;(<a href="https://docs.groovy-lang.org/docs/groovy-3.0.17/html/gapi/groovy/lang/Closure.html?is-external=true" title="class or interface in groovy.lang" class="externalLink">Closure</a>&nbsp;closure)</pre>
<div class="block">Renames a source file. The closure will be called with a single parameter, the name of the file.
 The closure should return a String object with a new target name. The closure may return null,
 in which case the original name will be used.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>closure</code> - rename closure</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>this</dd>
</dl>
</li>
</ul>
<a name="rename-org.gradle.api.Transformer-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>rename</h4>
<pre class="methodSignature"><a href="CopyProcessingSpec.html" title="interface in org.gradle.api.file">CopyProcessingSpec</a>&nbsp;rename&#8203;(<a href="../Transformer.html" title="interface in org.gradle.api">Transformer</a>&lt;@Nullable java.lang.String,&#8203;java.lang.String&gt;&nbsp;renamer)</pre>
<div class="block">Renames a source file. The function will be called with a single parameter, the name of the file.
 The function should return a new target name. The function may return null,
 in which case the original name will be used.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>renamer</code> - rename function</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>this</dd>
</dl>
</li>
</ul>
<a name="rename-java.lang.String-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>rename</h4>
<pre class="methodSignature"><a href="CopyProcessingSpec.html" title="interface in org.gradle.api.file">CopyProcessingSpec</a>&nbsp;rename&#8203;(java.lang.String&nbsp;sourceRegEx,
                          java.lang.String&nbsp;replaceWith)</pre>
<div class="block">Renames files based on a regular expression.  Uses java.util.regex type of regular expressions.  Note that the
 replace string should use the '$1' syntax to refer to capture groups in the source regular expression.  Files
 that do not match the source regular expression will be copied with the original name.

 <p> Example:
 <pre>
 rename '(.*)_OEM_BLUE_(.*)', '$1$2'
 </pre>
 would map the file 'style_OEM_BLUE_.css' to 'style.css'</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>sourceRegEx</code> - Source regular expression</dd>
<dd><code>replaceWith</code> - Replacement string (use $ syntax for capture groups)</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>this</dd>
</dl>
</li>
</ul>
<a name="rename-java.util.regex.Pattern-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>rename</h4>
<pre class="methodSignature"><a href="CopyProcessingSpec.html" title="interface in org.gradle.api.file">CopyProcessingSpec</a>&nbsp;rename&#8203;(java.util.regex.Pattern&nbsp;sourceRegEx,
                          java.lang.String&nbsp;replaceWith)</pre>
<div class="block">Renames files based on a regular expression. See <a href="#rename-java.lang.String-java.lang.String-"><code>rename(String, String)</code></a>.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>sourceRegEx</code> - Source regular expression</dd>
<dd><code>replaceWith</code> - Replacement string (use $ syntax for capture groups)</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>this</dd>
</dl>
</li>
</ul>
<a name="getFileMode--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getFileMode</h4>
<pre class="methodSignature">@Nullable
java.lang.Integer&nbsp;getFileMode()</pre>
<div class="block">Returns the Unix permissions to use for the target files. <code>null</code> means that existing
 permissions are preserved. It is dependent on the copy action implementation whether these permissions
 will actually be applied.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The file permissions, or <code>null</code> if existing permissions should be preserved.</dd>
<dt><span class="simpleTagLabel">API Note:</span></dt>
<dd>Consider using <a href="#getFilePermissions--"><code>getFilePermissions()</code></a> instead.</dd>
</dl>
</li>
</ul>
<a name="setFileMode-java.lang.Integer-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setFileMode</h4>
<pre class="methodSignature"><a href="CopyProcessingSpec.html" title="interface in org.gradle.api.file">CopyProcessingSpec</a>&nbsp;setFileMode&#8203;(@Nullable
                               java.lang.Integer&nbsp;mode)</pre>
<div class="block">Sets the Unix permissions to use for the target files. <code>null</code> means that existing
 permissions are preserved. It is dependent on the copy action implementation whether these permissions
 will actually be applied.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>mode</code> - The file permissions.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>this</dd>
<dt><span class="simpleTagLabel">API Note:</span></dt>
<dd>Consider using <a href="#filePermissions-org.gradle.api.Action-"><code>filePermissions(Action)</code></a> instead.</dd>
</dl>
</li>
</ul>
<a name="getDirMode--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getDirMode</h4>
<pre class="methodSignature">@Nullable
java.lang.Integer&nbsp;getDirMode()</pre>
<div class="block">Returns the Unix permissions to use for the target directories. <code>null</code> means that existing
 permissions are preserved. It is dependent on the copy action implementation whether these permissions
 will actually be applied.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The directory permissions, or <code>null</code> if existing permissions should be preserved.</dd>
<dt><span class="simpleTagLabel">API Note:</span></dt>
<dd>Consider using <a href="#getDirPermissions--"><code>getDirPermissions()</code></a> instead.</dd>
</dl>
</li>
</ul>
<a name="setDirMode-java.lang.Integer-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setDirMode</h4>
<pre class="methodSignature"><a href="CopyProcessingSpec.html" title="interface in org.gradle.api.file">CopyProcessingSpec</a>&nbsp;setDirMode&#8203;(@Nullable
                              java.lang.Integer&nbsp;mode)</pre>
<div class="block">Sets the Unix permissions to use for the target directories. <code>null</code> means that existing
 permissions are preserved. It is dependent on the copy action implementation whether these permissions
 will actually be applied.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>mode</code> - The directory permissions.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>this</dd>
<dt><span class="simpleTagLabel">API Note:</span></dt>
<dd>Consider using <a href="#dirPermissions-org.gradle.api.Action-"><code>dirPermissions(Action)</code></a> instead.</dd>
</dl>
</li>
</ul>
<a name="getFilePermissions--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getFilePermissions</h4>
<pre class="methodSignature"><a href="../Incubating.html" title="annotation in org.gradle.api">@Incubating</a>
<a href="../provider/Property.html" title="interface in org.gradle.api.provider">Property</a>&lt;<a href="ConfigurableFilePermissions.html" title="interface in org.gradle.api.file">ConfigurableFilePermissions</a>&gt;&nbsp;getFilePermissions()</pre>
<div class="block">Property for configuring file access permissions.
 For details see <a href="ConfigurableFilePermissions.html" title="interface in org.gradle.api.file"><code>ConfigurableFilePermissions</code></a>.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>8.3</dd>
</dl>
</li>
</ul>
<a name="filePermissions-org.gradle.api.Action-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>filePermissions</h4>
<pre class="methodSignature"><a href="../Incubating.html" title="annotation in org.gradle.api">@Incubating</a>
<a href="CopyProcessingSpec.html" title="interface in org.gradle.api.file">CopyProcessingSpec</a>&nbsp;filePermissions&#8203;(<a href="../Action.html" title="interface in org.gradle.api">Action</a>&lt;? super <a href="ConfigurableFilePermissions.html" title="interface in org.gradle.api.file">ConfigurableFilePermissions</a>&gt;&nbsp;configureAction)</pre>
<div class="block">Configuration action for specifying file access permissions.
 For details see <a href="ConfigurableFilePermissions.html" title="interface in org.gradle.api.file"><code>ConfigurableFilePermissions</code></a>.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>8.3</dd>
</dl>
</li>
</ul>
<a name="getDirPermissions--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getDirPermissions</h4>
<pre class="methodSignature"><a href="../Incubating.html" title="annotation in org.gradle.api">@Incubating</a>
<a href="../provider/Property.html" title="interface in org.gradle.api.provider">Property</a>&lt;<a href="ConfigurableFilePermissions.html" title="interface in org.gradle.api.file">ConfigurableFilePermissions</a>&gt;&nbsp;getDirPermissions()</pre>
<div class="block">Property for configuring directory access permissions.
 For details see <a href="ConfigurableFilePermissions.html" title="interface in org.gradle.api.file"><code>ConfigurableFilePermissions</code></a>.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>8.3</dd>
</dl>
</li>
</ul>
<a name="dirPermissions-org.gradle.api.Action-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>dirPermissions</h4>
<pre class="methodSignature"><a href="../Incubating.html" title="annotation in org.gradle.api">@Incubating</a>
<a href="CopyProcessingSpec.html" title="interface in org.gradle.api.file">CopyProcessingSpec</a>&nbsp;dirPermissions&#8203;(<a href="../Action.html" title="interface in org.gradle.api">Action</a>&lt;? super <a href="ConfigurableFilePermissions.html" title="interface in org.gradle.api.file">ConfigurableFilePermissions</a>&gt;&nbsp;configureAction)</pre>
<div class="block">Configuration action for specifying directory access permissions.
 For details see <a href="ConfigurableFilePermissions.html" title="interface in org.gradle.api.file"><code>ConfigurableFilePermissions</code></a>.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>8.3</dd>
</dl>
</li>
</ul>
<a name="eachFile-org.gradle.api.Action-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>eachFile</h4>
<pre class="methodSignature"><a href="CopyProcessingSpec.html" title="interface in org.gradle.api.file">CopyProcessingSpec</a>&nbsp;eachFile&#8203;(<a href="../Action.html" title="interface in org.gradle.api">Action</a>&lt;? super <a href="FileCopyDetails.html" title="interface in org.gradle.api.file">FileCopyDetails</a>&gt;&nbsp;action)</pre>
<div class="block">Adds an action to be applied to each file as it is about to be copied into its destination. The action can change
 the destination path of the file, filter the contents of the file, or exclude the file from the result entirely.
 Actions are executed in the order added, and are inherited from the parent spec.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>action</code> - The action to execute.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>this</dd>
</dl>
</li>
</ul>
<a name="eachFile-groovy.lang.Closure-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>eachFile</h4>
<pre class="methodSignature"><a href="CopyProcessingSpec.html" title="interface in org.gradle.api.file">CopyProcessingSpec</a>&nbsp;eachFile&#8203;(<a href="https://docs.groovy-lang.org/docs/groovy-3.0.17/html/gapi/groovy/lang/DelegatesTo.html?is-external=true" title="class or interface in groovy.lang" class="externalLink">@DelegatesTo</a>(<a href="https://docs.groovy-lang.org/docs/groovy-3.0.17/html/gapi/groovy/lang/DelegatesTo.html?is-external=true#value--" title="class or interface in groovy.lang" class="externalLink">value</a>=<a href="FileCopyDetails.html" title="interface in org.gradle.api.file">FileCopyDetails.class</a>,<a href="https://docs.groovy-lang.org/docs/groovy-3.0.17/html/gapi/groovy/lang/DelegatesTo.html?is-external=true#strategy--" title="class or interface in groovy.lang" class="externalLink">strategy</a>=1)
                            <a href="https://docs.groovy-lang.org/docs/groovy-3.0.17/html/gapi/groovy/lang/Closure.html?is-external=true" title="class or interface in groovy.lang" class="externalLink">Closure</a>&nbsp;closure)</pre>
<div class="block">Adds an action to be applied to each file as it about to be copied into its destination. The given closure is
 called with a <a href="FileCopyDetails.html" title="interface in org.gradle.api.file"><code>FileCopyDetails</code></a> as its parameter. Actions are executed in the order
 added, and are inherited from the parent spec.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>closure</code> - The action to execute.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>this</dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
