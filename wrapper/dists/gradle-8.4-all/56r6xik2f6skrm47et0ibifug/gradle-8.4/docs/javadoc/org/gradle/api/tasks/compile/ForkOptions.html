<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc -->
<title>ForkOptions (Gradle API 8.4)</title>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<link rel="stylesheet" type="text/css" href="../../../../../javadoc.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../jquery/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
<script type="text/javascript" src="../../../../../jquery/jszip/dist/jszip.min.js"></script>
<script type="text/javascript" src="../../../../../jquery/jszip-utils/dist/jszip-utils.min.js"></script>
<!--[if IE]>
<script type="text/javascript" src="../../../../../jquery/jszip-utils/dist/jszip-utils-ie.min.js"></script>
<![endif]-->
<script type="text/javascript" src="../../../../../jquery/jquery-3.6.0.min.js"></script>
<script type="text/javascript" src="../../../../../jquery/jquery-ui.min.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="ForkOptions (Gradle API 8.4)";
        }
    }
    catch(err) {
    }
//-->
var data = {"i0":10,"i1":10,"i2":10,"i3":10,"i4":10,"i5":10};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
var pathtoroot = "../../../../../";
var useModuleDirectories = false;
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="fixedNav">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../../allclasses.html">All&nbsp;Classes</a></li>
</ul>
<ul class="navListSearch">
<li><label for="search">SEARCH:</label>
<input type="text" id="search" value="search" disabled="disabled">
<input type="reset" id="reset" value="reset" disabled="disabled">
</li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
</div>
<div class="navPadding">&nbsp;</div>
<script type="text/javascript"><!--
$('.navPadding').css('padding-top', $('.fixedNav').css("height"));
//-->
</script>
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle"><span class="packageLabelInType">Package</span>&nbsp;<a href="package-summary.html">org.gradle.api.tasks.compile</a></div>
<h2 title="Class ForkOptions" class="title">Class ForkOptions</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li><a href="AbstractOptions.html" title="class in org.gradle.api.tasks.compile">org.gradle.api.tasks.compile.AbstractOptions</a></li>
<li>
<ul class="inheritance">
<li><a href="BaseForkOptions.html" title="class in org.gradle.api.tasks.compile">org.gradle.api.tasks.compile.BaseForkOptions</a></li>
<li>
<ul class="inheritance">
<li><a href="ProviderAwareCompilerDaemonForkOptions.html" title="class in org.gradle.api.tasks.compile">org.gradle.api.tasks.compile.ProviderAwareCompilerDaemonForkOptions</a></li>
<li>
<ul class="inheritance">
<li>org.gradle.api.tasks.compile.ForkOptions</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Implemented Interfaces:</dt>
<dd><code>java.io.Serializable</code></dd>
</dl>
<hr>
<pre>public abstract class <span class="typeNameLabel">ForkOptions</span>
extends <a href="ProviderAwareCompilerDaemonForkOptions.html" title="class in org.gradle.api.tasks.compile">ProviderAwareCompilerDaemonForkOptions</a></pre>
<div class="block">Fork options for Java compilation. Only take effect if <code>CompileOptions.fork</code> is <code>true</code>.</div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../../../serialized-form.html#org.gradle.api.tasks.compile.ForkOptions">Serialized Form</a></dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="memberSummary" summary="Constructor Summary table, listing constructors, and an explanation">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Constructor</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tr class="altColor">
<th class="colConstructorName" scope="row"><code><span class="memberNameLink"><a href="#ForkOptions--">ForkOptions</a></span>()</code></th>
<td class="colLast">&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colSecond" scope="col">Method</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getExecutable--">getExecutable</a></span>()</code></th>
<td class="colLast">
<div class="block">Returns the compiler executable to be used.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>java.io.File</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getJavaHome--">getJavaHome</a></span>()</code></th>
<td class="colLast">
<div class="block">Returns the Java home which contains the compiler to use.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getTempDir--">getTempDir</a></span>()</code></th>
<td class="colLast">
<div class="block">Returns the directory used for temporary files that may be created to pass
 command line arguments to the compiler process.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setExecutable-java.lang.String-">setExecutable</a></span>&#8203;(java.lang.String&nbsp;executable)</code></th>
<td class="colLast">
<div class="block">Sets the compiler executable to be used.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setJavaHome-java.io.File-">setJavaHome</a></span>&#8203;(java.io.File&nbsp;javaHome)</code></th>
<td class="colLast">
<div class="block">Sets the Java home which contains the compiler to use.</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setTempDir-java.lang.String-">setTempDir</a></span>&#8203;(java.lang.String&nbsp;tempDir)</code></th>
<td class="colLast">
<div class="block">Sets the directory used for temporary files that may be created to pass
 command line arguments to the compiler process.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.org.gradle.api.tasks.compile.ProviderAwareCompilerDaemonForkOptions">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;org.gradle.api.tasks.compile.<a href="ProviderAwareCompilerDaemonForkOptions.html" title="class in org.gradle.api.tasks.compile">ProviderAwareCompilerDaemonForkOptions</a></h3>
<code><a href="ProviderAwareCompilerDaemonForkOptions.html#getAllJvmArgs--">getAllJvmArgs</a>, <a href="ProviderAwareCompilerDaemonForkOptions.html#getJvmArgumentProviders--">getJvmArgumentProviders</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.org.gradle.api.tasks.compile.BaseForkOptions">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;org.gradle.api.tasks.compile.<a href="BaseForkOptions.html" title="class in org.gradle.api.tasks.compile">BaseForkOptions</a></h3>
<code><a href="BaseForkOptions.html#getJvmArgs--">getJvmArgs</a>, <a href="BaseForkOptions.html#getMemoryInitialSize--">getMemoryInitialSize</a>, <a href="BaseForkOptions.html#getMemoryMaximumSize--">getMemoryMaximumSize</a>, <a href="BaseForkOptions.html#setJvmArgs-java.util.List-">setJvmArgs</a>, <a href="BaseForkOptions.html#setMemoryInitialSize-java.lang.String-">setMemoryInitialSize</a>, <a href="BaseForkOptions.html#setMemoryMaximumSize-java.lang.String-">setMemoryMaximumSize</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.org.gradle.api.tasks.compile.AbstractOptions">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;org.gradle.api.tasks.compile.<a href="AbstractOptions.html" title="class in org.gradle.api.tasks.compile">AbstractOptions</a></h3>
<code><a href="AbstractOptions.html#define-java.util.Map-">define</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>clone, equals, finalize, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a name="ForkOptions--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>ForkOptions</h4>
<pre>public&nbsp;ForkOptions()</pre>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="getExecutable--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getExecutable</h4>
<pre class="methodSignature">@Nullable
<a href="../Optional.html" title="annotation in org.gradle.api.tasks">@Optional</a>
<a href="../Input.html" title="annotation in org.gradle.api.tasks">@Input</a>
public&nbsp;java.lang.String&nbsp;getExecutable()</pre>
<div class="block">Returns the compiler executable to be used.
 <p>
 Only takes effect if <code>CompileOptions.fork</code> is <code>true</code>. Defaults to <code>null</code>.
 <p>
 Setting the executable disables task output caching.</div>
</li>
</ul>
<a name="setExecutable-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setExecutable</h4>
<pre class="methodSignature">public&nbsp;void&nbsp;setExecutable&#8203;(@Nullable
                          java.lang.String&nbsp;executable)</pre>
<div class="block">Sets the compiler executable to be used.
 <p>
 Only takes effect if <code>CompileOptions.fork</code> is <code>true</code>. Defaults to <code>null</code>.
 <p>
 Setting the executable disables task output caching.</div>
</li>
</ul>
<a name="getJavaHome--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getJavaHome</h4>
<pre class="methodSignature"><a href="../Internal.html" title="annotation in org.gradle.api.tasks">@Internal</a>
@Nullable
public&nbsp;java.io.File&nbsp;getJavaHome()</pre>
<div class="block">Returns the Java home which contains the compiler to use.
 <p>
 Only takes effect if <code>CompileOptions.fork</code> is <code>true</code>. Defaults to <code>null</code>.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>3.5</dd>
</dl>
</li>
</ul>
<a name="setJavaHome-java.io.File-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setJavaHome</h4>
<pre class="methodSignature">public&nbsp;void&nbsp;setJavaHome&#8203;(@Nullable
                        java.io.File&nbsp;javaHome)</pre>
<div class="block">Sets the Java home which contains the compiler to use.
 <p>
 Only takes effect if <code>CompileOptions.fork</code> is <code>true</code>. Defaults to <code>null</code>.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>3.5</dd>
</dl>
</li>
</ul>
<a name="getTempDir--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getTempDir</h4>
<pre class="methodSignature"><a href="../Internal.html" title="annotation in org.gradle.api.tasks">@Internal</a>
@Nullable
public&nbsp;java.lang.String&nbsp;getTempDir()</pre>
<div class="block">Returns the directory used for temporary files that may be created to pass
 command line arguments to the compiler process. Defaults to <code>null</code>,
 in which case the directory will be chosen automatically.</div>
</li>
</ul>
<a name="setTempDir-java.lang.String-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>setTempDir</h4>
<pre class="methodSignature">public&nbsp;void&nbsp;setTempDir&#8203;(@Nullable
                       java.lang.String&nbsp;tempDir)</pre>
<div class="block">Sets the directory used for temporary files that may be created to pass
 command line arguments to the compiler process. Defaults to <code>null</code>,
 in which case the directory will be chosen automatically.</div>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../../allclasses.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
