<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc -->
<title>PluginManager (Gradle API 8.4)</title>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<link rel="stylesheet" type="text/css" href="../../../../javadoc.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../jquery/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
<script type="text/javascript" src="../../../../jquery/jszip/dist/jszip.min.js"></script>
<script type="text/javascript" src="../../../../jquery/jszip-utils/dist/jszip-utils.min.js"></script>
<!--[if IE]>
<script type="text/javascript" src="../../../../jquery/jszip-utils/dist/jszip-utils-ie.min.js"></script>
<![endif]-->
<script type="text/javascript" src="../../../../jquery/jquery-3.6.0.min.js"></script>
<script type="text/javascript" src="../../../../jquery/jquery-ui.min.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="PluginManager (Gradle API 8.4)";
        }
    }
    catch(err) {
    }
//-->
var data = {"i0":6,"i1":6,"i2":6,"i3":6,"i4":6};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],4:["t3","Abstract Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
var pathtoroot = "../../../../";
var useModuleDirectories = false;
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="fixedNav">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses.html">All&nbsp;Classes</a></li>
</ul>
<ul class="navListSearch">
<li><label for="search">SEARCH:</label>
<input type="text" id="search" value="search" disabled="disabled">
<input type="reset" id="reset" value="reset" disabled="disabled">
</li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
</div>
<div class="navPadding">&nbsp;</div>
<script type="text/javascript"><!--
$('.navPadding').css('padding-top', $('.fixedNav').css("height"));
//-->
</script>
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle"><span class="packageLabelInType">Package</span>&nbsp;<a href="package-summary.html">org.gradle.api.plugins</a></div>
<h2 title="Interface PluginManager" class="title">Interface PluginManager</h2>
</div>
<div class="contentContainer">
<div class="description">
<ul class="blockList">
<li class="blockList">
<hr>
<pre><a href="../NonExtensible.html" title="annotation in org.gradle.api">@NonExtensible</a>
public interface <span class="typeNameLabel">PluginManager</span></pre>
<div class="block">Facilitates applying plugins and determining which plugins have been applied to a <a href="PluginAware.html" title="interface in org.gradle.api.plugins"><code>PluginAware</code></a> object.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>2.3</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="PluginAware.html" title="interface in org.gradle.api.plugins"><code>PluginAware</code></a></dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t3" class="tableTab"><span><a href="javascript:show(4);">Abstract Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colSecond" scope="col">Method</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#apply-java.lang.Class-">apply</a></span>&#8203;(java.lang.Class&lt;?&gt;&nbsp;type)</code></th>
<td class="colLast">
<div class="block">Applies the given plugin.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#apply-java.lang.String-">apply</a></span>&#8203;(java.lang.String&nbsp;pluginId)</code></th>
<td class="colLast">
<div class="block">Applies the plugin with the given ID.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code><a href="AppliedPlugin.html" title="interface in org.gradle.api.plugins">AppliedPlugin</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#findPlugin-java.lang.String-">findPlugin</a></span>&#8203;(java.lang.String&nbsp;id)</code></th>
<td class="colLast">
<div class="block">Returns the information about the plugin that has been applied with the given ID, or null if no plugin has been applied with the given ID.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#hasPlugin-java.lang.String-">hasPlugin</a></span>&#8203;(java.lang.String&nbsp;id)</code></th>
<td class="colLast">
<div class="block">Returns <code>true</code> if a plugin with the given ID has already been applied, otherwise <code>false</code>.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#withPlugin-java.lang.String-org.gradle.api.Action-">withPlugin</a></span>&#8203;(java.lang.String&nbsp;id,
          <a href="../Action.html" title="interface in org.gradle.api">Action</a>&lt;? super <a href="AppliedPlugin.html" title="interface in org.gradle.api.plugins">AppliedPlugin</a>&gt;&nbsp;action)</code></th>
<td class="colLast">
<div class="block">Executes the given action when the specified plugin is applied.</div>
</td>
</tr>
</table>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="apply-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>apply</h4>
<pre class="methodSignature">void&nbsp;apply&#8203;(java.lang.String&nbsp;pluginId)</pre>
<div class="block">Applies the plugin with the given ID. Does nothing if the plugin has already been applied.
 <p>
 Plugins in the <code>"org.gradle"</code> namespace can be applied directly via name.
 That is, the following two lines are equivalent…
 <pre class='autoTested'>
 pluginManager.apply "org.gradle.java"
 pluginManager.apply "java"
 </pre></div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>pluginId</code> - the ID of the plugin to apply</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>2.3</dd>
</dl>
</li>
</ul>
<a name="apply-java.lang.Class-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>apply</h4>
<pre class="methodSignature">void&nbsp;apply&#8203;(java.lang.Class&lt;?&gt;&nbsp;type)</pre>
<div class="block">Applies the given plugin. Does nothing if the plugin has already been applied.
 <p>
 The given class should implement the <a href="../Plugin.html" title="interface in org.gradle.api"><code>Plugin</code></a> interface, and be parameterized for a compatible type of <code>this</code>.
 <p>
 The following two lines are equivalent…
 <pre class='autoTested'>
 pluginManager.apply org.gradle.api.plugins.JavaPlugin
 pluginManager.apply "org.gradle.java"
 </pre></div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>type</code> - the plugin class to apply</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>2.3</dd>
</dl>
</li>
</ul>
<a name="findPlugin-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>findPlugin</h4>
<pre class="methodSignature">@Nullable
<a href="AppliedPlugin.html" title="interface in org.gradle.api.plugins">AppliedPlugin</a>&nbsp;findPlugin&#8203;(java.lang.String&nbsp;id)</pre>
<div class="block">Returns the information about the plugin that has been applied with the given ID, or null if no plugin has been applied with the given ID.
 <p>
 Plugins in the <code>"org.gradle"</code> namespace (that is, core Gradle plugins) can be specified by either name (e.g. <code>"java"</code>) or ID <code>"org.gradle.java"</code>.
 All other plugins must be queried for by their full ID (e.g. <code>"org.company.some-plugin"</code>).
 <p>
 Some Gradle plugins have not yet migrated to fully qualified plugin IDs.
 Such plugins can be detected with this method by simply using the unqualified ID (e.g. <code>"some-third-party-plugin"</code>.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>id</code> - the plugin ID</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>information about the applied plugin, or <code>null</code> if no plugin has been applied with the given ID</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>2.3</dd>
</dl>
</li>
</ul>
<a name="hasPlugin-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>hasPlugin</h4>
<pre class="methodSignature">boolean&nbsp;hasPlugin&#8203;(java.lang.String&nbsp;id)</pre>
<div class="block">Returns <code>true</code> if a plugin with the given ID has already been applied, otherwise <code>false</code>.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>id</code> - the plugin ID. See <a href="#findPlugin-java.lang.String-"><code>findPlugin(String)</code></a> for details about this parameter.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd><code>true</code> if the plugin has been applied</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>2.3</dd>
</dl>
</li>
</ul>
<a name="withPlugin-java.lang.String-org.gradle.api.Action-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>withPlugin</h4>
<pre class="methodSignature">void&nbsp;withPlugin&#8203;(java.lang.String&nbsp;id,
                <a href="../Action.html" title="interface in org.gradle.api">Action</a>&lt;? super <a href="AppliedPlugin.html" title="interface in org.gradle.api.plugins">AppliedPlugin</a>&gt;&nbsp;action)</pre>
<div class="block">Executes the given action when the specified plugin is applied.
 <p>
 If a plugin with the specified ID has already been applied, the supplied action will be executed immediately.
 Otherwise, the action will executed immediately after a plugin with the specified ID is applied.
 <p>
 The given action is always executed after the plugin has been applied.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>id</code> - the plugin ID. See <a href="#findPlugin-java.lang.String-"><code>findPlugin(String)</code></a> for details about this parameter.</dd>
<dd><code>action</code> - the action to execute if/when the plugin is applied</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>2.3</dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
