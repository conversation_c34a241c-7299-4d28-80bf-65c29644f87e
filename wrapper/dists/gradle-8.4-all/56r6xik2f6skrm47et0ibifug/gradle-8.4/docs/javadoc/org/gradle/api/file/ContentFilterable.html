<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc -->
<title>ContentFilterable (Gradle API 8.4)</title>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<link rel="stylesheet" type="text/css" href="../../../../javadoc.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../jquery/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
<script type="text/javascript" src="../../../../jquery/jszip/dist/jszip.min.js"></script>
<script type="text/javascript" src="../../../../jquery/jszip-utils/dist/jszip-utils.min.js"></script>
<!--[if IE]>
<script type="text/javascript" src="../../../../jquery/jszip-utils/dist/jszip-utils-ie.min.js"></script>
<![endif]-->
<script type="text/javascript" src="../../../../jquery/jquery-3.6.0.min.js"></script>
<script type="text/javascript" src="../../../../jquery/jquery-ui.min.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="ContentFilterable (Gradle API 8.4)";
        }
    }
    catch(err) {
    }
//-->
var data = {"i0":6,"i1":6,"i2":6,"i3":6,"i4":6,"i5":6};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],4:["t3","Abstract Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
var pathtoroot = "../../../../";
var useModuleDirectories = false;
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="fixedNav">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses.html">All&nbsp;Classes</a></li>
</ul>
<ul class="navListSearch">
<li><label for="search">SEARCH:</label>
<input type="text" id="search" value="search" disabled="disabled">
<input type="reset" id="reset" value="reset" disabled="disabled">
</li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
</div>
<div class="navPadding">&nbsp;</div>
<script type="text/javascript"><!--
$('.navPadding').css('padding-top', $('.fixedNav').css("height"));
//-->
</script>
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle"><span class="packageLabelInType">Package</span>&nbsp;<a href="package-summary.html">org.gradle.api.file</a></div>
<h2 title="Interface ContentFilterable" class="title">Interface ContentFilterable</h2>
</div>
<div class="contentContainer">
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Known Subinterfaces:</dt>
<dd><code><a href="CopyProcessingSpec.html" title="interface in org.gradle.api.file">CopyProcessingSpec</a></code>, <code><a href="CopySpec.html" title="interface in org.gradle.api.file">CopySpec</a></code>, <code><a href="FileCopyDetails.html" title="interface in org.gradle.api.file">FileCopyDetails</a></code>, <code><a href="SyncSpec.html" title="interface in org.gradle.api.file">SyncSpec</a></code></dd>
</dl>
<dl>
<dt>All Known Implementing Classes:</dt>
<dd><code><a href="../tasks/bundling/AbstractArchiveTask.html" title="class in org.gradle.api.tasks.bundling">AbstractArchiveTask</a></code>, <code><a href="../tasks/AbstractCopyTask.html" title="class in org.gradle.api.tasks">AbstractCopyTask</a></code>, <code><a href="../tasks/Copy.html" title="class in org.gradle.api.tasks">Copy</a></code>, <code><a href="../../plugins/ear/Ear.html" title="class in org.gradle.plugins.ear">Ear</a></code>, <code><a href="../tasks/bundling/Jar.html" title="class in org.gradle.api.tasks.bundling">Jar</a></code>, <code><a href="../../jvm/tasks/Jar.html" title="class in org.gradle.jvm.tasks">Jar</a></code>, <code><a href="../../language/jvm/tasks/ProcessResources.html" title="class in org.gradle.language.jvm.tasks">ProcessResources</a></code>, <code><a href="../tasks/Sync.html" title="class in org.gradle.api.tasks">Sync</a></code>, <code><a href="../tasks/bundling/Tar.html" title="class in org.gradle.api.tasks.bundling">Tar</a></code>, <code><a href="../tasks/bundling/War.html" title="class in org.gradle.api.tasks.bundling">War</a></code>, <code><a href="../tasks/bundling/Zip.html" title="class in org.gradle.api.tasks.bundling">Zip</a></code></dd>
</dl>
<hr>
<pre>public interface <span class="typeNameLabel">ContentFilterable</span></pre>
<div class="block">Represents some binary resource whose content can be filtered.</div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t3" class="tableTab"><span><a href="javascript:show(4);">Abstract Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colSecond" scope="col">Method</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code><a href="ContentFilterable.html" title="interface in org.gradle.api.file">ContentFilterable</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#expand-java.util.Map-">expand</a></span>&#8203;(java.util.Map&lt;java.lang.String,&#8203;?&gt;&nbsp;properties)</code></th>
<td class="colLast">
<div class="block">Expands property references in each file as it is copied.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code><a href="ContentFilterable.html" title="interface in org.gradle.api.file">ContentFilterable</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#expand-java.util.Map-org.gradle.api.Action-">expand</a></span>&#8203;(java.util.Map&lt;java.lang.String,&#8203;?&gt;&nbsp;properties,
      <a href="../Action.html" title="interface in org.gradle.api">Action</a>&lt;? super <a href="ExpandDetails.html" title="interface in org.gradle.api.file">ExpandDetails</a>&gt;&nbsp;action)</code></th>
<td class="colLast">
<div class="block">Expands property references in each file as it is copied.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code><a href="ContentFilterable.html" title="interface in org.gradle.api.file">ContentFilterable</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#filter-groovy.lang.Closure-">filter</a></span>&#8203;(<a href="https://docs.groovy-lang.org/docs/groovy-3.0.17/html/gapi/groovy/lang/Closure.html?is-external=true" title="class or interface in groovy.lang" class="externalLink">Closure</a>&nbsp;closure)</code></th>
<td class="colLast">
<div class="block">Adds a content filter based on the provided closure.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code><a href="ContentFilterable.html" title="interface in org.gradle.api.file">ContentFilterable</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#filter-java.lang.Class-">filter</a></span>&#8203;(java.lang.Class&lt;? extends java.io.FilterReader&gt;&nbsp;filterType)</code></th>
<td class="colLast">
<div class="block">Adds a content filter to be used during the copy.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code><a href="ContentFilterable.html" title="interface in org.gradle.api.file">ContentFilterable</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#filter-java.util.Map-java.lang.Class-">filter</a></span>&#8203;(java.util.Map&lt;java.lang.String,&#8203;?&gt;&nbsp;properties,
      java.lang.Class&lt;? extends java.io.FilterReader&gt;&nbsp;filterType)</code></th>
<td class="colLast">
<div class="block">Adds a content filter to be used during the copy.</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code><a href="ContentFilterable.html" title="interface in org.gradle.api.file">ContentFilterable</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#filter-org.gradle.api.Transformer-">filter</a></span>&#8203;(<a href="../Transformer.html" title="interface in org.gradle.api">Transformer</a>&lt;@Nullable java.lang.String,&#8203;java.lang.String&gt;&nbsp;transformer)</code></th>
<td class="colLast">
<div class="block">Adds a content filter based on the provided transformer.</div>
</td>
</tr>
</table>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="filter-java.util.Map-java.lang.Class-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>filter</h4>
<pre class="methodSignature"><a href="ContentFilterable.html" title="interface in org.gradle.api.file">ContentFilterable</a>&nbsp;filter&#8203;(java.util.Map&lt;java.lang.String,&#8203;?&gt;&nbsp;properties,
                         java.lang.Class&lt;? extends java.io.FilterReader&gt;&nbsp;filterType)</pre>
<div class="block"><p>Adds a content filter to be used during the copy.  Multiple calls to filter, add additional filters to the
 filter chain.  Each filter should implement <code>java.io.FilterReader</code>. Include <code>
 org.apache.tools.ant.filters.*</code> for access to all the standard Ant filters.</p>

 <p>Filter properties may be specified using groovy map syntax.</p>

 <p> Examples:
 <pre>
    filter(HeadFilter, lines:25, skip:2)
    filter(ReplaceTokens, tokens:[copyright:'2009', version:'2.3.1'])
 </pre></div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>properties</code> - map of filter properties</dd>
<dd><code>filterType</code> - Class of filter to add</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>this</dd>
</dl>
</li>
</ul>
<a name="filter-java.lang.Class-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>filter</h4>
<pre class="methodSignature"><a href="ContentFilterable.html" title="interface in org.gradle.api.file">ContentFilterable</a>&nbsp;filter&#8203;(java.lang.Class&lt;? extends java.io.FilterReader&gt;&nbsp;filterType)</pre>
<div class="block"><p>Adds a content filter to be used during the copy.  Multiple calls to filter, add additional filters to the
 filter chain.  Each filter should implement <code>java.io.FilterReader</code>. Include <code>
 org.apache.tools.ant.filters.*</code> for access to all the standard Ant filters.</p>

 <p> Examples:
 <pre>
    filter(StripJavaComments)
    filter(com.mycompany.project.CustomFilter)
 </pre></div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>filterType</code> - Class of filter to add</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>this</dd>
</dl>
</li>
</ul>
<a name="filter-groovy.lang.Closure-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>filter</h4>
<pre class="methodSignature"><a href="ContentFilterable.html" title="interface in org.gradle.api.file">ContentFilterable</a>&nbsp;filter&#8203;(<a href="https://docs.groovy-lang.org/docs/groovy-3.0.17/html/gapi/groovy/lang/Closure.html?is-external=true" title="class or interface in groovy.lang" class="externalLink">Closure</a>&nbsp;closure)</pre>
<div class="block">Adds a content filter based on the provided closure.  The Closure will be called with each line (stripped of line
 endings) and should return a String to replace the line or <code>null</code> to remove the line.  If every line is
 removed, the result will be an empty file, not an absent one.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>closure</code> - to implement line based filtering</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>this</dd>
</dl>
</li>
</ul>
<a name="filter-org.gradle.api.Transformer-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>filter</h4>
<pre class="methodSignature"><a href="ContentFilterable.html" title="interface in org.gradle.api.file">ContentFilterable</a>&nbsp;filter&#8203;(<a href="../Transformer.html" title="interface in org.gradle.api">Transformer</a>&lt;@Nullable java.lang.String,&#8203;java.lang.String&gt;&nbsp;transformer)</pre>
<div class="block">Adds a content filter based on the provided transformer.  The Closure will be called with each line (stripped of line
 endings) and should return a String to replace the line or <code>null</code> to remove the line.  If every line is
 removed, the result will be an empty file, not an absent one.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>transformer</code> - to implement line based filtering</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>this</dd>
</dl>
</li>
</ul>
<a name="expand-java.util.Map-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>expand</h4>
<pre class="methodSignature"><a href="ContentFilterable.html" title="interface in org.gradle.api.file">ContentFilterable</a>&nbsp;expand&#8203;(java.util.Map&lt;java.lang.String,&#8203;?&gt;&nbsp;properties)</pre>
<div class="block"><p>Expands property references in each file as it is copied. More specifically, each file is transformed using
 Groovy's <a href="https://docs.groovy-lang.org/docs/groovy-3.0.17/html/gapi/groovy/text/SimpleTemplateEngine.html?is-external=true" title="class or interface in groovy.text" class="externalLink"><code>SimpleTemplateEngine</code></a>. This means you can use simple property references, such as
 <code>$property</code> or <code>${property}</code> in the file. You can also include arbitrary Groovy code in the
 file, such as <code>${version ?: 'unknown'}</code> or <code>${classpath*.name.join(' ')}</code>
 <p>
 Note that all escape sequences (<code>\n</code>, <code>\t</code>, <code>\\</code>, etc) are converted to the symbols
 they represent, so, for example, <code>\n</code> becomes newline. If this is undesirable then <a href="#expand-java.util.Map-org.gradle.api.Action-"><code>expand(Map, Action)</code></a>
 should be used to disable this behavior.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>properties</code> - reference-to-value map for substitution</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>this</dd>
</dl>
</li>
</ul>
<a name="expand-java.util.Map-org.gradle.api.Action-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>expand</h4>
<pre class="methodSignature"><a href="ContentFilterable.html" title="interface in org.gradle.api.file">ContentFilterable</a>&nbsp;expand&#8203;(java.util.Map&lt;java.lang.String,&#8203;?&gt;&nbsp;properties,
                         <a href="../Action.html" title="interface in org.gradle.api">Action</a>&lt;? super <a href="ExpandDetails.html" title="interface in org.gradle.api.file">ExpandDetails</a>&gt;&nbsp;action)</pre>
<div class="block"><p>Expands property references in each file as it is copied. More specifically, each file is transformed using
 Groovy's <a href="https://docs.groovy-lang.org/docs/groovy-3.0.17/html/gapi/groovy/text/SimpleTemplateEngine.html?is-external=true" title="class or interface in groovy.text" class="externalLink"><code>SimpleTemplateEngine</code></a>. This means you can use simple property references, such as
 <code>$property</code> or <code>${property}</code> in the file. You can also include arbitrary Groovy code in the
 file, such as <code>${version ?: 'unknown'}</code> or <code>${classpath*.name.join(' ')}</code>. The template
 engine can be configured with the provided action.
 <p>
 Note that by default all escape sequences (<code>\n</code>, <code>\t</code>, <code>\\</code>, etc) are converted to the symbols
 they represent, so, for example, <code>\n</code> becomes newline. This behavior is controlled by
 <a href="ExpandDetails.html#getEscapeBackslash--"><code>ExpandDetails.getEscapeBackslash()</code></a> property. It should be set to <code>true</code> to disable escape sequences
 conversion:
 <pre>
  expand(one: '1', two: 2) {
      escapeBackslash = true
  }
 </pre></div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>properties</code> - reference-to-value map for substitution</dd>
<dd><code>action</code> - action to perform additional configuration of the underlying template engine</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>this</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>7.2</dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
