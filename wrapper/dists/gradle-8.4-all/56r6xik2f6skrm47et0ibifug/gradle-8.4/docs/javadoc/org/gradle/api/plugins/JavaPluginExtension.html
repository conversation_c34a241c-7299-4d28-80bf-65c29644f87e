<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc -->
<title>JavaPluginExtension (Gradle API 8.4)</title>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<link rel="stylesheet" type="text/css" href="../../../../javadoc.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../jquery/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
<script type="text/javascript" src="../../../../jquery/jszip/dist/jszip.min.js"></script>
<script type="text/javascript" src="../../../../jquery/jszip-utils/dist/jszip-utils.min.js"></script>
<!--[if IE]>
<script type="text/javascript" src="../../../../jquery/jszip-utils/dist/jszip-utils-ie.min.js"></script>
<![endif]-->
<script type="text/javascript" src="../../../../jquery/jquery-3.6.0.min.js"></script>
<script type="text/javascript" src="../../../../jquery/jquery-ui.min.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="JavaPluginExtension (Gradle API 8.4)";
        }
    }
    catch(err) {
    }
//-->
var data = {"i0":6,"i1":6,"i2":6,"i3":6,"i4":6,"i5":6,"i6":6,"i7":6,"i8":6,"i9":6,"i10":6,"i11":6,"i12":6,"i13":6,"i14":6,"i15":6,"i16":6,"i17":6,"i18":6,"i19":6,"i20":6,"i21":6,"i22":6};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],4:["t3","Abstract Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
var pathtoroot = "../../../../";
var useModuleDirectories = false;
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="fixedNav">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses.html">All&nbsp;Classes</a></li>
</ul>
<ul class="navListSearch">
<li><label for="search">SEARCH:</label>
<input type="text" id="search" value="search" disabled="disabled">
<input type="reset" id="reset" value="reset" disabled="disabled">
</li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
</div>
<div class="navPadding">&nbsp;</div>
<script type="text/javascript"><!--
$('.navPadding').css('padding-top', $('.fixedNav').css("height"));
//-->
</script>
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle"><span class="packageLabelInType">Package</span>&nbsp;<a href="package-summary.html">org.gradle.api.plugins</a></div>
<h2 title="Interface JavaPluginExtension" class="title">Interface JavaPluginExtension</h2>
</div>
<div class="contentContainer">
<div class="description">
<ul class="blockList">
<li class="blockList">
<hr>
<pre>public interface <span class="typeNameLabel">JavaPluginExtension</span></pre>
<div class="block">Common configuration for Java based projects. This is added by the <a href="JavaBasePlugin.html" title="class in org.gradle.api.plugins"><code>JavaBasePlugin</code></a>.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>4.10</dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t3" class="tableTab"><span><a href="javascript:show(4);">Abstract Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colSecond" scope="col">Method</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#consistentResolution-org.gradle.api.Action-">consistentResolution</a></span>&#8203;(<a href="../Action.html" title="interface in org.gradle.api">Action</a>&lt;? super <a href="JavaResolutionConsistency.html" title="interface in org.gradle.api.plugins">JavaResolutionConsistency</a>&gt;&nbsp;action)</code></th>
<td class="colLast">
<div class="block">Configure the dependency resolution consistency for this Java project.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#disableAutoTargetJvm--">disableAutoTargetJvm</a></span>()</code></th>
<td class="colLast">
<div class="block">If this method is called, Gradle will not automatically try to fetch
 dependencies which have a JVM version compatible with the target compatibility
 of this module.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getAutoTargetJvmDisabled--">getAutoTargetJvmDisabled</a></span>()</code></th>
<td class="colLast">
<div class="block">Tells if automatic JVM targeting is enabled.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code><a href="../file/DirectoryProperty.html" title="interface in org.gradle.api.file">DirectoryProperty</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getDocsDir--">getDocsDir</a></span>()</code></th>
<td class="colLast">
<div class="block">Returns a file pointing to the root directory supposed to be used for all docs.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code><a href="../jvm/ModularitySpec.html" title="interface in org.gradle.api.jvm">ModularitySpec</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getModularity--">getModularity</a></span>()</code></th>
<td class="colLast">
<div class="block">Configure the module path handling for tasks that have a 'classpath' as input.</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code><a href="../JavaVersion.html" title="enum in org.gradle.api">JavaVersion</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getSourceCompatibility--">getSourceCompatibility</a></span>()</code></th>
<td class="colLast">
<div class="block">Returns the source compatibility used for compiling Java sources.</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code><a href="../tasks/SourceSetContainer.html" title="interface in org.gradle.api.tasks">SourceSetContainer</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getSourceSets--">getSourceSets</a></span>()</code></th>
<td class="colLast">
<div class="block">The source sets container.</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code><a href="../JavaVersion.html" title="enum in org.gradle.api">JavaVersion</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getTargetCompatibility--">getTargetCompatibility</a></span>()</code></th>
<td class="colLast">
<div class="block">Returns the target compatibility used for compiling Java sources.</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code><a href="../file/DirectoryProperty.html" title="interface in org.gradle.api.file">DirectoryProperty</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getTestReportDir--">getTestReportDir</a></span>()</code></th>
<td class="colLast">
<div class="block">Returns a file pointing to the root directory to be used for reports.</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code><a href="../file/DirectoryProperty.html" title="interface in org.gradle.api.file">DirectoryProperty</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getTestResultsDir--">getTestResultsDir</a></span>()</code></th>
<td class="colLast">
<div class="block">Returns a file pointing to the root directory of the test results.</div>
</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code><a href="../../jvm/toolchain/JavaToolchainSpec.html" title="interface in org.gradle.jvm.toolchain">JavaToolchainSpec</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getToolchain--">getToolchain</a></span>()</code></th>
<td class="colLast">
<div class="block">Gets the project wide toolchain requirements that will be used for tasks requiring a tool from the toolchain (e.g.</div>
</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code><a href="../java/archives/Manifest.html" title="interface in org.gradle.api.java.archives">Manifest</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#manifest--">manifest</a></span>()</code></th>
<td class="colLast">
<div class="block">Creates a new instance of a <a href="../java/archives/Manifest.html" title="interface in org.gradle.api.java.archives"><code>Manifest</code></a>.</div>
</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code><a href="../java/archives/Manifest.html" title="interface in org.gradle.api.java.archives">Manifest</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#manifest-groovy.lang.Closure-">manifest</a></span>&#8203;(<a href="https://docs.groovy-lang.org/docs/groovy-3.0.17/html/gapi/groovy/lang/Closure.html?is-external=true" title="class or interface in groovy.lang" class="externalLink">Closure</a>&nbsp;closure)</code></th>
<td class="colLast">
<div class="block">Creates and configures a new instance of a <a href="../java/archives/Manifest.html" title="interface in org.gradle.api.java.archives"><code>Manifest</code></a>.</div>
</td>
</tr>
<tr id="i13" class="rowColor">
<td class="colFirst"><code><a href="../java/archives/Manifest.html" title="interface in org.gradle.api.java.archives">Manifest</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#manifest-org.gradle.api.Action-">manifest</a></span>&#8203;(<a href="../Action.html" title="interface in org.gradle.api">Action</a>&lt;? super <a href="../java/archives/Manifest.html" title="interface in org.gradle.api.java.archives">Manifest</a>&gt;&nbsp;action)</code></th>
<td class="colLast">
<div class="block">Creates and configures a new instance of a <a href="../java/archives/Manifest.html" title="interface in org.gradle.api.java.archives"><code>Manifest</code></a>.</div>
</td>
</tr>
<tr id="i14" class="altColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#registerFeature-java.lang.String-org.gradle.api.Action-">registerFeature</a></span>&#8203;(java.lang.String&nbsp;name,
               <a href="../Action.html" title="interface in org.gradle.api">Action</a>&lt;? super <a href="FeatureSpec.html" title="interface in org.gradle.api.plugins">FeatureSpec</a>&gt;&nbsp;configureAction)</code></th>
<td class="colLast">
<div class="block">Registers a feature.</div>
</td>
</tr>
<tr id="i15" class="rowColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setSourceCompatibility-java.lang.Object-">setSourceCompatibility</a></span>&#8203;(java.lang.Object&nbsp;value)</code></th>
<td class="colLast">
<div class="block">Sets the source compatibility used for compiling Java sources.</div>
</td>
</tr>
<tr id="i16" class="altColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setSourceCompatibility-org.gradle.api.JavaVersion-">setSourceCompatibility</a></span>&#8203;(<a href="../JavaVersion.html" title="enum in org.gradle.api">JavaVersion</a>&nbsp;value)</code></th>
<td class="colLast">
<div class="block">Sets the source compatibility used for compiling Java sources.</div>
</td>
</tr>
<tr id="i17" class="rowColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setTargetCompatibility-java.lang.Object-">setTargetCompatibility</a></span>&#8203;(java.lang.Object&nbsp;value)</code></th>
<td class="colLast">
<div class="block">Sets the target compatibility used for compiling Java sources.</div>
</td>
</tr>
<tr id="i18" class="altColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setTargetCompatibility-org.gradle.api.JavaVersion-">setTargetCompatibility</a></span>&#8203;(<a href="../JavaVersion.html" title="enum in org.gradle.api">JavaVersion</a>&nbsp;value)</code></th>
<td class="colLast">
<div class="block">Sets the target compatibility used for compiling Java sources.</div>
</td>
</tr>
<tr id="i19" class="rowColor">
<td class="colFirst"><code>java.lang.Object</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#sourceSets-groovy.lang.Closure-">sourceSets</a></span>&#8203;(<a href="https://docs.groovy-lang.org/docs/groovy-3.0.17/html/gapi/groovy/lang/Closure.html?is-external=true" title="class or interface in groovy.lang" class="externalLink">Closure</a>&nbsp;closure)</code></th>
<td class="colLast">
<div class="block">Configures the source sets of this project.</div>
</td>
</tr>
<tr id="i20" class="altColor">
<td class="colFirst"><code><a href="../../jvm/toolchain/JavaToolchainSpec.html" title="interface in org.gradle.jvm.toolchain">JavaToolchainSpec</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#toolchain-org.gradle.api.Action-">toolchain</a></span>&#8203;(<a href="../Action.html" title="interface in org.gradle.api">Action</a>&lt;? super <a href="../../jvm/toolchain/JavaToolchainSpec.html" title="interface in org.gradle.jvm.toolchain">JavaToolchainSpec</a>&gt;&nbsp;action)</code></th>
<td class="colLast">
<div class="block">Configures the project wide toolchain requirements for tasks that require a tool from the toolchain (e.g.</div>
</td>
</tr>
<tr id="i21" class="rowColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#withJavadocJar--">withJavadocJar</a></span>()</code></th>
<td class="colLast">
<div class="block">Adds a task <code>javadocJar</code> that will package the output of the <code>javadoc</code> task in a JAR with classifier <code>javadoc</code>.</div>
</td>
</tr>
<tr id="i22" class="altColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#withSourcesJar--">withSourcesJar</a></span>()</code></th>
<td class="colLast">
<div class="block">Adds a task <code>sourcesJar</code> that will package the Java sources of the main <a href="../tasks/SourceSet.html" title="interface in org.gradle.api.tasks"><code>SourceSet</code></a> in a JAR with classifier <code>sources</code>.</div>
</td>
</tr>
</table>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="getSourceCompatibility--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getSourceCompatibility</h4>
<pre class="methodSignature"><a href="../JavaVersion.html" title="enum in org.gradle.api">JavaVersion</a>&nbsp;getSourceCompatibility()</pre>
<div class="block">Returns the source compatibility used for compiling Java sources.</div>
</li>
</ul>
<a name="setSourceCompatibility-org.gradle.api.JavaVersion-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setSourceCompatibility</h4>
<pre class="methodSignature">void&nbsp;setSourceCompatibility&#8203;(<a href="../JavaVersion.html" title="enum in org.gradle.api">JavaVersion</a>&nbsp;value)</pre>
<div class="block">Sets the source compatibility used for compiling Java sources.
 <p>
 This property cannot be set if a <a href="#getToolchain--"><code>toolchain</code></a> has been configured.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - The value for the source compatibility</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="#toolchain-org.gradle.api.Action-"><code>toolchain(Action)</code></a></dd>
</dl>
</li>
</ul>
<a name="getTargetCompatibility--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getTargetCompatibility</h4>
<pre class="methodSignature"><a href="../JavaVersion.html" title="enum in org.gradle.api">JavaVersion</a>&nbsp;getTargetCompatibility()</pre>
<div class="block">Returns the target compatibility used for compiling Java sources.</div>
</li>
</ul>
<a name="setTargetCompatibility-org.gradle.api.JavaVersion-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setTargetCompatibility</h4>
<pre class="methodSignature">void&nbsp;setTargetCompatibility&#8203;(<a href="../JavaVersion.html" title="enum in org.gradle.api">JavaVersion</a>&nbsp;value)</pre>
<div class="block">Sets the target compatibility used for compiling Java sources.
 <p>
 This property cannot be set if a <a href="#getToolchain--"><code>toolchain</code></a> has been configured.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - The value for the target compatibility</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="#toolchain-org.gradle.api.Action-"><code>toolchain(Action)</code></a></dd>
</dl>
</li>
</ul>
<a name="registerFeature-java.lang.String-org.gradle.api.Action-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>registerFeature</h4>
<pre class="methodSignature">void&nbsp;registerFeature&#8203;(java.lang.String&nbsp;name,
                     <a href="../Action.html" title="interface in org.gradle.api">Action</a>&lt;? super <a href="FeatureSpec.html" title="interface in org.gradle.api.plugins">FeatureSpec</a>&gt;&nbsp;configureAction)</pre>
<div class="block">Registers a feature.
 <p>
 The new feature will have a default capability corresponding to the
 "group", "name" + feature name and version of this project. For example,
 if the group of the component is "org", that the project name is "lib"
 the version is "1.0" and the feature name is "myFeature", then a
 capability named "org:lib-my-feature:1.0" is automatically added.
 <p>
 In order to consume this feature in another module add a dependency like
 the following:

 <pre>
  dependencies {
      implementation(project(":lib")) {
          capabilities {
              requireCapability("org:lib-my-feature:1.0")
          }
      }
  }
 </pre>

 The <a href="FeatureSpec.html#capability-java.lang.String-java.lang.String-java.lang.String-"><code>FeatureSpec.capability(String, String, String)</code></a> method can be
 used to refine the capabilities of this feature.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>name</code> - the name of the feature</dd>
<dd><code>configureAction</code> - the configuration for the feature</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>5.3</dd>
</dl>
</li>
</ul>
<a name="disableAutoTargetJvm--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>disableAutoTargetJvm</h4>
<pre class="methodSignature">void&nbsp;disableAutoTargetJvm()</pre>
<div class="block">If this method is called, Gradle will not automatically try to fetch
 dependencies which have a JVM version compatible with the target compatibility
 of this module.
 <P>
 This should be used whenever the default behavior is not
 applicable, in particular when for some reason it's not possible to split
 a module and that this module only has some classes which require dependencies
 on higher versions.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>5.3</dd>
</dl>
</li>
</ul>
<a name="withJavadocJar--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>withJavadocJar</h4>
<pre class="methodSignature">void&nbsp;withJavadocJar()</pre>
<div class="block">Adds a task <code>javadocJar</code> that will package the output of the <code>javadoc</code> task in a JAR with classifier <code>javadoc</code>.
 <P>
 The produced artifact is registered as a documentation variant on the <code>java</code> component and added as a dependency on the <code>assemble</code> task.
 This means that if <code>maven-publish</code> or <code>ivy-publish</code> is also applied, the javadoc JAR will be published.
 <P>
 If the project already has a task named <code>javadocJar</code> then no task is created.
 <P>
 The publishing of the Javadoc variant can also be disabled using <a href="../component/ConfigurationVariantDetails.html#skip--"><code>ConfigurationVariantDetails.skip()</code></a>
 through <a href="../component/AdhocComponentWithVariants.html#withVariantsFromConfiguration-org.gradle.api.artifacts.Configuration-org.gradle.api.Action-"><code>AdhocComponentWithVariants.withVariantsFromConfiguration(Configuration, Action)</code></a>,
 if it should only be built locally by calling or wiring the ':javadocJar' task.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>6.0</dd>
</dl>
</li>
</ul>
<a name="withSourcesJar--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>withSourcesJar</h4>
<pre class="methodSignature">void&nbsp;withSourcesJar()</pre>
<div class="block">Adds a task <code>sourcesJar</code> that will package the Java sources of the main <a href="../tasks/SourceSet.html" title="interface in org.gradle.api.tasks"><code>SourceSet</code></a> in a JAR with classifier <code>sources</code>.
 <P>
 The produced artifact is registered as a documentation variant on the <code>java</code> component and added as a dependency on the <code>assemble</code> task.
 This means that if <code>maven-publish</code> or <code>ivy-publish</code> is also applied, the sources JAR will be published.
 <P>
 If the project already has a task named <code>sourcesJar</code> then no task is created.
 <P>
 The publishing of the sources variant can be disabled using <a href="../component/ConfigurationVariantDetails.html#skip--"><code>ConfigurationVariantDetails.skip()</code></a>
 through <a href="../component/AdhocComponentWithVariants.html#withVariantsFromConfiguration-org.gradle.api.artifacts.Configuration-org.gradle.api.Action-"><code>AdhocComponentWithVariants.withVariantsFromConfiguration(Configuration, Action)</code></a>,
 if it should only be built locally by calling or wiring the ':sourcesJar' task.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>6.0</dd>
</dl>
</li>
</ul>
<a name="getModularity--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getModularity</h4>
<pre class="methodSignature"><a href="../jvm/ModularitySpec.html" title="interface in org.gradle.api.jvm">ModularitySpec</a>&nbsp;getModularity()</pre>
<div class="block">Configure the module path handling for tasks that have a 'classpath' as input. The module classpath handling defines
 to determine for each entry if it is passed to Java tools using '-classpath' or '--module-path'.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>6.4</dd>
</dl>
</li>
</ul>
<a name="getToolchain--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getToolchain</h4>
<pre class="methodSignature"><a href="../../jvm/toolchain/JavaToolchainSpec.html" title="interface in org.gradle.jvm.toolchain">JavaToolchainSpec</a>&nbsp;getToolchain()</pre>
<div class="block">Gets the project wide toolchain requirements that will be used for tasks requiring a tool from the toolchain (e.g. <a href="../tasks/compile/JavaCompile.html" title="class in org.gradle.api.tasks.compile"><code>JavaCompile</code></a>).
 <p>
 Configuring a toolchain cannot be used together with <code>sourceCompatibility</code> or <code>targetCompatibility</code> on this extension.
 Both values will be sourced from the toolchain.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>6.7</dd>
</dl>
</li>
</ul>
<a name="toolchain-org.gradle.api.Action-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>toolchain</h4>
<pre class="methodSignature"><a href="../../jvm/toolchain/JavaToolchainSpec.html" title="interface in org.gradle.jvm.toolchain">JavaToolchainSpec</a>&nbsp;toolchain&#8203;(<a href="../Action.html" title="interface in org.gradle.api">Action</a>&lt;? super <a href="../../jvm/toolchain/JavaToolchainSpec.html" title="interface in org.gradle.jvm.toolchain">JavaToolchainSpec</a>&gt;&nbsp;action)</pre>
<div class="block">Configures the project wide toolchain requirements for tasks that require a tool from the toolchain (e.g. <a href="../tasks/compile/JavaCompile.html" title="class in org.gradle.api.tasks.compile"><code>JavaCompile</code></a>).
 <p>
 Configuring a toolchain cannot be used together with <code>sourceCompatibility</code> or <code>targetCompatibility</code> on this extension.
 Both values will be sourced from the toolchain.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>6.7</dd>
</dl>
</li>
</ul>
<a name="consistentResolution-org.gradle.api.Action-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>consistentResolution</h4>
<pre class="methodSignature"><a href="../Incubating.html" title="annotation in org.gradle.api">@Incubating</a>
void&nbsp;consistentResolution&#8203;(<a href="../Action.html" title="interface in org.gradle.api">Action</a>&lt;? super <a href="JavaResolutionConsistency.html" title="interface in org.gradle.api.plugins">JavaResolutionConsistency</a>&gt;&nbsp;action)</pre>
<div class="block">Configure the dependency resolution consistency for this Java project.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>action</code> - the configuration action</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>6.8</dd>
</dl>
</li>
</ul>
<a name="sourceSets-groovy.lang.Closure-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>sourceSets</h4>
<pre class="methodSignature">java.lang.Object&nbsp;sourceSets&#8203;(<a href="https://docs.groovy-lang.org/docs/groovy-3.0.17/html/gapi/groovy/lang/Closure.html?is-external=true" title="class or interface in groovy.lang" class="externalLink">Closure</a>&nbsp;closure)</pre>
<div class="block">Configures the source sets of this project.

 <p>The given closure is executed to configure the <a href="../tasks/SourceSetContainer.html" title="interface in org.gradle.api.tasks"><code>SourceSetContainer</code></a>. The <a href="../tasks/SourceSetContainer.html" title="interface in org.gradle.api.tasks"><code>SourceSetContainer</code></a>
 is passed to the closure as its delegate.
 <p>
 See the example below how <a href="../tasks/SourceSet.html" title="interface in org.gradle.api.tasks"><code>SourceSet</code></a> 'main' is accessed and how the <a href="../file/SourceDirectorySet.html" title="interface in org.gradle.api.file"><code>SourceDirectorySet</code></a> 'java'
 is configured to exclude some package from compilation.

 <pre class='autoTested'>
 plugins {
     id 'java'
 }

 sourceSets {
   main {
     java {
       exclude 'some/unwanted/package/**'
     }
   }
 }
 </pre></div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>closure</code> - The closure to execute.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>NamedDomainObjectContainer&lt;org.gradle.api.tasks.SourceSet&gt;</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>7.1</dd>
</dl>
</li>
</ul>
<a name="getDocsDir--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getDocsDir</h4>
<pre class="methodSignature"><a href="../file/DirectoryProperty.html" title="interface in org.gradle.api.file">DirectoryProperty</a>&nbsp;getDocsDir()</pre>
<div class="block">Returns a file pointing to the root directory supposed to be used for all docs.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>7.1</dd>
</dl>
</li>
</ul>
<a name="getTestResultsDir--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getTestResultsDir</h4>
<pre class="methodSignature"><a href="../file/DirectoryProperty.html" title="interface in org.gradle.api.file">DirectoryProperty</a>&nbsp;getTestResultsDir()</pre>
<div class="block">Returns a file pointing to the root directory of the test results.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>7.1</dd>
</dl>
</li>
</ul>
<a name="getTestReportDir--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getTestReportDir</h4>
<pre class="methodSignature"><a href="../file/DirectoryProperty.html" title="interface in org.gradle.api.file">DirectoryProperty</a>&nbsp;getTestReportDir()</pre>
<div class="block">Returns a file pointing to the root directory to be used for reports.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>7.1</dd>
</dl>
</li>
</ul>
<a name="setSourceCompatibility-java.lang.Object-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setSourceCompatibility</h4>
<pre class="methodSignature">void&nbsp;setSourceCompatibility&#8203;(java.lang.Object&nbsp;value)</pre>
<div class="block">Sets the source compatibility used for compiling Java sources.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - The value for the source compatibility as defined by <a href="../JavaVersion.html#toVersion-java.lang.Object-"><code>JavaVersion.toVersion(Object)</code></a></dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>7.1</dd>
</dl>
</li>
</ul>
<a name="setTargetCompatibility-java.lang.Object-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setTargetCompatibility</h4>
<pre class="methodSignature">void&nbsp;setTargetCompatibility&#8203;(java.lang.Object&nbsp;value)</pre>
<div class="block">Sets the target compatibility used for compiling Java sources.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - The value for the target compatibility as defined by <a href="../JavaVersion.html#toVersion-java.lang.Object-"><code>JavaVersion.toVersion(Object)</code></a></dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>7.1</dd>
</dl>
</li>
</ul>
<a name="manifest--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>manifest</h4>
<pre class="methodSignature"><a href="../java/archives/Manifest.html" title="interface in org.gradle.api.java.archives">Manifest</a>&nbsp;manifest()</pre>
<div class="block">Creates a new instance of a <a href="../java/archives/Manifest.html" title="interface in org.gradle.api.java.archives"><code>Manifest</code></a>.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>7.1</dd>
</dl>
</li>
</ul>
<a name="manifest-groovy.lang.Closure-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>manifest</h4>
<pre class="methodSignature"><a href="../java/archives/Manifest.html" title="interface in org.gradle.api.java.archives">Manifest</a>&nbsp;manifest&#8203;(<a href="https://docs.groovy-lang.org/docs/groovy-3.0.17/html/gapi/groovy/lang/DelegatesTo.html?is-external=true" title="class or interface in groovy.lang" class="externalLink">@DelegatesTo</a>(<a href="../java/archives/Manifest.html" title="interface in org.gradle.api.java.archives">Manifest.class</a>)
                  <a href="https://docs.groovy-lang.org/docs/groovy-3.0.17/html/gapi/groovy/lang/Closure.html?is-external=true" title="class or interface in groovy.lang" class="externalLink">Closure</a>&nbsp;closure)</pre>
<div class="block">Creates and configures a new instance of a <a href="../java/archives/Manifest.html" title="interface in org.gradle.api.java.archives"><code>Manifest</code></a>. The given closure configures
 the new manifest instance before it is returned.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>closure</code> - The closure to use to configure the manifest.</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>7.1</dd>
</dl>
</li>
</ul>
<a name="manifest-org.gradle.api.Action-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>manifest</h4>
<pre class="methodSignature"><a href="../java/archives/Manifest.html" title="interface in org.gradle.api.java.archives">Manifest</a>&nbsp;manifest&#8203;(<a href="../Action.html" title="interface in org.gradle.api">Action</a>&lt;? super <a href="../java/archives/Manifest.html" title="interface in org.gradle.api.java.archives">Manifest</a>&gt;&nbsp;action)</pre>
<div class="block">Creates and configures a new instance of a <a href="../java/archives/Manifest.html" title="interface in org.gradle.api.java.archives"><code>Manifest</code></a>.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>action</code> - The action to use to configure the manifest.</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>7.1</dd>
</dl>
</li>
</ul>
<a name="getSourceSets--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getSourceSets</h4>
<pre class="methodSignature"><a href="../tasks/SourceSetContainer.html" title="interface in org.gradle.api.tasks">SourceSetContainer</a>&nbsp;getSourceSets()</pre>
<div class="block">The source sets container.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>7.1</dd>
</dl>
</li>
</ul>
<a name="getAutoTargetJvmDisabled--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>getAutoTargetJvmDisabled</h4>
<pre class="methodSignature">boolean&nbsp;getAutoTargetJvmDisabled()</pre>
<div class="block">Tells if automatic JVM targeting is enabled. When disabled, Gradle
 will not automatically try to get dependencies corresponding to the
 same (or compatible) level as the target compatibility of this module.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>7.1</dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
