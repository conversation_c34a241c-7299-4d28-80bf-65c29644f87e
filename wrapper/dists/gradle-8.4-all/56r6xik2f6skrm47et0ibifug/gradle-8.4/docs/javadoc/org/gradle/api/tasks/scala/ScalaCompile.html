<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc -->
<title>ScalaCompile (Gradle API 8.4)</title>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<link rel="stylesheet" type="text/css" href="../../../../../javadoc.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../jquery/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
<script type="text/javascript" src="../../../../../jquery/jszip/dist/jszip.min.js"></script>
<script type="text/javascript" src="../../../../../jquery/jszip-utils/dist/jszip-utils.min.js"></script>
<!--[if IE]>
<script type="text/javascript" src="../../../../../jquery/jszip-utils/dist/jszip-utils-ie.min.js"></script>
<![endif]-->
<script type="text/javascript" src="../../../../../jquery/jquery-3.6.0.min.js"></script>
<script type="text/javascript" src="../../../../../jquery/jquery-ui.min.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="ScalaCompile (Gradle API 8.4)";
        }
    }
    catch(err) {
    }
//-->
var data = {"i0":10,"i1":10,"i2":10,"i3":10,"i4":10,"i5":10,"i6":10,"i7":10,"i8":10,"i9":10,"i10":10};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
var pathtoroot = "../../../../../";
var useModuleDirectories = false;
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="fixedNav">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../../allclasses.html">All&nbsp;Classes</a></li>
</ul>
<ul class="navListSearch">
<li><label for="search">SEARCH:</label>
<input type="text" id="search" value="search" disabled="disabled">
<input type="reset" id="reset" value="reset" disabled="disabled">
</li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li><a href="#nested.class.summary">Nested</a>&nbsp;|&nbsp;</li>
<li><a href="#field.summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
</div>
<div class="navPadding">&nbsp;</div>
<script type="text/javascript"><!--
$('.navPadding').css('padding-top', $('.fixedNav').css("height"));
//-->
</script>
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle"><span class="packageLabelInType">Package</span>&nbsp;<a href="package-summary.html">org.gradle.api.tasks.scala</a></div>
<h2 title="Class ScalaCompile" class="title">Class ScalaCompile</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>org.gradle.api.internal.AbstractTask</li>
<li>
<ul class="inheritance">
<li><a href="../../DefaultTask.html" title="class in org.gradle.api">org.gradle.api.DefaultTask</a></li>
<li>
<ul class="inheritance">
<li>org.gradle.api.internal.ConventionTask</li>
<li>
<ul class="inheritance">
<li><a href="../SourceTask.html" title="class in org.gradle.api.tasks">org.gradle.api.tasks.SourceTask</a></li>
<li>
<ul class="inheritance">
<li><a href="../compile/AbstractCompile.html" title="class in org.gradle.api.tasks.compile">org.gradle.api.tasks.compile.AbstractCompile</a></li>
<li>
<ul class="inheritance">
<li><a href="../../../language/scala/tasks/AbstractScalaCompile.html" title="class in org.gradle.language.scala.tasks">org.gradle.language.scala.tasks.AbstractScalaCompile</a></li>
<li>
<ul class="inheritance">
<li>org.gradle.api.tasks.scala.ScalaCompile</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Implemented Interfaces:</dt>
<dd><code>java.lang.Comparable&lt;<a href="../../Task.html" title="interface in org.gradle.api">Task</a>&gt;</code>, <code>org.gradle.api.internal.DynamicObjectAware</code>, <code>org.gradle.api.internal.IConventionAware</code>, <code>org.gradle.api.internal.TaskInternal</code>, <code>org.gradle.api.internal.tasks.compile.HasCompileOptions</code>, <code><a href="../../plugins/ExtensionAware.html" title="interface in org.gradle.api.plugins">ExtensionAware</a></code>, <code><a href="../../Task.html" title="interface in org.gradle.api">Task</a></code>, <code><a href="../util/PatternFilterable.html" title="interface in org.gradle.api.tasks.util">PatternFilterable</a></code>, <code><a href="../../../util/Configurable.html" title="interface in org.gradle.util">Configurable</a>&lt;<a href="../../Task.html" title="interface in org.gradle.api">Task</a>&gt;</code></dd>
</dl>
<hr>
<pre><a href="../CacheableTask.html" title="annotation in org.gradle.api.tasks">@CacheableTask</a>
public abstract class <span class="typeNameLabel">ScalaCompile</span>
extends <a href="../../../language/scala/tasks/AbstractScalaCompile.html" title="class in org.gradle.language.scala.tasks">AbstractScalaCompile</a></pre>
<div class="block">Compiles Scala source files, and optionally, Java source files.</div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== NESTED CLASS SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="nested.class.summary">
<!--   -->
</a>
<h3>Nested Class Summary</h3>
<ul class="blockList">
<li class="blockList"><a name="nested.classes.inherited.from.class.org.gradle.api.Task">
<!--   -->
</a>
<h3>Nested classes/interfaces inherited from interface&nbsp;org.gradle.api.<a href="../../Task.html" title="interface in org.gradle.api">Task</a></h3>
<code><a href="../../Task.Namer.html" title="class in org.gradle.api">Task.Namer</a></code></li>
</ul>
</li>
</ul>
<!-- =========== FIELD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.summary">
<!--   -->
</a>
<h3>Field Summary</h3>
<ul class="blockList">
<li class="blockList"><a name="fields.inherited.from.class.org.gradle.language.scala.tasks.AbstractScalaCompile">
<!--   -->
</a>
<h3>Fields inherited from class&nbsp;org.gradle.language.scala.tasks.<a href="../../../language/scala/tasks/AbstractScalaCompile.html" title="class in org.gradle.language.scala.tasks">AbstractScalaCompile</a></h3>
<code><a href="../../../language/scala/tasks/AbstractScalaCompile.html#LOGGER">LOGGER</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="fields.inherited.from.class.org.gradle.api.Task">
<!--   -->
</a>
<h3>Fields inherited from interface&nbsp;org.gradle.api.<a href="../../Task.html" title="interface in org.gradle.api">Task</a></h3>
<code><a href="../../Task.html#TASK_ACTION">TASK_ACTION</a>, <a href="../../Task.html#TASK_CONSTRUCTOR_ARGS">TASK_CONSTRUCTOR_ARGS</a>, <a href="../../Task.html#TASK_DEPENDS_ON">TASK_DEPENDS_ON</a>, <a href="../../Task.html#TASK_DESCRIPTION">TASK_DESCRIPTION</a>, <a href="../../Task.html#TASK_GROUP">TASK_GROUP</a>, <a href="../../Task.html#TASK_NAME">TASK_NAME</a>, <a href="../../Task.html#TASK_OVERWRITE">TASK_OVERWRITE</a>, <a href="../../Task.html#TASK_TYPE">TASK_TYPE</a></code></li>
</ul>
</li>
</ul>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="memberSummary" summary="Constructor Summary table, listing constructors, and an explanation">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Constructor</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tr class="altColor">
<th class="colConstructorName" scope="row"><code><span class="memberNameLink"><a href="#ScalaCompile--">ScalaCompile</a></span>()</code></th>
<td class="colLast">&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colSecond" scope="col">Method</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>protected void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#assertScalaClasspathIsNonEmpty--">assertScalaClasspathIsNonEmpty</a></span>()</code></th>
<td class="colLast">&nbsp;</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>protected org.gradle.api.internal.tasks.scala.ScalaJavaJointCompileSpec</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#createSpec--">createSpec</a></span>()</code></th>
<td class="colLast">&nbsp;</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>protected org.gradle.language.base.internal.compile.Compiler&lt;org.gradle.api.internal.tasks.scala.ScalaJavaJointCompileSpec&gt;</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getCompiler-org.gradle.api.internal.tasks.scala.ScalaJavaJointCompileSpec-">getCompiler</a></span>&#8203;(org.gradle.api.internal.tasks.scala.ScalaJavaJointCompileSpec&nbsp;spec)</code></th>
<td class="colLast">&nbsp;</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code><a href="../../file/FileCollection.html" title="interface in org.gradle.api.file">FileCollection</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getScalaClasspath--">getScalaClasspath</a></span>()</code></th>
<td class="colLast">
<div class="block">Returns the classpath to use to load the Scala compiler.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code><a href="ScalaCompileOptions.html" title="class in org.gradle.api.tasks.scala">ScalaCompileOptions</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getScalaCompileOptions--">getScalaCompileOptions</a></span>()</code></th>
<td class="colLast">
<div class="block">Returns the Scala compilation options.</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code><a href="../../file/FileCollection.html" title="interface in org.gradle.api.file">FileCollection</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getScalaCompilerPlugins--">getScalaCompilerPlugins</a></span>()</code></th>
<td class="colLast">
<div class="block">Returns the Scala compiler plugins to use.</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code><a href="../../file/FileCollection.html" title="interface in org.gradle.api.file">FileCollection</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getZincClasspath--">getZincClasspath</a></span>()</code></th>
<td class="colLast">
<div class="block">Returns the classpath to use to load the Zinc incremental compiler.</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setCompiler-org.gradle.language.base.internal.compile.Compiler-">setCompiler</a></span>&#8203;(org.gradle.language.base.internal.compile.Compiler&lt;org.gradle.api.internal.tasks.scala.ScalaJavaJointCompileSpec&gt;&nbsp;compiler)</code></th>
<td class="colLast">
<div class="block">For testing only.</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setScalaClasspath-org.gradle.api.file.FileCollection-">setScalaClasspath</a></span>&#8203;(<a href="../../file/FileCollection.html" title="interface in org.gradle.api.file">FileCollection</a>&nbsp;scalaClasspath)</code></th>
<td class="colLast">&nbsp;</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setScalaCompilerPlugins-org.gradle.api.file.FileCollection-">setScalaCompilerPlugins</a></span>&#8203;(<a href="../../file/FileCollection.html" title="interface in org.gradle.api.file">FileCollection</a>&nbsp;scalaCompilerPlugins)</code></th>
<td class="colLast">
<div class="block">Sets the Scala compiler plugins to use.</div>
</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setZincClasspath-org.gradle.api.file.FileCollection-">setZincClasspath</a></span>&#8203;(<a href="../../file/FileCollection.html" title="interface in org.gradle.api.file">FileCollection</a>&nbsp;zincClasspath)</code></th>
<td class="colLast">&nbsp;</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.org.gradle.language.scala.tasks.AbstractScalaCompile">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;org.gradle.language.scala.tasks.<a href="../../../language/scala/tasks/AbstractScalaCompile.html" title="class in org.gradle.language.scala.tasks">AbstractScalaCompile</a></h3>
<code><a href="../../../language/scala/tasks/AbstractScalaCompile.html#compile--">compile</a>, <a href="../../../language/scala/tasks/AbstractScalaCompile.html#getAnalysisFiles--">getAnalysisFiles</a>, <a href="../../../language/scala/tasks/AbstractScalaCompile.html#getAnalysisMappingFile--">getAnalysisMappingFile</a>, <a href="../../../language/scala/tasks/AbstractScalaCompile.html#getCachedClasspathTransformer--">getCachedClasspathTransformer</a>, <a href="../../../language/scala/tasks/AbstractScalaCompile.html#getDeleter--">getDeleter</a>, <a href="../../../language/scala/tasks/AbstractScalaCompile.html#getJavaLauncher--">getJavaLauncher</a>, <a href="../../../language/scala/tasks/AbstractScalaCompile.html#getJavaToolchainService--">getJavaToolchainService</a>, <a href="../../../language/scala/tasks/AbstractScalaCompile.html#getJvmVersion--">getJvmVersion</a>, <a href="../../../language/scala/tasks/AbstractScalaCompile.html#getObjectFactory--">getObjectFactory</a>, <a href="../../../language/scala/tasks/AbstractScalaCompile.html#getOptions--">getOptions</a>, <a href="../../../language/scala/tasks/AbstractScalaCompile.html#getProjectLayout--">getProjectLayout</a>, <a href="../../../language/scala/tasks/AbstractScalaCompile.html#getSource--">getSource</a>, <a href="../../../language/scala/tasks/AbstractScalaCompile.html#getToolchain--">getToolchain</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.org.gradle.api.tasks.compile.AbstractCompile">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;org.gradle.api.tasks.compile.<a href="../compile/AbstractCompile.html" title="class in org.gradle.api.tasks.compile">AbstractCompile</a></h3>
<code><a href="../compile/AbstractCompile.html#getClasspath--">getClasspath</a>, <a href="../compile/AbstractCompile.html#getDestinationDir--">getDestinationDir</a>, <a href="../compile/AbstractCompile.html#getDestinationDirectory--">getDestinationDirectory</a>, <a href="../compile/AbstractCompile.html#getSourceCompatibility--">getSourceCompatibility</a>, <a href="../compile/AbstractCompile.html#getTargetCompatibility--">getTargetCompatibility</a>, <a href="../compile/AbstractCompile.html#setClasspath-org.gradle.api.file.FileCollection-">setClasspath</a>, <a href="../compile/AbstractCompile.html#setDestinationDir-java.io.File-">setDestinationDir</a>, <a href="../compile/AbstractCompile.html#setDestinationDir-org.gradle.api.provider.Provider-">setDestinationDir</a>, <a href="../compile/AbstractCompile.html#setSourceCompatibility-java.lang.String-">setSourceCompatibility</a>, <a href="../compile/AbstractCompile.html#setTargetCompatibility-java.lang.String-">setTargetCompatibility</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.org.gradle.api.tasks.SourceTask">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;org.gradle.api.tasks.<a href="../SourceTask.html" title="class in org.gradle.api.tasks">SourceTask</a></h3>
<code><a href="../SourceTask.html#exclude-groovy.lang.Closure-">exclude</a>, <a href="../SourceTask.html#exclude-java.lang.Iterable-">exclude</a>, <a href="../SourceTask.html#exclude-java.lang.String...-">exclude</a>, <a href="../SourceTask.html#exclude-org.gradle.api.specs.Spec-">exclude</a>, <a href="../SourceTask.html#getExcludes--">getExcludes</a>, <a href="../SourceTask.html#getIncludes--">getIncludes</a>, <a href="../SourceTask.html#getPatternSet--">getPatternSet</a>, <a href="../SourceTask.html#getPatternSetFactory--">getPatternSetFactory</a>, <a href="../SourceTask.html#include-groovy.lang.Closure-">include</a>, <a href="../SourceTask.html#include-java.lang.Iterable-">include</a>, <a href="../SourceTask.html#include-java.lang.String...-">include</a>, <a href="../SourceTask.html#include-org.gradle.api.specs.Spec-">include</a>, <a href="../SourceTask.html#setExcludes-java.lang.Iterable-">setExcludes</a>, <a href="../SourceTask.html#setIncludes-java.lang.Iterable-">setIncludes</a>, <a href="../SourceTask.html#setSource-java.lang.Object-">setSource</a>, <a href="../SourceTask.html#setSource-org.gradle.api.file.FileTree-">setSource</a>, <a href="../SourceTask.html#source-java.lang.Object...-">source</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.org.gradle.api.internal.ConventionTask">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;org.gradle.api.internal.ConventionTask</h3>
<code>conventionMapping, conventionMapping, getConventionMapping</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.org.gradle.api.DefaultTask">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;org.gradle.api.<a href="../../DefaultTask.html" title="class in org.gradle.api">DefaultTask</a></h3>
<code><a href="../../DefaultTask.html#compareTo-org.gradle.api.Task-">compareTo</a>, <a href="../../DefaultTask.html#configure-groovy.lang.Closure-">configure</a>, <a href="../../DefaultTask.html#dependsOn-java.lang.Object...-">dependsOn</a>, <a href="../../DefaultTask.html#doFirst-groovy.lang.Closure-">doFirst</a>, <a href="../../DefaultTask.html#doFirst-java.lang.String-org.gradle.api.Action-">doFirst</a>, <a href="../../DefaultTask.html#doFirst-org.gradle.api.Action-">doFirst</a>, <a href="../../DefaultTask.html#doLast-groovy.lang.Closure-">doLast</a>, <a href="../../DefaultTask.html#doLast-java.lang.String-org.gradle.api.Action-">doLast</a>, <a href="../../DefaultTask.html#doLast-org.gradle.api.Action-">doLast</a>, <a href="../../DefaultTask.html#finalizedBy-java.lang.Object...-">finalizedBy</a>, <a href="../../DefaultTask.html#getActions--">getActions</a>, <a href="../../DefaultTask.html#getAnt--">getAnt</a>, <a href="../../DefaultTask.html#getDependsOn--">getDependsOn</a>, <a href="../../DefaultTask.html#getDescription--">getDescription</a>, <a href="../../DefaultTask.html#getDestroyables--">getDestroyables</a>, <a href="../../DefaultTask.html#getDidWork--">getDidWork</a>, <a href="../../DefaultTask.html#getEnabled--">getEnabled</a>, <a href="../../DefaultTask.html#getExtensions--">getExtensions</a>, <a href="../../DefaultTask.html#getFinalizedBy--">getFinalizedBy</a>, <a href="../../DefaultTask.html#getGroup--">getGroup</a>, <a href="../../DefaultTask.html#getInputs--">getInputs</a>, <a href="../../DefaultTask.html#getLocalState--">getLocalState</a>, <a href="../../DefaultTask.html#getLogger--">getLogger</a>, <a href="../../DefaultTask.html#getLogging--">getLogging</a>, <a href="../../DefaultTask.html#getMustRunAfter--">getMustRunAfter</a>, <a href="../../DefaultTask.html#getName--">getName</a>, <a href="../../DefaultTask.html#getOutputs--">getOutputs</a>, <a href="../../DefaultTask.html#getPath--">getPath</a>, <a href="../../DefaultTask.html#getProject--">getProject</a>, <a href="../../DefaultTask.html#getShouldRunAfter--">getShouldRunAfter</a>, <a href="../../DefaultTask.html#getState--">getState</a>, <a href="../../DefaultTask.html#getTaskDependencies--">getTaskDependencies</a>, <a href="../../DefaultTask.html#getTemporaryDir--">getTemporaryDir</a>, <a href="../../DefaultTask.html#getTimeout--">getTimeout</a>, <a href="../../DefaultTask.html#hasProperty-java.lang.String-">hasProperty</a>, <a href="../../DefaultTask.html#mustRunAfter-java.lang.Object...-">mustRunAfter</a>, <a href="../../DefaultTask.html#onlyIf-groovy.lang.Closure-">onlyIf</a>, <a href="../../DefaultTask.html#onlyIf-java.lang.String-org.gradle.api.specs.Spec-">onlyIf</a>, <a href="../../DefaultTask.html#onlyIf-org.gradle.api.specs.Spec-">onlyIf</a>, <a href="../../DefaultTask.html#property-java.lang.String-">property</a>, <a href="../../DefaultTask.html#setActions-java.util.List-">setActions</a>, <a href="../../DefaultTask.html#setDependsOn-java.lang.Iterable-">setDependsOn</a>, <a href="../../DefaultTask.html#setDescription-java.lang.String-">setDescription</a>, <a href="../../DefaultTask.html#setDidWork-boolean-">setDidWork</a>, <a href="../../DefaultTask.html#setEnabled-boolean-">setEnabled</a>, <a href="../../DefaultTask.html#setFinalizedBy-java.lang.Iterable-">setFinalizedBy</a>, <a href="../../DefaultTask.html#setGroup-java.lang.String-">setGroup</a>, <a href="../../DefaultTask.html#setMustRunAfter-java.lang.Iterable-">setMustRunAfter</a>, <a href="../../DefaultTask.html#setOnlyIf-groovy.lang.Closure-">setOnlyIf</a>, <a href="../../DefaultTask.html#setOnlyIf-java.lang.String-org.gradle.api.specs.Spec-">setOnlyIf</a>, <a href="../../DefaultTask.html#setOnlyIf-org.gradle.api.specs.Spec-">setOnlyIf</a>, <a href="../../DefaultTask.html#setProperty-java.lang.String-java.lang.Object-">setProperty</a>, <a href="../../DefaultTask.html#setShouldRunAfter-java.lang.Iterable-">setShouldRunAfter</a>, <a href="../../DefaultTask.html#shouldRunAfter-java.lang.Object...-">shouldRunAfter</a>, <a href="../../DefaultTask.html#usesService-org.gradle.api.provider.Provider-">usesService</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.org.gradle.api.internal.AbstractTask">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;org.gradle.api.internal.AbstractTask</h3>
<code>acceptServiceReferences, appendParallelSafeAction, doNotTrackState, getAsDynamicObject, getConvention, getIdentityPath, getImpliesSubProjects, getLifecycleDependencies, getOnlyIf, getReasonNotToTrackState, getReasonTaskIsIncompatibleWithConfigurationCache, getRequiredServices, getServices, getSharedResources, getStandardOutputCapture, getTaskActions, getTaskIdentity, getTemporaryDirFactory, hasTaskActions, injectIntoNewInstance, isCompatibleWithConfigurationCache, isEnabled, isHasCustomActions, notCompatibleWithConfigurationCache, prependParallelSafeAction, setImpliesSubProjects</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>clone, equals, finalize, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.org.gradle.api.Task">
<!--   -->
</a>
<h3>Methods inherited from interface&nbsp;org.gradle.api.<a href="../../Task.html" title="interface in org.gradle.api">Task</a></h3>
<code><a href="../../Task.html#doNotTrackState-java.lang.String-">doNotTrackState</a>, <a href="../../Task.html#getConvention--">getConvention</a>, <a href="../../Task.html#notCompatibleWithConfigurationCache-java.lang.String-">notCompatibleWithConfigurationCache</a></code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a name="ScalaCompile--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>ScalaCompile</h4>
<pre>public&nbsp;ScalaCompile()</pre>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="getScalaCompileOptions--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getScalaCompileOptions</h4>
<pre class="methodSignature">public&nbsp;<a href="ScalaCompileOptions.html" title="class in org.gradle.api.tasks.scala">ScalaCompileOptions</a>&nbsp;getScalaCompileOptions()</pre>
<div class="block"><span class="descfrmTypeLabel">Description copied from class:&nbsp;<code><a href="../../../language/scala/tasks/AbstractScalaCompile.html#getScalaCompileOptions--">AbstractScalaCompile</a></code></span></div>
<div class="block">Returns the Scala compilation options.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code><a href="../../../language/scala/tasks/AbstractScalaCompile.html#getScalaCompileOptions--">getScalaCompileOptions</a></code>&nbsp;in class&nbsp;<code><a href="../../../language/scala/tasks/AbstractScalaCompile.html" title="class in org.gradle.language.scala.tasks">AbstractScalaCompile</a></code></dd>
</dl>
</li>
</ul>
<a name="getScalaClasspath--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getScalaClasspath</h4>
<pre class="methodSignature"><a href="../Classpath.html" title="annotation in org.gradle.api.tasks">@Classpath</a>
public&nbsp;<a href="../../file/FileCollection.html" title="interface in org.gradle.api.file">FileCollection</a>&nbsp;getScalaClasspath()</pre>
<div class="block">Returns the classpath to use to load the Scala compiler.</div>
</li>
</ul>
<a name="setScalaClasspath-org.gradle.api.file.FileCollection-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setScalaClasspath</h4>
<pre class="methodSignature">public&nbsp;void&nbsp;setScalaClasspath&#8203;(<a href="../../file/FileCollection.html" title="interface in org.gradle.api.file">FileCollection</a>&nbsp;scalaClasspath)</pre>
</li>
</ul>
<a name="getScalaCompilerPlugins--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getScalaCompilerPlugins</h4>
<pre class="methodSignature"><a href="../Classpath.html" title="annotation in org.gradle.api.tasks">@Classpath</a>
public&nbsp;<a href="../../file/FileCollection.html" title="interface in org.gradle.api.file">FileCollection</a>&nbsp;getScalaCompilerPlugins()</pre>
<div class="block">Returns the Scala compiler plugins to use.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>6.4</dd>
</dl>
</li>
</ul>
<a name="setScalaCompilerPlugins-org.gradle.api.file.FileCollection-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setScalaCompilerPlugins</h4>
<pre class="methodSignature">public&nbsp;void&nbsp;setScalaCompilerPlugins&#8203;(<a href="../../file/FileCollection.html" title="interface in org.gradle.api.file">FileCollection</a>&nbsp;scalaCompilerPlugins)</pre>
<div class="block">Sets the Scala compiler plugins to use.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>scalaCompilerPlugins</code> - Collection of Scala compiler plugins.</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>6.4</dd>
</dl>
</li>
</ul>
<a name="createSpec--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createSpec</h4>
<pre class="methodSignature">protected&nbsp;org.gradle.api.internal.tasks.scala.ScalaJavaJointCompileSpec&nbsp;createSpec()</pre>
<dl>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code><a href="../../../language/scala/tasks/AbstractScalaCompile.html#createSpec--">createSpec</a></code>&nbsp;in class&nbsp;<code><a href="../../../language/scala/tasks/AbstractScalaCompile.html" title="class in org.gradle.language.scala.tasks">AbstractScalaCompile</a></code></dd>
</dl>
</li>
</ul>
<a name="getZincClasspath--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getZincClasspath</h4>
<pre class="methodSignature"><a href="../Classpath.html" title="annotation in org.gradle.api.tasks">@Classpath</a>
public&nbsp;<a href="../../file/FileCollection.html" title="interface in org.gradle.api.file">FileCollection</a>&nbsp;getZincClasspath()</pre>
<div class="block">Returns the classpath to use to load the Zinc incremental compiler. This compiler in turn loads the Scala compiler.</div>
</li>
</ul>
<a name="setZincClasspath-org.gradle.api.file.FileCollection-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setZincClasspath</h4>
<pre class="methodSignature">public&nbsp;void&nbsp;setZincClasspath&#8203;(<a href="../../file/FileCollection.html" title="interface in org.gradle.api.file">FileCollection</a>&nbsp;zincClasspath)</pre>
</li>
</ul>
<a name="setCompiler-org.gradle.language.base.internal.compile.Compiler-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCompiler</h4>
<pre class="methodSignature">public&nbsp;void&nbsp;setCompiler&#8203;(org.gradle.language.base.internal.compile.Compiler&lt;org.gradle.api.internal.tasks.scala.ScalaJavaJointCompileSpec&gt;&nbsp;compiler)</pre>
<div class="block">For testing only.</div>
</li>
</ul>
<a name="getCompiler-org.gradle.api.internal.tasks.scala.ScalaJavaJointCompileSpec-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCompiler</h4>
<pre class="methodSignature">protected&nbsp;org.gradle.language.base.internal.compile.Compiler&lt;org.gradle.api.internal.tasks.scala.ScalaJavaJointCompileSpec&gt;&nbsp;getCompiler&#8203;(org.gradle.api.internal.tasks.scala.ScalaJavaJointCompileSpec&nbsp;spec)</pre>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../language/scala/tasks/AbstractScalaCompile.html#getCompiler-org.gradle.api.internal.tasks.scala.ScalaJavaJointCompileSpec-">getCompiler</a></code>&nbsp;in class&nbsp;<code><a href="../../../language/scala/tasks/AbstractScalaCompile.html" title="class in org.gradle.language.scala.tasks">AbstractScalaCompile</a></code></dd>
</dl>
</li>
</ul>
<a name="assertScalaClasspathIsNonEmpty--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>assertScalaClasspathIsNonEmpty</h4>
<pre class="methodSignature">protected&nbsp;void&nbsp;assertScalaClasspathIsNonEmpty()</pre>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../../allclasses.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li><a href="#nested.class.summary">Nested</a>&nbsp;|&nbsp;</li>
<li><a href="#field.summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
