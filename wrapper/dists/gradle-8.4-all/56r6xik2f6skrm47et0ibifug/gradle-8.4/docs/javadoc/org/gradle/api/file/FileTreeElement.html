<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc -->
<title>FileTreeElement (Gradle API 8.4)</title>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<link rel="stylesheet" type="text/css" href="../../../../javadoc.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../jquery/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
<script type="text/javascript" src="../../../../jquery/jszip/dist/jszip.min.js"></script>
<script type="text/javascript" src="../../../../jquery/jszip-utils/dist/jszip-utils.min.js"></script>
<!--[if IE]>
<script type="text/javascript" src="../../../../jquery/jszip-utils/dist/jszip-utils-ie.min.js"></script>
<![endif]-->
<script type="text/javascript" src="../../../../jquery/jquery-3.6.0.min.js"></script>
<script type="text/javascript" src="../../../../jquery/jquery-ui.min.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="FileTreeElement (Gradle API 8.4)";
        }
    }
    catch(err) {
    }
//-->
var data = {"i0":6,"i1":6,"i2":6,"i3":6,"i4":6,"i5":6,"i6":6,"i7":6,"i8":6,"i9":6,"i10":6,"i11":6};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],4:["t3","Abstract Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
var pathtoroot = "../../../../";
var useModuleDirectories = false;
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="fixedNav">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses.html">All&nbsp;Classes</a></li>
</ul>
<ul class="navListSearch">
<li><label for="search">SEARCH:</label>
<input type="text" id="search" value="search" disabled="disabled">
<input type="reset" id="reset" value="reset" disabled="disabled">
</li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
</div>
<div class="navPadding">&nbsp;</div>
<script type="text/javascript"><!--
$('.navPadding').css('padding-top', $('.fixedNav').css("height"));
//-->
</script>
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle"><span class="packageLabelInType">Package</span>&nbsp;<a href="package-summary.html">org.gradle.api.file</a></div>
<h2 title="Interface FileTreeElement" class="title">Interface FileTreeElement</h2>
</div>
<div class="contentContainer">
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Known Subinterfaces:</dt>
<dd><code><a href="FileCopyDetails.html" title="interface in org.gradle.api.file">FileCopyDetails</a></code>, <code><a href="FileVisitDetails.html" title="interface in org.gradle.api.file">FileVisitDetails</a></code></dd>
</dl>
<hr>
<pre>public interface <span class="typeNameLabel">FileTreeElement</span></pre>
<div class="block">Information about a file in a directory/file tree.</div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t3" class="tableTab"><span><a href="javascript:show(4);">Abstract Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colSecond" scope="col">Method</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#copyTo-java.io.File-">copyTo</a></span>&#8203;(java.io.File&nbsp;target)</code></th>
<td class="colLast">
<div class="block">Copies this file to the given target file.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#copyTo-java.io.OutputStream-">copyTo</a></span>&#8203;(java.io.OutputStream&nbsp;output)</code></th>
<td class="colLast">
<div class="block">Copies the content of this file to an output stream.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>java.io.File</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getFile--">getFile</a></span>()</code></th>
<td class="colLast">
<div class="block">Returns the file being visited.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>long</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getLastModified--">getLastModified</a></span>()</code></th>
<td class="colLast">
<div class="block">Returns the last modified time of this file at the time of file traversal.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>int</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getMode--">getMode</a></span>()</code></th>
<td class="colLast">&nbsp;</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>java.lang.String</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getName--">getName</a></span>()</code></th>
<td class="colLast">
<div class="block">Returns the base name of this file.</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getPath--">getPath</a></span>()</code></th>
<td class="colLast">
<div class="block">Returns the path of this file, relative to the root of the containing file tree.</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code><a href="FilePermissions.html" title="interface in org.gradle.api.file">FilePermissions</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getPermissions--">getPermissions</a></span>()</code></th>
<td class="colLast">
<div class="block">Provides a read-only view of access permissions of this file.</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code><a href="RelativePath.html" title="class in org.gradle.api.file">RelativePath</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getRelativePath--">getRelativePath</a></span>()</code></th>
<td class="colLast">
<div class="block">Returns the path of this file, relative to the root of the containing file tree.</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>long</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getSize--">getSize</a></span>()</code></th>
<td class="colLast">
<div class="block">Returns the size of this file at the time of file traversal.</div>
</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#isDirectory--">isDirectory</a></span>()</code></th>
<td class="colLast">
<div class="block">Returns true if this element is a directory, or false if this element is a regular file.</div>
</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code>java.io.InputStream</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#open--">open</a></span>()</code></th>
<td class="colLast">
<div class="block">Opens this file as an input stream.</div>
</td>
</tr>
</table>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="getFile--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getFile</h4>
<pre class="methodSignature">java.io.File&nbsp;getFile()</pre>
<div class="block">Returns the file being visited.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The file. Never returns null.</dd>
</dl>
</li>
</ul>
<a name="isDirectory--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isDirectory</h4>
<pre class="methodSignature">boolean&nbsp;isDirectory()</pre>
<div class="block">Returns true if this element is a directory, or false if this element is a regular file.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>true if this element is a directory.</dd>
</dl>
</li>
</ul>
<a name="getLastModified--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getLastModified</h4>
<pre class="methodSignature">long&nbsp;getLastModified()</pre>
<div class="block">Returns the last modified time of this file at the time of file traversal.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The last modified time.</dd>
</dl>
</li>
</ul>
<a name="getSize--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getSize</h4>
<pre class="methodSignature">long&nbsp;getSize()</pre>
<div class="block">Returns the size of this file at the time of file traversal.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The size, in bytes.</dd>
</dl>
</li>
</ul>
<a name="open--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>open</h4>
<pre class="methodSignature">java.io.InputStream&nbsp;open()</pre>
<div class="block">Opens this file as an input stream. Generally, calling this method is more performant than calling <code>new
 FileInputStream(getFile())</code>.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The input stream. Never returns null. The caller is responsible for closing this stream.</dd>
</dl>
</li>
</ul>
<a name="copyTo-java.io.OutputStream-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>copyTo</h4>
<pre class="methodSignature">void&nbsp;copyTo&#8203;(java.io.OutputStream&nbsp;output)</pre>
<div class="block">Copies the content of this file to an output stream. Generally, calling this method is more performant than
 calling <code>new FileInputStream(getFile())</code>.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>output</code> - The output stream to write to. The caller is responsible for closing this stream.</dd>
</dl>
</li>
</ul>
<a name="copyTo-java.io.File-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>copyTo</h4>
<pre class="methodSignature">boolean&nbsp;copyTo&#8203;(java.io.File&nbsp;target)</pre>
<div class="block">Copies this file to the given target file. Does not copy the file if the target is already a copy of this file.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>target</code> - the target file.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>true if this file was copied, false if it was up-to-date</dd>
</dl>
</li>
</ul>
<a name="getName--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getName</h4>
<pre class="methodSignature">java.lang.String&nbsp;getName()</pre>
<div class="block">Returns the base name of this file.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The name. Never returns null.</dd>
</dl>
</li>
</ul>
<a name="getPath--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getPath</h4>
<pre class="methodSignature">java.lang.String&nbsp;getPath()</pre>
<div class="block">Returns the path of this file, relative to the root of the containing file tree. Always uses '/' as the hierarchy
 separator, regardless of platform file separator. Same as calling <code>getRelativePath().getPathString()</code>.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The path. Never returns null.</dd>
</dl>
</li>
</ul>
<a name="getRelativePath--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getRelativePath</h4>
<pre class="methodSignature"><a href="RelativePath.html" title="class in org.gradle.api.file">RelativePath</a>&nbsp;getRelativePath()</pre>
<div class="block">Returns the path of this file, relative to the root of the containing file tree.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The path. Never returns null.</dd>
</dl>
</li>
</ul>
<a name="getMode--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getMode</h4>
<pre class="methodSignature">int&nbsp;getMode()</pre>
</li>
</ul>
<a name="getPermissions--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>getPermissions</h4>
<pre class="methodSignature"><a href="../Incubating.html" title="annotation in org.gradle.api">@Incubating</a>
<a href="FilePermissions.html" title="interface in org.gradle.api.file">FilePermissions</a>&nbsp;getPermissions()</pre>
<div class="block">Provides a read-only view of access permissions of this file.
 For details see <a href="FilePermissions.html" title="interface in org.gradle.api.file"><code>FilePermissions</code></a>.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>8.3</dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
