<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc -->
<title>FileCollection (Gradle API 8.4)</title>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<link rel="stylesheet" type="text/css" href="../../../../javadoc.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../jquery/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
<script type="text/javascript" src="../../../../jquery/jszip/dist/jszip.min.js"></script>
<script type="text/javascript" src="../../../../jquery/jszip-utils/dist/jszip-utils.min.js"></script>
<!--[if IE]>
<script type="text/javascript" src="../../../../jquery/jszip-utils/dist/jszip-utils-ie.min.js"></script>
<![endif]-->
<script type="text/javascript" src="../../../../jquery/jquery-3.6.0.min.js"></script>
<script type="text/javascript" src="../../../../jquery/jquery-ui.min.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="FileCollection (Gradle API 8.4)";
        }
    }
    catch(err) {
    }
//-->
var data = {"i0":6,"i1":6,"i2":6,"i3":6,"i4":6,"i5":6,"i6":6,"i7":6,"i8":6,"i9":6,"i10":6,"i11":6,"i12":6};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],4:["t3","Abstract Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
var pathtoroot = "../../../../";
var useModuleDirectories = false;
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="fixedNav">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses.html">All&nbsp;Classes</a></li>
</ul>
<ul class="navListSearch">
<li><label for="search">SEARCH:</label>
<input type="text" id="search" value="search" disabled="disabled">
<input type="reset" id="reset" value="reset" disabled="disabled">
</li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li><a href="#nested.class.summary">Nested</a>&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
</div>
<div class="navPadding">&nbsp;</div>
<script type="text/javascript"><!--
$('.navPadding').css('padding-top', $('.fixedNav').css("height"));
//-->
</script>
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle"><span class="packageLabelInType">Package</span>&nbsp;<a href="package-summary.html">org.gradle.api.file</a></div>
<h2 title="Interface FileCollection" class="title">Interface FileCollection</h2>
</div>
<div class="contentContainer">
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Superinterfaces:</dt>
<dd><code><a href="../tasks/AntBuilderAware.html" title="interface in org.gradle.api.tasks">AntBuilderAware</a></code>, <code><a href="../Buildable.html" title="interface in org.gradle.api">Buildable</a></code>, <code>java.lang.Iterable&lt;java.io.File&gt;</code></dd>
</dl>
<dl>
<dt>All Known Subinterfaces:</dt>
<dd><code><a href="../plugins/antlr/AntlrSourceDirectorySet.html" title="interface in org.gradle.api.plugins.antlr">AntlrSourceDirectorySet</a></code>, <code><a href="ConfigurableFileCollection.html" title="interface in org.gradle.api.file">ConfigurableFileCollection</a></code>, <code><a href="ConfigurableFileTree.html" title="interface in org.gradle.api.file">ConfigurableFileTree</a></code>, <code><a href="../artifacts/Configuration.html" title="interface in org.gradle.api.artifacts">Configuration</a></code>, <code><a href="../artifacts/ConsumableConfiguration.html" title="interface in org.gradle.api.artifacts">ConsumableConfiguration</a></code>, <code><a href="../artifacts/DependencyScopeConfiguration.html" title="interface in org.gradle.api.artifacts">DependencyScopeConfiguration</a></code>, <code><a href="FileTree.html" title="interface in org.gradle.api.file">FileTree</a></code>, <code><a href="../tasks/GroovySourceDirectorySet.html" title="interface in org.gradle.api.tasks">GroovySourceDirectorySet</a></code>, <code><a href="../artifacts/ResolvableConfiguration.html" title="interface in org.gradle.api.artifacts">ResolvableConfiguration</a></code>, <code><a href="../tasks/ScalaSourceDirectorySet.html" title="interface in org.gradle.api.tasks">ScalaSourceDirectorySet</a></code>, <code><a href="SourceDirectorySet.html" title="interface in org.gradle.api.file">SourceDirectorySet</a></code>, <code><a href="../tasks/SourceSetOutput.html" title="interface in org.gradle.api.tasks">SourceSetOutput</a></code></dd>
</dl>
<hr>
<pre>public interface <span class="typeNameLabel">FileCollection</span>
extends java.lang.Iterable&lt;java.io.File&gt;, <a href="../tasks/AntBuilderAware.html" title="interface in org.gradle.api.tasks">AntBuilderAware</a>, <a href="../Buildable.html" title="interface in org.gradle.api">Buildable</a></pre>
<div class="block"><p>A <code>FileCollection</code> represents a collection of file system locations which you can query in certain ways. A file collection
 is can be used to define a classpath, or a set of source files, or to add files to an archive.</p>

 <p>There are no methods on this interface that allow the contents of the collection to be modified. However, there are a number of sub-interfaces, such as <a href="ConfigurableFileCollection.html" title="interface in org.gradle.api.file"><code>ConfigurableFileCollection</code></a> that
 allow changes to be made.</p>

 <p>A file collection may contain task outputs. The file collection tracks not just a set of files, but also the tasks that produce those files. When a file collection is used as a task input
 property, Gradle will take care of automatically adding dependencies between the consuming task and the producing tasks.</p>

 <p>You can obtain a <code>FileCollection</code> instance using <a href="../Project.html#files-java.lang.Object...-"><code>Project.files(java.lang.Object...)</code></a>.</p></div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== NESTED CLASS SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="nested.class.summary">
<!--   -->
</a>
<h3>Nested Class Summary</h3>
<table class="memberSummary" summary="Nested Class Summary table, listing nested classes, and an explanation">
<caption><span>Nested Classes</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colSecond" scope="col">Interface</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static class&nbsp;</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="FileCollection.AntType.html" title="enum in org.gradle.api.file">FileCollection.AntType</a></span></code></th>
<td class="colLast">
<div class="block">Ant types which a <code>FileCollection</code> can be mapped to.</div>
</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t3" class="tableTab"><span><a href="javascript:show(4);">Abstract Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colSecond" scope="col">Method</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>java.lang.Object</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#addToAntBuilder-java.lang.Object-java.lang.String-">addToAntBuilder</a></span>&#8203;(java.lang.Object&nbsp;builder,
               java.lang.String&nbsp;nodeName)</code></th>
<td class="colLast">
<div class="block">Adds this collection to an Ant task as a nested node.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#addToAntBuilder-java.lang.Object-java.lang.String-org.gradle.api.file.FileCollection.AntType-">addToAntBuilder</a></span>&#8203;(java.lang.Object&nbsp;builder,
               java.lang.String&nbsp;nodeName,
               <a href="FileCollection.AntType.html" title="enum in org.gradle.api.file">FileCollection.AntType</a>&nbsp;type)</code></th>
<td class="colLast">
<div class="block">Adds this collection to an Ant task as a nested node.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#contains-java.io.File-">contains</a></span>&#8203;(java.io.File&nbsp;file)</code></th>
<td class="colLast">
<div class="block">Determines whether this collection contains the given file.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code><a href="FileCollection.html" title="interface in org.gradle.api.file">FileCollection</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#filter-groovy.lang.Closure-">filter</a></span>&#8203;(<a href="https://docs.groovy-lang.org/docs/groovy-3.0.17/html/gapi/groovy/lang/Closure.html?is-external=true" title="class or interface in groovy.lang" class="externalLink">Closure</a>&nbsp;filterClosure)</code></th>
<td class="colLast">
<div class="block">Restricts the contents of this collection to those files which match the given criteria.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code><a href="FileCollection.html" title="interface in org.gradle.api.file">FileCollection</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#filter-org.gradle.api.specs.Spec-">filter</a></span>&#8203;(<a href="../specs/Spec.html" title="interface in org.gradle.api.specs">Spec</a>&lt;? super java.io.File&gt;&nbsp;filterSpec)</code></th>
<td class="colLast">
<div class="block">Restricts the contents of this collection to those files which match the given criteria.</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code><a href="FileTree.html" title="interface in org.gradle.api.file">FileTree</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getAsFileTree--">getAsFileTree</a></span>()</code></th>
<td class="colLast">
<div class="block">Converts this collection to a <a href="FileTree.html" title="interface in org.gradle.api.file"><code>FileTree</code></a>, if not already.</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getAsPath--">getAsPath</a></span>()</code></th>
<td class="colLast">
<div class="block">Returns the contents of this collection as a platform-specific path.</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code><a href="../provider/Provider.html" title="interface in org.gradle.api.provider">Provider</a>&lt;java.util.Set&lt;<a href="FileSystemLocation.html" title="interface in org.gradle.api.file">FileSystemLocation</a>&gt;&gt;</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getElements--">getElements</a></span>()</code></th>
<td class="colLast">
<div class="block">Returns the contents of this file collection as a <a href="../provider/Provider.html" title="interface in org.gradle.api.provider"><code>Provider</code></a> of <a href="FileSystemLocation.html" title="interface in org.gradle.api.file"><code>FileSystemLocation</code></a> instances.</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>java.util.Set&lt;java.io.File&gt;</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getFiles--">getFiles</a></span>()</code></th>
<td class="colLast">
<div class="block">Returns the contents of this collection as a <code>Set</code>.</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>java.io.File</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getSingleFile--">getSingleFile</a></span>()</code></th>
<td class="colLast">
<div class="block">Returns the content of this collection, asserting it contains exactly one file.</div>
</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#isEmpty--">isEmpty</a></span>()</code></th>
<td class="colLast">
<div class="block">Returns true if this collection is empty.</div>
</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code><a href="FileCollection.html" title="interface in org.gradle.api.file">FileCollection</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#minus-org.gradle.api.file.FileCollection-">minus</a></span>&#8203;(<a href="FileCollection.html" title="interface in org.gradle.api.file">FileCollection</a>&nbsp;collection)</code></th>
<td class="colLast">
<div class="block">Returns a <code>FileCollection</code> which contains the difference between this collection and the given
 collection.</div>
</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code><a href="FileCollection.html" title="interface in org.gradle.api.file">FileCollection</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#plus-org.gradle.api.file.FileCollection-">plus</a></span>&#8203;(<a href="FileCollection.html" title="interface in org.gradle.api.file">FileCollection</a>&nbsp;collection)</code></th>
<td class="colLast">
<div class="block">Returns a <code>FileCollection</code> which contains the union of this collection and the given collection.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.org.gradle.api.Buildable">
<!--   -->
</a>
<h3>Methods inherited from interface&nbsp;org.gradle.api.<a href="../Buildable.html" title="interface in org.gradle.api">Buildable</a></h3>
<code><a href="../Buildable.html#getBuildDependencies--">getBuildDependencies</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Iterable">
<!--   -->
</a>
<h3>Methods inherited from interface&nbsp;java.lang.Iterable</h3>
<code>forEach, iterator, spliterator</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="getSingleFile--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getSingleFile</h4>
<pre class="methodSignature">java.io.File&nbsp;getSingleFile()
                    throws java.lang.IllegalStateException</pre>
<div class="block">Returns the content of this collection, asserting it contains exactly one file.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The file.</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code>java.lang.IllegalStateException</code> - when this collection does not contain exactly one file.</dd>
</dl>
</li>
</ul>
<a name="getFiles--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getFiles</h4>
<pre class="methodSignature">java.util.Set&lt;java.io.File&gt;&nbsp;getFiles()</pre>
<div class="block">Returns the contents of this collection as a <code>Set</code>. The contents of a file collection may change over time.

 <p>Note that this method returns <code>File</code> objects that represent locations on the file system. These <code>File</code> objects do not necessarily refer to regular files.
 Depending on the implementation of this file collection and how it has been configured, the returned set may contain directories, or missing files, or any other kind of
 file system element.
 </p></div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The files. Returns an empty set if this collection is empty.</dd>
</dl>
</li>
</ul>
<a name="contains-java.io.File-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>contains</h4>
<pre class="methodSignature">boolean&nbsp;contains&#8203;(java.io.File&nbsp;file)</pre>
<div class="block">Determines whether this collection contains the given file. Generally, this method is more efficient than calling
 <code>getFiles().contains(file)</code>.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>file</code> - The file to check for.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>true if this collection contains the given file, false otherwise.</dd>
</dl>
</li>
</ul>
<a name="getAsPath--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getAsPath</h4>
<pre class="methodSignature">java.lang.String&nbsp;getAsPath()</pre>
<div class="block">Returns the contents of this collection as a platform-specific path. This can be used, for example, in an Ant
 &lt;path&gt; element.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The path. Returns an empty string if this collection is empty.</dd>
</dl>
</li>
</ul>
<a name="plus-org.gradle.api.file.FileCollection-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>plus</h4>
<pre class="methodSignature"><a href="FileCollection.html" title="interface in org.gradle.api.file">FileCollection</a>&nbsp;plus&#8203;(<a href="FileCollection.html" title="interface in org.gradle.api.file">FileCollection</a>&nbsp;collection)</pre>
<div class="block"><p>Returns a <code>FileCollection</code> which contains the union of this collection and the given collection. The
 returned collection is live, and tracks changes to both source collections.</p>

 <p>You can call this method in your build script using the <code>+</code> operator.</p></div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>collection</code> - The other collection. Should not be null.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>A new collection containing the union.</dd>
</dl>
</li>
</ul>
<a name="minus-org.gradle.api.file.FileCollection-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>minus</h4>
<pre class="methodSignature"><a href="FileCollection.html" title="interface in org.gradle.api.file">FileCollection</a>&nbsp;minus&#8203;(<a href="FileCollection.html" title="interface in org.gradle.api.file">FileCollection</a>&nbsp;collection)</pre>
<div class="block"><p>Returns a <code>FileCollection</code> which contains the difference between this collection and the given
 collection. The returned collection is live, and tracks changes to both source collections.</p>

 <p>You can call this method in your build script using the <code>-</code> operator.</p></div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>collection</code> - The other collection. Should not be null.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>A new collection containing the difference.</dd>
</dl>
</li>
</ul>
<a name="filter-groovy.lang.Closure-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>filter</h4>
<pre class="methodSignature"><a href="FileCollection.html" title="interface in org.gradle.api.file">FileCollection</a>&nbsp;filter&#8203;(<a href="https://docs.groovy-lang.org/docs/groovy-3.0.17/html/gapi/groovy/lang/Closure.html?is-external=true" title="class or interface in groovy.lang" class="externalLink">Closure</a>&nbsp;filterClosure)</pre>
<div class="block"><p>Restricts the contents of this collection to those files which match the given criteria. The filtered
 collection is live, so that it reflects any changes to this collection.</p>

 <p>The given closure is passed the @{link File} as a parameter, and should return a boolean value. The closure should return <code>true</code>
 to include the file in the result and <code>false</code> to exclude the file from the result.</p></div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>filterClosure</code> - The closure to use to select the contents of the filtered collection.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The filtered collection.</dd>
</dl>
</li>
</ul>
<a name="filter-org.gradle.api.specs.Spec-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>filter</h4>
<pre class="methodSignature"><a href="FileCollection.html" title="interface in org.gradle.api.file">FileCollection</a>&nbsp;filter&#8203;(<a href="../specs/Spec.html" title="interface in org.gradle.api.specs">Spec</a>&lt;? super java.io.File&gt;&nbsp;filterSpec)</pre>
<div class="block"><p>Restricts the contents of this collection to those files which match the given criteria. The filtered
 collection is live, so that it reflects any changes to this collection.</p></div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>filterSpec</code> - The criteria to use to select the contents of the filtered collection.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The filtered collection.</dd>
</dl>
</li>
</ul>
<a name="isEmpty--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isEmpty</h4>
<pre class="methodSignature">boolean&nbsp;isEmpty()</pre>
<div class="block">Returns true if this collection is empty. Generally, calling this method is more efficient than calling <code>
 getFiles().isEmpty()</code>.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>true if this collection is empty, false otherwise.</dd>
</dl>
</li>
</ul>
<a name="getAsFileTree--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getAsFileTree</h4>
<pre class="methodSignature"><a href="FileTree.html" title="interface in org.gradle.api.file">FileTree</a>&nbsp;getAsFileTree()</pre>
<div class="block">Converts this collection to a <a href="FileTree.html" title="interface in org.gradle.api.file"><code>FileTree</code></a>, if not already. For each file in this collection, the resulting file
 tree will contain the source file at the root of the tree. For each directory in this collection, the resulting
 file tree will contain all the files under the source directory.

 <p>The returned <a href="FileTree.html" title="interface in org.gradle.api.file"><code>FileTree</code></a> is live, and tracks changes to this file collection and the producer tasks of this file collection.</p></div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>this collection as a <a href="FileTree.html" title="interface in org.gradle.api.file"><code>FileTree</code></a>. Never returns null.</dd>
</dl>
</li>
</ul>
<a name="getElements--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getElements</h4>
<pre class="methodSignature"><a href="../provider/Provider.html" title="interface in org.gradle.api.provider">Provider</a>&lt;java.util.Set&lt;<a href="FileSystemLocation.html" title="interface in org.gradle.api.file">FileSystemLocation</a>&gt;&gt;&nbsp;getElements()</pre>
<div class="block">Returns the contents of this file collection as a <a href="../provider/Provider.html" title="interface in org.gradle.api.provider"><code>Provider</code></a> of <a href="FileSystemLocation.html" title="interface in org.gradle.api.file"><code>FileSystemLocation</code></a> instances. See <a href="#getFiles--"><code>getFiles()</code></a> for more details.

 <p>The returned <a href="../provider/Provider.html" title="interface in org.gradle.api.provider"><code>Provider</code></a> is live, and tracks changes to this file collection and the producer tasks of this file collection.</p></div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>5.6</dd>
</dl>
</li>
</ul>
<a name="addToAntBuilder-java.lang.Object-java.lang.String-org.gradle.api.file.FileCollection.AntType-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>addToAntBuilder</h4>
<pre class="methodSignature">void&nbsp;addToAntBuilder&#8203;(java.lang.Object&nbsp;builder,
                     java.lang.String&nbsp;nodeName,
                     <a href="FileCollection.AntType.html" title="enum in org.gradle.api.file">FileCollection.AntType</a>&nbsp;type)</pre>
<div class="block">Adds this collection to an Ant task as a nested node. The given type determines how this collection is added:

 <ul>

 <li><a href="FileCollection.AntType.html#MatchingTask"><code>FileCollection.AntType.MatchingTask</code></a>: adds this collection to an Ant MatchingTask. The collection is converted to a
 set of source directories and include and exclude patterns. The source directories as added as an Ant Path with
 the given node name. The patterns are added using 'include' and 'exclude' nodes.</li>

 <li><a href="FileCollection.AntType.html#FileSet"><code>FileCollection.AntType.FileSet</code></a>: adds this collection as zero or more Ant FileSets with the given node name.</li>

 <li><a href="FileCollection.AntType.html#ResourceCollection"><code>FileCollection.AntType.ResourceCollection</code></a>: adds this collection as zero or more Ant ResourceCollections with the
 given node name.</li>

 </ul>

 You should prefer using <a href="FileCollection.AntType.html#ResourceCollection"><code>FileCollection.AntType.ResourceCollection</code></a>, if the target Ant task supports it, as this is
 generally the most efficient. Using the other types may involve copying the contents of this collection to a
 temporary directory.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>builder</code> - The builder to add this collection to.</dd>
<dd><code>nodeName</code> - The target node name.</dd>
<dd><code>type</code> - The target Ant type</dd>
</dl>
</li>
</ul>
<a name="addToAntBuilder-java.lang.Object-java.lang.String-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>addToAntBuilder</h4>
<pre class="methodSignature">java.lang.Object&nbsp;addToAntBuilder&#8203;(java.lang.Object&nbsp;builder,
                                 java.lang.String&nbsp;nodeName)</pre>
<div class="block">Adds this collection to an Ant task as a nested node. Equivalent to calling <code>addToAntBuilder(builder,
 nodeName,AntType.ResourceCollection)</code>.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../tasks/AntBuilderAware.html#addToAntBuilder-java.lang.Object-java.lang.String-">addToAntBuilder</a></code>&nbsp;in interface&nbsp;<code><a href="../tasks/AntBuilderAware.html" title="interface in org.gradle.api.tasks">AntBuilderAware</a></code></dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li><a href="#nested.class.summary">Nested</a>&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
