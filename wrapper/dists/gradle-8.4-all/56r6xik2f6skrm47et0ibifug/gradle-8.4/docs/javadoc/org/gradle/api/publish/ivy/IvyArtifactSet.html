<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc -->
<title>IvyArtifactSet (Gradle API 8.4)</title>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<link rel="stylesheet" type="text/css" href="../../../../../javadoc.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../jquery/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
<script type="text/javascript" src="../../../../../jquery/jszip/dist/jszip.min.js"></script>
<script type="text/javascript" src="../../../../../jquery/jszip-utils/dist/jszip-utils.min.js"></script>
<!--[if IE]>
<script type="text/javascript" src="../../../../../jquery/jszip-utils/dist/jszip-utils-ie.min.js"></script>
<![endif]-->
<script type="text/javascript" src="../../../../../jquery/jquery-3.6.0.min.js"></script>
<script type="text/javascript" src="../../../../../jquery/jquery-ui.min.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="IvyArtifactSet (Gradle API 8.4)";
        }
    }
    catch(err) {
    }
//-->
var data = {"i0":6,"i1":6};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],4:["t3","Abstract Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
var pathtoroot = "../../../../../";
var useModuleDirectories = false;
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="fixedNav">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../../allclasses.html">All&nbsp;Classes</a></li>
</ul>
<ul class="navListSearch">
<li><label for="search">SEARCH:</label>
<input type="text" id="search" value="search" disabled="disabled">
<input type="reset" id="reset" value="reset" disabled="disabled">
</li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
</div>
<div class="navPadding">&nbsp;</div>
<script type="text/javascript"><!--
$('.navPadding').css('padding-top', $('.fixedNav').css("height"));
//-->
</script>
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle"><span class="packageLabelInType">Package</span>&nbsp;<a href="package-summary.html">org.gradle.api.publish.ivy</a></div>
<h2 title="Interface IvyArtifactSet" class="title">Interface IvyArtifactSet</h2>
</div>
<div class="contentContainer">
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Superinterfaces:</dt>
<dd><code>java.util.Collection&lt;<a href="IvyArtifact.html" title="interface in org.gradle.api.publish.ivy">IvyArtifact</a>&gt;</code>, <code><a href="../../DomainObjectCollection.html" title="interface in org.gradle.api">DomainObjectCollection</a>&lt;<a href="IvyArtifact.html" title="interface in org.gradle.api.publish.ivy">IvyArtifact</a>&gt;</code>, <code><a href="../../DomainObjectSet.html" title="interface in org.gradle.api">DomainObjectSet</a>&lt;<a href="IvyArtifact.html" title="interface in org.gradle.api.publish.ivy">IvyArtifact</a>&gt;</code>, <code>java.lang.Iterable&lt;<a href="IvyArtifact.html" title="interface in org.gradle.api.publish.ivy">IvyArtifact</a>&gt;</code>, <code>java.util.Set&lt;<a href="IvyArtifact.html" title="interface in org.gradle.api.publish.ivy">IvyArtifact</a>&gt;</code></dd>
</dl>
<hr>
<pre>public interface <span class="typeNameLabel">IvyArtifactSet</span>
extends <a href="../../DomainObjectSet.html" title="interface in org.gradle.api">DomainObjectSet</a>&lt;<a href="IvyArtifact.html" title="interface in org.gradle.api.publish.ivy">IvyArtifact</a>&gt;</pre>
<div class="block">A Collection of <a href="IvyArtifact.html" title="interface in org.gradle.api.publish.ivy"><code>IvyArtifact</code></a>s to be included in an <a href="IvyPublication.html" title="interface in org.gradle.api.publish.ivy"><code>IvyPublication</code></a>.

 Being a <a href="../../DomainObjectSet.html" title="interface in org.gradle.api"><code>DomainObjectSet</code></a>, an <code>IvyArtifactSet</code> provides convenient methods for querying, filtering, and applying actions to the set of <a href="IvyArtifact.html" title="interface in org.gradle.api.publish.ivy"><code>IvyArtifact</code></a>s.

 <pre class='autoTested'>
 plugins {
     id 'ivy-publish'
 }

 def publication = publishing.publications.create("my-pub", IvyPublication)
 def artifacts = publication.artifacts

 artifacts.matching({
     it.type == "source"
 }).all({
     it.extension = "src.jar"
 })
 </pre></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../DomainObjectSet.html" title="interface in org.gradle.api"><code>DomainObjectSet</code></a></dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t3" class="tableTab"><span><a href="javascript:show(4);">Abstract Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colSecond" scope="col">Method</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code><a href="IvyArtifact.html" title="interface in org.gradle.api.publish.ivy">IvyArtifact</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#artifact-java.lang.Object-">artifact</a></span>&#8203;(java.lang.Object&nbsp;source)</code></th>
<td class="colLast">
<div class="block">Creates and adds a <a href="IvyArtifact.html" title="interface in org.gradle.api.publish.ivy"><code>IvyArtifact</code></a> to the set.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code><a href="IvyArtifact.html" title="interface in org.gradle.api.publish.ivy">IvyArtifact</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#artifact-java.lang.Object-org.gradle.api.Action-">artifact</a></span>&#8203;(java.lang.Object&nbsp;source,
        <a href="../../Action.html" title="interface in org.gradle.api">Action</a>&lt;? super <a href="IvyArtifact.html" title="interface in org.gradle.api.publish.ivy">IvyArtifact</a>&gt;&nbsp;config)</code></th>
<td class="colLast">
<div class="block">Creates and adds a <a href="IvyArtifact.html" title="interface in org.gradle.api.publish.ivy"><code>IvyArtifact</code></a> to the set, which is configured by the associated action.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.util.Collection">
<!--   -->
</a>
<h3>Methods inherited from interface&nbsp;java.util.Collection</h3>
<code>parallelStream, removeIf, stream, toArray</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.org.gradle.api.DomainObjectCollection">
<!--   -->
</a>
<h3>Methods inherited from interface&nbsp;org.gradle.api.<a href="../../DomainObjectCollection.html" title="interface in org.gradle.api">DomainObjectCollection</a></h3>
<code><a href="../../DomainObjectCollection.html#addAllLater-org.gradle.api.provider.Provider-">addAllLater</a>, <a href="../../DomainObjectCollection.html#addLater-org.gradle.api.provider.Provider-">addLater</a>, <a href="../../DomainObjectCollection.html#all-groovy.lang.Closure-">all</a>, <a href="../../DomainObjectCollection.html#all-org.gradle.api.Action-">all</a>, <a href="../../DomainObjectCollection.html#configureEach-org.gradle.api.Action-">configureEach</a>, <a href="../../DomainObjectCollection.html#whenObjectAdded-groovy.lang.Closure-">whenObjectAdded</a>, <a href="../../DomainObjectCollection.html#whenObjectAdded-org.gradle.api.Action-">whenObjectAdded</a>, <a href="../../DomainObjectCollection.html#whenObjectRemoved-groovy.lang.Closure-">whenObjectRemoved</a>, <a href="../../DomainObjectCollection.html#whenObjectRemoved-org.gradle.api.Action-">whenObjectRemoved</a>, <a href="../../DomainObjectCollection.html#withType-java.lang.Class-groovy.lang.Closure-">withType</a>, <a href="../../DomainObjectCollection.html#withType-java.lang.Class-org.gradle.api.Action-">withType</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.org.gradle.api.DomainObjectSet">
<!--   -->
</a>
<h3>Methods inherited from interface&nbsp;org.gradle.api.<a href="../../DomainObjectSet.html" title="interface in org.gradle.api">DomainObjectSet</a></h3>
<code><a href="../../DomainObjectSet.html#findAll-groovy.lang.Closure-">findAll</a>, <a href="../../DomainObjectSet.html#matching-groovy.lang.Closure-">matching</a>, <a href="../../DomainObjectSet.html#matching-org.gradle.api.specs.Spec-">matching</a>, <a href="../../DomainObjectSet.html#withType-java.lang.Class-">withType</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Iterable">
<!--   -->
</a>
<h3>Methods inherited from interface&nbsp;java.lang.Iterable</h3>
<code>forEach</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.util.Set">
<!--   -->
</a>
<h3>Methods inherited from interface&nbsp;java.util.Set</h3>
<code>add, addAll, clear, contains, containsAll, equals, hashCode, isEmpty, iterator, remove, removeAll, retainAll, size, spliterator, toArray, toArray</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="artifact-java.lang.Object-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>artifact</h4>
<pre class="methodSignature"><a href="IvyArtifact.html" title="interface in org.gradle.api.publish.ivy">IvyArtifact</a>&nbsp;artifact&#8203;(java.lang.Object&nbsp;source)</pre>
<div class="block">Creates and adds a <a href="IvyArtifact.html" title="interface in org.gradle.api.publish.ivy"><code>IvyArtifact</code></a> to the set.

 The semantics of this method are the same as <a href="IvyPublication.html#artifact-java.lang.Object-"><code>IvyPublication.artifact(Object)</code></a>.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>source</code> - The source of the artifact content.</dd>
</dl>
</li>
</ul>
<a name="artifact-java.lang.Object-org.gradle.api.Action-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>artifact</h4>
<pre class="methodSignature"><a href="IvyArtifact.html" title="interface in org.gradle.api.publish.ivy">IvyArtifact</a>&nbsp;artifact&#8203;(java.lang.Object&nbsp;source,
                     <a href="../../Action.html" title="interface in org.gradle.api">Action</a>&lt;? super <a href="IvyArtifact.html" title="interface in org.gradle.api.publish.ivy">IvyArtifact</a>&gt;&nbsp;config)</pre>
<div class="block">Creates and adds a <a href="IvyArtifact.html" title="interface in org.gradle.api.publish.ivy"><code>IvyArtifact</code></a> to the set, which is configured by the associated action.

 The semantics of this method are the same as <a href="IvyPublication.html#artifact-java.lang.Object-org.gradle.api.Action-"><code>IvyPublication.artifact(Object, Action)</code></a>.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>source</code> - The source of the artifact.</dd>
<dd><code>config</code> - An action to configure the values of the constructed <a href="IvyArtifact.html" title="interface in org.gradle.api.publish.ivy"><code>IvyArtifact</code></a>.</dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../../allclasses.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
