<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc -->
<title>TestFailureDetails (Gradle API 8.4)</title>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<link rel="stylesheet" type="text/css" href="../../../../../javadoc.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../jquery/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
<script type="text/javascript" src="../../../../../jquery/jszip/dist/jszip.min.js"></script>
<script type="text/javascript" src="../../../../../jquery/jszip-utils/dist/jszip-utils.min.js"></script>
<!--[if IE]>
<script type="text/javascript" src="../../../../../jquery/jszip-utils/dist/jszip-utils-ie.min.js"></script>
<![endif]-->
<script type="text/javascript" src="../../../../../jquery/jquery-3.6.0.min.js"></script>
<script type="text/javascript" src="../../../../../jquery/jquery-ui.min.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="TestFailureDetails (Gradle API 8.4)";
        }
    }
    catch(err) {
    }
//-->
var data = {"i0":6,"i1":6,"i2":6,"i3":6,"i4":6,"i5":6,"i6":6,"i7":6,"i8":6};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],4:["t3","Abstract Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
var pathtoroot = "../../../../../";
var useModuleDirectories = false;
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="fixedNav">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../../allclasses.html">All&nbsp;Classes</a></li>
</ul>
<ul class="navListSearch">
<li><label for="search">SEARCH:</label>
<input type="text" id="search" value="search" disabled="disabled">
<input type="reset" id="reset" value="reset" disabled="disabled">
</li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
</div>
<div class="navPadding">&nbsp;</div>
<script type="text/javascript"><!--
$('.navPadding').css('padding-top', $('.fixedNav').css("height"));
//-->
</script>
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle"><span class="packageLabelInType">Package</span>&nbsp;<a href="package-summary.html">org.gradle.api.tasks.testing</a></div>
<h2 title="Interface TestFailureDetails" class="title">Interface TestFailureDetails</h2>
</div>
<div class="contentContainer">
<div class="description">
<ul class="blockList">
<li class="blockList">
<hr>
<pre><a href="../../Incubating.html" title="annotation in org.gradle.api">@Incubating</a>
public interface <span class="typeNameLabel">TestFailureDetails</span></pre>
<div class="block">Contains serializable structural information about a test failure.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>7.6</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><code>TestFailure</code></dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t3" class="tableTab"><span><a href="javascript:show(4);">Abstract Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colSecond" scope="col">Method</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getActual--">getActual</a></span>()</code></th>
<td class="colLast">
<div class="block">Returns a string representation of the actual value for an assertion failure.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>byte[]</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getActualContent--">getActualContent</a></span>()</code></th>
<td class="colLast">
<div class="block">Returns the actual content of a file comparison assertion failure.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getClassName--">getClassName</a></span>()</code></th>
<td class="colLast">
<div class="block">The fully-qualified name of the underlying exception type.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>java.lang.String</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getExpected--">getExpected</a></span>()</code></th>
<td class="colLast">
<div class="block">Returns a string representation of the expected value for an assertion failure.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>byte[]</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getExpectedContent--">getExpectedContent</a></span>()</code></th>
<td class="colLast">
<div class="block">Returns the expected content of a file comparison assertion failure.</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>java.lang.String</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getMessage--">getMessage</a></span>()</code></th>
<td class="colLast">
<div class="block">Returns the failure message.</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getStacktrace--">getStacktrace</a></span>()</code></th>
<td class="colLast">
<div class="block">Returns the stacktrace of the failure.</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#isAssertionFailure--">isAssertionFailure</a></span>()</code></th>
<td class="colLast">
<div class="block">Returns true if the represented failure is recognized as an assertion failure.</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#isFileComparisonFailure--">isFileComparisonFailure</a></span>()</code></th>
<td class="colLast">
<div class="block">Returns true if the represented failure is recognized as a file comparison failure.</div>
</td>
</tr>
</table>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="getMessage--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getMessage</h4>
<pre class="methodSignature">@Nullable
java.lang.String&nbsp;getMessage()</pre>
<div class="block">Returns the failure message.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the failure message</dd>
</dl>
</li>
</ul>
<a name="getClassName--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getClassName</h4>
<pre class="methodSignature">java.lang.String&nbsp;getClassName()</pre>
<div class="block">The fully-qualified name of the underlying exception type.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the class name</dd>
</dl>
</li>
</ul>
<a name="getStacktrace--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getStacktrace</h4>
<pre class="methodSignature">java.lang.String&nbsp;getStacktrace()</pre>
<div class="block">Returns the stacktrace of the failure.
 <p>
 The instances are created on the test worker side allowing the clients not to deal with non-serializable exceptions.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the stacktrace string</dd>
</dl>
</li>
</ul>
<a name="isAssertionFailure--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isAssertionFailure</h4>
<pre class="methodSignature">boolean&nbsp;isAssertionFailure()</pre>
<div class="block">Returns true if the represented failure is recognized as an assertion failure.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd><code>true</code> for assertion failures</dd>
</dl>
</li>
</ul>
<a name="isFileComparisonFailure--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isFileComparisonFailure</h4>
<pre class="methodSignature">boolean&nbsp;isFileComparisonFailure()</pre>
<div class="block">Returns true if the represented failure is recognized as a file comparison failure.
 <p>
 If this field is <code>true</code>, then the <a href="#getExpectedContent--"><code>getExpectedContent()</code></a> and <a href="#getActualContent--"><code>getActualContent()</code></a> methods <i>might</i> return non-null values.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd><code>true</code> if this failure is a file comparison failure</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>8.3</dd>
</dl>
</li>
</ul>
<a name="getExpectedContent--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getExpectedContent</h4>
<pre class="methodSignature">@Nullable
byte[]&nbsp;getExpectedContent()</pre>
<div class="block">Returns the expected content of a file comparison assertion failure.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the expected file contents or <code>null</code> if the test framework doesn't supply detailed information on assertion failures, or it is not a file comparison failure</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>8.3</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="#isFileComparisonFailure--"><code>isFileComparisonFailure()</code></a></dd>
</dl>
</li>
</ul>
<a name="getActualContent--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getActualContent</h4>
<pre class="methodSignature">@Nullable
byte[]&nbsp;getActualContent()</pre>
<div class="block">Returns the actual content of a file comparison assertion failure.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the expected file contents or <code>null</code> if the test framework doesn't supply detailed information on assertion failures, or it is not a file comparison failure</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>8.3</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="#isFileComparisonFailure--"><code>isFileComparisonFailure()</code></a></dd>
</dl>
</li>
</ul>
<a name="getExpected--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getExpected</h4>
<pre class="methodSignature">@Nullable
java.lang.String&nbsp;getExpected()</pre>
<div class="block">Returns a string representation of the expected value for an assertion failure.
 <p>
 If the current instance does not represent an assertion failure, or the test failure doesn't provide any information about expected and actual values then the method returns <code>null</code>.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The expected value</dd>
</dl>
</li>
</ul>
<a name="getActual--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>getActual</h4>
<pre class="methodSignature">@Nullable
java.lang.String&nbsp;getActual()</pre>
<div class="block">Returns a string representation of the actual value for an assertion failure.
 <p>
 If the current instance does not represent an assertion failure, or the test failure doesn't provide any information about expected and actual values then the method returns <code>null</code>.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The actual value</dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../../allclasses.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
