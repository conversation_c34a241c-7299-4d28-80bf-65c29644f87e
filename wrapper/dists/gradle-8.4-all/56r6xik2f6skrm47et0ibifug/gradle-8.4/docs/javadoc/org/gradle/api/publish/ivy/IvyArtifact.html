<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc -->
<title>IvyArtifact (Gradle API 8.4)</title>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<link rel="stylesheet" type="text/css" href="../../../../../javadoc.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../jquery/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
<script type="text/javascript" src="../../../../../jquery/jszip/dist/jszip.min.js"></script>
<script type="text/javascript" src="../../../../../jquery/jszip-utils/dist/jszip-utils.min.js"></script>
<!--[if IE]>
<script type="text/javascript" src="../../../../../jquery/jszip-utils/dist/jszip-utils-ie.min.js"></script>
<![endif]-->
<script type="text/javascript" src="../../../../../jquery/jquery-3.6.0.min.js"></script>
<script type="text/javascript" src="../../../../../jquery/jquery-ui.min.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="IvyArtifact (Gradle API 8.4)";
        }
    }
    catch(err) {
    }
//-->
var data = {"i0":6,"i1":6,"i2":6,"i3":6,"i4":6,"i5":6,"i6":6,"i7":6,"i8":6,"i9":6};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],4:["t3","Abstract Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
var pathtoroot = "../../../../../";
var useModuleDirectories = false;
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="fixedNav">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../../allclasses.html">All&nbsp;Classes</a></li>
</ul>
<ul class="navListSearch">
<li><label for="search">SEARCH:</label>
<input type="text" id="search" value="search" disabled="disabled">
<input type="reset" id="reset" value="reset" disabled="disabled">
</li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
</div>
<div class="navPadding">&nbsp;</div>
<script type="text/javascript"><!--
$('.navPadding').css('padding-top', $('.fixedNav').css("height"));
//-->
</script>
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle"><span class="packageLabelInType">Package</span>&nbsp;<a href="package-summary.html">org.gradle.api.publish.ivy</a></div>
<h2 title="Interface IvyArtifact" class="title">Interface IvyArtifact</h2>
</div>
<div class="contentContainer">
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Superinterfaces:</dt>
<dd><code><a href="../../Buildable.html" title="interface in org.gradle.api">Buildable</a></code>, <code><a href="../PublicationArtifact.html" title="interface in org.gradle.api.publish">PublicationArtifact</a></code></dd>
</dl>
<hr>
<pre>public interface <span class="typeNameLabel">IvyArtifact</span>
extends <a href="../PublicationArtifact.html" title="interface in org.gradle.api.publish">PublicationArtifact</a></pre>
<div class="block">An artifact published as part of a <a href="IvyPublication.html" title="interface in org.gradle.api.publish.ivy"><code>IvyPublication</code></a>.</div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t3" class="tableTab"><span><a href="javascript:show(4);">Abstract Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colSecond" scope="col">Method</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getClassifier--">getClassifier</a></span>()</code></th>
<td class="colLast">
<div class="block">The classifier used to publish the artifact file.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>java.lang.String</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getConf--">getConf</a></span>()</code></th>
<td class="colLast">
<div class="block">A comma separated list of public configurations in which this artifact is published.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getExtension--">getExtension</a></span>()</code></th>
<td class="colLast">
<div class="block">The extension used to publish the artifact file, never <code>null</code>.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>java.lang.String</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getName--">getName</a></span>()</code></th>
<td class="colLast">
<div class="block">The name used to publish the artifact file, never <code>null</code>.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getType--">getType</a></span>()</code></th>
<td class="colLast">
<div class="block">The type used to publish the artifact file, never <code>null</code>.</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setClassifier-java.lang.String-">setClassifier</a></span>&#8203;(java.lang.String&nbsp;classifier)</code></th>
<td class="colLast">
<div class="block">Sets the classifier used to publish the artifact file.</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setConf-java.lang.String-">setConf</a></span>&#8203;(java.lang.String&nbsp;conf)</code></th>
<td class="colLast">
<div class="block">Sets a comma separated list of public configurations in which this artifact is published.</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setExtension-java.lang.String-">setExtension</a></span>&#8203;(java.lang.String&nbsp;extension)</code></th>
<td class="colLast">
<div class="block">Sets the extension used to publish the artifact file.</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setName-java.lang.String-">setName</a></span>&#8203;(java.lang.String&nbsp;name)</code></th>
<td class="colLast">
<div class="block">Sets the name used to publish the artifact file.</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setType-java.lang.String-">setType</a></span>&#8203;(java.lang.String&nbsp;type)</code></th>
<td class="colLast">
<div class="block">Sets the type used to publish the artifact file.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.org.gradle.api.Buildable">
<!--   -->
</a>
<h3>Methods inherited from interface&nbsp;org.gradle.api.<a href="../../Buildable.html" title="interface in org.gradle.api">Buildable</a></h3>
<code><a href="../../Buildable.html#getBuildDependencies--">getBuildDependencies</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.org.gradle.api.publish.PublicationArtifact">
<!--   -->
</a>
<h3>Methods inherited from interface&nbsp;org.gradle.api.publish.<a href="../PublicationArtifact.html" title="interface in org.gradle.api.publish">PublicationArtifact</a></h3>
<code><a href="../PublicationArtifact.html#builtBy-java.lang.Object...-">builtBy</a>, <a href="../PublicationArtifact.html#getFile--">getFile</a></code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="getName--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getName</h4>
<pre class="methodSignature">java.lang.String&nbsp;getName()</pre>
<div class="block">The name used to publish the artifact file, never <code>null</code>.
 Defaults to the name of the module that this artifact belongs to.</div>
</li>
</ul>
<a name="setName-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setName</h4>
<pre class="methodSignature">void&nbsp;setName&#8203;(java.lang.String&nbsp;name)</pre>
<div class="block">Sets the name used to publish the artifact file.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>name</code> - The name.</dd>
</dl>
</li>
</ul>
<a name="getType--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getType</h4>
<pre class="methodSignature">java.lang.String&nbsp;getType()</pre>
<div class="block">The type used to publish the artifact file, never <code>null</code>.</div>
</li>
</ul>
<a name="setType-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setType</h4>
<pre class="methodSignature">void&nbsp;setType&#8203;(java.lang.String&nbsp;type)</pre>
<div class="block">Sets the type used to publish the artifact file.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>type</code> - The type.</dd>
</dl>
</li>
</ul>
<a name="getExtension--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getExtension</h4>
<pre class="methodSignature">java.lang.String&nbsp;getExtension()</pre>
<div class="block">The extension used to publish the artifact file, never <code>null</code>.
 For an artifact without an extension, this value will be an empty String.</div>
</li>
</ul>
<a name="setExtension-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setExtension</h4>
<pre class="methodSignature">void&nbsp;setExtension&#8203;(java.lang.String&nbsp;extension)</pre>
<div class="block">Sets the extension used to publish the artifact file.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>extension</code> - The extension.</dd>
</dl>
</li>
</ul>
<a name="getClassifier--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getClassifier</h4>
<pre class="methodSignature">@Nullable
java.lang.String&nbsp;getClassifier()</pre>
<div class="block">The classifier used to publish the artifact file.
 A <code>null</code> value (the default) indicates that this artifact will be published without a classifier.</div>
</li>
</ul>
<a name="setClassifier-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setClassifier</h4>
<pre class="methodSignature">void&nbsp;setClassifier&#8203;(@Nullable
                   java.lang.String&nbsp;classifier)</pre>
<div class="block">Sets the classifier used to publish the artifact file.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>classifier</code> - The classifier.</dd>
</dl>
</li>
</ul>
<a name="getConf--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getConf</h4>
<pre class="methodSignature">@Nullable
java.lang.String&nbsp;getConf()</pre>
<div class="block">A comma separated list of public configurations in which this artifact is published.
 The '*' wildcard is used to designate that the artifact is published in all public configurations.
 A <code>null</code> value (the default) indicates that this artifact will be published without a conf attribute.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The value of 'conf' for this artifact.</dd>
</dl>
</li>
</ul>
<a name="setConf-java.lang.String-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>setConf</h4>
<pre class="methodSignature">void&nbsp;setConf&#8203;(@Nullable
             java.lang.String&nbsp;conf)</pre>
<div class="block">Sets a comma separated list of public configurations in which this artifact is published.
 The '*' wildcard can be used to designate that the artifact is published in all public configurations.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>conf</code> - The value of 'conf' for this artifact.</dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../../allclasses.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
