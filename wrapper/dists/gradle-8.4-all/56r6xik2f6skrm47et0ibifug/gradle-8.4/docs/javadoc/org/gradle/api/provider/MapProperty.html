<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc -->
<title>MapProperty (Gradle API 8.4)</title>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<link rel="stylesheet" type="text/css" href="../../../../javadoc.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../jquery/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
<script type="text/javascript" src="../../../../jquery/jszip/dist/jszip.min.js"></script>
<script type="text/javascript" src="../../../../jquery/jszip-utils/dist/jszip-utils.min.js"></script>
<!--[if IE]>
<script type="text/javascript" src="../../../../jquery/jszip-utils/dist/jszip-utils-ie.min.js"></script>
<![endif]-->
<script type="text/javascript" src="../../../../jquery/jquery-3.6.0.min.js"></script>
<script type="text/javascript" src="../../../../jquery/jquery-ui.min.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="MapProperty (Gradle API 8.4)";
        }
    }
    catch(err) {
    }
//-->
var data = {"i0":6,"i1":6,"i2":6,"i3":6,"i4":6,"i5":6,"i6":6,"i7":6,"i8":6,"i9":6,"i10":6,"i11":6,"i12":6,"i13":6};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],4:["t3","Abstract Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
var pathtoroot = "../../../../";
var useModuleDirectories = false;
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="fixedNav">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses.html">All&nbsp;Classes</a></li>
</ul>
<ul class="navListSearch">
<li><label for="search">SEARCH:</label>
<input type="text" id="search" value="search" disabled="disabled">
<input type="reset" id="reset" value="reset" disabled="disabled">
</li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
</div>
<div class="navPadding">&nbsp;</div>
<script type="text/javascript"><!--
$('.navPadding').css('padding-top', $('.fixedNav').css("height"));
//-->
</script>
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle"><span class="packageLabelInType">Package</span>&nbsp;<a href="package-summary.html">org.gradle.api.provider</a></div>
<h2 title="Interface MapProperty" class="title">Interface MapProperty&lt;K,&#8203;V&gt;</h2>
</div>
<div class="contentContainer">
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt><span class="paramLabel">Type Parameters:</span></dt>
<dd><code>K</code> - the type of keys.</dd>
<dd><code>V</code> - the type of values.</dd>
</dl>
<dl>
<dt>All Superinterfaces:</dt>
<dd><code><a href="HasConfigurableValue.html" title="interface in org.gradle.api.provider">HasConfigurableValue</a></code>, <code><a href="Provider.html" title="interface in org.gradle.api.provider">Provider</a>&lt;java.util.Map&lt;K,&#8203;V&gt;&gt;</code></dd>
</dl>
<hr>
<pre><a href="../SupportsKotlinAssignmentOverloading.html" title="annotation in org.gradle.api">@SupportsKotlinAssignmentOverloading</a>
public interface <span class="typeNameLabel">MapProperty&lt;K,&#8203;V&gt;</span>
extends <a href="Provider.html" title="interface in org.gradle.api.provider">Provider</a>&lt;java.util.Map&lt;K,&#8203;V&gt;&gt;, <a href="HasConfigurableValue.html" title="interface in org.gradle.api.provider">HasConfigurableValue</a></pre>
<div class="block">Represents a property whose type is a <code>Map</code> of keys of type <a href="MapProperty.html" title="interface in org.gradle.api.provider"><code>MapProperty</code></a> and values of type <a href="MapProperty.html" title="interface in org.gradle.api.provider"><code>MapProperty</code></a>. Retains iteration order.

 <p>
 You can create a <a href="MapProperty.html" title="interface in org.gradle.api.provider"><code>MapProperty</code></a> instance using factory method <a href="../model/ObjectFactory.html#mapProperty-java.lang.Class-java.lang.Class-"><code>ObjectFactory.mapProperty(Class, Class)</code></a>.
 </p>

 <p><b>Note:</b> This interface is not intended for implementation by build script or plugin authors.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>5.1</dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t3" class="tableTab"><span><a href="javascript:show(4);">Abstract Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colSecond" scope="col">Method</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code><a href="MapProperty.html" title="interface in org.gradle.api.provider">MapProperty</a>&lt;<a href="MapProperty.html" title="type parameter in MapProperty">K</a>,&#8203;<a href="MapProperty.html" title="type parameter in MapProperty">V</a>&gt;</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#convention-java.util.Map-">convention</a></span>&#8203;(java.util.Map&lt;? extends <a href="MapProperty.html" title="type parameter in MapProperty">K</a>,&#8203;? extends <a href="MapProperty.html" title="type parameter in MapProperty">V</a>&gt;&nbsp;value)</code></th>
<td class="colLast">
<div class="block">Specifies the value to use as the convention for this property.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code><a href="MapProperty.html" title="interface in org.gradle.api.provider">MapProperty</a>&lt;<a href="MapProperty.html" title="type parameter in MapProperty">K</a>,&#8203;<a href="MapProperty.html" title="type parameter in MapProperty">V</a>&gt;</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#convention-org.gradle.api.provider.Provider-">convention</a></span>&#8203;(<a href="Provider.html" title="interface in org.gradle.api.provider">Provider</a>&lt;? extends java.util.Map&lt;? extends <a href="MapProperty.html" title="type parameter in MapProperty">K</a>,&#8203;? extends <a href="MapProperty.html" title="type parameter in MapProperty">V</a>&gt;&gt;&nbsp;valueProvider)</code></th>
<td class="colLast">
<div class="block">Specifies the provider of the value to use as the convention for this property.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code><a href="MapProperty.html" title="interface in org.gradle.api.provider">MapProperty</a>&lt;<a href="MapProperty.html" title="type parameter in MapProperty">K</a>,&#8203;<a href="MapProperty.html" title="type parameter in MapProperty">V</a>&gt;</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#empty--">empty</a></span>()</code></th>
<td class="colLast">
<div class="block">Sets the value of this property to an empty map, and replaces any existing value.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#finalizeValue--">finalizeValue</a></span>()</code></th>
<td class="colLast">
<div class="block">Disallows further changes to the value of this property.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code><a href="Provider.html" title="interface in org.gradle.api.provider">Provider</a>&lt;<a href="MapProperty.html" title="type parameter in MapProperty">V</a>&gt;</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getting-K-">getting</a></span>&#8203;(<a href="MapProperty.html" title="type parameter in MapProperty">K</a>&nbsp;key)</code></th>
<td class="colLast">
<div class="block">Returns a provider that resolves to the value of the mapping of the given key.</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code><a href="Provider.html" title="interface in org.gradle.api.provider">Provider</a>&lt;java.util.Set&lt;<a href="MapProperty.html" title="type parameter in MapProperty">K</a>&gt;&gt;</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#keySet--">keySet</a></span>()</code></th>
<td class="colLast">
<div class="block">Returns a <a href="Provider.html" title="interface in org.gradle.api.provider"><code>Provider</code></a> that returns the set of keys for the map that is the property value.</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#put-K-org.gradle.api.provider.Provider-">put</a></span>&#8203;(<a href="MapProperty.html" title="type parameter in MapProperty">K</a>&nbsp;key,
   <a href="Provider.html" title="interface in org.gradle.api.provider">Provider</a>&lt;? extends <a href="MapProperty.html" title="type parameter in MapProperty">V</a>&gt;&nbsp;providerOfValue)</code></th>
<td class="colLast">
<div class="block">Adds a map entry to the property value.</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#put-K-V-">put</a></span>&#8203;(<a href="MapProperty.html" title="type parameter in MapProperty">K</a>&nbsp;key,
   <a href="MapProperty.html" title="type parameter in MapProperty">V</a>&nbsp;value)</code></th>
<td class="colLast">
<div class="block">Adds a map entry to the property value.</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#putAll-java.util.Map-">putAll</a></span>&#8203;(java.util.Map&lt;? extends <a href="MapProperty.html" title="type parameter in MapProperty">K</a>,&#8203;? extends <a href="MapProperty.html" title="type parameter in MapProperty">V</a>&gt;&nbsp;entries)</code></th>
<td class="colLast">
<div class="block">Adds all entries from another <code>Map</code> to the property value.</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#putAll-org.gradle.api.provider.Provider-">putAll</a></span>&#8203;(<a href="Provider.html" title="interface in org.gradle.api.provider">Provider</a>&lt;? extends java.util.Map&lt;? extends <a href="MapProperty.html" title="type parameter in MapProperty">K</a>,&#8203;? extends <a href="MapProperty.html" title="type parameter in MapProperty">V</a>&gt;&gt;&nbsp;provider)</code></th>
<td class="colLast">
<div class="block">Adds all entries from another <code>Map</code> to the property value.</div>
</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#set-java.util.Map-">set</a></span>&#8203;(java.util.Map&lt;? extends <a href="MapProperty.html" title="type parameter in MapProperty">K</a>,&#8203;? extends <a href="MapProperty.html" title="type parameter in MapProperty">V</a>&gt;&nbsp;entries)</code></th>
<td class="colLast">
<div class="block">Sets the value of this property to the entries of the given Map, and replaces any existing value.</div>
</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#set-org.gradle.api.provider.Provider-">set</a></span>&#8203;(<a href="Provider.html" title="interface in org.gradle.api.provider">Provider</a>&lt;? extends java.util.Map&lt;? extends <a href="MapProperty.html" title="type parameter in MapProperty">K</a>,&#8203;? extends <a href="MapProperty.html" title="type parameter in MapProperty">V</a>&gt;&gt;&nbsp;provider)</code></th>
<td class="colLast">
<div class="block">Sets the property to have the same value of the given provider, and replaces any existing value.</div>
</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code><a href="MapProperty.html" title="interface in org.gradle.api.provider">MapProperty</a>&lt;<a href="MapProperty.html" title="type parameter in MapProperty">K</a>,&#8203;<a href="MapProperty.html" title="type parameter in MapProperty">V</a>&gt;</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#value-java.util.Map-">value</a></span>&#8203;(java.util.Map&lt;? extends <a href="MapProperty.html" title="type parameter in MapProperty">K</a>,&#8203;? extends <a href="MapProperty.html" title="type parameter in MapProperty">V</a>&gt;&nbsp;entries)</code></th>
<td class="colLast">
<div class="block">Sets the value of this property to the entries of the given Map, and replaces any existing value.</div>
</td>
</tr>
<tr id="i13" class="rowColor">
<td class="colFirst"><code><a href="MapProperty.html" title="interface in org.gradle.api.provider">MapProperty</a>&lt;<a href="MapProperty.html" title="type parameter in MapProperty">K</a>,&#8203;<a href="MapProperty.html" title="type parameter in MapProperty">V</a>&gt;</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#value-org.gradle.api.provider.Provider-">value</a></span>&#8203;(<a href="Provider.html" title="interface in org.gradle.api.provider">Provider</a>&lt;? extends java.util.Map&lt;? extends <a href="MapProperty.html" title="type parameter in MapProperty">K</a>,&#8203;? extends <a href="MapProperty.html" title="type parameter in MapProperty">V</a>&gt;&gt;&nbsp;provider)</code></th>
<td class="colLast">
<div class="block">Sets the property to have the same value of the given provider, and replaces any existing value.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.org.gradle.api.provider.HasConfigurableValue">
<!--   -->
</a>
<h3>Methods inherited from interface&nbsp;org.gradle.api.provider.<a href="HasConfigurableValue.html" title="interface in org.gradle.api.provider">HasConfigurableValue</a></h3>
<code><a href="HasConfigurableValue.html#disallowChanges--">disallowChanges</a>, <a href="HasConfigurableValue.html#disallowUnsafeRead--">disallowUnsafeRead</a>, <a href="HasConfigurableValue.html#finalizeValueOnRead--">finalizeValueOnRead</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.org.gradle.api.provider.Provider">
<!--   -->
</a>
<h3>Methods inherited from interface&nbsp;org.gradle.api.provider.<a href="Provider.html" title="interface in org.gradle.api.provider">Provider</a></h3>
<code><a href="Provider.html#filter-java.util.function.Predicate-">filter</a>, <a href="Provider.html#flatMap-org.gradle.api.Transformer-">flatMap</a>, <a href="Provider.html#forUseAtConfigurationTime--">forUseAtConfigurationTime</a>, <a href="Provider.html#get--">get</a>, <a href="Provider.html#getOrElse-T-">getOrElse</a>, <a href="Provider.html#getOrNull--">getOrNull</a>, <a href="Provider.html#isPresent--">isPresent</a>, <a href="Provider.html#map-org.gradle.api.Transformer-">map</a>, <a href="Provider.html#orElse-org.gradle.api.provider.Provider-">orElse</a>, <a href="Provider.html#orElse-T-">orElse</a>, <a href="Provider.html#zip-org.gradle.api.provider.Provider-java.util.function.BiFunction-">zip</a></code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="empty--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>empty</h4>
<pre class="methodSignature"><a href="MapProperty.html" title="interface in org.gradle.api.provider">MapProperty</a>&lt;<a href="MapProperty.html" title="type parameter in MapProperty">K</a>,&#8203;<a href="MapProperty.html" title="type parameter in MapProperty">V</a>&gt;&nbsp;empty()</pre>
<div class="block">Sets the value of this property to an empty map, and replaces any existing value.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>this property.</dd>
</dl>
</li>
</ul>
<a name="getting-java.lang.Object-">
<!--   -->
</a><a name="getting-K-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getting</h4>
<pre class="methodSignature"><a href="Provider.html" title="interface in org.gradle.api.provider">Provider</a>&lt;<a href="MapProperty.html" title="type parameter in MapProperty">V</a>&gt;&nbsp;getting&#8203;(<a href="MapProperty.html" title="type parameter in MapProperty">K</a>&nbsp;key)</pre>
<div class="block">Returns a provider that resolves to the value of the mapping of the given key. It will have no value
 if the property has no value, or if it does not contain a mapping for the key.

 <p>The returned provider will track the value of this property and query its value when it is queried.</p>

 <p>This method is equivalent to

 <pre><code>
     map(m -&gt; m.get(key))
 </code></pre>

 but possibly more efficient.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>key</code> - the key</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>a <a href="Provider.html" title="interface in org.gradle.api.provider"><code>Provider</code></a> for the value</dd>
</dl>
</li>
</ul>
<a name="set-java.util.Map-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>set</h4>
<pre class="methodSignature">void&nbsp;set&#8203;(@Nullable
         java.util.Map&lt;? extends <a href="MapProperty.html" title="type parameter in MapProperty">K</a>,&#8203;? extends <a href="MapProperty.html" title="type parameter in MapProperty">V</a>&gt;&nbsp;entries)</pre>
<div class="block">Sets the value of this property to the entries of the given Map, and replaces any existing value.
 This property will query the entries of the map each time the value of this property is queried.

 <p>This method can also be used to discard the value of the property, by passing <code>null</code> as the value.
 The convention for this property, if any, will be used to provide the value instead.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>entries</code> - the entries, can be <code>null</code></dd>
</dl>
</li>
</ul>
<a name="set-org.gradle.api.provider.Provider-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>set</h4>
<pre class="methodSignature">void&nbsp;set&#8203;(<a href="Provider.html" title="interface in org.gradle.api.provider">Provider</a>&lt;? extends java.util.Map&lt;? extends <a href="MapProperty.html" title="type parameter in MapProperty">K</a>,&#8203;? extends <a href="MapProperty.html" title="type parameter in MapProperty">V</a>&gt;&gt;&nbsp;provider)</pre>
<div class="block">Sets the property to have the same value of the given provider, and replaces any existing value.

 This property will track the value of the provider and query its value each time the value of this property is queried.
 When the provider has no value, this property will also have no value.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>provider</code> - Provider of the entries.</dd>
</dl>
</li>
</ul>
<a name="value-java.util.Map-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>value</h4>
<pre class="methodSignature"><a href="MapProperty.html" title="interface in org.gradle.api.provider">MapProperty</a>&lt;<a href="MapProperty.html" title="type parameter in MapProperty">K</a>,&#8203;<a href="MapProperty.html" title="type parameter in MapProperty">V</a>&gt;&nbsp;value&#8203;(@Nullable
                             java.util.Map&lt;? extends <a href="MapProperty.html" title="type parameter in MapProperty">K</a>,&#8203;? extends <a href="MapProperty.html" title="type parameter in MapProperty">V</a>&gt;&nbsp;entries)</pre>
<div class="block">Sets the value of this property to the entries of the given Map, and replaces any existing value.
 This property will query the entries of the map each time the value of this property is queried.

 <p>This is the same as <a href="#set-java.util.Map-"><code>set(Map)</code></a> but returns this property to allow method chaining.</p></div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>entries</code> - the entries, can be <code>null</code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>this</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>5.6</dd>
</dl>
</li>
</ul>
<a name="value-org.gradle.api.provider.Provider-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>value</h4>
<pre class="methodSignature"><a href="MapProperty.html" title="interface in org.gradle.api.provider">MapProperty</a>&lt;<a href="MapProperty.html" title="type parameter in MapProperty">K</a>,&#8203;<a href="MapProperty.html" title="type parameter in MapProperty">V</a>&gt;&nbsp;value&#8203;(<a href="Provider.html" title="interface in org.gradle.api.provider">Provider</a>&lt;? extends java.util.Map&lt;? extends <a href="MapProperty.html" title="type parameter in MapProperty">K</a>,&#8203;? extends <a href="MapProperty.html" title="type parameter in MapProperty">V</a>&gt;&gt;&nbsp;provider)</pre>
<div class="block">Sets the property to have the same value of the given provider, and replaces any existing value.

 This property will track the value of the provider and query its value each time the value of this property is queried.
 When the provider has no value, this property will also have no value.

 <p>This is the same as <a href="#set-org.gradle.api.provider.Provider-"><code>set(Provider)</code></a> but returns this property to allow method chaining.</p></div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>provider</code> - Provider of the entries.</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>5.6</dd>
</dl>
</li>
</ul>
<a name="put-java.lang.Object-java.lang.Object-">
<!--   -->
</a><a name="put-K-V-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>put</h4>
<pre class="methodSignature">void&nbsp;put&#8203;(<a href="MapProperty.html" title="type parameter in MapProperty">K</a>&nbsp;key,
         <a href="MapProperty.html" title="type parameter in MapProperty">V</a>&nbsp;value)</pre>
<div class="block">Adds a map entry to the property value.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>key</code> - the key</dd>
<dd><code>value</code> - the value</dd>
</dl>
</li>
</ul>
<a name="put-java.lang.Object-org.gradle.api.provider.Provider-">
<!--   -->
</a><a name="put-K-org.gradle.api.provider.Provider-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>put</h4>
<pre class="methodSignature">void&nbsp;put&#8203;(<a href="MapProperty.html" title="type parameter in MapProperty">K</a>&nbsp;key,
         <a href="Provider.html" title="interface in org.gradle.api.provider">Provider</a>&lt;? extends <a href="MapProperty.html" title="type parameter in MapProperty">V</a>&gt;&nbsp;providerOfValue)</pre>
<div class="block">Adds a map entry to the property value.

 <p>The given provider will be queried when the value of this property is queried.
 This property will have no value when the given provider has no value.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>key</code> - the key</dd>
<dd><code>providerOfValue</code> - the provider of the value</dd>
</dl>
</li>
</ul>
<a name="putAll-java.util.Map-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>putAll</h4>
<pre class="methodSignature">void&nbsp;putAll&#8203;(java.util.Map&lt;? extends <a href="MapProperty.html" title="type parameter in MapProperty">K</a>,&#8203;? extends <a href="MapProperty.html" title="type parameter in MapProperty">V</a>&gt;&nbsp;entries)</pre>
<div class="block">Adds all entries from another <code>Map</code> to the property value.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>entries</code> - a <code>Map</code> containing the entries to add</dd>
</dl>
</li>
</ul>
<a name="putAll-org.gradle.api.provider.Provider-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>putAll</h4>
<pre class="methodSignature">void&nbsp;putAll&#8203;(<a href="Provider.html" title="interface in org.gradle.api.provider">Provider</a>&lt;? extends java.util.Map&lt;? extends <a href="MapProperty.html" title="type parameter in MapProperty">K</a>,&#8203;? extends <a href="MapProperty.html" title="type parameter in MapProperty">V</a>&gt;&gt;&nbsp;provider)</pre>
<div class="block">Adds all entries from another <code>Map</code> to the property value.

 <p>The given provider will be queried when the value of this property is queried.
 This property will have no value when the given provider has no value.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>provider</code> - the provider of the entries</dd>
</dl>
</li>
</ul>
<a name="keySet--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>keySet</h4>
<pre class="methodSignature"><a href="Provider.html" title="interface in org.gradle.api.provider">Provider</a>&lt;java.util.Set&lt;<a href="MapProperty.html" title="type parameter in MapProperty">K</a>&gt;&gt;&nbsp;keySet()</pre>
<div class="block">Returns a <a href="Provider.html" title="interface in org.gradle.api.provider"><code>Provider</code></a> that returns the set of keys for the map that is the property value.

 <p>The returned provider will track the value of this property and query its value when it is queried.</p>

 <p>This method is equivalent to

 <pre><code>
     map(m -&gt; m.keySet())
 </code></pre>

 but possibly more efficient.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>a <a href="Provider.html" title="interface in org.gradle.api.provider"><code>Provider</code></a> that provides the set of keys for the map</dd>
</dl>
</li>
</ul>
<a name="convention-java.util.Map-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>convention</h4>
<pre class="methodSignature"><a href="MapProperty.html" title="interface in org.gradle.api.provider">MapProperty</a>&lt;<a href="MapProperty.html" title="type parameter in MapProperty">K</a>,&#8203;<a href="MapProperty.html" title="type parameter in MapProperty">V</a>&gt;&nbsp;convention&#8203;(@Nullable
                                  java.util.Map&lt;? extends <a href="MapProperty.html" title="type parameter in MapProperty">K</a>,&#8203;? extends <a href="MapProperty.html" title="type parameter in MapProperty">V</a>&gt;&nbsp;value)</pre>
<div class="block">Specifies the value to use as the convention for this property. The convention is used when no value has been set for this property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - The value, or <code>null</code> when the convention is that the property has no value.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>this</dd>
</dl>
</li>
</ul>
<a name="convention-org.gradle.api.provider.Provider-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>convention</h4>
<pre class="methodSignature"><a href="MapProperty.html" title="interface in org.gradle.api.provider">MapProperty</a>&lt;<a href="MapProperty.html" title="type parameter in MapProperty">K</a>,&#8203;<a href="MapProperty.html" title="type parameter in MapProperty">V</a>&gt;&nbsp;convention&#8203;(<a href="Provider.html" title="interface in org.gradle.api.provider">Provider</a>&lt;? extends java.util.Map&lt;? extends <a href="MapProperty.html" title="type parameter in MapProperty">K</a>,&#8203;? extends <a href="MapProperty.html" title="type parameter in MapProperty">V</a>&gt;&gt;&nbsp;valueProvider)</pre>
<div class="block">Specifies the provider of the value to use as the convention for this property. The convention is used when no value has been set for this property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>valueProvider</code> - The provider of the value.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>this</dd>
</dl>
</li>
</ul>
<a name="finalizeValue--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>finalizeValue</h4>
<pre class="methodSignature">void&nbsp;finalizeValue()</pre>
<div class="block">Disallows further changes to the value of this property. Calls to methods that change the value of this property, such as <a href="#set-java.util.Map-"><code>set(Map)</code></a> or <a href="#put-K-V-"><code>put(Object, Object)</code></a> will fail.

 <p>When this property has elements provided by a <a href="Provider.html" title="interface in org.gradle.api.provider"><code>Provider</code></a>, the value of the provider is queried when this method is called and the value of the provider will no longer be tracked.</p>

 <p>Note that although the value of the property will not change, the resulting map may contain mutable objects. Calling this method does not guarantee that the value will become immutable.</p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="HasConfigurableValue.html#finalizeValue--">finalizeValue</a></code>&nbsp;in interface&nbsp;<code><a href="HasConfigurableValue.html" title="interface in org.gradle.api.provider">HasConfigurableValue</a></code></dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
