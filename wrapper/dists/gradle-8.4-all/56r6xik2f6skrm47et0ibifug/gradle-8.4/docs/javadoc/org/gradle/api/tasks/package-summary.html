<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc -->
<title>org.gradle.api.tasks (Gradle API 8.4)</title>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<link rel="stylesheet" type="text/css" href="../../../../javadoc.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../jquery/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
<script type="text/javascript" src="../../../../jquery/jszip/dist/jszip.min.js"></script>
<script type="text/javascript" src="../../../../jquery/jszip-utils/dist/jszip-utils.min.js"></script>
<!--[if IE]>
<script type="text/javascript" src="../../../../jquery/jszip-utils/dist/jszip-utils-ie.min.js"></script>
<![endif]-->
<script type="text/javascript" src="../../../../jquery/jquery-3.6.0.min.js"></script>
<script type="text/javascript" src="../../../../jquery/jquery-ui.min.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="org.gradle.api.tasks (Gradle API 8.4)";
        }
    }
    catch(err) {
    }
//-->
var pathtoroot = "../../../../";
var useModuleDirectories = false;
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="fixedNav">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../index.html">Overview</a></li>
<li class="navBarCell1Rev">Package</li>
<li>Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses.html">All&nbsp;Classes</a></li>
</ul>
<ul class="navListSearch">
<li><label for="search">SEARCH:</label>
<input type="text" id="search" value="search" disabled="disabled">
<input type="reset" id="reset" value="reset" disabled="disabled">
</li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
</div>
<div class="navPadding">&nbsp;</div>
<script type="text/javascript"><!--
$('.navPadding').css('padding-top', $('.fixedNav').css("height"));
//-->
</script>
<div class="header">
<p><a href="../NonNullApi.html" title="annotation in org.gradle.api">@NonNullApi</a>
</p>
<h1 title="Package" class="title">Package&nbsp;org.gradle.api.tasks</h1>
</div>
<div class="contentContainer"><a name="package.description">
<!--   -->
</a>
<div class="block">The standard <a href="../Task.html" title="interface in org.gradle.api"><code>Task</code></a> implementations.</div>
<ul class="blockList">
<li class="blockList">
<table class="typeSummary" summary="Interface Summary table, listing interfaces, and an explanation">
<caption><span>Interface Summary</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Interface</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tbody>
<tr class="altColor">
<th class="colFirst" scope="row"><a href="AntBuilderAware.html" title="interface in org.gradle.api.tasks">AntBuilderAware</a></th>
<td class="colLast">
<div class="block">An <code>AntBuilderAware</code> represents an object which can add itself to Ant tasks, using an <a href="../AntBuilder.html" title="class in org.gradle.api"><code>AntBuilder</code></a>.</div>
</td>
</tr>
<tr class="rowColor">
<th class="colFirst" scope="row"><a href="ClasspathNormalizer.html" title="interface in org.gradle.api.tasks">ClasspathNormalizer</a></th>
<td class="colLast">
<div class="block">Normalizes file input that represents a Java runtime classpath.</div>
</td>
</tr>
<tr class="altColor">
<th class="colFirst" scope="row"><a href="CompileClasspathNormalizer.html" title="interface in org.gradle.api.tasks">CompileClasspathNormalizer</a></th>
<td class="colLast">
<div class="block">Normalizes file input that represents a Java compile classpath.</div>
</td>
</tr>
<tr class="rowColor">
<th class="colFirst" scope="row"><a href="FileNormalizer.html" title="interface in org.gradle.api.tasks">FileNormalizer</a></th>
<td class="colLast">
<div class="block">A normalizer used to remove unwanted noise when considering file inputs.</div>
</td>
</tr>
<tr class="altColor">
<th class="colFirst" scope="row"><a href="GroovySourceDirectorySet.html" title="interface in org.gradle.api.tasks">GroovySourceDirectorySet</a></th>
<td class="colLast">
<div class="block">Represents a Groovy source set.</div>
</td>
</tr>
<tr class="rowColor">
<th class="colFirst" scope="row"><a href="GroovySourceSet.html" title="interface in org.gradle.api.tasks">GroovySourceSet</a></th>
<td class="colLast">Deprecated.
<div class="deprecationComment">Using convention to contribute to source sets is deprecated.</div>
</td>
</tr>
<tr class="altColor">
<th class="colFirst" scope="row"><a href="ScalaSourceDirectorySet.html" title="interface in org.gradle.api.tasks">ScalaSourceDirectorySet</a></th>
<td class="colLast">
<div class="block">A <code>ScalaSourceDirectorySet</code> defines the properties and methods added to a <a href="SourceSet.html" title="interface in org.gradle.api.tasks"><code>SourceSet</code></a> by the <code>ScalaPlugin</code>.</div>
</td>
</tr>
<tr class="rowColor">
<th class="colFirst" scope="row"><a href="ScalaSourceSet.html" title="interface in org.gradle.api.tasks">ScalaSourceSet</a></th>
<td class="colLast">Deprecated.
<div class="deprecationComment">Using conventions to contribute source sets is deprecated.</div>
</td>
</tr>
<tr class="altColor">
<th class="colFirst" scope="row"><a href="SourceSet.html" title="interface in org.gradle.api.tasks">SourceSet</a></th>
<td class="colLast">
<div class="block">A <code>SourceSet</code> represents a logical group of Java source and resource files.</div>
</td>
</tr>
<tr class="rowColor">
<th class="colFirst" scope="row"><a href="SourceSetContainer.html" title="interface in org.gradle.api.tasks">SourceSetContainer</a></th>
<td class="colLast">
<div class="block">A <code>SourceSetContainer</code> manages a set of <a href="SourceSet.html" title="interface in org.gradle.api.tasks"><code>SourceSet</code></a> objects.</div>
</td>
</tr>
<tr class="altColor">
<th class="colFirst" scope="row"><a href="SourceSetOutput.html" title="interface in org.gradle.api.tasks">SourceSetOutput</a></th>
<td class="colLast">
<div class="block">A collection of all output directories (compiled classes, processed resources, etc.) - notice that <a href="SourceSetOutput.html" title="interface in org.gradle.api.tasks"><code>SourceSetOutput</code></a> extends <a href="../file/FileCollection.html" title="interface in org.gradle.api.file"><code>FileCollection</code></a>.</div>
</td>
</tr>
<tr class="rowColor">
<th class="colFirst" scope="row"><a href="TaskCollection.html" title="interface in org.gradle.api.tasks">TaskCollection</a>&lt;T extends <a href="../Task.html" title="interface in org.gradle.api">Task</a>&gt;</th>
<td class="colLast">
<div class="block">A <code>TaskCollection</code> contains a set of <a href="../Task.html" title="interface in org.gradle.api"><code>Task</code></a> instances, and provides a number of query methods.</div>
</td>
</tr>
<tr class="altColor">
<th class="colFirst" scope="row"><a href="TaskContainer.html" title="interface in org.gradle.api.tasks">TaskContainer</a></th>
<td class="colLast">
<div class="block">A <code>TaskContainer</code> is responsible for managing a set of <a href="../Task.html" title="interface in org.gradle.api"><code>Task</code></a> instances.</div>
</td>
</tr>
<tr class="rowColor">
<th class="colFirst" scope="row"><a href="TaskDependency.html" title="interface in org.gradle.api.tasks">TaskDependency</a></th>
<td class="colLast">
<div class="block">A <code>TaskDependency</code> represents an <em>unordered</em> set of tasks which a <a href="../Task.html" title="interface in org.gradle.api"><code>Task</code></a> depends on.</div>
</td>
</tr>
<tr class="altColor">
<th class="colFirst" scope="row"><a href="TaskDestroyables.html" title="interface in org.gradle.api.tasks">TaskDestroyables</a></th>
<td class="colLast">
<div class="block">Represents the files or directories that a <a href="../Task.html" title="interface in org.gradle.api"><code>Task</code></a> destroys (removes).</div>
</td>
</tr>
<tr class="rowColor">
<th class="colFirst" scope="row"><a href="TaskFilePropertyBuilder.html" title="interface in org.gradle.api.tasks">TaskFilePropertyBuilder</a></th>
<td class="colLast">
<div class="block">Describes a property of a task that contains zero or more files.</div>
</td>
</tr>
<tr class="altColor">
<th class="colFirst" scope="row"><a href="TaskInputFilePropertyBuilder.html" title="interface in org.gradle.api.tasks">TaskInputFilePropertyBuilder</a></th>
<td class="colLast">
<div class="block">Describes an input property of a task that contains zero or more files.</div>
</td>
</tr>
<tr class="rowColor">
<th class="colFirst" scope="row"><a href="TaskInputPropertyBuilder.html" title="interface in org.gradle.api.tasks">TaskInputPropertyBuilder</a></th>
<td class="colLast">
<div class="block">Describes an input property of a task.</div>
</td>
</tr>
<tr class="altColor">
<th class="colFirst" scope="row"><a href="TaskInputs.html" title="interface in org.gradle.api.tasks">TaskInputs</a></th>
<td class="colLast">
<div class="block">A <code>TaskInputs</code> represents the inputs for a task.</div>
</td>
</tr>
<tr class="rowColor">
<th class="colFirst" scope="row"><a href="TaskLocalState.html" title="interface in org.gradle.api.tasks">TaskLocalState</a></th>
<td class="colLast">
<div class="block">Represents the files or directories that represent the local state of a <a href="../Task.html" title="interface in org.gradle.api"><code>Task</code></a>.</div>
</td>
</tr>
<tr class="altColor">
<th class="colFirst" scope="row"><a href="TaskOutputFilePropertyBuilder.html" title="interface in org.gradle.api.tasks">TaskOutputFilePropertyBuilder</a></th>
<td class="colLast">
<div class="block">Describes an output property of a task that contains zero or more files.</div>
</td>
</tr>
<tr class="rowColor">
<th class="colFirst" scope="row"><a href="TaskOutputs.html" title="interface in org.gradle.api.tasks">TaskOutputs</a></th>
<td class="colLast">
<div class="block">A <code>TaskOutputs</code> represents the outputs of a task.</div>
</td>
</tr>
<tr class="altColor">
<th class="colFirst" scope="row"><a href="TaskPropertyBuilder.html" title="interface in org.gradle.api.tasks">TaskPropertyBuilder</a></th>
<td class="colLast">
<div class="block">Describes a property of a task.</div>
</td>
</tr>
<tr class="rowColor">
<th class="colFirst" scope="row"><a href="TaskProvider.html" title="interface in org.gradle.api.tasks">TaskProvider</a>&lt;T extends <a href="../Task.html" title="interface in org.gradle.api">Task</a>&gt;</th>
<td class="colLast">
<div class="block">Providers a task of the given type.</div>
</td>
</tr>
<tr class="altColor">
<th class="colFirst" scope="row"><a href="TaskReference.html" title="interface in org.gradle.api.tasks">TaskReference</a></th>
<td class="colLast">
<div class="block">A lightweight reference to a task.</div>
</td>
</tr>
<tr class="rowColor">
<th class="colFirst" scope="row"><a href="TaskState.html" title="interface in org.gradle.api.tasks">TaskState</a></th>
<td class="colLast">
<div class="block"><code>TaskState</code> provides information about the execution state of a <a href="../Task.html" title="interface in org.gradle.api"><code>Task</code></a>.</div>
</td>
</tr>
<tr class="altColor">
<th class="colFirst" scope="row"><a href="VerificationTask.html" title="interface in org.gradle.api.tasks">VerificationTask</a></th>
<td class="colLast">
<div class="block">A <code>VerificationTask</code> is a task which performs some verification of the artifacts produced by a build.</div>
</td>
</tr>
<tr class="rowColor">
<th class="colFirst" scope="row"><a href="WorkResult.html" title="interface in org.gradle.api.tasks">WorkResult</a></th>
<td class="colLast">
<div class="block">Provides information about some work which was performed.</div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList">
<table class="typeSummary" summary="Class Summary table, listing classes, and an explanation">
<caption><span>Class Summary</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Class</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tbody>
<tr class="altColor">
<th class="colFirst" scope="row"><a href="AbstractCopyTask.html" title="class in org.gradle.api.tasks">AbstractCopyTask</a></th>
<td class="colLast">
<div class="block"><code>AbstractCopyTask</code> is the base class for all copy tasks.</div>
</td>
</tr>
<tr class="rowColor">
<th class="colFirst" scope="row"><a href="AbstractExecTask.html" title="class in org.gradle.api.tasks">AbstractExecTask</a>&lt;T extends <a href="AbstractExecTask.html" title="class in org.gradle.api.tasks">AbstractExecTask</a>&gt;</th>
<td class="colLast">
<div class="block"><code>AbstractExecTask</code> is the base class for all exec tasks.</div>
</td>
</tr>
<tr class="altColor">
<th class="colFirst" scope="row"><a href="Copy.html" title="class in org.gradle.api.tasks">Copy</a></th>
<td class="colLast">
<div class="block">Copies files into a destination directory.</div>
</td>
</tr>
<tr class="rowColor">
<th class="colFirst" scope="row"><a href="Delete.html" title="class in org.gradle.api.tasks">Delete</a></th>
<td class="colLast">
<div class="block">Deletes files or directories.</div>
</td>
</tr>
<tr class="altColor">
<th class="colFirst" scope="row"><a href="Exec.html" title="class in org.gradle.api.tasks">Exec</a></th>
<td class="colLast">
<div class="block">Executes a command line process.</div>
</td>
</tr>
<tr class="rowColor">
<th class="colFirst" scope="row"><a href="GradleBuild.html" title="class in org.gradle.api.tasks">GradleBuild</a></th>
<td class="colLast">
<div class="block">Executes a Gradle build.</div>
</td>
</tr>
<tr class="altColor">
<th class="colFirst" scope="row"><a href="GroovyRuntime.html" title="class in org.gradle.api.tasks">GroovyRuntime</a></th>
<td class="colLast">
<div class="block">Provides information related to the Groovy runtime(s) used in a project.</div>
</td>
</tr>
<tr class="rowColor">
<th class="colFirst" scope="row"><a href="JavaExec.html" title="class in org.gradle.api.tasks">JavaExec</a></th>
<td class="colLast">
<div class="block">Executes a Java application in a child process.</div>
</td>
</tr>
<tr class="altColor">
<th class="colFirst" scope="row"><a href="ScalaRuntime.html" title="class in org.gradle.api.tasks">ScalaRuntime</a></th>
<td class="colLast">
<div class="block">Provides information related to the Scala runtime(s) used in a project.</div>
</td>
</tr>
<tr class="rowColor">
<th class="colFirst" scope="row"><a href="SourceTask.html" title="class in org.gradle.api.tasks">SourceTask</a></th>
<td class="colLast">
<div class="block">A <code>SourceTask</code> performs some operation on source files.</div>
</td>
</tr>
<tr class="altColor">
<th class="colFirst" scope="row"><a href="Sync.html" title="class in org.gradle.api.tasks">Sync</a></th>
<td class="colLast">
<div class="block">Synchronizes the contents of a destination directory with some source directories and files.</div>
</td>
</tr>
<tr class="rowColor">
<th class="colFirst" scope="row"><a href="Upload.html" title="class in org.gradle.api.tasks">Upload</a></th>
<td class="colLast">Deprecated.
<div class="deprecationComment">This class is scheduled for removal in a future version.</div>
</td>
</tr>
<tr class="altColor">
<th class="colFirst" scope="row"><a href="WorkResults.html" title="class in org.gradle.api.tasks">WorkResults</a></th>
<td class="colLast">
<div class="block">Helps access trivial <a href="WorkResult.html" title="interface in org.gradle.api.tasks"><code>WorkResult</code></a> objects.</div>
</td>
</tr>
<tr class="rowColor">
<th class="colFirst" scope="row"><a href="WriteProperties.html" title="class in org.gradle.api.tasks">WriteProperties</a></th>
<td class="colLast">
<div class="block">Writes a <code>Properties</code> in a way that the results can be expected to be reproducible.</div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList">
<table class="typeSummary" summary="Enum Summary table, listing enums, and an explanation">
<caption><span>Enum Summary</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Enum</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tbody>
<tr class="altColor">
<th class="colFirst" scope="row"><a href="PathSensitivity.html" title="enum in org.gradle.api.tasks">PathSensitivity</a></th>
<td class="colLast">
<div class="block">Enumeration of different path handling strategies for task properties.</div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList">
<table class="typeSummary" summary="Exception Summary table, listing exceptions, and an explanation">
<caption><span>Exception Summary</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Exception</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tbody>
<tr class="altColor">
<th class="colFirst" scope="row"><a href="StopActionException.html" title="class in org.gradle.api.tasks">StopActionException</a></th>
<td class="colLast">
<div class="block">A <code>StopActionException</code> is be thrown by a task <a href="../Action.html" title="interface in org.gradle.api"><code>Action</code></a> or task action closure to
 stop its own execution and to start execution of the task's next action.</div>
</td>
</tr>
<tr class="rowColor">
<th class="colFirst" scope="row"><a href="StopExecutionException.html" title="class in org.gradle.api.tasks">StopExecutionException</a></th>
<td class="colLast">
<div class="block">A <code>StopExecutionException</code> is thrown by a <a href="../Action.html" title="interface in org.gradle.api"><code>Action</code></a> or task action closure to
 stop execution of the current task and start execution of the next task.</div>
</td>
</tr>
<tr class="altColor">
<th class="colFirst" scope="row"><a href="TaskExecutionException.html" title="class in org.gradle.api.tasks">TaskExecutionException</a></th>
<td class="colLast">
<div class="block">A <code>TaskExecutionException</code> is thrown when a task fails to execute successfully.</div>
</td>
</tr>
<tr class="rowColor">
<th class="colFirst" scope="row"><a href="TaskInstantiationException.html" title="class in org.gradle.api.tasks">TaskInstantiationException</a></th>
<td class="colLast">
<div class="block">A <code>TaskInstantiationException</code> is thrown when a task cannot be instantiated for some reason.</div>
</td>
</tr>
<tr class="altColor">
<th class="colFirst" scope="row"><a href="VerificationException.html" title="class in org.gradle.api.tasks">VerificationException</a></th>
<td class="colLast">
<div class="block">Signals that tests have failed.</div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList">
<table class="typeSummary" summary="Annotation Types Summary table, listing annotation types, and an explanation">
<caption><span>Annotation Types Summary</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Annotation Type</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tbody>
<tr class="altColor">
<th class="colFirst" scope="row"><a href="CacheableTask.html" title="annotation in org.gradle.api.tasks">CacheableTask</a></th>
<td class="colLast">
<div class="block">Attached to a task type to indicate that task output caching should be enabled by default for tasks of this type.</div>
</td>
</tr>
<tr class="rowColor">
<th class="colFirst" scope="row"><a href="Classpath.html" title="annotation in org.gradle.api.tasks">Classpath</a></th>
<td class="colLast">
<div class="block">Marks a property as specifying a JVM classpath for a task.</div>
</td>
</tr>
<tr class="altColor">
<th class="colFirst" scope="row"><a href="CompileClasspath.html" title="annotation in org.gradle.api.tasks">CompileClasspath</a></th>
<td class="colLast">
<div class="block">Marks a property as specifying a Java compile classpath for a task.</div>
</td>
</tr>
<tr class="rowColor">
<th class="colFirst" scope="row"><a href="Console.html" title="annotation in org.gradle.api.tasks">Console</a></th>
<td class="colLast">
<div class="block">Attached to a task property to indicate that the property is not to be taken into account for up-to-date checking,
 because its value only influences the console output of the task.</div>
</td>
</tr>
<tr class="altColor">
<th class="colFirst" scope="row"><a href="Destroys.html" title="annotation in org.gradle.api.tasks">Destroys</a></th>
<td class="colLast">
<div class="block">Marks a property as specifying a file or directory that a task destroys.</div>
</td>
</tr>
<tr class="rowColor">
<th class="colFirst" scope="row"><a href="IgnoreEmptyDirectories.html" title="annotation in org.gradle.api.tasks">IgnoreEmptyDirectories</a></th>
<td class="colLast">
<div class="block">Attached to an input property to specify that directories should be ignored
 when snapshotting inputs.</div>
</td>
</tr>
<tr class="altColor">
<th class="colFirst" scope="row"><a href="Input.html" title="annotation in org.gradle.api.tasks">Input</a></th>
<td class="colLast">
<div class="block">Attached to a task property to indicate that the property specifies some input value for the task.</div>
</td>
</tr>
<tr class="rowColor">
<th class="colFirst" scope="row"><a href="InputDirectory.html" title="annotation in org.gradle.api.tasks">InputDirectory</a></th>
<td class="colLast">
<div class="block">Marks a property as specifying an input directory for a task.</div>
</td>
</tr>
<tr class="altColor">
<th class="colFirst" scope="row"><a href="InputFile.html" title="annotation in org.gradle.api.tasks">InputFile</a></th>
<td class="colLast">
<div class="block">Marks a property as specifying an input file for a task.</div>
</td>
</tr>
<tr class="rowColor">
<th class="colFirst" scope="row"><a href="InputFiles.html" title="annotation in org.gradle.api.tasks">InputFiles</a></th>
<td class="colLast">
<div class="block">Marks a property as specifying the input files for a task.</div>
</td>
</tr>
<tr class="altColor">
<th class="colFirst" scope="row"><a href="Internal.html" title="annotation in org.gradle.api.tasks">Internal</a></th>
<td class="colLast">
<div class="block">Attached to a task property to indicate that the property is not to be taken into account for up-to-date checking.</div>
</td>
</tr>
<tr class="rowColor">
<th class="colFirst" scope="row"><a href="LocalState.html" title="annotation in org.gradle.api.tasks">LocalState</a></th>
<td class="colLast">
<div class="block">Marks a property as specifying local state for a task.</div>
</td>
</tr>
<tr class="altColor">
<th class="colFirst" scope="row"><a href="Nested.html" title="annotation in org.gradle.api.tasks">Nested</a></th>
<td class="colLast">
<div class="block">Marks a property as specifying a nested bean, whose properties should be checked for annotations.</div>
</td>
</tr>
<tr class="rowColor">
<th class="colFirst" scope="row"><a href="Optional.html" title="annotation in org.gradle.api.tasks">Optional</a></th>
<td class="colLast">
<div class="block">Marks a task property as optional.</div>
</td>
</tr>
<tr class="altColor">
<th class="colFirst" scope="row"><a href="OutputDirectories.html" title="annotation in org.gradle.api.tasks">OutputDirectories</a></th>
<td class="colLast">
<div class="block">Marks a property as specifying one or more output directories for a task.</div>
</td>
</tr>
<tr class="rowColor">
<th class="colFirst" scope="row"><a href="OutputDirectory.html" title="annotation in org.gradle.api.tasks">OutputDirectory</a></th>
<td class="colLast">
<div class="block">Marks a property as specifying an output directory for a task.</div>
</td>
</tr>
<tr class="altColor">
<th class="colFirst" scope="row"><a href="OutputFile.html" title="annotation in org.gradle.api.tasks">OutputFile</a></th>
<td class="colLast">
<div class="block">Marks a property as specifying an output file for a task.</div>
</td>
</tr>
<tr class="rowColor">
<th class="colFirst" scope="row"><a href="OutputFiles.html" title="annotation in org.gradle.api.tasks">OutputFiles</a></th>
<td class="colLast">
<div class="block">Marks a property as specifying one or more output files for a task.</div>
</td>
</tr>
<tr class="altColor">
<th class="colFirst" scope="row"><a href="PathSensitive.html" title="annotation in org.gradle.api.tasks">PathSensitive</a></th>
<td class="colLast">
<div class="block">Annotates a task file property, specifying which part of the file paths should be considered during up-to-date checks.</div>
</td>
</tr>
<tr class="rowColor">
<th class="colFirst" scope="row"><a href="SkipWhenEmpty.html" title="annotation in org.gradle.api.tasks">SkipWhenEmpty</a></th>
<td class="colLast">
<div class="block">Attached to a task property to indicate that the task should be skipped when the value of the property is an empty
 <a href="../file/FileCollection.html" title="interface in org.gradle.api.file"><code>FileCollection</code></a> or directory.</div>
</td>
</tr>
<tr class="altColor">
<th class="colFirst" scope="row"><a href="TaskAction.html" title="annotation in org.gradle.api.tasks">TaskAction</a></th>
<td class="colLast">
<div class="block">Marks a method as the action to run when the task is executed.</div>
</td>
</tr>
<tr class="rowColor">
<th class="colFirst" scope="row"><a href="UntrackedTask.html" title="annotation in org.gradle.api.tasks">UntrackedTask</a></th>
<td class="colLast">
<div class="block">Attached to a task to declare that the task should be untracked.</div>
</td>
</tr>
</tbody>
</table>
</li>
</ul>
</div>
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../index.html">Overview</a></li>
<li class="navBarCell1Rev">Package</li>
<li>Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
