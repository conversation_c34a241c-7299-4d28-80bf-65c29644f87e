<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc -->
<title><PERSON><PERSON> (Gradle API 8.4)</title>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<link rel="stylesheet" type="text/css" href="../../../../javadoc.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../jquery/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
<script type="text/javascript" src="../../../../jquery/jszip/dist/jszip.min.js"></script>
<script type="text/javascript" src="../../../../jquery/jszip-utils/dist/jszip-utils.min.js"></script>
<!--[if IE]>
<script type="text/javascript" src="../../../../jquery/jszip-utils/dist/jszip-utils-ie.min.js"></script>
<![endif]-->
<script type="text/javascript" src="../../../../jquery/jquery-3.6.0.min.js"></script>
<script type="text/javascript" src="../../../../jquery/jquery-ui.min.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="Logger (Gradle API 8.4)";
        }
    }
    catch(err) {
    }
//-->
var data = {"i0":6,"i1":6,"i2":6,"i3":6,"i4":6,"i5":6,"i6":6,"i7":6,"i8":6,"i9":6,"i10":6,"i11":6,"i12":6,"i13":6};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],4:["t3","Abstract Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
var pathtoroot = "../../../../";
var useModuleDirectories = false;
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="fixedNav">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses.html">All&nbsp;Classes</a></li>
</ul>
<ul class="navListSearch">
<li><label for="search">SEARCH:</label>
<input type="text" id="search" value="search" disabled="disabled">
<input type="reset" id="reset" value="reset" disabled="disabled">
</li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field.summary">Field</a>&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
</div>
<div class="navPadding">&nbsp;</div>
<script type="text/javascript"><!--
$('.navPadding').css('padding-top', $('.fixedNav').css("height"));
//-->
</script>
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle"><span class="packageLabelInType">Package</span>&nbsp;<a href="package-summary.html">org.gradle.api.logging</a></div>
<h2 title="Interface Logger" class="title">Interface Logger</h2>
</div>
<div class="contentContainer">
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Superinterfaces:</dt>
<dd><code>org.slf4j.Logger</code></dd>
</dl>
<hr>
<pre>public interface <span class="typeNameLabel">Logger</span>
extends org.slf4j.Logger</pre>
<div class="block"><p>An extension to the SLF4J <code>Logger</code> interface, which adds the <code>quiet</code> and <code>lifecycle</code> log
 levels.</p>

 <p>You can obtain a <code>Logger</code> instance using <a href="Logging.html#getLogger-java.lang.Class-"><code>Logging.getLogger(Class)</code></a> or <a href="Logging.html#getLogger-java.lang.String-"><code>Logging.getLogger(String)</code></a>. A <code>Logger</code> instance is also available through <a href="../Project.html#getLogger--"><code>Project.getLogger()</code></a>, <a href="../Task.html#getLogger--"><code>Task.getLogger()</code></a> and <a href="../Script.html#getLogger--"><code>Script.getLogger()</code></a>.</p>
 <br>
 <p><b>CAUTION!</b>
 Logging sensitive information (credentials, tokens, certain environment variables) above <a href="#debug-java.lang.String-java.lang.Object...-"><code>debug(java.lang.String, java.lang.Object...)</code></a> level is a security vulnerability.
 See <a href="https://docs.gradle.org/current/userguide/logging.html#sec:debug_security">our recommendations</a> for more information.
 </p></div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- =========== FIELD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.summary">
<!--   -->
</a>
<h3>Field Summary</h3>
<ul class="blockList">
<li class="blockList"><a name="fields.inherited.from.class.org.slf4j.Logger">
<!--   -->
</a>
<h3>Fields inherited from interface&nbsp;org.slf4j.Logger</h3>
<code>ROOT_LOGGER_NAME</code></li>
</ul>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t3" class="tableTab"><span><a href="javascript:show(4);">Abstract Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colSecond" scope="col">Method</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#debug-java.lang.String-java.lang.Object...-">debug</a></span>&#8203;(java.lang.String&nbsp;message,
     java.lang.Object...&nbsp;objects)</code></th>
<td class="colLast">
<div class="block">Multiple-parameters friendly debug method</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#info-java.lang.String-java.lang.Object...-">info</a></span>&#8203;(java.lang.String&nbsp;message,
    java.lang.Object...&nbsp;objects)</code></th>
<td class="colLast">
<div class="block">Logs the given message at info log level.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#isEnabled-org.gradle.api.logging.LogLevel-">isEnabled</a></span>&#8203;(<a href="LogLevel.html" title="enum in org.gradle.api.logging">LogLevel</a>&nbsp;level)</code></th>
<td class="colLast">
<div class="block">Returns true if the given log level is enabled for this logger.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#isLifecycleEnabled--">isLifecycleEnabled</a></span>()</code></th>
<td class="colLast">
<div class="block">Returns true if lifecycle log level is enabled for this logger.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#isQuietEnabled--">isQuietEnabled</a></span>()</code></th>
<td class="colLast">
<div class="block">Returns true if quiet log level is enabled for this logger.</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#lifecycle-java.lang.String-">lifecycle</a></span>&#8203;(java.lang.String&nbsp;message)</code></th>
<td class="colLast">
<div class="block">Logs the given message at lifecycle log level.</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#lifecycle-java.lang.String-java.lang.Object...-">lifecycle</a></span>&#8203;(java.lang.String&nbsp;message,
         java.lang.Object...&nbsp;objects)</code></th>
<td class="colLast">
<div class="block">Logs the given message at lifecycle log level.</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#lifecycle-java.lang.String-java.lang.Throwable-">lifecycle</a></span>&#8203;(java.lang.String&nbsp;message,
         java.lang.Throwable&nbsp;throwable)</code></th>
<td class="colLast">
<div class="block">Logs the given message at lifecycle log level.</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#log-org.gradle.api.logging.LogLevel-java.lang.String-">log</a></span>&#8203;(<a href="LogLevel.html" title="enum in org.gradle.api.logging">LogLevel</a>&nbsp;level,
   java.lang.String&nbsp;message)</code></th>
<td class="colLast">
<div class="block">Logs the given message at the given log level.</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#log-org.gradle.api.logging.LogLevel-java.lang.String-java.lang.Object...-">log</a></span>&#8203;(<a href="LogLevel.html" title="enum in org.gradle.api.logging">LogLevel</a>&nbsp;level,
   java.lang.String&nbsp;message,
   java.lang.Object...&nbsp;objects)</code></th>
<td class="colLast">
<div class="block">Logs the given message at the given log level.</div>
</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#log-org.gradle.api.logging.LogLevel-java.lang.String-java.lang.Throwable-">log</a></span>&#8203;(<a href="LogLevel.html" title="enum in org.gradle.api.logging">LogLevel</a>&nbsp;level,
   java.lang.String&nbsp;message,
   java.lang.Throwable&nbsp;throwable)</code></th>
<td class="colLast">
<div class="block">Logs the given message at the given log level.</div>
</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#quiet-java.lang.String-">quiet</a></span>&#8203;(java.lang.String&nbsp;message)</code></th>
<td class="colLast">
<div class="block">Logs the given message at quiet log level.</div>
</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#quiet-java.lang.String-java.lang.Object...-">quiet</a></span>&#8203;(java.lang.String&nbsp;message,
     java.lang.Object...&nbsp;objects)</code></th>
<td class="colLast">
<div class="block">Logs the given message at quiet log level.</div>
</td>
</tr>
<tr id="i13" class="rowColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#quiet-java.lang.String-java.lang.Throwable-">quiet</a></span>&#8203;(java.lang.String&nbsp;message,
     java.lang.Throwable&nbsp;throwable)</code></th>
<td class="colLast">
<div class="block">Logs the given message at quiet log level.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.org.slf4j.Logger">
<!--   -->
</a>
<h3>Methods inherited from interface&nbsp;org.slf4j.Logger</h3>
<code>debug, debug, debug, debug, debug, debug, debug, debug, debug, error, error, error, error, error, error, error, error, error, error, getName, info, info, info, info, info, info, info, info, info, isDebugEnabled, isDebugEnabled, isErrorEnabled, isErrorEnabled, isInfoEnabled, isInfoEnabled, isTraceEnabled, isTraceEnabled, isWarnEnabled, isWarnEnabled, trace, trace, trace, trace, trace, trace, trace, trace, trace, trace, warn, warn, warn, warn, warn, warn, warn, warn, warn, warn</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="isLifecycleEnabled--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isLifecycleEnabled</h4>
<pre class="methodSignature">boolean&nbsp;isLifecycleEnabled()</pre>
<div class="block">Returns true if lifecycle log level is enabled for this logger.</div>
</li>
</ul>
<a name="debug-java.lang.String-java.lang.Object...-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>debug</h4>
<pre class="methodSignature">void&nbsp;debug&#8203;(java.lang.String&nbsp;message,
           java.lang.Object...&nbsp;objects)</pre>
<div class="block">Multiple-parameters friendly debug method</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code>debug</code>&nbsp;in interface&nbsp;<code>org.slf4j.Logger</code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>message</code> - the log message</dd>
<dd><code>objects</code> - the log message parameters</dd>
</dl>
</li>
</ul>
<a name="lifecycle-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>lifecycle</h4>
<pre class="methodSignature">void&nbsp;lifecycle&#8203;(java.lang.String&nbsp;message)</pre>
<div class="block">Logs the given message at lifecycle log level.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>message</code> - the log message.</dd>
</dl>
</li>
</ul>
<a name="lifecycle-java.lang.String-java.lang.Object...-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>lifecycle</h4>
<pre class="methodSignature">void&nbsp;lifecycle&#8203;(java.lang.String&nbsp;message,
               java.lang.Object...&nbsp;objects)</pre>
<div class="block">Logs the given message at lifecycle log level.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>message</code> - the log message.</dd>
<dd><code>objects</code> - the log message parameters.</dd>
</dl>
</li>
</ul>
<a name="lifecycle-java.lang.String-java.lang.Throwable-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>lifecycle</h4>
<pre class="methodSignature">void&nbsp;lifecycle&#8203;(java.lang.String&nbsp;message,
               java.lang.Throwable&nbsp;throwable)</pre>
<div class="block">Logs the given message at lifecycle log level.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>message</code> - the log message.</dd>
<dd><code>throwable</code> - the exception to log.</dd>
</dl>
</li>
</ul>
<a name="isQuietEnabled--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isQuietEnabled</h4>
<pre class="methodSignature">boolean&nbsp;isQuietEnabled()</pre>
<div class="block">Returns true if quiet log level is enabled for this logger.</div>
</li>
</ul>
<a name="quiet-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>quiet</h4>
<pre class="methodSignature">void&nbsp;quiet&#8203;(java.lang.String&nbsp;message)</pre>
<div class="block">Logs the given message at quiet log level.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>message</code> - the log message.</dd>
</dl>
</li>
</ul>
<a name="quiet-java.lang.String-java.lang.Object...-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>quiet</h4>
<pre class="methodSignature">void&nbsp;quiet&#8203;(java.lang.String&nbsp;message,
           java.lang.Object...&nbsp;objects)</pre>
<div class="block">Logs the given message at quiet log level.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>message</code> - the log message.</dd>
<dd><code>objects</code> - the log message parameters.</dd>
</dl>
</li>
</ul>
<a name="info-java.lang.String-java.lang.Object...-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>info</h4>
<pre class="methodSignature">void&nbsp;info&#8203;(java.lang.String&nbsp;message,
          java.lang.Object...&nbsp;objects)</pre>
<div class="block">Logs the given message at info log level.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code>info</code>&nbsp;in interface&nbsp;<code>org.slf4j.Logger</code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>message</code> - the log message.</dd>
<dd><code>objects</code> - the log message parameters.</dd>
</dl>
</li>
</ul>
<a name="quiet-java.lang.String-java.lang.Throwable-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>quiet</h4>
<pre class="methodSignature">void&nbsp;quiet&#8203;(java.lang.String&nbsp;message,
           java.lang.Throwable&nbsp;throwable)</pre>
<div class="block">Logs the given message at quiet log level.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>message</code> - the log message.</dd>
<dd><code>throwable</code> - the exception to log.</dd>
</dl>
</li>
</ul>
<a name="isEnabled-org.gradle.api.logging.LogLevel-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isEnabled</h4>
<pre class="methodSignature">boolean&nbsp;isEnabled&#8203;(<a href="LogLevel.html" title="enum in org.gradle.api.logging">LogLevel</a>&nbsp;level)</pre>
<div class="block">Returns true if the given log level is enabled for this logger.</div>
</li>
</ul>
<a name="log-org.gradle.api.logging.LogLevel-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>log</h4>
<pre class="methodSignature">void&nbsp;log&#8203;(<a href="LogLevel.html" title="enum in org.gradle.api.logging">LogLevel</a>&nbsp;level,
         java.lang.String&nbsp;message)</pre>
<div class="block">Logs the given message at the given log level.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>level</code> - the log level.</dd>
<dd><code>message</code> - the log message.</dd>
</dl>
</li>
</ul>
<a name="log-org.gradle.api.logging.LogLevel-java.lang.String-java.lang.Object...-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>log</h4>
<pre class="methodSignature">void&nbsp;log&#8203;(<a href="LogLevel.html" title="enum in org.gradle.api.logging">LogLevel</a>&nbsp;level,
         java.lang.String&nbsp;message,
         java.lang.Object...&nbsp;objects)</pre>
<div class="block">Logs the given message at the given log level.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>level</code> - the log level.</dd>
<dd><code>message</code> - the log message.</dd>
<dd><code>objects</code> - the log message parameters.</dd>
</dl>
</li>
</ul>
<a name="log-org.gradle.api.logging.LogLevel-java.lang.String-java.lang.Throwable-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>log</h4>
<pre class="methodSignature">void&nbsp;log&#8203;(<a href="LogLevel.html" title="enum in org.gradle.api.logging">LogLevel</a>&nbsp;level,
         java.lang.String&nbsp;message,
         java.lang.Throwable&nbsp;throwable)</pre>
<div class="block">Logs the given message at the given log level.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>level</code> - the log level.</dd>
<dd><code>message</code> - the log message.</dd>
<dd><code>throwable</code> - the exception to log.</dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field.summary">Field</a>&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
