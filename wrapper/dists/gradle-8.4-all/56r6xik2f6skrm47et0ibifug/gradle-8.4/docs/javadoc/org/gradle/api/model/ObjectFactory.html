<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc -->
<title>ObjectFactory (Gradle API 8.4)</title>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<link rel="stylesheet" type="text/css" href="../../../../javadoc.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../jquery/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
<script type="text/javascript" src="../../../../jquery/jszip/dist/jszip.min.js"></script>
<script type="text/javascript" src="../../../../jquery/jszip-utils/dist/jszip-utils.min.js"></script>
<!--[if IE]>
<script type="text/javascript" src="../../../../jquery/jszip-utils/dist/jszip-utils-ie.min.js"></script>
<![endif]-->
<script type="text/javascript" src="../../../../jquery/jquery-3.6.0.min.js"></script>
<script type="text/javascript" src="../../../../jquery/jquery-ui.min.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="ObjectFactory (Gradle API 8.4)";
        }
    }
    catch(err) {
    }
//-->
var data = {"i0":6,"i1":6,"i2":6,"i3":6,"i4":6,"i5":6,"i6":6,"i7":6,"i8":6,"i9":6,"i10":6,"i11":6,"i12":6,"i13":6,"i14":6,"i15":6,"i16":6};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],4:["t3","Abstract Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
var pathtoroot = "../../../../";
var useModuleDirectories = false;
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="fixedNav">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses.html">All&nbsp;Classes</a></li>
</ul>
<ul class="navListSearch">
<li><label for="search">SEARCH:</label>
<input type="text" id="search" value="search" disabled="disabled">
<input type="reset" id="reset" value="reset" disabled="disabled">
</li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
</div>
<div class="navPadding">&nbsp;</div>
<script type="text/javascript"><!--
$('.navPadding').css('padding-top', $('.fixedNav').css("height"));
//-->
</script>
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle"><span class="packageLabelInType">Package</span>&nbsp;<a href="package-summary.html">org.gradle.api.model</a></div>
<h2 title="Interface ObjectFactory" class="title">Interface ObjectFactory</h2>
</div>
<div class="contentContainer">
<div class="description">
<ul class="blockList">
<li class="blockList">
<hr>
<pre>public interface <span class="typeNameLabel">ObjectFactory</span></pre>
<div class="block">A factory for creating various kinds of model objects.
 <p>
 An instance of the factory can be injected into a task, plugin or other object by annotating a public constructor or property getter method with <code>javax.inject.Inject</code>.
 It is also available via <a href="../Project.html#getObjects--"><code>Project.getObjects()</code></a>.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>4.0</dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t3" class="tableTab"><span><a href="javascript:show(4);">Abstract Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colSecond" scope="col">Method</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code><a href="../file/DirectoryProperty.html" title="interface in org.gradle.api.file">DirectoryProperty</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#directoryProperty--">directoryProperty</a></span>()</code></th>
<td class="colLast">
<div class="block">Creates a new <a href="../file/DirectoryProperty.html" title="interface in org.gradle.api.file"><code>DirectoryProperty</code></a> that uses the project directory to resolve relative paths, if required.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>&lt;T&gt;&nbsp;<a href="../NamedDomainObjectContainer.html" title="interface in org.gradle.api">NamedDomainObjectContainer</a>&lt;T&gt;</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#domainObjectContainer-java.lang.Class-">domainObjectContainer</a></span>&#8203;(java.lang.Class&lt;T&gt;&nbsp;elementType)</code></th>
<td class="colLast">
<div class="block">Creates a new <a href="../NamedDomainObjectContainer.html" title="interface in org.gradle.api"><code>NamedDomainObjectContainer</code></a> for managing named objects of the specified type.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>&lt;T&gt;&nbsp;<a href="../NamedDomainObjectContainer.html" title="interface in org.gradle.api">NamedDomainObjectContainer</a>&lt;T&gt;</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#domainObjectContainer-java.lang.Class-org.gradle.api.NamedDomainObjectFactory-">domainObjectContainer</a></span>&#8203;(java.lang.Class&lt;T&gt;&nbsp;elementType,
                     <a href="../NamedDomainObjectFactory.html" title="interface in org.gradle.api">NamedDomainObjectFactory</a>&lt;T&gt;&nbsp;factory)</code></th>
<td class="colLast">
<div class="block">Creates a new <a href="../NamedDomainObjectContainer.html" title="interface in org.gradle.api"><code>NamedDomainObjectContainer</code></a> for managing named objects of the specified type.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>&lt;T&gt;&nbsp;<a href="../DomainObjectSet.html" title="interface in org.gradle.api">DomainObjectSet</a>&lt;T&gt;</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#domainObjectSet-java.lang.Class-">domainObjectSet</a></span>&#8203;(java.lang.Class&lt;T&gt;&nbsp;elementType)</code></th>
<td class="colLast">
<div class="block">Creates a new <a href="../DomainObjectSet.html" title="interface in org.gradle.api"><code>DomainObjectSet</code></a> for managing objects of the specified type.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code><a href="../file/ConfigurableFileCollection.html" title="interface in org.gradle.api.file">ConfigurableFileCollection</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#fileCollection--">fileCollection</a></span>()</code></th>
<td class="colLast">
<div class="block">Creates a new <a href="../file/ConfigurableFileCollection.html" title="interface in org.gradle.api.file"><code>ConfigurableFileCollection</code></a>.</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code><a href="../file/RegularFileProperty.html" title="interface in org.gradle.api.file">RegularFileProperty</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#fileProperty--">fileProperty</a></span>()</code></th>
<td class="colLast">
<div class="block">Creates a new <a href="../file/RegularFileProperty.html" title="interface in org.gradle.api.file"><code>RegularFileProperty</code></a> that uses the project directory to resolve relative paths, if required.</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code><a href="../file/ConfigurableFileTree.html" title="interface in org.gradle.api.file">ConfigurableFileTree</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#fileTree--">fileTree</a></span>()</code></th>
<td class="colLast">
<div class="block">Creates a new <a href="../file/ConfigurableFileTree.html" title="interface in org.gradle.api.file"><code>ConfigurableFileTree</code></a>.</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>&lt;T&gt;&nbsp;<a href="../provider/ListProperty.html" title="interface in org.gradle.api.provider">ListProperty</a>&lt;T&gt;</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#listProperty-java.lang.Class-">listProperty</a></span>&#8203;(java.lang.Class&lt;T&gt;&nbsp;elementType)</code></th>
<td class="colLast">
<div class="block">Creates a <a href="../provider/ListProperty.html" title="interface in org.gradle.api.provider"><code>ListProperty</code></a> implementation to hold a <code>List</code> of the given element type <code>T</code>.</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>&lt;K,&#8203;V&gt;<br><a href="../provider/MapProperty.html" title="interface in org.gradle.api.provider">MapProperty</a>&lt;K,&#8203;V&gt;</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#mapProperty-java.lang.Class-java.lang.Class-">mapProperty</a></span>&#8203;(java.lang.Class&lt;K&gt;&nbsp;keyType,
           java.lang.Class&lt;V&gt;&nbsp;valueType)</code></th>
<td class="colLast">
<div class="block">Creates a <a href="../provider/MapProperty.html" title="interface in org.gradle.api.provider"><code>MapProperty</code></a> implementation to hold a <code>Map</code> of the given key type <code>K</code> and value type <code>V</code>.</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>&lt;T extends <a href="../Named.html" title="interface in org.gradle.api">Named</a>&gt;<br>T</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#named-java.lang.Class-java.lang.String-">named</a></span>&#8203;(java.lang.Class&lt;T&gt;&nbsp;type,
     java.lang.String&nbsp;name)</code></th>
<td class="colLast">
<div class="block">Creates a simple immutable <a href="../Named.html" title="interface in org.gradle.api"><code>Named</code></a> object of the given type and name.</div>
</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code>&lt;T&gt;&nbsp;<a href="../NamedDomainObjectList.html" title="interface in org.gradle.api">NamedDomainObjectList</a>&lt;T&gt;</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#namedDomainObjectList-java.lang.Class-">namedDomainObjectList</a></span>&#8203;(java.lang.Class&lt;T&gt;&nbsp;elementType)</code></th>
<td class="colLast">
<div class="block">Creates a new <a href="../NamedDomainObjectList.html" title="interface in org.gradle.api"><code>NamedDomainObjectList</code></a> for managing named objects of the specified type.</div>
</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code>&lt;T&gt;&nbsp;<a href="../NamedDomainObjectSet.html" title="interface in org.gradle.api">NamedDomainObjectSet</a>&lt;T&gt;</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#namedDomainObjectSet-java.lang.Class-">namedDomainObjectSet</a></span>&#8203;(java.lang.Class&lt;T&gt;&nbsp;elementType)</code></th>
<td class="colLast">
<div class="block">Creates a new <a href="../NamedDomainObjectSet.html" title="interface in org.gradle.api"><code>NamedDomainObjectSet</code></a> for managing named objects of the specified type.</div>
</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code>&lt;T&gt;&nbsp;T</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#newInstance-java.lang.Class-java.lang.Object...-">newInstance</a></span>&#8203;(java.lang.Class&lt;? extends T&gt;&nbsp;type,
           java.lang.Object...&nbsp;parameters)</code></th>
<td class="colLast">
<div class="block">Create a new instance of T, using <code>parameters</code> as the construction parameters.</div>
</td>
</tr>
<tr id="i13" class="rowColor">
<td class="colFirst"><code>&lt;T&gt;&nbsp;<a href="../ExtensiblePolymorphicDomainObjectContainer.html" title="interface in org.gradle.api">ExtensiblePolymorphicDomainObjectContainer</a>&lt;T&gt;</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#polymorphicDomainObjectContainer-java.lang.Class-">polymorphicDomainObjectContainer</a></span>&#8203;(java.lang.Class&lt;T&gt;&nbsp;elementType)</code></th>
<td class="colLast">
<div class="block">Creates a new <a href="../ExtensiblePolymorphicDomainObjectContainer.html" title="interface in org.gradle.api"><code>ExtensiblePolymorphicDomainObjectContainer</code></a> for managing named objects of the specified type.</div>
</td>
</tr>
<tr id="i14" class="altColor">
<td class="colFirst"><code>&lt;T&gt;&nbsp;<a href="../provider/Property.html" title="interface in org.gradle.api.provider">Property</a>&lt;T&gt;</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#property-java.lang.Class-">property</a></span>&#8203;(java.lang.Class&lt;T&gt;&nbsp;valueType)</code></th>
<td class="colLast">
<div class="block">Creates a <a href="../provider/Property.html" title="interface in org.gradle.api.provider"><code>Property</code></a> implementation to hold values of the given type.</div>
</td>
</tr>
<tr id="i15" class="rowColor">
<td class="colFirst"><code>&lt;T&gt;&nbsp;<a href="../provider/SetProperty.html" title="interface in org.gradle.api.provider">SetProperty</a>&lt;T&gt;</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setProperty-java.lang.Class-">setProperty</a></span>&#8203;(java.lang.Class&lt;T&gt;&nbsp;elementType)</code></th>
<td class="colLast">
<div class="block">Creates a <a href="../provider/SetProperty.html" title="interface in org.gradle.api.provider"><code>SetProperty</code></a> implementation to hold a <code>Set</code> of the given element type <code>T</code>.</div>
</td>
</tr>
<tr id="i16" class="altColor">
<td class="colFirst"><code><a href="../file/SourceDirectorySet.html" title="interface in org.gradle.api.file">SourceDirectorySet</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#sourceDirectorySet-java.lang.String-java.lang.String-">sourceDirectorySet</a></span>&#8203;(java.lang.String&nbsp;name,
                  java.lang.String&nbsp;displayName)</code></th>
<td class="colLast">
<div class="block">Creates a <a href="../file/SourceDirectorySet.html" title="interface in org.gradle.api.file"><code>SourceDirectorySet</code></a>.</div>
</td>
</tr>
</table>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="named-java.lang.Class-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>named</h4>
<pre class="methodSignature">&lt;T extends <a href="../Named.html" title="interface in org.gradle.api">Named</a>&gt;&nbsp;T&nbsp;named&#8203;(java.lang.Class&lt;T&gt;&nbsp;type,
                          java.lang.String&nbsp;name)
                   throws <a href="../reflect/ObjectInstantiationException.html" title="class in org.gradle.api.reflect">ObjectInstantiationException</a></pre>
<div class="block">Creates a simple immutable <a href="../Named.html" title="interface in org.gradle.api"><code>Named</code></a> object of the given type and name.

 <p>The given type can be an interface that extends <a href="../Named.html" title="interface in org.gradle.api"><code>Named</code></a> or an abstract class that 'implements' <a href="../Named.html" title="interface in org.gradle.api"><code>Named</code></a>. An abstract class, if provided:</p>
 <ul>
     <li>Must provide a zero-args constructor that is not private.</li>
     <li>Must not define or inherit any instance fields.</li>
     <li>Should not provide an implementation for <a href="../Named.html#getName--"><code>Named.getName()</code></a> and should define this method as abstract. Any implementation will be overridden.</li>
     <li>Must not define or inherit any other abstract methods.</li>
 </ul>

 <p>An interface, if provided, must not define or inherit any other methods.</p>

 <p>Objects created using this method are not decorated or extensible.</p></div>
<dl>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code><a href="../reflect/ObjectInstantiationException.html" title="class in org.gradle.api.reflect">ObjectInstantiationException</a></code> - On failure to create the new instance.</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>4.0</dd>
</dl>
</li>
</ul>
<a name="newInstance-java.lang.Class-java.lang.Object...-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>newInstance</h4>
<pre class="methodSignature">&lt;T&gt;&nbsp;T&nbsp;newInstance&#8203;(java.lang.Class&lt;? extends T&gt;&nbsp;type,
                  java.lang.Object...&nbsp;parameters)
           throws <a href="../reflect/ObjectInstantiationException.html" title="class in org.gradle.api.reflect">ObjectInstantiationException</a></pre>
<div class="block">Create a new instance of T, using <code>parameters</code> as the construction parameters.

 <p>The type must be non-final, and can be a class, abstract class or interface.</p>

 <p>Objects created using this method are decorated and extensible, meaning that they have DSL support mixed in and can be extended using the `extensions` property, similar to the <a href="../Project.html" title="interface in org.gradle.api"><code>Project</code></a> object.</p>

 <p>An @Inject annotation is required on any constructor that accepts parameters because JSR-330 semantics for dependency injection are used. In addition to those parameters provided as an argument to this method, the following services are also available for injection:</p>

 <ul>
     <li><a href="ObjectFactory.html" title="interface in org.gradle.api.model"><code>ObjectFactory</code></a>.</li>
     <li><a href="../file/ProjectLayout.html" title="interface in org.gradle.api.file"><code>ProjectLayout</code></a>.</li>
     <li><a href="../provider/ProviderFactory.html" title="interface in org.gradle.api.provider"><code>ProviderFactory</code></a>.</li>
     <li><a href="../../process/ExecOperations.html" title="interface in org.gradle.process"><code>ExecOperations</code></a></li>
     <li><a href="../file/FileSystemOperations.html" title="interface in org.gradle.api.file"><code>FileSystemOperations</code></a></li>
 </ul></div>
<dl>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code><a href="../reflect/ObjectInstantiationException.html" title="class in org.gradle.api.reflect">ObjectInstantiationException</a></code> - On failure to create the new instance.</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>4.2</dd>
</dl>
</li>
</ul>
<a name="sourceDirectorySet-java.lang.String-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>sourceDirectorySet</h4>
<pre class="methodSignature"><a href="../file/SourceDirectorySet.html" title="interface in org.gradle.api.file">SourceDirectorySet</a>&nbsp;sourceDirectorySet&#8203;(java.lang.String&nbsp;name,
                                      java.lang.String&nbsp;displayName)</pre>
<div class="block">Creates a <a href="../file/SourceDirectorySet.html" title="interface in org.gradle.api.file"><code>SourceDirectorySet</code></a>.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>name</code> - A short name for the set.</dd>
<dd><code>displayName</code> - A human consumable display name for the set.</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>5.0</dd>
</dl>
</li>
</ul>
<a name="fileCollection--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>fileCollection</h4>
<pre class="methodSignature"><a href="../file/ConfigurableFileCollection.html" title="interface in org.gradle.api.file">ConfigurableFileCollection</a>&nbsp;fileCollection()</pre>
<div class="block">Creates a new <a href="../file/ConfigurableFileCollection.html" title="interface in org.gradle.api.file"><code>ConfigurableFileCollection</code></a>. The collection is initially empty.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>5.3</dd>
</dl>
</li>
</ul>
<a name="fileTree--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>fileTree</h4>
<pre class="methodSignature"><a href="../file/ConfigurableFileTree.html" title="interface in org.gradle.api.file">ConfigurableFileTree</a>&nbsp;fileTree()</pre>
<div class="block">Creates a new <a href="../file/ConfigurableFileTree.html" title="interface in org.gradle.api.file"><code>ConfigurableFileTree</code></a>. The tree will have no base dir specified.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>6.0</dd>
</dl>
</li>
</ul>
<a name="domainObjectContainer-java.lang.Class-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>domainObjectContainer</h4>
<pre class="methodSignature">&lt;T&gt;&nbsp;<a href="../NamedDomainObjectContainer.html" title="interface in org.gradle.api">NamedDomainObjectContainer</a>&lt;T&gt;&nbsp;domainObjectContainer&#8203;(java.lang.Class&lt;T&gt;&nbsp;elementType)</pre>
<div class="block"><p>Creates a new <a href="../NamedDomainObjectContainer.html" title="interface in org.gradle.api"><code>NamedDomainObjectContainer</code></a> for managing named objects of the specified type.</p>

 <p>The specified element type must have a public constructor which takes the name as a String parameter. The type must be non-final and a class or abstract class.</p>

 <p>Interfaces are supported if they declare a read-only <code>name</code> property of type String, and are otherwise empty or consist entirely of managed properties.</p>

 <p>All objects <b>MUST</b> expose their name as a bean property called "name". The name must be constant for the life of the object.</p>

 <p>The objects created by the container are decorated and extensible, and have services available for injection. See <a href="#newInstance-java.lang.Class-java.lang.Object...-"><code>newInstance(Class, Object...)</code></a> for more details.</p></div>
<dl>
<dt><span class="paramLabel">Type Parameters:</span></dt>
<dd><code>T</code> - The type of objects for the container to contain.</dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>elementType</code> - The type of objects for the container to contain.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The container. Never returns null.</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>5.5</dd>
</dl>
</li>
</ul>
<a name="domainObjectContainer-java.lang.Class-org.gradle.api.NamedDomainObjectFactory-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>domainObjectContainer</h4>
<pre class="methodSignature">&lt;T&gt;&nbsp;<a href="../NamedDomainObjectContainer.html" title="interface in org.gradle.api">NamedDomainObjectContainer</a>&lt;T&gt;&nbsp;domainObjectContainer&#8203;(java.lang.Class&lt;T&gt;&nbsp;elementType,
                                                        <a href="../NamedDomainObjectFactory.html" title="interface in org.gradle.api">NamedDomainObjectFactory</a>&lt;T&gt;&nbsp;factory)</pre>
<div class="block"><p>Creates a new <a href="../NamedDomainObjectContainer.html" title="interface in org.gradle.api"><code>NamedDomainObjectContainer</code></a> for managing named objects of the specified type. The given factory is used to create object instances.</p>

 <p>All objects <b>MUST</b> expose their name as a bean property named "name". The name must be constant for the life of the object.</p></div>
<dl>
<dt><span class="paramLabel">Type Parameters:</span></dt>
<dd><code>T</code> - The type of objects for the container to contain.</dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>elementType</code> - The type of objects for the container to contain.</dd>
<dd><code>factory</code> - The factory to use to create object instances.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The container. Never returns null.</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>5.5</dd>
</dl>
</li>
</ul>
<a name="polymorphicDomainObjectContainer-java.lang.Class-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>polymorphicDomainObjectContainer</h4>
<pre class="methodSignature">&lt;T&gt;&nbsp;<a href="../ExtensiblePolymorphicDomainObjectContainer.html" title="interface in org.gradle.api">ExtensiblePolymorphicDomainObjectContainer</a>&lt;T&gt;&nbsp;polymorphicDomainObjectContainer&#8203;(java.lang.Class&lt;T&gt;&nbsp;elementType)</pre>
<div class="block"><p>Creates a new <a href="../ExtensiblePolymorphicDomainObjectContainer.html" title="interface in org.gradle.api"><code>ExtensiblePolymorphicDomainObjectContainer</code></a> for managing named objects of the specified type.</p>

 <p>The returned container will not have any factories or bindings registered.</p></div>
<dl>
<dt><span class="paramLabel">Type Parameters:</span></dt>
<dd><code>T</code> - The type of objects for the container to contain.</dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>elementType</code> - The type of objects for the container to contain.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The container.</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>6.1</dd>
</dl>
</li>
</ul>
<a name="domainObjectSet-java.lang.Class-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>domainObjectSet</h4>
<pre class="methodSignature">&lt;T&gt;&nbsp;<a href="../DomainObjectSet.html" title="interface in org.gradle.api">DomainObjectSet</a>&lt;T&gt;&nbsp;domainObjectSet&#8203;(java.lang.Class&lt;T&gt;&nbsp;elementType)</pre>
<div class="block">Creates a new <a href="../DomainObjectSet.html" title="interface in org.gradle.api"><code>DomainObjectSet</code></a> for managing objects of the specified type.</div>
<dl>
<dt><span class="paramLabel">Type Parameters:</span></dt>
<dd><code>T</code> - The type of objects for the domain object set to contain.</dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>elementType</code> - The type of objects for the domain object set to contain.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The domain object set. Never returns null.</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>5.5</dd>
</dl>
</li>
</ul>
<a name="namedDomainObjectSet-java.lang.Class-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>namedDomainObjectSet</h4>
<pre class="methodSignature">&lt;T&gt;&nbsp;<a href="../NamedDomainObjectSet.html" title="interface in org.gradle.api">NamedDomainObjectSet</a>&lt;T&gt;&nbsp;namedDomainObjectSet&#8203;(java.lang.Class&lt;T&gt;&nbsp;elementType)</pre>
<div class="block">Creates a new <a href="../NamedDomainObjectSet.html" title="interface in org.gradle.api"><code>NamedDomainObjectSet</code></a> for managing named objects of the specified type.

 <p>All objects <b>MUST</b> expose their name as a bean property called "name". The name must be constant for the life of the object.</p></div>
<dl>
<dt><span class="paramLabel">Type Parameters:</span></dt>
<dd><code>T</code> - The type of objects for the domain object set to contain.</dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>elementType</code> - The type of objects for the domain object set to contain.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The domain object set.</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>6.1</dd>
</dl>
</li>
</ul>
<a name="namedDomainObjectList-java.lang.Class-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>namedDomainObjectList</h4>
<pre class="methodSignature">&lt;T&gt;&nbsp;<a href="../NamedDomainObjectList.html" title="interface in org.gradle.api">NamedDomainObjectList</a>&lt;T&gt;&nbsp;namedDomainObjectList&#8203;(java.lang.Class&lt;T&gt;&nbsp;elementType)</pre>
<div class="block">Creates a new <a href="../NamedDomainObjectList.html" title="interface in org.gradle.api"><code>NamedDomainObjectList</code></a> for managing named objects of the specified type.

 <p>All objects <b>MUST</b> expose their name as a bean property called "name". The name must be constant for the life of the object.</p></div>
<dl>
<dt><span class="paramLabel">Type Parameters:</span></dt>
<dd><code>T</code> - The type of objects for the domain object set to contain.</dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>elementType</code> - The type of objects for the domain object set to contain.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The domain object list.</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>6.1</dd>
</dl>
</li>
</ul>
<a name="property-java.lang.Class-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>property</h4>
<pre class="methodSignature">&lt;T&gt;&nbsp;<a href="../provider/Property.html" title="interface in org.gradle.api.provider">Property</a>&lt;T&gt;&nbsp;property&#8203;(java.lang.Class&lt;T&gt;&nbsp;valueType)</pre>
<div class="block">Creates a <a href="../provider/Property.html" title="interface in org.gradle.api.provider"><code>Property</code></a> implementation to hold values of the given type. The property has no initial value.

 <p>For certain types, there are more specialized property factory methods available:</p>
 <ul>
 <li>For <code>List</code> properties, you should use <a href="#listProperty-java.lang.Class-"><code>listProperty(Class)</code></a>.</li>
 <li>For <code>Set</code> properties, you should use <a href="#setProperty-java.lang.Class-"><code>setProperty(Class)</code></a>.</li>
 <li>For <code>Map</code> properties, you should use <a href="#mapProperty-java.lang.Class-java.lang.Class-"><code>mapProperty(Class, Class)</code></a>.</li>
 <li>For <a href="../file/Directory.html" title="interface in org.gradle.api.file"><code>Directory</code></a> properties, you should use <a href="#directoryProperty--"><code>directoryProperty()</code></a>.</li>
 <li>For <a href="../file/RegularFile.html" title="interface in org.gradle.api.file"><code>RegularFile</code></a> properties, you should use <a href="#fileProperty--"><code>fileProperty()</code></a>.</li>
 </ul></div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>valueType</code> - The type of the property.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The property. Never returns null.</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>4.3</dd>
</dl>
</li>
</ul>
<a name="listProperty-java.lang.Class-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>listProperty</h4>
<pre class="methodSignature">&lt;T&gt;&nbsp;<a href="../provider/ListProperty.html" title="interface in org.gradle.api.provider">ListProperty</a>&lt;T&gt;&nbsp;listProperty&#8203;(java.lang.Class&lt;T&gt;&nbsp;elementType)</pre>
<div class="block">Creates a <a href="../provider/ListProperty.html" title="interface in org.gradle.api.provider"><code>ListProperty</code></a> implementation to hold a <code>List</code> of the given element type <code>T</code>. The property has an empty list as its initial value.

 <p>The implementation will return immutable <code>List</code> values from its query methods.</p></div>
<dl>
<dt><span class="paramLabel">Type Parameters:</span></dt>
<dd><code>T</code> - The type of element.</dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>elementType</code> - The type of element.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The property. Never returns null.</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>4.3</dd>
</dl>
</li>
</ul>
<a name="setProperty-java.lang.Class-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setProperty</h4>
<pre class="methodSignature">&lt;T&gt;&nbsp;<a href="../provider/SetProperty.html" title="interface in org.gradle.api.provider">SetProperty</a>&lt;T&gt;&nbsp;setProperty&#8203;(java.lang.Class&lt;T&gt;&nbsp;elementType)</pre>
<div class="block">Creates a <a href="../provider/SetProperty.html" title="interface in org.gradle.api.provider"><code>SetProperty</code></a> implementation to hold a <code>Set</code> of the given element type <code>T</code>. The property has an empty set as its initial value.

 <p>The implementation will return immutable <code>Set</code> values from its query methods.</p></div>
<dl>
<dt><span class="paramLabel">Type Parameters:</span></dt>
<dd><code>T</code> - The type of element.</dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>elementType</code> - The type of element.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The property. Never returns null.</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>4.5</dd>
</dl>
</li>
</ul>
<a name="mapProperty-java.lang.Class-java.lang.Class-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>mapProperty</h4>
<pre class="methodSignature">&lt;K,&#8203;V&gt;&nbsp;<a href="../provider/MapProperty.html" title="interface in org.gradle.api.provider">MapProperty</a>&lt;K,&#8203;V&gt;&nbsp;mapProperty&#8203;(java.lang.Class&lt;K&gt;&nbsp;keyType,
                                               java.lang.Class&lt;V&gt;&nbsp;valueType)</pre>
<div class="block">Creates a <a href="../provider/MapProperty.html" title="interface in org.gradle.api.provider"><code>MapProperty</code></a> implementation to hold a <code>Map</code> of the given key type <code>K</code> and value type <code>V</code>. The property has an empty map as its initial value.

 <p>The implementation will return immutable <code>Map</code> values from its query methods.</p></div>
<dl>
<dt><span class="paramLabel">Type Parameters:</span></dt>
<dd><code>K</code> - the type of key.</dd>
<dd><code>V</code> - the type of value.</dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>keyType</code> - the type of key.</dd>
<dd><code>valueType</code> - the type of value.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the property. Never returns null.</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>5.1</dd>
</dl>
</li>
</ul>
<a name="directoryProperty--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>directoryProperty</h4>
<pre class="methodSignature"><a href="../file/DirectoryProperty.html" title="interface in org.gradle.api.file">DirectoryProperty</a>&nbsp;directoryProperty()</pre>
<div class="block">Creates a new <a href="../file/DirectoryProperty.html" title="interface in org.gradle.api.file"><code>DirectoryProperty</code></a> that uses the project directory to resolve relative paths, if required. The property has no initial value.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>5.0</dd>
</dl>
</li>
</ul>
<a name="fileProperty--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>fileProperty</h4>
<pre class="methodSignature"><a href="../file/RegularFileProperty.html" title="interface in org.gradle.api.file">RegularFileProperty</a>&nbsp;fileProperty()</pre>
<div class="block">Creates a new <a href="../file/RegularFileProperty.html" title="interface in org.gradle.api.file"><code>RegularFileProperty</code></a> that uses the project directory to resolve relative paths, if required. The property has no initial value.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>5.0</dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
