<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc -->
<title>Wrapper (Gradle API 8.4)</title>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<link rel="stylesheet" type="text/css" href="../../../../../javadoc.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../jquery/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
<script type="text/javascript" src="../../../../../jquery/jszip/dist/jszip.min.js"></script>
<script type="text/javascript" src="../../../../../jquery/jszip-utils/dist/jszip-utils.min.js"></script>
<!--[if IE]>
<script type="text/javascript" src="../../../../../jquery/jszip-utils/dist/jszip-utils-ie.min.js"></script>
<![endif]-->
<script type="text/javascript" src="../../../../../jquery/jquery-3.6.0.min.js"></script>
<script type="text/javascript" src="../../../../../jquery/jquery-ui.min.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="Wrapper (Gradle API 8.4)";
        }
    }
    catch(err) {
    }
//-->
var data = {"i0":10,"i1":10,"i2":10,"i3":10,"i4":10,"i5":10,"i6":10,"i7":10,"i8":10,"i9":10,"i10":10,"i11":10,"i12":10,"i13":10,"i14":10,"i15":6,"i16":10,"i17":10,"i18":10,"i19":10,"i20":10,"i21":10,"i22":10,"i23":10,"i24":10,"i25":10,"i26":10,"i27":10,"i28":10};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],4:["t3","Abstract Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
var pathtoroot = "../../../../../";
var useModuleDirectories = false;
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="fixedNav">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../../allclasses.html">All&nbsp;Classes</a></li>
</ul>
<ul class="navListSearch">
<li><label for="search">SEARCH:</label>
<input type="text" id="search" value="search" disabled="disabled">
<input type="reset" id="reset" value="reset" disabled="disabled">
</li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li><a href="#nested.class.summary">Nested</a>&nbsp;|&nbsp;</li>
<li><a href="#field.summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#field.detail">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
</div>
<div class="navPadding">&nbsp;</div>
<script type="text/javascript"><!--
$('.navPadding').css('padding-top', $('.fixedNav').css("height"));
//-->
</script>
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle"><span class="packageLabelInType">Package</span>&nbsp;<a href="package-summary.html">org.gradle.api.tasks.wrapper</a></div>
<h2 title="Class Wrapper" class="title">Class Wrapper</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>org.gradle.api.internal.AbstractTask</li>
<li>
<ul class="inheritance">
<li><a href="../../DefaultTask.html" title="class in org.gradle.api">org.gradle.api.DefaultTask</a></li>
<li>
<ul class="inheritance">
<li>org.gradle.api.tasks.wrapper.Wrapper</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Implemented Interfaces:</dt>
<dd><code>java.lang.Comparable&lt;<a href="../../Task.html" title="interface in org.gradle.api">Task</a>&gt;</code>, <code>org.gradle.api.internal.DynamicObjectAware</code>, <code>org.gradle.api.internal.TaskInternal</code>, <code><a href="../../plugins/ExtensionAware.html" title="interface in org.gradle.api.plugins">ExtensionAware</a></code>, <code><a href="../../Task.html" title="interface in org.gradle.api">Task</a></code>, <code><a href="../../../util/Configurable.html" title="interface in org.gradle.util">Configurable</a>&lt;<a href="../../Task.html" title="interface in org.gradle.api">Task</a>&gt;</code></dd>
</dl>
<hr>
<pre><a href="../../../work/DisableCachingByDefault.html" title="annotation in org.gradle.work">@DisableCachingByDefault</a>(<a href="../../../work/DisableCachingByDefault.html#because--">because</a>="Updating the wrapper is not worth caching")
public abstract class <span class="typeNameLabel">Wrapper</span>
extends <a href="../../DefaultTask.html" title="class in org.gradle.api">DefaultTask</a></pre>
<div class="block"><p>Generates scripts (for *nix and windows) which allow you to build your project with Gradle, without having to
 install Gradle.

 <p>When a user executes a wrapper script the first time, the script downloads and installs the appropriate Gradle
 distribution and runs the build against this downloaded distribution. Any installed Gradle distribution is ignored
 when using the wrapper scripts.

 <p>The scripts generated by this task are intended to be committed to your version control system. This task also
 generates a small <code>gradle-wrapper.jar</code> bootstrap JAR file and properties file which should also be committed to
 your VCS. The scripts delegates to this JAR.</div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== NESTED CLASS SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="nested.class.summary">
<!--   -->
</a>
<h3>Nested Class Summary</h3>
<table class="memberSummary" summary="Nested Class Summary table, listing nested classes, and an explanation">
<caption><span>Nested Classes</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colSecond" scope="col">Class</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static class&nbsp;</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="Wrapper.DistributionType.html" title="enum in org.gradle.api.tasks.wrapper">Wrapper.DistributionType</a></span></code></th>
<td class="colLast">
<div class="block">Specifies the Gradle distribution type.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static class&nbsp;</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="Wrapper.PathBase.html" title="enum in org.gradle.api.tasks.wrapper">Wrapper.PathBase</a></span></code></th>
<td class="colLast">
<div class="block">Specifies how the wrapper path should be interpreted.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="nested.classes.inherited.from.class.org.gradle.api.Task">
<!--   -->
</a>
<h3>Nested classes/interfaces inherited from interface&nbsp;org.gradle.api.<a href="../../Task.html" title="interface in org.gradle.api">Task</a></h3>
<code><a href="../../Task.Namer.html" title="class in org.gradle.api">Task.Namer</a></code></li>
</ul>
</li>
</ul>
<!-- =========== FIELD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.summary">
<!--   -->
</a>
<h3>Field Summary</h3>
<table class="memberSummary" summary="Field Summary table, listing fields, and an explanation">
<caption><span>Fields</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colSecond" scope="col">Field</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#DEFAULT_DISTRIBUTION_PARENT_NAME">DEFAULT_DISTRIBUTION_PARENT_NAME</a></span></code></th>
<td class="colLast">&nbsp;</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="fields.inherited.from.class.org.gradle.api.Task">
<!--   -->
</a>
<h3>Fields inherited from interface&nbsp;org.gradle.api.<a href="../../Task.html" title="interface in org.gradle.api">Task</a></h3>
<code><a href="../../Task.html#TASK_ACTION">TASK_ACTION</a>, <a href="../../Task.html#TASK_CONSTRUCTOR_ARGS">TASK_CONSTRUCTOR_ARGS</a>, <a href="../../Task.html#TASK_DEPENDS_ON">TASK_DEPENDS_ON</a>, <a href="../../Task.html#TASK_DESCRIPTION">TASK_DESCRIPTION</a>, <a href="../../Task.html#TASK_GROUP">TASK_GROUP</a>, <a href="../../Task.html#TASK_NAME">TASK_NAME</a>, <a href="../../Task.html#TASK_OVERWRITE">TASK_OVERWRITE</a>, <a href="../../Task.html#TASK_TYPE">TASK_TYPE</a></code></li>
</ul>
</li>
</ul>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="memberSummary" summary="Constructor Summary table, listing constructors, and an explanation">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Constructor</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tr class="altColor">
<th class="colConstructorName" scope="row"><code><span class="memberNameLink"><a href="#Wrapper--">Wrapper</a></span>()</code></th>
<td class="colLast">&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t3" class="tableTab"><span><a href="javascript:show(4);">Abstract Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colSecond" scope="col">Method</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code><a href="Wrapper.PathBase.html" title="enum in org.gradle.api.tasks.wrapper">Wrapper.PathBase</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getArchiveBase--">getArchiveBase</a></span>()</code></th>
<td class="colLast">
<div class="block">The archive base specifies whether the unpacked wrapper distribution should be stored in the project or in the
 gradle user home dir.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>java.lang.String</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getArchivePath--">getArchivePath</a></span>()</code></th>
<td class="colLast">
<div class="block">Returns the path where the gradle distributions archive should be saved (i.e.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>java.util.List&lt;<a href="Wrapper.DistributionType.html" title="enum in org.gradle.api.tasks.wrapper">Wrapper.DistributionType</a>&gt;</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getAvailableDistributionTypes--">getAvailableDistributionTypes</a></span>()</code></th>
<td class="colLast">
<div class="block">The list of available gradle distribution types.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>java.io.File</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getBatchScript--">getBatchScript</a></span>()</code></th>
<td class="colLast">
<div class="block">Returns the file to write the wrapper batch script to.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code><a href="Wrapper.PathBase.html" title="enum in org.gradle.api.tasks.wrapper">Wrapper.PathBase</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getDistributionBase--">getDistributionBase</a></span>()</code></th>
<td class="colLast">
<div class="block">The distribution base specifies whether the unpacked wrapper distribution should be stored in the project or in
 the gradle user home dir.</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>java.lang.String</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getDistributionPath--">getDistributionPath</a></span>()</code></th>
<td class="colLast">
<div class="block">Returns the path where the gradle distributions needed by the wrapper are unzipped.</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getDistributionSha256Sum--">getDistributionSha256Sum</a></span>()</code></th>
<td class="colLast">
<div class="block">The SHA-256 hash sum of the gradle distribution.</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code><a href="Wrapper.DistributionType.html" title="enum in org.gradle.api.tasks.wrapper">Wrapper.DistributionType</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getDistributionType--">getDistributionType</a></span>()</code></th>
<td class="colLast">
<div class="block">Returns the type of the Gradle distribution to be used by the wrapper.</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getDistributionUrl--">getDistributionUrl</a></span>()</code></th>
<td class="colLast">
<div class="block">The URL to download the gradle distribution from.</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>protected org.gradle.api.internal.file.FileLookup</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getFileLookup--">getFileLookup</a></span>()</code></th>
<td class="colLast">&nbsp;</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getGradleVersion--">getGradleVersion</a></span>()</code></th>
<td class="colLast">
<div class="block">Returns the gradle version for the wrapper.</div>
</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code>java.io.File</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getJarFile--">getJarFile</a></span>()</code></th>
<td class="colLast">
<div class="block">Returns the file to write the wrapper jar file to.</div>
</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code><a href="../../provider/Property.html" title="interface in org.gradle.api.provider">Property</a>&lt;java.lang.Integer&gt;</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getNetworkTimeout--">getNetworkTimeout</a></span>()</code></th>
<td class="colLast">
<div class="block">The network timeout specifies how many ms to wait for when the wrapper is performing network operations, such
 as downloading the wrapper jar.</div>
</td>
</tr>
<tr id="i13" class="rowColor">
<td class="colFirst"><code>java.io.File</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getPropertiesFile--">getPropertiesFile</a></span>()</code></th>
<td class="colLast">
<div class="block">Returns the file to write the wrapper properties to.</div>
</td>
</tr>
<tr id="i14" class="altColor">
<td class="colFirst"><code>java.io.File</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getScriptFile--">getScriptFile</a></span>()</code></th>
<td class="colLast">
<div class="block">Returns the file to write the wrapper script to.</div>
</td>
</tr>
<tr id="i15" class="rowColor">
<td class="colFirst"><code>abstract <a href="../../provider/Property.html" title="interface in org.gradle.api.provider">Property</a>&lt;java.lang.Boolean&gt;</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getValidateDistributionUrl--">getValidateDistributionUrl</a></span>()</code></th>
<td class="colLast">
<div class="block">Indicates if this task will validate the distribution url that has been configured.</div>
</td>
</tr>
<tr id="i16" class="altColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setArchiveBase-org.gradle.api.tasks.wrapper.Wrapper.PathBase-">setArchiveBase</a></span>&#8203;(<a href="Wrapper.PathBase.html" title="enum in org.gradle.api.tasks.wrapper">Wrapper.PathBase</a>&nbsp;archiveBase)</code></th>
<td class="colLast">
<div class="block">The archive base specifies whether the unpacked wrapper distribution should be stored in the project or in the
 gradle user home dir.</div>
</td>
</tr>
<tr id="i17" class="rowColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setArchivePath-java.lang.String-">setArchivePath</a></span>&#8203;(java.lang.String&nbsp;archivePath)</code></th>
<td class="colLast">
<div class="block">Set's the path where the gradle distributions archive should be saved (i.e.</div>
</td>
</tr>
<tr id="i18" class="altColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setDistributionBase-org.gradle.api.tasks.wrapper.Wrapper.PathBase-">setDistributionBase</a></span>&#8203;(<a href="Wrapper.PathBase.html" title="enum in org.gradle.api.tasks.wrapper">Wrapper.PathBase</a>&nbsp;distributionBase)</code></th>
<td class="colLast">
<div class="block">The distribution base specifies whether the unpacked wrapper distribution should be stored in the project or in
 the gradle user home dir.</div>
</td>
</tr>
<tr id="i19" class="rowColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setDistributionPath-java.lang.String-">setDistributionPath</a></span>&#8203;(java.lang.String&nbsp;distributionPath)</code></th>
<td class="colLast">
<div class="block">Sets the path where the gradle distributions needed by the wrapper are unzipped.</div>
</td>
</tr>
<tr id="i20" class="altColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setDistributionSha256Sum-java.lang.String-">setDistributionSha256Sum</a></span>&#8203;(java.lang.String&nbsp;distributionSha256Sum)</code></th>
<td class="colLast">
<div class="block">The SHA-256 hash sum of the gradle distribution.</div>
</td>
</tr>
<tr id="i21" class="rowColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setDistributionType-org.gradle.api.tasks.wrapper.Wrapper.DistributionType-">setDistributionType</a></span>&#8203;(<a href="Wrapper.DistributionType.html" title="enum in org.gradle.api.tasks.wrapper">Wrapper.DistributionType</a>&nbsp;distributionType)</code></th>
<td class="colLast">
<div class="block">The type of the Gradle distribution to be used by the wrapper.</div>
</td>
</tr>
<tr id="i22" class="altColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setDistributionUrl-java.lang.String-">setDistributionUrl</a></span>&#8203;(java.lang.String&nbsp;url)</code></th>
<td class="colLast">
<div class="block">The URL to download the gradle distribution from.</div>
</td>
</tr>
<tr id="i23" class="rowColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setGradleVersion-java.lang.String-">setGradleVersion</a></span>&#8203;(java.lang.String&nbsp;gradleVersion)</code></th>
<td class="colLast">
<div class="block">The version of the gradle distribution required by the wrapper.</div>
</td>
</tr>
<tr id="i24" class="altColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setJarFile-java.io.File-">setJarFile</a></span>&#8203;(java.io.File&nbsp;jarFile)</code></th>
<td class="colLast">
<div class="block">The file to write the wrapper jar file to.</div>
</td>
</tr>
<tr id="i25" class="rowColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setJarFile-java.lang.Object-">setJarFile</a></span>&#8203;(java.lang.Object&nbsp;jarFile)</code></th>
<td class="colLast">
<div class="block">The file to write the wrapper jar file to.</div>
</td>
</tr>
<tr id="i26" class="altColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setScriptFile-java.io.File-">setScriptFile</a></span>&#8203;(java.io.File&nbsp;scriptFile)</code></th>
<td class="colLast">
<div class="block">The file to write the wrapper script to.</div>
</td>
</tr>
<tr id="i27" class="rowColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setScriptFile-java.lang.Object-">setScriptFile</a></span>&#8203;(java.lang.Object&nbsp;scriptFile)</code></th>
<td class="colLast">
<div class="block">The file to write the wrapper script to.</div>
</td>
</tr>
<tr id="i28" class="altColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setWrapperVersionsResources-org.gradle.api.tasks.wrapper.WrapperVersionsResources-">setWrapperVersionsResources</a></span>&#8203;(<a href="WrapperVersionsResources.html" title="interface in org.gradle.api.tasks.wrapper">WrapperVersionsResources</a>&nbsp;wrapperVersionsResources)</code></th>
<td class="colLast">
<div class="block">Set Wrapper versions resources.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.org.gradle.api.DefaultTask">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;org.gradle.api.<a href="../../DefaultTask.html" title="class in org.gradle.api">DefaultTask</a></h3>
<code><a href="../../DefaultTask.html#compareTo-org.gradle.api.Task-">compareTo</a>, <a href="../../DefaultTask.html#configure-groovy.lang.Closure-">configure</a>, <a href="../../DefaultTask.html#dependsOn-java.lang.Object...-">dependsOn</a>, <a href="../../DefaultTask.html#doFirst-groovy.lang.Closure-">doFirst</a>, <a href="../../DefaultTask.html#doFirst-java.lang.String-org.gradle.api.Action-">doFirst</a>, <a href="../../DefaultTask.html#doFirst-org.gradle.api.Action-">doFirst</a>, <a href="../../DefaultTask.html#doLast-groovy.lang.Closure-">doLast</a>, <a href="../../DefaultTask.html#doLast-java.lang.String-org.gradle.api.Action-">doLast</a>, <a href="../../DefaultTask.html#doLast-org.gradle.api.Action-">doLast</a>, <a href="../../DefaultTask.html#finalizedBy-java.lang.Object...-">finalizedBy</a>, <a href="../../DefaultTask.html#getActions--">getActions</a>, <a href="../../DefaultTask.html#getAnt--">getAnt</a>, <a href="../../DefaultTask.html#getDependsOn--">getDependsOn</a>, <a href="../../DefaultTask.html#getDescription--">getDescription</a>, <a href="../../DefaultTask.html#getDestroyables--">getDestroyables</a>, <a href="../../DefaultTask.html#getDidWork--">getDidWork</a>, <a href="../../DefaultTask.html#getEnabled--">getEnabled</a>, <a href="../../DefaultTask.html#getExtensions--">getExtensions</a>, <a href="../../DefaultTask.html#getFinalizedBy--">getFinalizedBy</a>, <a href="../../DefaultTask.html#getGroup--">getGroup</a>, <a href="../../DefaultTask.html#getInputs--">getInputs</a>, <a href="../../DefaultTask.html#getLocalState--">getLocalState</a>, <a href="../../DefaultTask.html#getLogger--">getLogger</a>, <a href="../../DefaultTask.html#getLogging--">getLogging</a>, <a href="../../DefaultTask.html#getMustRunAfter--">getMustRunAfter</a>, <a href="../../DefaultTask.html#getName--">getName</a>, <a href="../../DefaultTask.html#getOutputs--">getOutputs</a>, <a href="../../DefaultTask.html#getPath--">getPath</a>, <a href="../../DefaultTask.html#getProject--">getProject</a>, <a href="../../DefaultTask.html#getShouldRunAfter--">getShouldRunAfter</a>, <a href="../../DefaultTask.html#getState--">getState</a>, <a href="../../DefaultTask.html#getTaskDependencies--">getTaskDependencies</a>, <a href="../../DefaultTask.html#getTemporaryDir--">getTemporaryDir</a>, <a href="../../DefaultTask.html#getTimeout--">getTimeout</a>, <a href="../../DefaultTask.html#hasProperty-java.lang.String-">hasProperty</a>, <a href="../../DefaultTask.html#mustRunAfter-java.lang.Object...-">mustRunAfter</a>, <a href="../../DefaultTask.html#onlyIf-groovy.lang.Closure-">onlyIf</a>, <a href="../../DefaultTask.html#onlyIf-java.lang.String-org.gradle.api.specs.Spec-">onlyIf</a>, <a href="../../DefaultTask.html#onlyIf-org.gradle.api.specs.Spec-">onlyIf</a>, <a href="../../DefaultTask.html#property-java.lang.String-">property</a>, <a href="../../DefaultTask.html#setActions-java.util.List-">setActions</a>, <a href="../../DefaultTask.html#setDependsOn-java.lang.Iterable-">setDependsOn</a>, <a href="../../DefaultTask.html#setDescription-java.lang.String-">setDescription</a>, <a href="../../DefaultTask.html#setDidWork-boolean-">setDidWork</a>, <a href="../../DefaultTask.html#setEnabled-boolean-">setEnabled</a>, <a href="../../DefaultTask.html#setFinalizedBy-java.lang.Iterable-">setFinalizedBy</a>, <a href="../../DefaultTask.html#setGroup-java.lang.String-">setGroup</a>, <a href="../../DefaultTask.html#setMustRunAfter-java.lang.Iterable-">setMustRunAfter</a>, <a href="../../DefaultTask.html#setOnlyIf-groovy.lang.Closure-">setOnlyIf</a>, <a href="../../DefaultTask.html#setOnlyIf-java.lang.String-org.gradle.api.specs.Spec-">setOnlyIf</a>, <a href="../../DefaultTask.html#setOnlyIf-org.gradle.api.specs.Spec-">setOnlyIf</a>, <a href="../../DefaultTask.html#setProperty-java.lang.String-java.lang.Object-">setProperty</a>, <a href="../../DefaultTask.html#setShouldRunAfter-java.lang.Iterable-">setShouldRunAfter</a>, <a href="../../DefaultTask.html#shouldRunAfter-java.lang.Object...-">shouldRunAfter</a>, <a href="../../DefaultTask.html#usesService-org.gradle.api.provider.Provider-">usesService</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.org.gradle.api.internal.AbstractTask">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;org.gradle.api.internal.AbstractTask</h3>
<code>acceptServiceReferences, appendParallelSafeAction, doNotTrackState, getAsDynamicObject, getConvention, getIdentityPath, getImpliesSubProjects, getLifecycleDependencies, getOnlyIf, getReasonNotToTrackState, getReasonTaskIsIncompatibleWithConfigurationCache, getRequiredServices, getServices, getSharedResources, getStandardOutputCapture, getTaskActions, getTaskIdentity, getTemporaryDirFactory, hasTaskActions, injectIntoNewInstance, isCompatibleWithConfigurationCache, isEnabled, isHasCustomActions, notCompatibleWithConfigurationCache, prependParallelSafeAction, setImpliesSubProjects</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>clone, equals, finalize, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.org.gradle.api.Task">
<!--   -->
</a>
<h3>Methods inherited from interface&nbsp;org.gradle.api.<a href="../../Task.html" title="interface in org.gradle.api">Task</a></h3>
<code><a href="../../Task.html#doNotTrackState-java.lang.String-">doNotTrackState</a>, <a href="../../Task.html#getConvention--">getConvention</a>, <a href="../../Task.html#notCompatibleWithConfigurationCache-java.lang.String-">notCompatibleWithConfigurationCache</a></code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ FIELD DETAIL =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.detail">
<!--   -->
</a>
<h3>Field Detail</h3>
<a name="DEFAULT_DISTRIBUTION_PARENT_NAME">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>DEFAULT_DISTRIBUTION_PARENT_NAME</h4>
<pre>public static final&nbsp;java.lang.String DEFAULT_DISTRIBUTION_PARENT_NAME</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../../../constant-values.html#org.gradle.api.tasks.wrapper.Wrapper.DEFAULT_DISTRIBUTION_PARENT_NAME">Constant Field Values</a></dd>
</dl>
</li>
</ul>
</li>
</ul>
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a name="Wrapper--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>Wrapper</h4>
<pre>public&nbsp;Wrapper()</pre>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="getFileLookup--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getFileLookup</h4>
<pre class="methodSignature">@Inject
protected&nbsp;org.gradle.api.internal.file.FileLookup&nbsp;getFileLookup()</pre>
</li>
</ul>
<a name="getScriptFile--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getScriptFile</h4>
<pre class="methodSignature"><a href="../OutputFile.html" title="annotation in org.gradle.api.tasks">@OutputFile</a>
public&nbsp;java.io.File&nbsp;getScriptFile()</pre>
<div class="block">Returns the file to write the wrapper script to.</div>
</li>
</ul>
<a name="setScriptFile-java.io.File-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setScriptFile</h4>
<pre class="methodSignature">public&nbsp;void&nbsp;setScriptFile&#8203;(java.io.File&nbsp;scriptFile)</pre>
<div class="block">The file to write the wrapper script to.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>4.0</dd>
</dl>
</li>
</ul>
<a name="setScriptFile-java.lang.Object-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setScriptFile</h4>
<pre class="methodSignature">public&nbsp;void&nbsp;setScriptFile&#8203;(java.lang.Object&nbsp;scriptFile)</pre>
<div class="block">The file to write the wrapper script to.</div>
</li>
</ul>
<a name="getBatchScript--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getBatchScript</h4>
<pre class="methodSignature"><a href="../OutputFile.html" title="annotation in org.gradle.api.tasks">@OutputFile</a>
public&nbsp;java.io.File&nbsp;getBatchScript()</pre>
<div class="block">Returns the file to write the wrapper batch script to.</div>
</li>
</ul>
<a name="getJarFile--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getJarFile</h4>
<pre class="methodSignature"><a href="../OutputFile.html" title="annotation in org.gradle.api.tasks">@OutputFile</a>
public&nbsp;java.io.File&nbsp;getJarFile()</pre>
<div class="block">Returns the file to write the wrapper jar file to.</div>
</li>
</ul>
<a name="setJarFile-java.io.File-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setJarFile</h4>
<pre class="methodSignature">public&nbsp;void&nbsp;setJarFile&#8203;(java.io.File&nbsp;jarFile)</pre>
<div class="block">The file to write the wrapper jar file to.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>4.0</dd>
</dl>
</li>
</ul>
<a name="setJarFile-java.lang.Object-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setJarFile</h4>
<pre class="methodSignature">public&nbsp;void&nbsp;setJarFile&#8203;(java.lang.Object&nbsp;jarFile)</pre>
<div class="block">The file to write the wrapper jar file to.</div>
</li>
</ul>
<a name="getPropertiesFile--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getPropertiesFile</h4>
<pre class="methodSignature"><a href="../OutputFile.html" title="annotation in org.gradle.api.tasks">@OutputFile</a>
public&nbsp;java.io.File&nbsp;getPropertiesFile()</pre>
<div class="block">Returns the file to write the wrapper properties to.</div>
</li>
</ul>
<a name="getDistributionPath--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getDistributionPath</h4>
<pre class="methodSignature"><a href="../Input.html" title="annotation in org.gradle.api.tasks">@Input</a>
public&nbsp;java.lang.String&nbsp;getDistributionPath()</pre>
<div class="block">Returns the path where the gradle distributions needed by the wrapper are unzipped. The path is relative to the
 distribution base directory</div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="#setDistributionPath-java.lang.String-"><code>setDistributionPath(String)</code></a></dd>
</dl>
</li>
</ul>
<a name="setDistributionPath-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setDistributionPath</h4>
<pre class="methodSignature">public&nbsp;void&nbsp;setDistributionPath&#8203;(java.lang.String&nbsp;distributionPath)</pre>
<div class="block">Sets the path where the gradle distributions needed by the wrapper are unzipped. The path is relative to the
 distribution base directory</div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="#setDistributionPath-java.lang.String-"><code>setDistributionPath(String)</code></a></dd>
</dl>
</li>
</ul>
<a name="setWrapperVersionsResources-org.gradle.api.tasks.wrapper.WrapperVersionsResources-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setWrapperVersionsResources</h4>
<pre class="methodSignature"><a href="../../Incubating.html" title="annotation in org.gradle.api">@Incubating</a>
public&nbsp;void&nbsp;setWrapperVersionsResources&#8203;(<a href="WrapperVersionsResources.html" title="interface in org.gradle.api.tasks.wrapper">WrapperVersionsResources</a>&nbsp;wrapperVersionsResources)</pre>
<div class="block">Set Wrapper versions resources.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>8.1</dd>
</dl>
</li>
</ul>
<a name="getGradleVersion--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getGradleVersion</h4>
<pre class="methodSignature"><a href="../Input.html" title="annotation in org.gradle.api.tasks">@Input</a>
public&nbsp;java.lang.String&nbsp;getGradleVersion()</pre>
<div class="block">Returns the gradle version for the wrapper.</div>
<dl>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code><a href="../../GradleException.html" title="class in org.gradle.api">GradleException</a></code> - if the label that can be provided via <a href="#setGradleVersion-java.lang.String-"><code>setGradleVersion(String)</code></a> can not be resolved at the moment. For example, there is not a `release-candidate` available at all times.</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="#setGradleVersion-java.lang.String-"><code>setGradleVersion(String)</code></a></dd>
</dl>
</li>
</ul>
<a name="setGradleVersion-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setGradleVersion</h4>
<pre class="methodSignature">public&nbsp;void&nbsp;setGradleVersion&#8203;(java.lang.String&nbsp;gradleVersion)</pre>
<div class="block">The version of the gradle distribution required by the wrapper.
 This is usually the same version of Gradle you use for building your project.
 The following labels are allowed to specify a version: <code>latest</code>, <code>release-candidate</code>, <code>nightly</code>, and <code>release-nightly</code>

 <p>The resulting distribution url is validated before it is written to the gradle-wrapper.properties file.</div>
</li>
</ul>
<a name="getDistributionType--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getDistributionType</h4>
<pre class="methodSignature"><a href="../Input.html" title="annotation in org.gradle.api.tasks">@Input</a>
public&nbsp;<a href="Wrapper.DistributionType.html" title="enum in org.gradle.api.tasks.wrapper">Wrapper.DistributionType</a>&nbsp;getDistributionType()</pre>
<div class="block">Returns the type of the Gradle distribution to be used by the wrapper.</div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="#setDistributionType-org.gradle.api.tasks.wrapper.Wrapper.DistributionType-"><code>setDistributionType(DistributionType)</code></a></dd>
</dl>
</li>
</ul>
<a name="setDistributionType-org.gradle.api.tasks.wrapper.Wrapper.DistributionType-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setDistributionType</h4>
<pre class="methodSignature">public&nbsp;void&nbsp;setDistributionType&#8203;(<a href="Wrapper.DistributionType.html" title="enum in org.gradle.api.tasks.wrapper">Wrapper.DistributionType</a>&nbsp;distributionType)</pre>
<div class="block">The type of the Gradle distribution to be used by the wrapper. By default, this is <a href="Wrapper.DistributionType.html#BIN"><code>Wrapper.DistributionType.BIN</code></a>,
 which is the binary-only Gradle distribution without documentation.</div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="Wrapper.DistributionType.html" title="enum in org.gradle.api.tasks.wrapper"><code>Wrapper.DistributionType</code></a></dd>
</dl>
</li>
</ul>
<a name="getAvailableDistributionTypes--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getAvailableDistributionTypes</h4>
<pre class="methodSignature">public&nbsp;java.util.List&lt;<a href="Wrapper.DistributionType.html" title="enum in org.gradle.api.tasks.wrapper">Wrapper.DistributionType</a>&gt;&nbsp;getAvailableDistributionTypes()</pre>
<div class="block">The list of available gradle distribution types.</div>
</li>
</ul>
<a name="getDistributionUrl--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getDistributionUrl</h4>
<pre class="methodSignature"><a href="../Input.html" title="annotation in org.gradle.api.tasks">@Input</a>
public&nbsp;java.lang.String&nbsp;getDistributionUrl()</pre>
<div class="block">The URL to download the gradle distribution from.

 <p>If not set, the download URL is the default for the specified <a href="#getGradleVersion--"><code>getGradleVersion()</code></a>.

 <p>If <a href="#getGradleVersion--"><code>getGradleVersion()</code></a> is not set, will return null.

 <p>The wrapper downloads a certain distribution only once and caches it. If your distribution base is the
 project, you might submit the distribution to your version control system. That way no download is necessary at
 all. This might be in particular interesting, if you provide a custom gradle snapshot to the wrapper, because you
 don't need to provide a download server then.</div>
</li>
</ul>
<a name="setDistributionUrl-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setDistributionUrl</h4>
<pre class="methodSignature">public&nbsp;void&nbsp;setDistributionUrl&#8203;(java.lang.String&nbsp;url)</pre>
<div class="block">The URL to download the gradle distribution from.

 <p>If not set, the download URL is the default for the specified <a href="#getGradleVersion--"><code>getGradleVersion()</code></a>.

 <p>If <a href="#getGradleVersion--"><code>getGradleVersion()</code></a> is not set, will return null.

 <p>The wrapper downloads a certain distribution and caches it. If your distribution base is the
 project, you might submit the distribution to your version control system. That way no download is necessary at
 all. This might be in particular interesting, if you provide a custom gradle snapshot to the wrapper, because you
 don't need to provide a download server then.

 <p>The distribution url is validated before it is written to the gradle-wrapper.properties file.</div>
</li>
</ul>
<a name="getDistributionSha256Sum--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getDistributionSha256Sum</h4>
<pre class="methodSignature">@Nullable
<a href="../Optional.html" title="annotation in org.gradle.api.tasks">@Optional</a>
<a href="../Input.html" title="annotation in org.gradle.api.tasks">@Input</a>
public&nbsp;java.lang.String&nbsp;getDistributionSha256Sum()</pre>
<div class="block">The SHA-256 hash sum of the gradle distribution.

 <p>If not set, the hash sum of the gradle distribution is not verified.

 <p>The wrapper allows for verification of the downloaded Gradle distribution via SHA-256 hash sum comparison.
 This increases security against targeted attacks by preventing a man-in-the-middle attacker from tampering with
 the downloaded Gradle distribution.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>4.5</dd>
</dl>
</li>
</ul>
<a name="setDistributionSha256Sum-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setDistributionSha256Sum</h4>
<pre class="methodSignature">public&nbsp;void&nbsp;setDistributionSha256Sum&#8203;(@Nullable
                                     java.lang.String&nbsp;distributionSha256Sum)</pre>
<div class="block">The SHA-256 hash sum of the gradle distribution.

 <p>If not set, the hash sum of the gradle distribution is not verified.

 <p>The wrapper allows for verification of the downloaded Gradle distribution via SHA-256 hash sum comparison.
 This increases security against targeted attacks by preventing a man-in-the-middle attacker from tampering with
 the downloaded Gradle distribution.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>4.5</dd>
</dl>
</li>
</ul>
<a name="getDistributionBase--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getDistributionBase</h4>
<pre class="methodSignature"><a href="../Input.html" title="annotation in org.gradle.api.tasks">@Input</a>
public&nbsp;<a href="Wrapper.PathBase.html" title="enum in org.gradle.api.tasks.wrapper">Wrapper.PathBase</a>&nbsp;getDistributionBase()</pre>
<div class="block">The distribution base specifies whether the unpacked wrapper distribution should be stored in the project or in
 the gradle user home dir.</div>
</li>
</ul>
<a name="setDistributionBase-org.gradle.api.tasks.wrapper.Wrapper.PathBase-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setDistributionBase</h4>
<pre class="methodSignature">public&nbsp;void&nbsp;setDistributionBase&#8203;(<a href="Wrapper.PathBase.html" title="enum in org.gradle.api.tasks.wrapper">Wrapper.PathBase</a>&nbsp;distributionBase)</pre>
<div class="block">The distribution base specifies whether the unpacked wrapper distribution should be stored in the project or in
 the gradle user home dir.</div>
</li>
</ul>
<a name="getArchivePath--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getArchivePath</h4>
<pre class="methodSignature"><a href="../Input.html" title="annotation in org.gradle.api.tasks">@Input</a>
public&nbsp;java.lang.String&nbsp;getArchivePath()</pre>
<div class="block">Returns the path where the gradle distributions archive should be saved (i.e. the parent dir). The path is
 relative to the archive base directory.</div>
</li>
</ul>
<a name="setArchivePath-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setArchivePath</h4>
<pre class="methodSignature">public&nbsp;void&nbsp;setArchivePath&#8203;(java.lang.String&nbsp;archivePath)</pre>
<div class="block">Set's the path where the gradle distributions archive should be saved (i.e. the parent dir). The path is relative
 to the parent dir specified with <a href="#getArchiveBase--"><code>getArchiveBase()</code></a>.</div>
</li>
</ul>
<a name="getArchiveBase--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getArchiveBase</h4>
<pre class="methodSignature"><a href="../Input.html" title="annotation in org.gradle.api.tasks">@Input</a>
public&nbsp;<a href="Wrapper.PathBase.html" title="enum in org.gradle.api.tasks.wrapper">Wrapper.PathBase</a>&nbsp;getArchiveBase()</pre>
<div class="block">The archive base specifies whether the unpacked wrapper distribution should be stored in the project or in the
 gradle user home dir.</div>
</li>
</ul>
<a name="setArchiveBase-org.gradle.api.tasks.wrapper.Wrapper.PathBase-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setArchiveBase</h4>
<pre class="methodSignature">public&nbsp;void&nbsp;setArchiveBase&#8203;(<a href="Wrapper.PathBase.html" title="enum in org.gradle.api.tasks.wrapper">Wrapper.PathBase</a>&nbsp;archiveBase)</pre>
<div class="block">The archive base specifies whether the unpacked wrapper distribution should be stored in the project or in the
 gradle user home dir.</div>
</li>
</ul>
<a name="getNetworkTimeout--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getNetworkTimeout</h4>
<pre class="methodSignature"><a href="../Input.html" title="annotation in org.gradle.api.tasks">@Input</a>
<a href="../../Incubating.html" title="annotation in org.gradle.api">@Incubating</a>
<a href="../Optional.html" title="annotation in org.gradle.api.tasks">@Optional</a>
public&nbsp;<a href="../../provider/Property.html" title="interface in org.gradle.api.provider">Property</a>&lt;java.lang.Integer&gt;&nbsp;getNetworkTimeout()</pre>
<div class="block">The network timeout specifies how many ms to wait for when the wrapper is performing network operations, such
 as downloading the wrapper jar.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>7.6</dd>
</dl>
</li>
</ul>
<a name="getValidateDistributionUrl--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>getValidateDistributionUrl</h4>
<pre class="methodSignature"><a href="../../Incubating.html" title="annotation in org.gradle.api">@Incubating</a>
<a href="../Input.html" title="annotation in org.gradle.api.tasks">@Input</a>
public abstract&nbsp;<a href="../../provider/Property.html" title="interface in org.gradle.api.provider">Property</a>&lt;java.lang.Boolean&gt;&nbsp;getValidateDistributionUrl()</pre>
<div class="block">Indicates if this task will validate the distribution url that has been configured.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>whether this task will validate the distribution url</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>8.2</dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../../allclasses.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li><a href="#nested.class.summary">Nested</a>&nbsp;|&nbsp;</li>
<li><a href="#field.summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#field.detail">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
