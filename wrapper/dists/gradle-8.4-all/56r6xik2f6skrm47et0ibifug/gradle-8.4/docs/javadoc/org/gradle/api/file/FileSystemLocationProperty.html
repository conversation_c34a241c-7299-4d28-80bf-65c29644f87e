<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc -->
<title>FileSystemLocationProperty (Gradle API 8.4)</title>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<link rel="stylesheet" type="text/css" href="../../../../javadoc.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../jquery/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
<script type="text/javascript" src="../../../../jquery/jszip/dist/jszip.min.js"></script>
<script type="text/javascript" src="../../../../jquery/jszip-utils/dist/jszip-utils.min.js"></script>
<!--[if IE]>
<script type="text/javascript" src="../../../../jquery/jszip-utils/dist/jszip-utils-ie.min.js"></script>
<![endif]-->
<script type="text/javascript" src="../../../../jquery/jquery-3.6.0.min.js"></script>
<script type="text/javascript" src="../../../../jquery/jquery-ui.min.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="FileSystemLocationProperty (Gradle API 8.4)";
        }
    }
    catch(err) {
    }
//-->
var data = {"i0":6,"i1":6,"i2":6,"i3":6,"i4":6};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],4:["t3","Abstract Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
var pathtoroot = "../../../../";
var useModuleDirectories = false;
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="fixedNav">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses.html">All&nbsp;Classes</a></li>
</ul>
<ul class="navListSearch">
<li><label for="search">SEARCH:</label>
<input type="text" id="search" value="search" disabled="disabled">
<input type="reset" id="reset" value="reset" disabled="disabled">
</li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
</div>
<div class="navPadding">&nbsp;</div>
<script type="text/javascript"><!--
$('.navPadding').css('padding-top', $('.fixedNav').css("height"));
//-->
</script>
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle"><span class="packageLabelInType">Package</span>&nbsp;<a href="package-summary.html">org.gradle.api.file</a></div>
<h2 title="Interface FileSystemLocationProperty" class="title">Interface FileSystemLocationProperty&lt;T extends <a href="FileSystemLocation.html" title="interface in org.gradle.api.file">FileSystemLocation</a>&gt;</h2>
</div>
<div class="contentContainer">
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt><span class="paramLabel">Type Parameters:</span></dt>
<dd><code>T</code> - The type of location.</dd>
</dl>
<dl>
<dt>All Superinterfaces:</dt>
<dd><code><a href="../provider/HasConfigurableValue.html" title="interface in org.gradle.api.provider">HasConfigurableValue</a></code>, <code><a href="../provider/Property.html" title="interface in org.gradle.api.provider">Property</a>&lt;T&gt;</code>, <code><a href="../provider/Provider.html" title="interface in org.gradle.api.provider">Provider</a>&lt;T&gt;</code></dd>
</dl>
<dl>
<dt>All Known Subinterfaces:</dt>
<dd><code><a href="DirectoryProperty.html" title="interface in org.gradle.api.file">DirectoryProperty</a></code>, <code><a href="RegularFileProperty.html" title="interface in org.gradle.api.file">RegularFileProperty</a></code></dd>
</dl>
<hr>
<pre>public interface <span class="typeNameLabel">FileSystemLocationProperty&lt;T extends <a href="FileSystemLocation.html" title="interface in org.gradle.api.file">FileSystemLocation</a>&gt;</span>
extends <a href="../provider/Property.html" title="interface in org.gradle.api.provider">Property</a>&lt;T&gt;</pre>
<div class="block">Represents some element of the file system. A file system element has two parts: its location and its content. A file system element's content, may be the output of a task
 or tasks. This property object keeps track of both the location and the task or tasks that produce the content of the element.

 <p><b>Note:</b> This interface is not intended for implementation by build script or plugin authors.</p></div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>5.6</dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t3" class="tableTab"><span><a href="javascript:show(4);">Abstract Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colSecond" scope="col">Method</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code><a href="FileSystemLocationProperty.html" title="interface in org.gradle.api.file">FileSystemLocationProperty</a>&lt;<a href="FileSystemLocationProperty.html" title="type parameter in FileSystemLocationProperty">T</a>&gt;</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#fileProvider-org.gradle.api.provider.Provider-">fileProvider</a></span>&#8203;(<a href="../provider/Provider.html" title="interface in org.gradle.api.provider">Provider</a>&lt;java.io.File&gt;&nbsp;provider)</code></th>
<td class="colLast">
<div class="block">Sets the location of this file, using a <code>File</code> <a href="../provider/Provider.html" title="interface in org.gradle.api.provider"><code>Provider</code></a> instance.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code><a href="FileSystemLocationProperty.html" title="interface in org.gradle.api.file">FileSystemLocationProperty</a>&lt;<a href="FileSystemLocationProperty.html" title="type parameter in FileSystemLocationProperty">T</a>&gt;</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#fileValue-java.io.File-">fileValue</a></span>&#8203;(java.io.File&nbsp;file)</code></th>
<td class="colLast">
<div class="block">Sets the location of this file, using a <code>File</code> instance.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code><a href="../provider/Provider.html" title="interface in org.gradle.api.provider">Provider</a>&lt;java.io.File&gt;</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getAsFile--">getAsFile</a></span>()</code></th>
<td class="colLast">
<div class="block">Views the location of this file as a <code>File</code>.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code><a href="../provider/Provider.html" title="interface in org.gradle.api.provider">Provider</a>&lt;<a href="FileSystemLocationProperty.html" title="type parameter in FileSystemLocationProperty">T</a>&gt;</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getLocationOnly--">getLocationOnly</a></span>()</code></th>
<td class="colLast">
<div class="block">Returns the location of the file system element, and discards details of the task that produces its content.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#set-java.io.File-">set</a></span>&#8203;(java.io.File&nbsp;file)</code></th>
<td class="colLast">
<div class="block">Sets the location of this file, using a <code>File</code> instance.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.org.gradle.api.provider.HasConfigurableValue">
<!--   -->
</a>
<h3>Methods inherited from interface&nbsp;org.gradle.api.provider.<a href="../provider/HasConfigurableValue.html" title="interface in org.gradle.api.provider">HasConfigurableValue</a></h3>
<code><a href="../provider/HasConfigurableValue.html#disallowChanges--">disallowChanges</a>, <a href="../provider/HasConfigurableValue.html#disallowUnsafeRead--">disallowUnsafeRead</a>, <a href="../provider/HasConfigurableValue.html#finalizeValueOnRead--">finalizeValueOnRead</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.org.gradle.api.provider.Property">
<!--   -->
</a>
<h3>Methods inherited from interface&nbsp;org.gradle.api.provider.<a href="../provider/Property.html" title="interface in org.gradle.api.provider">Property</a></h3>
<code><a href="../provider/Property.html#convention-org.gradle.api.provider.Provider-">convention</a>, <a href="../provider/Property.html#convention-T-">convention</a>, <a href="../provider/Property.html#finalizeValue--">finalizeValue</a>, <a href="../provider/Property.html#set-org.gradle.api.provider.Provider-">set</a>, <a href="../provider/Property.html#set-T-">set</a>, <a href="../provider/Property.html#value-org.gradle.api.provider.Provider-">value</a>, <a href="../provider/Property.html#value-T-">value</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.org.gradle.api.provider.Provider">
<!--   -->
</a>
<h3>Methods inherited from interface&nbsp;org.gradle.api.provider.<a href="../provider/Provider.html" title="interface in org.gradle.api.provider">Provider</a></h3>
<code><a href="../provider/Provider.html#filter-java.util.function.Predicate-">filter</a>, <a href="../provider/Provider.html#flatMap-org.gradle.api.Transformer-">flatMap</a>, <a href="../provider/Provider.html#forUseAtConfigurationTime--">forUseAtConfigurationTime</a>, <a href="../provider/Provider.html#get--">get</a>, <a href="../provider/Provider.html#getOrElse-T-">getOrElse</a>, <a href="../provider/Provider.html#getOrNull--">getOrNull</a>, <a href="../provider/Provider.html#isPresent--">isPresent</a>, <a href="../provider/Provider.html#map-org.gradle.api.Transformer-">map</a>, <a href="../provider/Provider.html#orElse-org.gradle.api.provider.Provider-">orElse</a>, <a href="../provider/Provider.html#orElse-T-">orElse</a>, <a href="../provider/Provider.html#zip-org.gradle.api.provider.Provider-java.util.function.BiFunction-">zip</a></code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="getAsFile--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getAsFile</h4>
<pre class="methodSignature"><a href="../provider/Provider.html" title="interface in org.gradle.api.provider">Provider</a>&lt;java.io.File&gt;&nbsp;getAsFile()</pre>
<div class="block">Views the location of this file as a <code>File</code>.</div>
</li>
</ul>
<a name="set-java.io.File-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>set</h4>
<pre class="methodSignature">void&nbsp;set&#8203;(@Nullable
         java.io.File&nbsp;file)</pre>
<div class="block">Sets the location of this file, using a <code>File</code> instance. <code>File</code> instances with relative paths are resolved relative to the project directory of the project
 that owns this property instance.</div>
</li>
</ul>
<a name="fileValue-java.io.File-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>fileValue</h4>
<pre class="methodSignature"><a href="FileSystemLocationProperty.html" title="interface in org.gradle.api.file">FileSystemLocationProperty</a>&lt;<a href="FileSystemLocationProperty.html" title="type parameter in FileSystemLocationProperty">T</a>&gt;&nbsp;fileValue&#8203;(@Nullable
                                        java.io.File&nbsp;file)</pre>
<div class="block">Sets the location of this file, using a <code>File</code> instance. <code>File</code> instances with relative paths are resolved relative to the project directory of the project
 that owns this property instance.

 <p>This method is the same as <a href="#set-java.io.File-"><code>set(File)</code></a> but allows method chaining.</p></div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>this</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>6.0</dd>
</dl>
</li>
</ul>
<a name="fileProvider-org.gradle.api.provider.Provider-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>fileProvider</h4>
<pre class="methodSignature"><a href="FileSystemLocationProperty.html" title="interface in org.gradle.api.file">FileSystemLocationProperty</a>&lt;<a href="FileSystemLocationProperty.html" title="type parameter in FileSystemLocationProperty">T</a>&gt;&nbsp;fileProvider&#8203;(<a href="../provider/Provider.html" title="interface in org.gradle.api.provider">Provider</a>&lt;java.io.File&gt;&nbsp;provider)</pre>
<div class="block">Sets the location of this file, using a <code>File</code> <a href="../provider/Provider.html" title="interface in org.gradle.api.provider"><code>Provider</code></a> instance. <code>File</code> instances with relative paths are resolved relative to the project directory of the project
 that owns this property instance.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>this</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>6.0</dd>
</dl>
</li>
</ul>
<a name="getLocationOnly--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>getLocationOnly</h4>
<pre class="methodSignature"><a href="../provider/Provider.html" title="interface in org.gradle.api.provider">Provider</a>&lt;<a href="FileSystemLocationProperty.html" title="type parameter in FileSystemLocationProperty">T</a>&gt;&nbsp;getLocationOnly()</pre>
<div class="block">Returns the location of the file system element, and discards details of the task that produces its content. This allows the location, or a value derived from it, to be used as an input to some other task without implying any dependency on the producing task. This should only be used when the task does, in fact, not use the content of this file system element.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>5.6</dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
