<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc -->
<title>AuthenticationSupported (Gradle API 8.4)</title>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<link rel="stylesheet" type="text/css" href="../../../../../javadoc.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../jquery/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
<script type="text/javascript" src="../../../../../jquery/jszip/dist/jszip.min.js"></script>
<script type="text/javascript" src="../../../../../jquery/jszip-utils/dist/jszip-utils.min.js"></script>
<!--[if IE]>
<script type="text/javascript" src="../../../../../jquery/jszip-utils/dist/jszip-utils-ie.min.js"></script>
<![endif]-->
<script type="text/javascript" src="../../../../../jquery/jquery-3.6.0.min.js"></script>
<script type="text/javascript" src="../../../../../jquery/jquery-ui.min.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="AuthenticationSupported (Gradle API 8.4)";
        }
    }
    catch(err) {
    }
//-->
var data = {"i0":6,"i1":6,"i2":6,"i3":6,"i4":6,"i5":6,"i6":6};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],4:["t3","Abstract Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
var pathtoroot = "../../../../../";
var useModuleDirectories = false;
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="fixedNav">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../../allclasses.html">All&nbsp;Classes</a></li>
</ul>
<ul class="navListSearch">
<li><label for="search">SEARCH:</label>
<input type="text" id="search" value="search" disabled="disabled">
<input type="reset" id="reset" value="reset" disabled="disabled">
</li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
</div>
<div class="navPadding">&nbsp;</div>
<script type="text/javascript"><!--
$('.navPadding').css('padding-top', $('.fixedNav').css("height"));
//-->
</script>
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle"><span class="packageLabelInType">Package</span>&nbsp;<a href="package-summary.html">org.gradle.api.artifacts.repositories</a></div>
<h2 title="Interface AuthenticationSupported" class="title">Interface AuthenticationSupported</h2>
</div>
<div class="contentContainer">
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Known Subinterfaces:</dt>
<dd><code><a href="IvyArtifactRepository.html" title="interface in org.gradle.api.artifacts.repositories">IvyArtifactRepository</a></code>, <code><a href="../../../jvm/toolchain/JavaToolchainRepository.html" title="interface in org.gradle.jvm.toolchain">JavaToolchainRepository</a></code>, <code><a href="MavenArtifactRepository.html" title="interface in org.gradle.api.artifacts.repositories">MavenArtifactRepository</a></code></dd>
</dl>
<hr>
<pre>public interface <span class="typeNameLabel">AuthenticationSupported</span></pre>
<div class="block">An artifact repository which supports username/password authentication.</div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t3" class="tableTab"><span><a href="javascript:show(4);">Abstract Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colSecond" scope="col">Method</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#authentication-org.gradle.api.Action-">authentication</a></span>&#8203;(<a href="../../Action.html" title="interface in org.gradle.api">Action</a>&lt;? super <a href="AuthenticationContainer.html" title="interface in org.gradle.api.artifacts.repositories">AuthenticationContainer</a>&gt;&nbsp;action)</code></th>
<td class="colLast">
<div class="block">Configures the authentication schemes for this repository.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#credentials-java.lang.Class-">credentials</a></span>&#8203;(java.lang.Class&lt;? extends <a href="../../credentials/Credentials.html" title="interface in org.gradle.api.credentials">Credentials</a>&gt;&nbsp;credentialsType)</code></th>
<td class="colLast">
<div class="block">Configures the credentials for this repository that will be provided by the build.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>&lt;T extends <a href="../../credentials/Credentials.html" title="interface in org.gradle.api.credentials">Credentials</a>&gt;<br>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#credentials-java.lang.Class-org.gradle.api.Action-">credentials</a></span>&#8203;(java.lang.Class&lt;T&gt;&nbsp;credentialsType,
           <a href="../../Action.html" title="interface in org.gradle.api">Action</a>&lt;? super T&gt;&nbsp;action)</code></th>
<td class="colLast">
<div class="block">Configures the credentials for this repository using the supplied action.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#credentials-org.gradle.api.Action-">credentials</a></span>&#8203;(<a href="../../Action.html" title="interface in org.gradle.api">Action</a>&lt;? super <a href="PasswordCredentials.html" title="interface in org.gradle.api.artifacts.repositories">PasswordCredentials</a>&gt;&nbsp;action)</code></th>
<td class="colLast">
<div class="block">Configures the username and password credentials for this repository using the supplied action.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code><a href="AuthenticationContainer.html" title="interface in org.gradle.api.artifacts.repositories">AuthenticationContainer</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getAuthentication--">getAuthentication</a></span>()</code></th>
<td class="colLast">
<div class="block">Returns the authentication schemes for this repository.</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code><a href="PasswordCredentials.html" title="interface in org.gradle.api.artifacts.repositories">PasswordCredentials</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getCredentials--">getCredentials</a></span>()</code></th>
<td class="colLast">
<div class="block">Returns the username and password credentials used to authenticate to this repository.</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>&lt;T extends <a href="../../credentials/Credentials.html" title="interface in org.gradle.api.credentials">Credentials</a>&gt;<br>T</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getCredentials-java.lang.Class-">getCredentials</a></span>&#8203;(java.lang.Class&lt;T&gt;&nbsp;credentialsType)</code></th>
<td class="colLast">
<div class="block">Returns the credentials of the specified type used to authenticate with this repository.</div>
</td>
</tr>
</table>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="getCredentials--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCredentials</h4>
<pre class="methodSignature"><a href="PasswordCredentials.html" title="interface in org.gradle.api.artifacts.repositories">PasswordCredentials</a>&nbsp;getCredentials()</pre>
<div class="block">Returns the username and password credentials used to authenticate to this repository.
 <p>
 If no credentials have been assigned to this repository, an empty set of username and password credentials is assigned to this repository and returned.
 <p>
 If you are using a different type of credentials than <a href="PasswordCredentials.html" title="interface in org.gradle.api.artifacts.repositories"><code>PasswordCredentials</code></a>, please use <a href="#getCredentials-java.lang.Class-"><code>getCredentials(Class)</code></a> to obtain the credentials.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the credentials</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code>java.lang.IllegalStateException</code> - if the credential type was previously set with <a href="#credentials-java.lang.Class-org.gradle.api.Action-"><code>credentials(Class, Action)</code></a> where the type was not <a href="PasswordCredentials.html" title="interface in org.gradle.api.artifacts.repositories"><code>PasswordCredentials</code></a></dd>
</dl>
</li>
</ul>
<a name="getCredentials-java.lang.Class-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCredentials</h4>
<pre class="methodSignature">&lt;T extends <a href="../../credentials/Credentials.html" title="interface in org.gradle.api.credentials">Credentials</a>&gt;&nbsp;T&nbsp;getCredentials&#8203;(java.lang.Class&lt;T&gt;&nbsp;credentialsType)</pre>
<div class="block">Returns the credentials of the specified type used to authenticate with this repository.
 <p>
 If no credentials have been assigned to this repository, an empty set of credentials of the specified type is assigned to this repository and returned.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>credentialsType</code> - type of the credential</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The credentials</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code>java.lang.IllegalArgumentException</code> - when the credentials assigned to this repository are not assignable to the specified type</dd>
</dl>
</li>
</ul>
<a name="credentials-org.gradle.api.Action-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>credentials</h4>
<pre class="methodSignature">void&nbsp;credentials&#8203;(<a href="../../Action.html" title="interface in org.gradle.api">Action</a>&lt;? super <a href="PasswordCredentials.html" title="interface in org.gradle.api.artifacts.repositories">PasswordCredentials</a>&gt;&nbsp;action)</pre>
<div class="block">Configures the username and password credentials for this repository using the supplied action.
 <p>
 If no credentials have been assigned to this repository, an empty set of username and password credentials is assigned to this repository and passed to the action.
 <pre class='autoTested'>
 repositories {
     maven {
         url "${url}"
         credentials {
             username = 'joe'
             password = 'secret'
         }
     }
 }
 </pre></div>
<dl>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code>java.lang.IllegalStateException</code> - when the credentials assigned to this repository are not of type <a href="PasswordCredentials.html" title="interface in org.gradle.api.artifacts.repositories"><code>PasswordCredentials</code></a></dd>
</dl>
</li>
</ul>
<a name="credentials-java.lang.Class-org.gradle.api.Action-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>credentials</h4>
<pre class="methodSignature">&lt;T extends <a href="../../credentials/Credentials.html" title="interface in org.gradle.api.credentials">Credentials</a>&gt;&nbsp;void&nbsp;credentials&#8203;(java.lang.Class&lt;T&gt;&nbsp;credentialsType,
                                         <a href="../../Action.html" title="interface in org.gradle.api">Action</a>&lt;? super T&gt;&nbsp;action)</pre>
<div class="block">Configures the credentials for this repository using the supplied action.
 <p>
 If no credentials have been assigned to this repository, an empty set of credentials of the specified type will be assigned to this repository and given to the configuration action.
 If credentials have already been specified for this repository, they will be passed to the given configuration action.
 <pre class='autoTested'>
 repositories {
     maven {
         url "${url}"
         credentials(AwsCredentials) {
             accessKey "myAccessKey"
             secretKey "mySecret"
         }
     }
 }
 </pre>
 <p>
 The following credential types are currently supported for the <code>credentialsType</code> argument:
 <ul>
 <li><a href="PasswordCredentials.html" title="interface in org.gradle.api.artifacts.repositories"><code>PasswordCredentials</code></a></li>
 <li><a href="../../credentials/AwsCredentials.html" title="interface in org.gradle.api.credentials"><code>AwsCredentials</code></a></li>
 </ul></div>
<dl>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code>java.lang.IllegalArgumentException</code> - if <code>credentialsType</code> is not of a supported type</dd>
<dd><code>java.lang.IllegalArgumentException</code> - if <code>credentialsType</code> is of a different type to the credentials previously specified for this repository</dd>
</dl>
</li>
</ul>
<a name="credentials-java.lang.Class-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>credentials</h4>
<pre class="methodSignature">void&nbsp;credentials&#8203;(java.lang.Class&lt;? extends <a href="../../credentials/Credentials.html" title="interface in org.gradle.api.credentials">Credentials</a>&gt;&nbsp;credentialsType)</pre>
<div class="block">Configures the credentials for this repository that will be provided by the build.
 <p>
 Credentials will be provided from Gradle properties based on the repository name.
 If credentials for this repository can not be resolved and the repository will be used in the current build, then the build will fail to start and point to the missing configuration.
 <pre class='autoTested'>
 repositories {
     maven {
         url "${url}"
         credentials(PasswordCredentials)
     }
 }
 </pre>
 <p>
 The following credential types are currently supported for the <code>credentialsType</code> argument:
 <ul>
 <li><a href="../../credentials/PasswordCredentials.html" title="interface in org.gradle.api.credentials"><code>PasswordCredentials</code></a></li>
 <li><a href="../../credentials/AwsCredentials.html" title="interface in org.gradle.api.credentials"><code>AwsCredentials</code></a></li>
 <li><a href="../../credentials/HttpHeaderCredentials.html" title="interface in org.gradle.api.credentials"><code>HttpHeaderCredentials</code></a></li>
 </ul></div>
<dl>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code>java.lang.IllegalArgumentException</code> - if <code>credentialsType</code> is not of a supported type</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>6.6</dd>
</dl>
</li>
</ul>
<a name="authentication-org.gradle.api.Action-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>authentication</h4>
<pre class="methodSignature">void&nbsp;authentication&#8203;(<a href="../../Action.html" title="interface in org.gradle.api">Action</a>&lt;? super <a href="AuthenticationContainer.html" title="interface in org.gradle.api.artifacts.repositories">AuthenticationContainer</a>&gt;&nbsp;action)</pre>
<div class="block"><p>Configures the authentication schemes for this repository.

 <p>This method executes the given action against the <a href="AuthenticationContainer.html" title="interface in org.gradle.api.artifacts.repositories"><code>AuthenticationContainer</code></a> for this project. The <a href="AuthenticationContainer.html" title="interface in org.gradle.api.artifacts.repositories"><code>AuthenticationContainer</code></a> is passed to the closure as the closure's delegate.
 <p>
 If no authentication schemes have been assigned to this repository, a default set of authentication schemes are used based on the repository's transport scheme.

 <pre class='autoTested'>
 repositories {
     maven {
         url "${url}"
         authentication {
             basic(BasicAuthentication)
         }
     }
 }
 </pre>
 <p>
 Supported authentication scheme types extend <a href="../../../authentication/Authentication.html" title="interface in org.gradle.authentication"><code>Authentication</code></a>.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>action</code> - the action to use to configure the authentication schemes.</dd>
</dl>
</li>
</ul>
<a name="getAuthentication--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>getAuthentication</h4>
<pre class="methodSignature"><a href="AuthenticationContainer.html" title="interface in org.gradle.api.artifacts.repositories">AuthenticationContainer</a>&nbsp;getAuthentication()</pre>
<div class="block">Returns the authentication schemes for this repository.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the authentication schemes for this repository</dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../../allclasses.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
