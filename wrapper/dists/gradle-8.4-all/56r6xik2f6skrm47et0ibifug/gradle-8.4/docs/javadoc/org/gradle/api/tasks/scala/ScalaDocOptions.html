<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc -->
<title>ScalaDocOptions (Gradle API 8.4)</title>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<link rel="stylesheet" type="text/css" href="../../../../../javadoc.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../jquery/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
<script type="text/javascript" src="../../../../../jquery/jszip/dist/jszip.min.js"></script>
<script type="text/javascript" src="../../../../../jquery/jszip-utils/dist/jszip-utils.min.js"></script>
<!--[if IE]>
<script type="text/javascript" src="../../../../../jquery/jszip-utils/dist/jszip-utils-ie.min.js"></script>
<![endif]-->
<script type="text/javascript" src="../../../../../jquery/jquery-3.6.0.min.js"></script>
<script type="text/javascript" src="../../../../../jquery/jquery-ui.min.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="ScalaDocOptions (Gradle API 8.4)";
        }
    }
    catch(err) {
    }
//-->
var data = {"i0":10,"i1":10,"i2":10,"i3":10,"i4":10,"i5":10,"i6":10,"i7":10,"i8":10,"i9":10,"i10":10,"i11":10,"i12":10,"i13":10,"i14":10,"i15":10,"i16":10,"i17":10};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
var pathtoroot = "../../../../../";
var useModuleDirectories = false;
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="fixedNav">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../../allclasses.html">All&nbsp;Classes</a></li>
</ul>
<ul class="navListSearch">
<li><label for="search">SEARCH:</label>
<input type="text" id="search" value="search" disabled="disabled">
<input type="reset" id="reset" value="reset" disabled="disabled">
</li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
</div>
<div class="navPadding">&nbsp;</div>
<script type="text/javascript"><!--
$('.navPadding').css('padding-top', $('.fixedNav').css("height"));
//-->
</script>
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle"><span class="packageLabelInType">Package</span>&nbsp;<a href="package-summary.html">org.gradle.api.tasks.scala</a></div>
<h2 title="Class ScalaDocOptions" class="title">Class ScalaDocOptions</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li><a href="../compile/AbstractOptions.html" title="class in org.gradle.api.tasks.compile">org.gradle.api.tasks.compile.AbstractOptions</a></li>
<li>
<ul class="inheritance">
<li>org.gradle.api.tasks.scala.ScalaDocOptions</li>
</ul>
</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Implemented Interfaces:</dt>
<dd><code>java.io.Serializable</code></dd>
</dl>
<hr>
<pre>public abstract class <span class="typeNameLabel">ScalaDocOptions</span>
extends <a href="../compile/AbstractOptions.html" title="class in org.gradle.api.tasks.compile">AbstractOptions</a></pre>
<div class="block">Options for the ScalaDoc tool.</div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../../../serialized-form.html#org.gradle.api.tasks.scala.ScalaDocOptions">Serialized Form</a></dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="memberSummary" summary="Constructor Summary table, listing constructors, and an explanation">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Constructor</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tr class="altColor">
<th class="colConstructorName" scope="row"><code><span class="memberNameLink"><a href="#ScalaDocOptions--">ScalaDocOptions</a></span>()</code></th>
<td class="colLast">&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colSecond" scope="col">Method</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>java.util.List&lt;java.lang.String&gt;</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getAdditionalParameters--">getAdditionalParameters</a></span>()</code></th>
<td class="colLast">
<div class="block">Returns the additional parameters passed to the compiler.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>java.lang.String</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getBottom--">getBottom</a></span>()</code></th>
<td class="colLast">
<div class="block">Returns the HTML text to appear in the bottom text for each page.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getDocTitle--">getDocTitle</a></span>()</code></th>
<td class="colLast">
<div class="block">Returns the HTML text to appear in the main frame title.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>java.lang.String</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getFooter--">getFooter</a></span>()</code></th>
<td class="colLast">
<div class="block">Returns the HTML text to appear in the footer for each page.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getHeader--">getHeader</a></span>()</code></th>
<td class="colLast">
<div class="block">Returns the HTML text to appear in the header for each page.</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>java.lang.String</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getTop--">getTop</a></span>()</code></th>
<td class="colLast">
<div class="block">Returns the HTML text to appear in the top text for each page.</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getWindowTitle--">getWindowTitle</a></span>()</code></th>
<td class="colLast">
<div class="block">Returns the text to appear in the window title.</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#isDeprecation--">isDeprecation</a></span>()</code></th>
<td class="colLast">
<div class="block">Tells whether to generate deprecation information.</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#isUnchecked--">isUnchecked</a></span>()</code></th>
<td class="colLast">
<div class="block">Tells whether to generate unchecked information.</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setAdditionalParameters-java.util.List-">setAdditionalParameters</a></span>&#8203;(java.util.List&lt;java.lang.String&gt;&nbsp;additionalParameters)</code></th>
<td class="colLast">
<div class="block">Sets the additional parameters passed to the compiler.</div>
</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setBottom-java.lang.String-">setBottom</a></span>&#8203;(java.lang.String&nbsp;bottom)</code></th>
<td class="colLast">
<div class="block">Sets the HTML text to appear in the bottom text for each page.</div>
</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setDeprecation-boolean-">setDeprecation</a></span>&#8203;(boolean&nbsp;deprecation)</code></th>
<td class="colLast">
<div class="block">Sets whether to generate deprecation information.</div>
</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setDocTitle-java.lang.String-">setDocTitle</a></span>&#8203;(java.lang.String&nbsp;docTitle)</code></th>
<td class="colLast">
<div class="block">Sets the HTML text to appear in the main frame title.</div>
</td>
</tr>
<tr id="i13" class="rowColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setFooter-java.lang.String-">setFooter</a></span>&#8203;(java.lang.String&nbsp;footer)</code></th>
<td class="colLast">
<div class="block">Sets the HTML text to appear in the footer for each page.</div>
</td>
</tr>
<tr id="i14" class="altColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setHeader-java.lang.String-">setHeader</a></span>&#8203;(java.lang.String&nbsp;header)</code></th>
<td class="colLast">
<div class="block">Sets the HTML text to appear in the header for each page.</div>
</td>
</tr>
<tr id="i15" class="rowColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setTop-java.lang.String-">setTop</a></span>&#8203;(java.lang.String&nbsp;top)</code></th>
<td class="colLast">
<div class="block">Sets the HTML text to appear in the top text for each page.</div>
</td>
</tr>
<tr id="i16" class="altColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setUnchecked-boolean-">setUnchecked</a></span>&#8203;(boolean&nbsp;unchecked)</code></th>
<td class="colLast">
<div class="block">Sets whether to generate unchecked information.</div>
</td>
</tr>
<tr id="i17" class="rowColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setWindowTitle-java.lang.String-">setWindowTitle</a></span>&#8203;(java.lang.String&nbsp;windowTitle)</code></th>
<td class="colLast">
<div class="block">Sets the text to appear in the window title.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.org.gradle.api.tasks.compile.AbstractOptions">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;org.gradle.api.tasks.compile.<a href="../compile/AbstractOptions.html" title="class in org.gradle.api.tasks.compile">AbstractOptions</a></h3>
<code><a href="../compile/AbstractOptions.html#define-java.util.Map-">define</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>clone, equals, finalize, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a name="ScalaDocOptions--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>ScalaDocOptions</h4>
<pre>public&nbsp;ScalaDocOptions()</pre>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="isDeprecation--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isDeprecation</h4>
<pre class="methodSignature"><a href="../Input.html" title="annotation in org.gradle.api.tasks">@Input</a>
public&nbsp;boolean&nbsp;isDeprecation()</pre>
<div class="block">Tells whether to generate deprecation information.</div>
</li>
</ul>
<a name="setDeprecation-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setDeprecation</h4>
<pre class="methodSignature">public&nbsp;void&nbsp;setDeprecation&#8203;(boolean&nbsp;deprecation)</pre>
<div class="block">Sets whether to generate deprecation information.</div>
</li>
</ul>
<a name="isUnchecked--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isUnchecked</h4>
<pre class="methodSignature"><a href="../Input.html" title="annotation in org.gradle.api.tasks">@Input</a>
public&nbsp;boolean&nbsp;isUnchecked()</pre>
<div class="block">Tells whether to generate unchecked information.</div>
</li>
</ul>
<a name="setUnchecked-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setUnchecked</h4>
<pre class="methodSignature">public&nbsp;void&nbsp;setUnchecked&#8203;(boolean&nbsp;unchecked)</pre>
<div class="block">Sets whether to generate unchecked information.</div>
</li>
</ul>
<a name="getWindowTitle--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getWindowTitle</h4>
<pre class="methodSignature">@Nullable
<a href="../Optional.html" title="annotation in org.gradle.api.tasks">@Optional</a>
<a href="../Input.html" title="annotation in org.gradle.api.tasks">@Input</a>
public&nbsp;java.lang.String&nbsp;getWindowTitle()</pre>
<div class="block">Returns the text to appear in the window title.</div>
</li>
</ul>
<a name="setWindowTitle-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setWindowTitle</h4>
<pre class="methodSignature">public&nbsp;void&nbsp;setWindowTitle&#8203;(@Nullable
                           java.lang.String&nbsp;windowTitle)</pre>
<div class="block">Sets the text to appear in the window title.</div>
</li>
</ul>
<a name="getDocTitle--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getDocTitle</h4>
<pre class="methodSignature">@Nullable
<a href="../Optional.html" title="annotation in org.gradle.api.tasks">@Optional</a>
<a href="../Input.html" title="annotation in org.gradle.api.tasks">@Input</a>
public&nbsp;java.lang.String&nbsp;getDocTitle()</pre>
<div class="block">Returns the HTML text to appear in the main frame title.</div>
</li>
</ul>
<a name="setDocTitle-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setDocTitle</h4>
<pre class="methodSignature">public&nbsp;void&nbsp;setDocTitle&#8203;(@Nullable
                        java.lang.String&nbsp;docTitle)</pre>
<div class="block">Sets the HTML text to appear in the main frame title.</div>
</li>
</ul>
<a name="getHeader--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getHeader</h4>
<pre class="methodSignature">@Nullable
<a href="../Optional.html" title="annotation in org.gradle.api.tasks">@Optional</a>
<a href="../Input.html" title="annotation in org.gradle.api.tasks">@Input</a>
public&nbsp;java.lang.String&nbsp;getHeader()</pre>
<div class="block">Returns the HTML text to appear in the header for each page.</div>
</li>
</ul>
<a name="setHeader-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setHeader</h4>
<pre class="methodSignature">public&nbsp;void&nbsp;setHeader&#8203;(@Nullable
                      java.lang.String&nbsp;header)</pre>
<div class="block">Sets the HTML text to appear in the header for each page.</div>
</li>
</ul>
<a name="getFooter--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getFooter</h4>
<pre class="methodSignature">@Nullable
<a href="../Optional.html" title="annotation in org.gradle.api.tasks">@Optional</a>
<a href="../Input.html" title="annotation in org.gradle.api.tasks">@Input</a>
public&nbsp;java.lang.String&nbsp;getFooter()</pre>
<div class="block">Returns the HTML text to appear in the footer for each page.</div>
</li>
</ul>
<a name="setFooter-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setFooter</h4>
<pre class="methodSignature">public&nbsp;void&nbsp;setFooter&#8203;(@Nullable
                      java.lang.String&nbsp;footer)</pre>
<div class="block">Sets the HTML text to appear in the footer for each page.</div>
</li>
</ul>
<a name="getTop--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getTop</h4>
<pre class="methodSignature">@Nullable
<a href="../Optional.html" title="annotation in org.gradle.api.tasks">@Optional</a>
<a href="../Input.html" title="annotation in org.gradle.api.tasks">@Input</a>
public&nbsp;java.lang.String&nbsp;getTop()</pre>
<div class="block">Returns the HTML text to appear in the top text for each page.</div>
</li>
</ul>
<a name="setTop-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setTop</h4>
<pre class="methodSignature">public&nbsp;void&nbsp;setTop&#8203;(@Nullable
                   java.lang.String&nbsp;top)</pre>
<div class="block">Sets the HTML text to appear in the top text for each page.</div>
</li>
</ul>
<a name="getBottom--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getBottom</h4>
<pre class="methodSignature">@Nullable
<a href="../Optional.html" title="annotation in org.gradle.api.tasks">@Optional</a>
<a href="../Input.html" title="annotation in org.gradle.api.tasks">@Input</a>
public&nbsp;java.lang.String&nbsp;getBottom()</pre>
<div class="block">Returns the HTML text to appear in the bottom text for each page.</div>
</li>
</ul>
<a name="setBottom-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setBottom</h4>
<pre class="methodSignature">public&nbsp;void&nbsp;setBottom&#8203;(@Nullable
                      java.lang.String&nbsp;bottom)</pre>
<div class="block">Sets the HTML text to appear in the bottom text for each page.</div>
</li>
</ul>
<a name="getAdditionalParameters--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getAdditionalParameters</h4>
<pre class="methodSignature">@Nullable
<a href="../Optional.html" title="annotation in org.gradle.api.tasks">@Optional</a>
<a href="../Input.html" title="annotation in org.gradle.api.tasks">@Input</a>
public&nbsp;java.util.List&lt;java.lang.String&gt;&nbsp;getAdditionalParameters()</pre>
<div class="block">Returns the additional parameters passed to the compiler.
 Each parameter starts with '-'.</div>
</li>
</ul>
<a name="setAdditionalParameters-java.util.List-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>setAdditionalParameters</h4>
<pre class="methodSignature">public&nbsp;void&nbsp;setAdditionalParameters&#8203;(@Nullable
                                    java.util.List&lt;java.lang.String&gt;&nbsp;additionalParameters)</pre>
<div class="block">Sets the additional parameters passed to the compiler.
 Each parameter must start with '-'.</div>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../../allclasses.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
