<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc -->
<title>CodeQualityExtension (Gradle API 8.4)</title>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<link rel="stylesheet" type="text/css" href="../../../../../javadoc.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../jquery/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
<script type="text/javascript" src="../../../../../jquery/jszip/dist/jszip.min.js"></script>
<script type="text/javascript" src="../../../../../jquery/jszip-utils/dist/jszip-utils.min.js"></script>
<!--[if IE]>
<script type="text/javascript" src="../../../../../jquery/jszip-utils/dist/jszip-utils-ie.min.js"></script>
<![endif]-->
<script type="text/javascript" src="../../../../../jquery/jquery-3.6.0.min.js"></script>
<script type="text/javascript" src="../../../../../jquery/jquery-ui.min.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="CodeQualityExtension (Gradle API 8.4)";
        }
    }
    catch(err) {
    }
//-->
var data = {"i0":10,"i1":10,"i2":10,"i3":10,"i4":10,"i5":10,"i6":10,"i7":10};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
var pathtoroot = "../../../../../";
var useModuleDirectories = false;
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="fixedNav">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../../allclasses.html">All&nbsp;Classes</a></li>
</ul>
<ul class="navListSearch">
<li><label for="search">SEARCH:</label>
<input type="text" id="search" value="search" disabled="disabled">
<input type="reset" id="reset" value="reset" disabled="disabled">
</li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
</div>
<div class="navPadding">&nbsp;</div>
<script type="text/javascript"><!--
$('.navPadding').css('padding-top', $('.fixedNav').css("height"));
//-->
</script>
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle"><span class="packageLabelInType">Package</span>&nbsp;<a href="package-summary.html">org.gradle.api.plugins.quality</a></div>
<h2 title="Class CodeQualityExtension" class="title">Class CodeQualityExtension</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>org.gradle.api.plugins.quality.CodeQualityExtension</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>Direct Known Subclasses:</dt>
<dd><code><a href="CheckstyleExtension.html" title="class in org.gradle.api.plugins.quality">CheckstyleExtension</a></code>, <code><a href="CodeNarcExtension.html" title="class in org.gradle.api.plugins.quality">CodeNarcExtension</a></code>, <code><a href="PmdExtension.html" title="class in org.gradle.api.plugins.quality">PmdExtension</a></code></dd>
</dl>
<hr>
<pre>public abstract class <span class="typeNameLabel">CodeQualityExtension</span>
extends java.lang.Object</pre>
<div class="block">Base Code Quality Extension.</div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="memberSummary" summary="Constructor Summary table, listing constructors, and an explanation">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Constructor</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tr class="altColor">
<th class="colConstructorName" scope="row"><code><span class="memberNameLink"><a href="#CodeQualityExtension--">CodeQualityExtension</a></span>()</code></th>
<td class="colLast">&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colSecond" scope="col">Method</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>java.io.File</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getReportsDir--">getReportsDir</a></span>()</code></th>
<td class="colLast">
<div class="block">The directory where reports will be generated.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>java.util.Collection&lt;<a href="../../tasks/SourceSet.html" title="interface in org.gradle.api.tasks">SourceSet</a>&gt;</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getSourceSets--">getSourceSets</a></span>()</code></th>
<td class="colLast">
<div class="block">The source sets to be analyzed as part of the <code>check</code> and <code>build</code> tasks.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getToolVersion--">getToolVersion</a></span>()</code></th>
<td class="colLast">
<div class="block">The version of the code quality tool to be used.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#isIgnoreFailures--">isIgnoreFailures</a></span>()</code></th>
<td class="colLast">
<div class="block">Whether to allow the build to continue if there are warnings.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setIgnoreFailures-boolean-">setIgnoreFailures</a></span>&#8203;(boolean&nbsp;ignoreFailures)</code></th>
<td class="colLast">
<div class="block">Whether to allow the build to continue if there are warnings.</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setReportsDir-java.io.File-">setReportsDir</a></span>&#8203;(java.io.File&nbsp;reportsDir)</code></th>
<td class="colLast">
<div class="block">The directory where reports will be generated.</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setSourceSets-java.util.Collection-">setSourceSets</a></span>&#8203;(java.util.Collection&lt;<a href="../../tasks/SourceSet.html" title="interface in org.gradle.api.tasks">SourceSet</a>&gt;&nbsp;sourceSets)</code></th>
<td class="colLast">
<div class="block">The source sets to be analyzed as part of the <code>check</code> and <code>build</code> tasks.</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setToolVersion-java.lang.String-">setToolVersion</a></span>&#8203;(java.lang.String&nbsp;toolVersion)</code></th>
<td class="colLast">
<div class="block">The version of the code quality tool to be used.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>clone, equals, finalize, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a name="CodeQualityExtension--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>CodeQualityExtension</h4>
<pre>public&nbsp;CodeQualityExtension()</pre>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="getToolVersion--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getToolVersion</h4>
<pre class="methodSignature">public&nbsp;java.lang.String&nbsp;getToolVersion()</pre>
<div class="block">The version of the code quality tool to be used.</div>
</li>
</ul>
<a name="setToolVersion-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setToolVersion</h4>
<pre class="methodSignature">public&nbsp;void&nbsp;setToolVersion&#8203;(java.lang.String&nbsp;toolVersion)</pre>
<div class="block">The version of the code quality tool to be used.</div>
</li>
</ul>
<a name="getSourceSets--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getSourceSets</h4>
<pre class="methodSignature">public&nbsp;java.util.Collection&lt;<a href="../../tasks/SourceSet.html" title="interface in org.gradle.api.tasks">SourceSet</a>&gt;&nbsp;getSourceSets()</pre>
<div class="block">The source sets to be analyzed as part of the <code>check</code> and <code>build</code> tasks.</div>
</li>
</ul>
<a name="setSourceSets-java.util.Collection-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setSourceSets</h4>
<pre class="methodSignature">public&nbsp;void&nbsp;setSourceSets&#8203;(java.util.Collection&lt;<a href="../../tasks/SourceSet.html" title="interface in org.gradle.api.tasks">SourceSet</a>&gt;&nbsp;sourceSets)</pre>
<div class="block">The source sets to be analyzed as part of the <code>check</code> and <code>build</code> tasks.</div>
</li>
</ul>
<a name="isIgnoreFailures--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isIgnoreFailures</h4>
<pre class="methodSignature">public&nbsp;boolean&nbsp;isIgnoreFailures()</pre>
<div class="block">Whether to allow the build to continue if there are warnings.

 Example: ignoreFailures = true</div>
</li>
</ul>
<a name="setIgnoreFailures-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setIgnoreFailures</h4>
<pre class="methodSignature">public&nbsp;void&nbsp;setIgnoreFailures&#8203;(boolean&nbsp;ignoreFailures)</pre>
<div class="block">Whether to allow the build to continue if there are warnings.

 Example: ignoreFailures = true</div>
</li>
</ul>
<a name="getReportsDir--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getReportsDir</h4>
<pre class="methodSignature">public&nbsp;java.io.File&nbsp;getReportsDir()</pre>
<div class="block">The directory where reports will be generated.</div>
</li>
</ul>
<a name="setReportsDir-java.io.File-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>setReportsDir</h4>
<pre class="methodSignature">public&nbsp;void&nbsp;setReportsDir&#8203;(java.io.File&nbsp;reportsDir)</pre>
<div class="block">The directory where reports will be generated.</div>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../../allclasses.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
