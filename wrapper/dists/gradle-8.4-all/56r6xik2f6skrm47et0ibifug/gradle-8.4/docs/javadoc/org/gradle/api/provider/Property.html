<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc -->
<title>Property (Gradle API 8.4)</title>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<link rel="stylesheet" type="text/css" href="../../../../javadoc.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../jquery/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
<script type="text/javascript" src="../../../../jquery/jszip/dist/jszip.min.js"></script>
<script type="text/javascript" src="../../../../jquery/jszip-utils/dist/jszip-utils.min.js"></script>
<!--[if IE]>
<script type="text/javascript" src="../../../../jquery/jszip-utils/dist/jszip-utils-ie.min.js"></script>
<![endif]-->
<script type="text/javascript" src="../../../../jquery/jquery-3.6.0.min.js"></script>
<script type="text/javascript" src="../../../../jquery/jquery-ui.min.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="Property (Gradle API 8.4)";
        }
    }
    catch(err) {
    }
//-->
var data = {"i0":6,"i1":6,"i2":6,"i3":6,"i4":6,"i5":6,"i6":6};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],4:["t3","Abstract Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
var pathtoroot = "../../../../";
var useModuleDirectories = false;
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="fixedNav">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses.html">All&nbsp;Classes</a></li>
</ul>
<ul class="navListSearch">
<li><label for="search">SEARCH:</label>
<input type="text" id="search" value="search" disabled="disabled">
<input type="reset" id="reset" value="reset" disabled="disabled">
</li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
</div>
<div class="navPadding">&nbsp;</div>
<script type="text/javascript"><!--
$('.navPadding').css('padding-top', $('.fixedNav').css("height"));
//-->
</script>
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle"><span class="packageLabelInType">Package</span>&nbsp;<a href="package-summary.html">org.gradle.api.provider</a></div>
<h2 title="Interface Property" class="title">Interface Property&lt;T&gt;</h2>
</div>
<div class="contentContainer">
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt><span class="paramLabel">Type Parameters:</span></dt>
<dd><code>T</code> - Type of value represented by the property</dd>
</dl>
<dl>
<dt>All Superinterfaces:</dt>
<dd><code><a href="HasConfigurableValue.html" title="interface in org.gradle.api.provider">HasConfigurableValue</a></code>, <code><a href="Provider.html" title="interface in org.gradle.api.provider">Provider</a>&lt;T&gt;</code></dd>
</dl>
<dl>
<dt>All Known Subinterfaces:</dt>
<dd><code><a href="../file/DirectoryProperty.html" title="interface in org.gradle.api.file">DirectoryProperty</a></code>, <code><a href="../file/FileSystemLocationProperty.html" title="interface in org.gradle.api.file">FileSystemLocationProperty</a>&lt;T&gt;</code>, <code><a href="../file/RegularFileProperty.html" title="interface in org.gradle.api.file">RegularFileProperty</a></code></dd>
</dl>
<hr>
<pre><a href="../SupportsKotlinAssignmentOverloading.html" title="annotation in org.gradle.api">@SupportsKotlinAssignmentOverloading</a>
public interface <span class="typeNameLabel">Property&lt;T&gt;</span>
extends <a href="Provider.html" title="interface in org.gradle.api.provider">Provider</a>&lt;T&gt;, <a href="HasConfigurableValue.html" title="interface in org.gradle.api.provider">HasConfigurableValue</a></pre>
<div class="block">A container object that represents a configurable value of a specific type. A <a href="Property.html" title="interface in org.gradle.api.provider"><code>Property</code></a> is also a
 <a href="Provider.html" title="interface in org.gradle.api.provider"><code>Provider</code></a> and can be used in the same way as a <a href="Provider.html" title="interface in org.gradle.api.provider"><code>Provider</code></a>. A property's value can be accessed
 using the methods of <a href="Provider.html" title="interface in org.gradle.api.provider"><code>Provider</code></a> such as <a href="Provider.html#get--"><code>get()</code></a>. The value can be modified by
 using the methods <a href="#set-T-"><code>set(Object)</code></a> and <a href="#set-org.gradle.api.provider.Provider-"><code>set(Provider)</code></a>, or their fluid API counterparts
 <a href="#value-T-"><code>value(Object)</code></a> and <a href="#value-org.gradle.api.provider.Provider-"><code>value(Provider)</code></a>.

 <p>
 A property may represent a task output. Such a property carries information about the task producing
 its value. When this property is attached to an input of another task, Gradle will automatically determine
 the task dependencies based on this connection.
 </p>

 <p>
 You can create a <a href="Property.html" title="interface in org.gradle.api.provider"><code>Property</code></a> instance using <a href="../model/ObjectFactory.html#property-java.lang.Class-"><code>ObjectFactory.property(Class)</code></a>. There are
 also several specialized subtypes of this interface that can be created using various other factory methods.
 </p>

 <p>
 <b>Note:</b> This interface is not intended for implementation by build script or plugin authors.
 </p></div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>4.3</dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t3" class="tableTab"><span><a href="javascript:show(4);">Abstract Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colSecond" scope="col">Method</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code><a href="Property.html" title="interface in org.gradle.api.provider">Property</a>&lt;<a href="Property.html" title="type parameter in Property">T</a>&gt;</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#convention-org.gradle.api.provider.Provider-">convention</a></span>&#8203;(<a href="Provider.html" title="interface in org.gradle.api.provider">Provider</a>&lt;? extends <a href="Property.html" title="type parameter in Property">T</a>&gt;&nbsp;provider)</code></th>
<td class="colLast">
<div class="block">Specifies the provider to be used to query the convention (default value) for this property.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code><a href="Property.html" title="interface in org.gradle.api.provider">Property</a>&lt;<a href="Property.html" title="type parameter in Property">T</a>&gt;</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#convention-T-">convention</a></span>&#8203;(<a href="Property.html" title="type parameter in Property">T</a>&nbsp;value)</code></th>
<td class="colLast">
<div class="block">Specifies the value to use as the convention (default value) for this property.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#finalizeValue--">finalizeValue</a></span>()</code></th>
<td class="colLast">
<div class="block">Disallows further changes to the value of this property.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#set-org.gradle.api.provider.Provider-">set</a></span>&#8203;(<a href="Provider.html" title="interface in org.gradle.api.provider">Provider</a>&lt;? extends <a href="Property.html" title="type parameter in Property">T</a>&gt;&nbsp;provider)</code></th>
<td class="colLast">
<div class="block">Sets the property to have the same value as the given provider, replacing whatever value the property already had.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#set-T-">set</a></span>&#8203;(<a href="Property.html" title="type parameter in Property">T</a>&nbsp;value)</code></th>
<td class="colLast">
<div class="block">Sets the value of the property to the given value, replacing whatever value the property already had.</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code><a href="Property.html" title="interface in org.gradle.api.provider">Property</a>&lt;<a href="Property.html" title="type parameter in Property">T</a>&gt;</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#value-org.gradle.api.provider.Provider-">value</a></span>&#8203;(<a href="Provider.html" title="interface in org.gradle.api.provider">Provider</a>&lt;? extends <a href="Property.html" title="type parameter in Property">T</a>&gt;&nbsp;provider)</code></th>
<td class="colLast">
<div class="block">Sets the property to have the same value as the given provider, replacing whatever value the property already had.</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code><a href="Property.html" title="interface in org.gradle.api.provider">Property</a>&lt;<a href="Property.html" title="type parameter in Property">T</a>&gt;</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#value-T-">value</a></span>&#8203;(<a href="Property.html" title="type parameter in Property">T</a>&nbsp;value)</code></th>
<td class="colLast">
<div class="block">Sets the value of the property to the given value, replacing whatever value the property already had.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.org.gradle.api.provider.HasConfigurableValue">
<!--   -->
</a>
<h3>Methods inherited from interface&nbsp;org.gradle.api.provider.<a href="HasConfigurableValue.html" title="interface in org.gradle.api.provider">HasConfigurableValue</a></h3>
<code><a href="HasConfigurableValue.html#disallowChanges--">disallowChanges</a>, <a href="HasConfigurableValue.html#disallowUnsafeRead--">disallowUnsafeRead</a>, <a href="HasConfigurableValue.html#finalizeValueOnRead--">finalizeValueOnRead</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.org.gradle.api.provider.Provider">
<!--   -->
</a>
<h3>Methods inherited from interface&nbsp;org.gradle.api.provider.<a href="Provider.html" title="interface in org.gradle.api.provider">Provider</a></h3>
<code><a href="Provider.html#filter-java.util.function.Predicate-">filter</a>, <a href="Provider.html#flatMap-org.gradle.api.Transformer-">flatMap</a>, <a href="Provider.html#forUseAtConfigurationTime--">forUseAtConfigurationTime</a>, <a href="Provider.html#get--">get</a>, <a href="Provider.html#getOrElse-T-">getOrElse</a>, <a href="Provider.html#getOrNull--">getOrNull</a>, <a href="Provider.html#isPresent--">isPresent</a>, <a href="Provider.html#map-org.gradle.api.Transformer-">map</a>, <a href="Provider.html#orElse-org.gradle.api.provider.Provider-">orElse</a>, <a href="Provider.html#orElse-T-">orElse</a>, <a href="Provider.html#zip-org.gradle.api.provider.Provider-java.util.function.BiFunction-">zip</a></code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="set-java.lang.Object-">
<!--   -->
</a><a name="set-T-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>set</h4>
<pre class="methodSignature">void&nbsp;set&#8203;(@Nullable
         <a href="Property.html" title="type parameter in Property">T</a>&nbsp;value)</pre>
<div class="block">Sets the value of the property to the given value, replacing whatever value the property already had.

 <p>
 This method can also be used to discard the value of the property, by passing it <code>null</code>. When the
 value is discarded (or has never been set in the first place), the convention (default value) for this
 property, if specified, will be used to provide the value instead.
 </p></div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - The value, can be null.</dd>
</dl>
</li>
</ul>
<a name="set-org.gradle.api.provider.Provider-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>set</h4>
<pre class="methodSignature">void&nbsp;set&#8203;(<a href="Provider.html" title="interface in org.gradle.api.provider">Provider</a>&lt;? extends <a href="Property.html" title="type parameter in Property">T</a>&gt;&nbsp;provider)</pre>
<div class="block">Sets the property to have the same value as the given provider, replacing whatever value the property already had.
 This property will track the value of the provider and query its value each time the value of the property is queried.
 When the provider has no value, this property will also have no value.

 <p>
 This method can NOT be used to discard the value of the property. Specifying a <code>null</code> provider will result
 in an <code>IllegalArgumentException</code> being thrown. When the provider has no value, this property will also have
 no value - regardless of whether or not a convention has been set.
 </p>

 <p>
 When the given provider represents a task output, this property will also carry the task dependency information
 from the provider.
 </p></div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>provider</code> - The provider of the property's value, can't be null.</dd>
</dl>
</li>
</ul>
<a name="value-java.lang.Object-">
<!--   -->
</a><a name="value-T-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>value</h4>
<pre class="methodSignature"><a href="Property.html" title="interface in org.gradle.api.provider">Property</a>&lt;<a href="Property.html" title="type parameter in Property">T</a>&gt;&nbsp;value&#8203;(@Nullable
                  <a href="Property.html" title="type parameter in Property">T</a>&nbsp;value)</pre>
<div class="block">Sets the value of the property to the given value, replacing whatever value the property already had.
 This is the same as <a href="#set-T-"><code>set(Object)</code></a> but returns this property to allow method chaining.

 <p>
 This method can also be used to discard the value of the property, by passing it <code>null</code>.
 When the value is discarded (or has never been set in the first place), the convention (default value)
 for this property, if specified, will be used to provide the value instead.
 </p></div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - The value, can be null.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>this</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>5.0</dd>
</dl>
</li>
</ul>
<a name="value-org.gradle.api.provider.Provider-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>value</h4>
<pre class="methodSignature"><a href="Property.html" title="interface in org.gradle.api.provider">Property</a>&lt;<a href="Property.html" title="type parameter in Property">T</a>&gt;&nbsp;value&#8203;(<a href="Provider.html" title="interface in org.gradle.api.provider">Provider</a>&lt;? extends <a href="Property.html" title="type parameter in Property">T</a>&gt;&nbsp;provider)</pre>
<div class="block">Sets the property to have the same value as the given provider, replacing whatever value the property already had.
 This property will track the value of the provider and query its value each time the value of the property is queried.
 When the provider has no value, this property will also have no value. This is the same as <a href="#set-org.gradle.api.provider.Provider-"><code>set(Provider)</code></a>
 but returns this property to allow method chaining.

 <p>
 This method can NOT be used to discard the value of the property. Specifying a <code>null</code> provider will result
 in an <code>IllegalArgumentException</code> being thrown. When the provider has no value, this property will also have
 no value - regardless of whether or not a convention has been set.
 </p>

 <p>
 When the given provider represents a task output, this property will also carry the task dependency information
 from the provider.
 </p></div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>provider</code> - The provider whose value to use.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>this</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>5.6</dd>
</dl>
</li>
</ul>
<a name="convention-java.lang.Object-">
<!--   -->
</a><a name="convention-T-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>convention</h4>
<pre class="methodSignature"><a href="Property.html" title="interface in org.gradle.api.provider">Property</a>&lt;<a href="Property.html" title="type parameter in Property">T</a>&gt;&nbsp;convention&#8203;(@Nullable
                       <a href="Property.html" title="type parameter in Property">T</a>&nbsp;value)</pre>
<div class="block">Specifies the value to use as the convention (default value) for this property. If the convention is set and
 no explicit value or value provider has been specified, then the convention will be returned as the value of
 the property (when queried).

 <p>
 This method can be used to specify that the property does not have a default value, by passing it
 <code>null</code>.
 </p></div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - The convention value, or <code>null</code> if the property should have no default value.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>this</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>5.1</dd>
</dl>
</li>
</ul>
<a name="convention-org.gradle.api.provider.Provider-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>convention</h4>
<pre class="methodSignature"><a href="Property.html" title="interface in org.gradle.api.provider">Property</a>&lt;<a href="Property.html" title="type parameter in Property">T</a>&gt;&nbsp;convention&#8203;(<a href="Provider.html" title="interface in org.gradle.api.provider">Provider</a>&lt;? extends <a href="Property.html" title="type parameter in Property">T</a>&gt;&nbsp;provider)</pre>
<div class="block">Specifies the provider to be used to query the convention (default value) for this property. If a convention
 provider has been set and no explicit value or value provider has been specified, then the convention
 provider's value will be returned as the value of the property (when queried).

 <p>
 The property's convention tracks the convention provider. Whenever the convention's actual value is
 needed, the convention provider will be queried anew.
 </p>

 <p>
 This method can't be used to specify that a property does not have a default value. Passing in a <code>null</code>
 provider will result in an <code>IllegalArgumentException</code> being thrown. When the provider doesn't have
 a value, then the property will behave as if it wouldn't have a convention specified.
 </p></div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>provider</code> - The provider of the property's convention value, can't be null.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>this</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>5.1</dd>
</dl>
</li>
</ul>
<a name="finalizeValue--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>finalizeValue</h4>
<pre class="methodSignature">void&nbsp;finalizeValue()</pre>
<div class="block">Disallows further changes to the value of this property. Calls to methods that change the value of this property,
 such as <a href="#set-T-"><code>set(Object)</code></a>, <a href="#set-org.gradle.api.provider.Provider-"><code>set(Provider)</code></a>, <a href="#value-T-"><code>value(Object)</code></a> and <a href="#value-org.gradle.api.provider.Provider-"><code>value(Provider)</code></a> will fail,
 by throwing an <code>IllegalStateException</code>.

 <p>
 When this property's value is specified via a <a href="Provider.html" title="interface in org.gradle.api.provider"><code>Provider</code></a>, calling <code>finalizeValue()</code> will trigger the
 querying of the provider and the obtained value will be set as the final value of the property. The value of the
 provider will not be tracked further.
 </p>

 <p>
 Note that although the value of the property will not change, the value itself may be a mutable object. Calling
 this method does not guarantee that the value will become immutable.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="HasConfigurableValue.html#finalizeValue--">finalizeValue</a></code>&nbsp;in interface&nbsp;<code><a href="HasConfigurableValue.html" title="interface in org.gradle.api.provider">HasConfigurableValue</a></code></dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>5.0</dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
