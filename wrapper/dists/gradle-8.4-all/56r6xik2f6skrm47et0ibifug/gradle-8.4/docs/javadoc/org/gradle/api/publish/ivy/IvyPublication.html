<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc -->
<title>IvyPublication (Gradle API 8.4)</title>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<link rel="stylesheet" type="text/css" href="../../../../../javadoc.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../jquery/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
<script type="text/javascript" src="../../../../../jquery/jszip/dist/jszip.min.js"></script>
<script type="text/javascript" src="../../../../../jquery/jszip-utils/dist/jszip-utils.min.js"></script>
<!--[if IE]>
<script type="text/javascript" src="../../../../../jquery/jszip-utils/dist/jszip-utils-ie.min.js"></script>
<![endif]-->
<script type="text/javascript" src="../../../../../jquery/jquery-3.6.0.min.js"></script>
<script type="text/javascript" src="../../../../../jquery/jquery-ui.min.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="IvyPublication (Gradle API 8.4)";
        }
    }
    catch(err) {
    }
//-->
var data = {"i0":6,"i1":6,"i2":6,"i3":6,"i4":6,"i5":6,"i6":6,"i7":6,"i8":6,"i9":6,"i10":6,"i11":6,"i12":6,"i13":6,"i14":6,"i15":6,"i16":6,"i17":6};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],4:["t3","Abstract Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
var pathtoroot = "../../../../../";
var useModuleDirectories = false;
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="fixedNav">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../../allclasses.html">All&nbsp;Classes</a></li>
</ul>
<ul class="navListSearch">
<li><label for="search">SEARCH:</label>
<input type="text" id="search" value="search" disabled="disabled">
<input type="reset" id="reset" value="reset" disabled="disabled">
</li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li><a href="#nested.class.summary">Nested</a>&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
</div>
<div class="navPadding">&nbsp;</div>
<script type="text/javascript"><!--
$('.navPadding').css('padding-top', $('.fixedNav').css("height"));
//-->
</script>
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle"><span class="packageLabelInType">Package</span>&nbsp;<a href="package-summary.html">org.gradle.api.publish.ivy</a></div>
<h2 title="Interface IvyPublication" class="title">Interface IvyPublication</h2>
</div>
<div class="contentContainer">
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Superinterfaces:</dt>
<dd><code><a href="../../Named.html" title="interface in org.gradle.api">Named</a></code>, <code><a href="../Publication.html" title="interface in org.gradle.api.publish">Publication</a></code></dd>
</dl>
<hr>
<pre>public interface <span class="typeNameLabel">IvyPublication</span>
extends <a href="../Publication.html" title="interface in org.gradle.api.publish">Publication</a></pre>
<div class="block">An <code>IvyPublication</code> is the representation/configuration of how Gradle should publish something in Ivy format, to an Ivy repository.

 You directly add a named Ivy publication the project's <code>publishing.publications</code> container by providing <a href="IvyPublication.html" title="interface in org.gradle.api.publish.ivy"><code>IvyPublication</code></a> as the type.
 <pre>
 publishing {
   publications {
     myPublicationName(IvyPublication) {
       // Configure the publication here
     }
   }
 }
 </pre>

 <p>
 The Ivy module identifying attributes of the publication are mapped as follows:
 </p>
 <ul>
 <li><code>module</code> - <code>project.name</code></li>
 <li><code>organisation</code> - <code>project.group</code></li>
 <li><code>revision</code> - <code>project.version</code></li>
 <li><code>status</code> - <code>project.status</code></li>
 </ul>

 <p>
 For certain common use cases, it's often sufficient to specify the component to publish, using (<a href="#from-org.gradle.api.component.SoftwareComponent-"><code>from(org.gradle.api.component.SoftwareComponent)</code></a>.
 The published component is used to determine which artifacts to publish, and which configurations and dependencies should be listed in the generated ivy descriptor file.
 </p><p>
 You can add configurations to the generated ivy descriptor file, by supplying a Closure to the <a href="#configurations-org.gradle.api.Action-"><code>configurations(org.gradle.api.Action)</code></a> method.
 </p><p>
 To add additional artifacts to the set published, use the <a href="#artifact-java.lang.Object-"><code>artifact(Object)</code></a> and <a href="#artifact-java.lang.Object-org.gradle.api.Action-"><code>artifact(Object, org.gradle.api.Action)</code></a> methods.
 You can also completely replace the set of published artifacts using <a href="#setArtifacts-java.lang.Iterable-"><code>setArtifacts(Iterable)</code></a>.
 Together, these methods give you full control over the artifacts to be published.
 </p><p>
 In addition, <a href="IvyModuleDescriptorSpec.html" title="interface in org.gradle.api.publish.ivy"><code>IvyModuleDescriptorSpec</code></a> provides configuration methods to customize licenses, authors, and the description to be published in the Ivy module descriptor.
 </p><p>
 For any other tweaks to the publication, it is possible to modify the generated Ivy descriptor file prior to publication. This is done using
 the <a href="IvyModuleDescriptorSpec.html#withXml-org.gradle.api.Action-"><code>IvyModuleDescriptorSpec.withXml(org.gradle.api.Action)</code></a> method, normally via a Closure passed to the <a href="#descriptor-org.gradle.api.Action-"><code>descriptor(org.gradle.api.Action)</code></a> method.
 </p>
 <h4>Example of publishing a java component with an added source jar and custom module description</h4>

 <pre class='autoTested'>
 plugins {
     id 'java'
     id 'ivy-publish'
 }

 task sourceJar(type: Jar) {
   from sourceSets.main.allJava
 }

 publishing {
   publications {
     myPublication(IvyPublication) {
       from components.java
       artifact(sourceJar) {
         type "source"
         extension "src.jar"
         conf "runtime"
       }
       descriptor {
         license {
           name = "Custom License"
         }
         author {
           name = "Custom Name"
         }
         description {
           text = "Custom Description"
         }
       }
     }
   }
 }
 </pre></div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>1.3</dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== NESTED CLASS SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="nested.class.summary">
<!--   -->
</a>
<h3>Nested Class Summary</h3>
<ul class="blockList">
<li class="blockList"><a name="nested.classes.inherited.from.class.org.gradle.api.Named">
<!--   -->
</a>
<h3>Nested classes/interfaces inherited from interface&nbsp;org.gradle.api.<a href="../../Named.html" title="interface in org.gradle.api">Named</a></h3>
<code><a href="../../Named.Namer.html" title="class in org.gradle.api">Named.Namer</a></code></li>
</ul>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t3" class="tableTab"><span><a href="javascript:show(4);">Abstract Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colSecond" scope="col">Method</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code><a href="IvyArtifact.html" title="interface in org.gradle.api.publish.ivy">IvyArtifact</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#artifact-java.lang.Object-">artifact</a></span>&#8203;(java.lang.Object&nbsp;source)</code></th>
<td class="colLast">
<div class="block">Creates a custom <a href="IvyArtifact.html" title="interface in org.gradle.api.publish.ivy"><code>IvyArtifact</code></a> to be included in the publication.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code><a href="IvyArtifact.html" title="interface in org.gradle.api.publish.ivy">IvyArtifact</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#artifact-java.lang.Object-org.gradle.api.Action-">artifact</a></span>&#8203;(java.lang.Object&nbsp;source,
        <a href="../../Action.html" title="interface in org.gradle.api">Action</a>&lt;? super <a href="IvyArtifact.html" title="interface in org.gradle.api.publish.ivy">IvyArtifact</a>&gt;&nbsp;config)</code></th>
<td class="colLast">
<div class="block">Creates an <a href="IvyArtifact.html" title="interface in org.gradle.api.publish.ivy"><code>IvyArtifact</code></a> to be included in the publication, which is configured by the associated action.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#configurations-org.gradle.api.Action-">configurations</a></span>&#8203;(<a href="../../Action.html" title="interface in org.gradle.api">Action</a>&lt;? super <a href="IvyConfigurationContainer.html" title="interface in org.gradle.api.publish.ivy">IvyConfigurationContainer</a>&gt;&nbsp;config)</code></th>
<td class="colLast">
<div class="block">Defines some <a href="IvyConfiguration.html" title="interface in org.gradle.api.publish.ivy"><code>IvyConfiguration</code></a>s that should be included in the published ivy module descriptor file.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#descriptor-org.gradle.api.Action-">descriptor</a></span>&#8203;(<a href="../../Action.html" title="interface in org.gradle.api">Action</a>&lt;? super <a href="IvyModuleDescriptorSpec.html" title="interface in org.gradle.api.publish.ivy">IvyModuleDescriptorSpec</a>&gt;&nbsp;configure)</code></th>
<td class="colLast">
<div class="block">Configures the descriptor that will be published.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#from-org.gradle.api.component.SoftwareComponent-">from</a></span>&#8203;(<a href="../../component/SoftwareComponent.html" title="interface in org.gradle.api.component">SoftwareComponent</a>&nbsp;component)</code></th>
<td class="colLast">
<div class="block">Provides the software component that should be published.</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code><a href="IvyArtifactSet.html" title="interface in org.gradle.api.publish.ivy">IvyArtifactSet</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getArtifacts--">getArtifacts</a></span>()</code></th>
<td class="colLast">
<div class="block">The complete set of artifacts for this publication.</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code><a href="IvyConfigurationContainer.html" title="interface in org.gradle.api.publish.ivy">IvyConfigurationContainer</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getConfigurations--">getConfigurations</a></span>()</code></th>
<td class="colLast">
<div class="block">Returns the complete set of configurations for this publication.</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code><a href="IvyModuleDescriptorSpec.html" title="interface in org.gradle.api.publish.ivy">IvyModuleDescriptorSpec</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getDescriptor--">getDescriptor</a></span>()</code></th>
<td class="colLast">
<div class="block">The module descriptor that will be published.</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getModule--">getModule</a></span>()</code></th>
<td class="colLast">
<div class="block">Returns the module for this publication.</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>java.lang.String</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getOrganisation--">getOrganisation</a></span>()</code></th>
<td class="colLast">
<div class="block">Returns the organisation for this publication.</div>
</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getRevision--">getRevision</a></span>()</code></th>
<td class="colLast">
<div class="block">Returns the revision for this publication.</div>
</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setArtifacts-java.lang.Iterable-">setArtifacts</a></span>&#8203;(java.lang.Iterable&lt;?&gt;&nbsp;sources)</code></th>
<td class="colLast">
<div class="block">Sets the artifacts for this publication.</div>
</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setModule-java.lang.String-">setModule</a></span>&#8203;(java.lang.String&nbsp;module)</code></th>
<td class="colLast">
<div class="block">Sets the module for this publication.</div>
</td>
</tr>
<tr id="i13" class="rowColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setOrganisation-java.lang.String-">setOrganisation</a></span>&#8203;(java.lang.String&nbsp;organisation)</code></th>
<td class="colLast">
<div class="block">Sets the organisation for this publication.</div>
</td>
</tr>
<tr id="i14" class="altColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setRevision-java.lang.String-">setRevision</a></span>&#8203;(java.lang.String&nbsp;revision)</code></th>
<td class="colLast">
<div class="block">Sets the revision for this publication.</div>
</td>
</tr>
<tr id="i15" class="rowColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#suppressAllIvyMetadataWarnings--">suppressAllIvyMetadataWarnings</a></span>()</code></th>
<td class="colLast">
<div class="block">Silences all the compatibility warnings for the Ivy publication.</div>
</td>
</tr>
<tr id="i16" class="altColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#suppressIvyMetadataWarningsFor-java.lang.String-">suppressIvyMetadataWarningsFor</a></span>&#8203;(java.lang.String&nbsp;variantName)</code></th>
<td class="colLast">
<div class="block">Silences the compatibility warnings for the Ivy publication for the specified variant.</div>
</td>
</tr>
<tr id="i17" class="rowColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#versionMapping-org.gradle.api.Action-">versionMapping</a></span>&#8203;(<a href="../../Action.html" title="interface in org.gradle.api">Action</a>&lt;? super <a href="../VersionMappingStrategy.html" title="interface in org.gradle.api.publish">VersionMappingStrategy</a>&gt;&nbsp;configureAction)</code></th>
<td class="colLast">
<div class="block">Configures the version mapping strategy.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.org.gradle.api.Named">
<!--   -->
</a>
<h3>Methods inherited from interface&nbsp;org.gradle.api.<a href="../../Named.html" title="interface in org.gradle.api">Named</a></h3>
<code><a href="../../Named.html#getName--">getName</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.org.gradle.api.publish.Publication">
<!--   -->
</a>
<h3>Methods inherited from interface&nbsp;org.gradle.api.publish.<a href="../Publication.html" title="interface in org.gradle.api.publish">Publication</a></h3>
<code><a href="../Publication.html#withBuildIdentifier--">withBuildIdentifier</a>, <a href="../Publication.html#withoutBuildIdentifier--">withoutBuildIdentifier</a></code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="getDescriptor--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getDescriptor</h4>
<pre class="methodSignature"><a href="IvyModuleDescriptorSpec.html" title="interface in org.gradle.api.publish.ivy">IvyModuleDescriptorSpec</a>&nbsp;getDescriptor()</pre>
<div class="block">The module descriptor that will be published.
 <p></div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The module descriptor that will be published.</dd>
</dl>
</li>
</ul>
<a name="descriptor-org.gradle.api.Action-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>descriptor</h4>
<pre class="methodSignature">void&nbsp;descriptor&#8203;(<a href="../../Action.html" title="interface in org.gradle.api">Action</a>&lt;? super <a href="IvyModuleDescriptorSpec.html" title="interface in org.gradle.api.publish.ivy">IvyModuleDescriptorSpec</a>&gt;&nbsp;configure)</pre>
<div class="block">Configures the descriptor that will be published.
 <p>
 The descriptor XML can be modified by using the <a href="IvyModuleDescriptorSpec.html#withXml-org.gradle.api.Action-"><code>IvyModuleDescriptorSpec.withXml(org.gradle.api.Action)</code></a> method.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>configure</code> - The configuration action.</dd>
</dl>
</li>
</ul>
<a name="from-org.gradle.api.component.SoftwareComponent-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>from</h4>
<pre class="methodSignature">void&nbsp;from&#8203;(<a href="../../component/SoftwareComponent.html" title="interface in org.gradle.api.component">SoftwareComponent</a>&nbsp;component)</pre>
<div class="block">Provides the software component that should be published.

 <ul>
     <li>Any artifacts declared by the component will be included in the publication.</li>
     <li>The dependencies declared by the component will be included in the published meta-data.</li>
 </ul>

 Currently 2 types of component are supported: 'components.java' (added by the JavaPlugin) and 'components.web' (added by the WarPlugin).
 For any individual IvyPublication, only a single component can be provided in this way.

 The following example demonstrates how to publish the 'java' component to a ivy repository.
 <pre class='autoTested'>
 plugins {
     id 'java'
     id 'ivy-publish'
 }

 publishing {
   publications {
     ivy(IvyPublication) {
       from components.java
     }
   }
 }
 </pre></div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>component</code> - The software component to publish.</dd>
</dl>
</li>
</ul>
<a name="configurations-org.gradle.api.Action-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>configurations</h4>
<pre class="methodSignature">void&nbsp;configurations&#8203;(<a href="../../Action.html" title="interface in org.gradle.api">Action</a>&lt;? super <a href="IvyConfigurationContainer.html" title="interface in org.gradle.api.publish.ivy">IvyConfigurationContainer</a>&gt;&nbsp;config)</pre>
<div class="block">Defines some <a href="IvyConfiguration.html" title="interface in org.gradle.api.publish.ivy"><code>IvyConfiguration</code></a>s that should be included in the published ivy module descriptor file.

 The following example demonstrates how to add a "testCompile" configuration, and a "testRuntime" configuration that extends it.
 <pre class='autoTested'>
 plugins {
     id 'java'
     id 'ivy-publish'
 }

 publishing {
   publications {
     ivy(IvyPublication) {
       configurations {
           testCompile {}
           testRuntime {
               extend "testCompile"
           }
       }
     }
   }
 }
 </pre></div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>config</code> - An action or closure to configure the values of the constructed <a href="IvyConfiguration.html" title="interface in org.gradle.api.publish.ivy"><code>IvyConfiguration</code></a>.</dd>
</dl>
</li>
</ul>
<a name="getConfigurations--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getConfigurations</h4>
<pre class="methodSignature"><a href="IvyConfigurationContainer.html" title="interface in org.gradle.api.publish.ivy">IvyConfigurationContainer</a>&nbsp;getConfigurations()</pre>
<div class="block">Returns the complete set of configurations for this publication.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the configurations</dd>
</dl>
</li>
</ul>
<a name="artifact-java.lang.Object-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>artifact</h4>
<pre class="methodSignature"><a href="IvyArtifact.html" title="interface in org.gradle.api.publish.ivy">IvyArtifact</a>&nbsp;artifact&#8203;(java.lang.Object&nbsp;source)</pre>
<div class="block">Creates a custom <a href="IvyArtifact.html" title="interface in org.gradle.api.publish.ivy"><code>IvyArtifact</code></a> to be included in the publication.

 The <code>artifact</code> method can take a variety of input:
 <ul>
     <li>A <a href="../../artifacts/PublishArtifact.html" title="interface in org.gradle.api.artifacts"><code>PublishArtifact</code></a> instance. Name, type, extension and classifier values are taken from the supplied instance.</li>
     <li>An <a href="../../tasks/bundling/AbstractArchiveTask.html" title="class in org.gradle.api.tasks.bundling"><code>AbstractArchiveTask</code></a> instance. Name, type, extension and classifier values are taken from the supplied instance.</li>
     <li>Anything that can be resolved to a <code>File</code> via the <a href="../../Project.html#file-java.lang.Object-"><code>Project.file(Object)</code></a> method.
          Name, extension and classifier values are interpolated from the file name.</li>
     <li>A <code>Map</code> that contains a 'source' entry that can be resolved as any of the other input types, including file.
         This map can contain additional attributes to further configure the constructed artifact.</li>
 </ul>

 The following example demonstrates the addition of various custom artifacts.
 <pre class='autoTested'>
 plugins {
     id 'ivy-publish'
 }

 task sourceJar(type: Jar) {
   archiveClassifier = "source"
 }

 task genDocs {
   doLast {
     // Generate 'my-docs-file.htm'
   }
 }

 publishing {
   publications {
     ivy(IvyPublication) {
       artifact sourceJar // Publish the output of the sourceJar task
       artifact 'my-file-name.jar' // Publish a file created outside of the build
       artifact source: 'my-docs-file.htm', classifier: 'docs', extension: 'html', builtBy: genDocs // Publish a file generated by the 'genDocs' task
     }
   }
 }
 </pre></div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>source</code> - The source of the artifact content.</dd>
</dl>
</li>
</ul>
<a name="artifact-java.lang.Object-org.gradle.api.Action-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>artifact</h4>
<pre class="methodSignature"><a href="IvyArtifact.html" title="interface in org.gradle.api.publish.ivy">IvyArtifact</a>&nbsp;artifact&#8203;(java.lang.Object&nbsp;source,
                     <a href="../../Action.html" title="interface in org.gradle.api">Action</a>&lt;? super <a href="IvyArtifact.html" title="interface in org.gradle.api.publish.ivy">IvyArtifact</a>&gt;&nbsp;config)</pre>
<div class="block">Creates an <a href="IvyArtifact.html" title="interface in org.gradle.api.publish.ivy"><code>IvyArtifact</code></a> to be included in the publication, which is configured by the associated action.

 The first parameter is used to create a custom artifact and add it to the publication, as per <a href="#artifact-java.lang.Object-"><code>artifact(Object)</code></a>.
 The created <a href="IvyArtifact.html" title="interface in org.gradle.api.publish.ivy"><code>IvyArtifact</code></a> is then configured using the supplied action.
 This method also accepts the configure action as a closure argument, by type coercion.

 <pre class='autoTested'>
 plugins {
     id 'ivy-publish'
 }

 task sourceJar(type: Jar) {
   archiveClassifier = "source"
 }

 task genDocs {
   doLast {
     // Generate 'my-docs-file.htm'
   }
 }

 publishing {
   publications {
     ivy(IvyPublication) {
       artifact(sourceJar) {
         // These values will be used instead of the values from the task. The task values will not be updated.
         classifier "src"
         extension "zip"
         conf "runtime-&gt;default"
       }
       artifact("my-docs-file.htm") {
         type "documentation"
         extension "html"
         builtBy genDocs
       }
     }
   }
 }
 </pre></div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>source</code> - The source of the artifact.</dd>
<dd><code>config</code> - An action to configure the values of the constructed <a href="IvyArtifact.html" title="interface in org.gradle.api.publish.ivy"><code>IvyArtifact</code></a>.</dd>
</dl>
</li>
</ul>
<a name="getArtifacts--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getArtifacts</h4>
<pre class="methodSignature"><a href="IvyArtifactSet.html" title="interface in org.gradle.api.publish.ivy">IvyArtifactSet</a>&nbsp;getArtifacts()</pre>
<div class="block">The complete set of artifacts for this publication.

 <p>
 Setting this property will clear any previously added artifacts and create artifacts from the specified sources.
 Each supplied source is interpreted as per <a href="#artifact-java.lang.Object-"><code>artifact(Object)</code></a>.

 For example, to exclude the dependencies declared by a component and instead use a custom set of artifacts:
 <pre class='autoTested'>
 plugins {
     id 'java'
     id 'ivy-publish'
 }

 task sourceJar(type: Jar) {
   archiveClassifier = "source"
 }

 publishing {
   publications {
     ivy(IvyPublication) {
       from components.java
       artifacts = ["my-custom-jar.jar", sourceJar]
     }
   }
 }
 </pre></div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the artifacts.</dd>
</dl>
</li>
</ul>
<a name="setArtifacts-java.lang.Iterable-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setArtifacts</h4>
<pre class="methodSignature">void&nbsp;setArtifacts&#8203;(java.lang.Iterable&lt;?&gt;&nbsp;sources)</pre>
<div class="block">Sets the artifacts for this publication. Each supplied value is interpreted as per <a href="#artifact-java.lang.Object-"><code>artifact(Object)</code></a>.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>sources</code> - The set of artifacts for this publication.</dd>
</dl>
</li>
</ul>
<a name="getOrganisation--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getOrganisation</h4>
<pre class="methodSignature">java.lang.String&nbsp;getOrganisation()</pre>
<div class="block">Returns the organisation for this publication.</div>
</li>
</ul>
<a name="setOrganisation-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setOrganisation</h4>
<pre class="methodSignature">void&nbsp;setOrganisation&#8203;(java.lang.String&nbsp;organisation)</pre>
<div class="block">Sets the organisation for this publication.</div>
</li>
</ul>
<a name="getModule--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getModule</h4>
<pre class="methodSignature">java.lang.String&nbsp;getModule()</pre>
<div class="block">Returns the module for this publication.</div>
</li>
</ul>
<a name="setModule-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setModule</h4>
<pre class="methodSignature">void&nbsp;setModule&#8203;(java.lang.String&nbsp;module)</pre>
<div class="block">Sets the module for this publication.</div>
</li>
</ul>
<a name="getRevision--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getRevision</h4>
<pre class="methodSignature">java.lang.String&nbsp;getRevision()</pre>
<div class="block">Returns the revision for this publication.</div>
</li>
</ul>
<a name="setRevision-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setRevision</h4>
<pre class="methodSignature">void&nbsp;setRevision&#8203;(java.lang.String&nbsp;revision)</pre>
<div class="block">Sets the revision for this publication.</div>
</li>
</ul>
<a name="versionMapping-org.gradle.api.Action-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>versionMapping</h4>
<pre class="methodSignature">void&nbsp;versionMapping&#8203;(<a href="../../Action.html" title="interface in org.gradle.api">Action</a>&lt;? super <a href="../VersionMappingStrategy.html" title="interface in org.gradle.api.publish">VersionMappingStrategy</a>&gt;&nbsp;configureAction)</pre>
<div class="block">Configures the version mapping strategy.

 For example, to use resolved versions for runtime dependencies:
 <pre class='autoTested'>
 plugins {
     id 'java'
     id 'ivy-publish'
 }

 publishing {
   publications {
     maven(IvyPublication) {
       from components.java
       versionMapping {
         usage('java-runtime'){
           fromResolutionResult()
         }
       }
     }
   }
 }
 </pre></div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>configureAction</code> - the configuration</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>5.4</dd>
</dl>
</li>
</ul>
<a name="suppressIvyMetadataWarningsFor-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>suppressIvyMetadataWarningsFor</h4>
<pre class="methodSignature">void&nbsp;suppressIvyMetadataWarningsFor&#8203;(java.lang.String&nbsp;variantName)</pre>
<div class="block">Silences the compatibility warnings for the Ivy publication for the specified variant.

 Warnings are emitted when Gradle features are used that cannot be mapped completely to Ivy xml.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>variantName</code> - the variant to silence warning for</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>6.0</dd>
</dl>
</li>
</ul>
<a name="suppressAllIvyMetadataWarnings--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>suppressAllIvyMetadataWarnings</h4>
<pre class="methodSignature">void&nbsp;suppressAllIvyMetadataWarnings()</pre>
<div class="block">Silences all the compatibility warnings for the Ivy publication.

 Warnings are emitted when Gradle features are used that cannot be mapped completely to Ivy xml.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>6.0</dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../../allclasses.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li><a href="#nested.class.summary">Nested</a>&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
