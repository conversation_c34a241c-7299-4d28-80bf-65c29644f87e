<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc -->
<title>AbstractArchiveTask (Gradle API 8.4)</title>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<link rel="stylesheet" type="text/css" href="../../../../../javadoc.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../jquery/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
<script type="text/javascript" src="../../../../../jquery/jszip/dist/jszip.min.js"></script>
<script type="text/javascript" src="../../../../../jquery/jszip-utils/dist/jszip-utils.min.js"></script>
<!--[if IE]>
<script type="text/javascript" src="../../../../../jquery/jszip-utils/dist/jszip-utils-ie.min.js"></script>
<![endif]-->
<script type="text/javascript" src="../../../../../jquery/jquery-3.6.0.min.js"></script>
<script type="text/javascript" src="../../../../../jquery/jquery-ui.min.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="AbstractArchiveTask (Gradle API 8.4)";
        }
    }
    catch(err) {
    }
//-->
var data = {"i0":10,"i1":10,"i2":10,"i3":10,"i4":10,"i5":10,"i6":10,"i7":42,"i8":10,"i9":10,"i10":10,"i11":10,"i12":10,"i13":10,"i14":10,"i15":10,"i16":10};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"],32:["t6","Deprecated Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
var pathtoroot = "../../../../../";
var useModuleDirectories = false;
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="fixedNav">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../../allclasses.html">All&nbsp;Classes</a></li>
</ul>
<ul class="navListSearch">
<li><label for="search">SEARCH:</label>
<input type="text" id="search" value="search" disabled="disabled">
<input type="reset" id="reset" value="reset" disabled="disabled">
</li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li><a href="#nested.class.summary">Nested</a>&nbsp;|&nbsp;</li>
<li><a href="#field.summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
</div>
<div class="navPadding">&nbsp;</div>
<script type="text/javascript"><!--
$('.navPadding').css('padding-top', $('.fixedNav').css("height"));
//-->
</script>
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle"><span class="packageLabelInType">Package</span>&nbsp;<a href="package-summary.html">org.gradle.api.tasks.bundling</a></div>
<h2 title="Class AbstractArchiveTask" class="title">Class AbstractArchiveTask</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>org.gradle.api.internal.AbstractTask</li>
<li>
<ul class="inheritance">
<li><a href="../../DefaultTask.html" title="class in org.gradle.api">org.gradle.api.DefaultTask</a></li>
<li>
<ul class="inheritance">
<li>org.gradle.api.internal.ConventionTask</li>
<li>
<ul class="inheritance">
<li><a href="../AbstractCopyTask.html" title="class in org.gradle.api.tasks">org.gradle.api.tasks.AbstractCopyTask</a></li>
<li>
<ul class="inheritance">
<li>org.gradle.api.tasks.bundling.AbstractArchiveTask</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Implemented Interfaces:</dt>
<dd><code>java.lang.Comparable&lt;<a href="../../Task.html" title="interface in org.gradle.api">Task</a>&gt;</code>, <code><a href="../../file/ContentFilterable.html" title="interface in org.gradle.api.file">ContentFilterable</a></code>, <code><a href="../../file/CopyProcessingSpec.html" title="interface in org.gradle.api.file">CopyProcessingSpec</a></code>, <code><a href="../../file/CopySourceSpec.html" title="interface in org.gradle.api.file">CopySourceSpec</a></code>, <code><a href="../../file/CopySpec.html" title="interface in org.gradle.api.file">CopySpec</a></code>, <code>org.gradle.api.internal.DynamicObjectAware</code>, <code>org.gradle.api.internal.file.copy.CopySpecSource</code>, <code>org.gradle.api.internal.IConventionAware</code>, <code>org.gradle.api.internal.TaskInternal</code>, <code><a href="../../plugins/ExtensionAware.html" title="interface in org.gradle.api.plugins">ExtensionAware</a></code>, <code><a href="../../Task.html" title="interface in org.gradle.api">Task</a></code>, <code><a href="../util/PatternFilterable.html" title="interface in org.gradle.api.tasks.util">PatternFilterable</a></code>, <code><a href="../../../util/Configurable.html" title="interface in org.gradle.util">Configurable</a>&lt;<a href="../../Task.html" title="interface in org.gradle.api">Task</a>&gt;</code></dd>
</dl>
<dl>
<dt>Direct Known Subclasses:</dt>
<dd><code><a href="Tar.html" title="class in org.gradle.api.tasks.bundling">Tar</a></code>, <code><a href="Zip.html" title="class in org.gradle.api.tasks.bundling">Zip</a></code></dd>
</dl>
<hr>
<pre><a href="../../../work/DisableCachingByDefault.html" title="annotation in org.gradle.work">@DisableCachingByDefault</a>(<a href="../../../work/DisableCachingByDefault.html#because--">because</a>="Abstract super-class, not to be instantiated directly")
public abstract class <span class="typeNameLabel">AbstractArchiveTask</span>
extends <a href="../AbstractCopyTask.html" title="class in org.gradle.api.tasks">AbstractCopyTask</a></pre>
<div class="block"><code>AbstractArchiveTask</code> is the base class for all archive tasks.</div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== NESTED CLASS SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="nested.class.summary">
<!--   -->
</a>
<h3>Nested Class Summary</h3>
<ul class="blockList">
<li class="blockList"><a name="nested.classes.inherited.from.class.org.gradle.api.Task">
<!--   -->
</a>
<h3>Nested classes/interfaces inherited from interface&nbsp;org.gradle.api.<a href="../../Task.html" title="interface in org.gradle.api">Task</a></h3>
<code><a href="../../Task.Namer.html" title="class in org.gradle.api">Task.Namer</a></code></li>
</ul>
</li>
</ul>
<!-- =========== FIELD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.summary">
<!--   -->
</a>
<h3>Field Summary</h3>
<ul class="blockList">
<li class="blockList"><a name="fields.inherited.from.class.org.gradle.api.Task">
<!--   -->
</a>
<h3>Fields inherited from interface&nbsp;org.gradle.api.<a href="../../Task.html" title="interface in org.gradle.api">Task</a></h3>
<code><a href="../../Task.html#TASK_ACTION">TASK_ACTION</a>, <a href="../../Task.html#TASK_CONSTRUCTOR_ARGS">TASK_CONSTRUCTOR_ARGS</a>, <a href="../../Task.html#TASK_DEPENDS_ON">TASK_DEPENDS_ON</a>, <a href="../../Task.html#TASK_DESCRIPTION">TASK_DESCRIPTION</a>, <a href="../../Task.html#TASK_GROUP">TASK_GROUP</a>, <a href="../../Task.html#TASK_NAME">TASK_NAME</a>, <a href="../../Task.html#TASK_OVERWRITE">TASK_OVERWRITE</a>, <a href="../../Task.html#TASK_TYPE">TASK_TYPE</a></code></li>
</ul>
</li>
</ul>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="memberSummary" summary="Constructor Summary table, listing constructors, and an explanation">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Constructor</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tr class="altColor">
<th class="colConstructorName" scope="row"><code><span class="memberNameLink"><a href="#AbstractArchiveTask--">AbstractArchiveTask</a></span>()</code></th>
<td class="colLast">&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t6" class="tableTab"><span><a href="javascript:show(32);">Deprecated Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colSecond" scope="col">Method</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>protected org.gradle.api.internal.file.copy.CopyActionExecuter</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#createCopyActionExecuter--">createCopyActionExecuter</a></span>()</code></th>
<td class="colLast">&nbsp;</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code><a href="../../provider/Property.html" title="interface in org.gradle.api.provider">Property</a>&lt;java.lang.String&gt;</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getArchiveAppendix--">getArchiveAppendix</a></span>()</code></th>
<td class="colLast">
<div class="block">Returns the appendix part of the archive name, if any.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code><a href="../../provider/Property.html" title="interface in org.gradle.api.provider">Property</a>&lt;java.lang.String&gt;</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getArchiveBaseName--">getArchiveBaseName</a></span>()</code></th>
<td class="colLast">
<div class="block">Returns the base name of the archive.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code><a href="../../provider/Property.html" title="interface in org.gradle.api.provider">Property</a>&lt;java.lang.String&gt;</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getArchiveClassifier--">getArchiveClassifier</a></span>()</code></th>
<td class="colLast">
<div class="block">Returns the classifier part of the archive name, if any.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code><a href="../../provider/Property.html" title="interface in org.gradle.api.provider">Property</a>&lt;java.lang.String&gt;</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getArchiveExtension--">getArchiveExtension</a></span>()</code></th>
<td class="colLast">
<div class="block">Returns the extension part of the archive name.</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code><a href="../../provider/Provider.html" title="interface in org.gradle.api.provider">Provider</a>&lt;<a href="../../file/RegularFile.html" title="interface in org.gradle.api.file">RegularFile</a>&gt;</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getArchiveFile--">getArchiveFile</a></span>()</code></th>
<td class="colLast">
<div class="block">The <a href="../../file/RegularFile.html" title="interface in org.gradle.api.file"><code>RegularFile</code></a> where the archive is constructed.</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code><a href="../../provider/Property.html" title="interface in org.gradle.api.provider">Property</a>&lt;java.lang.String&gt;</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getArchiveFileName--">getArchiveFileName</a></span>()</code></th>
<td class="colLast">
<div class="block">Returns the archive name.</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>java.io.File</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getArchivePath--">getArchivePath</a></span>()</code></th>
<td class="colLast">
<div class="block"><span class="deprecatedLabel">Deprecated.</span>
<div class="deprecationComment">Use <a href="#getArchiveFile--"><code>getArchiveFile()</code></a></div>
</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code><a href="../../provider/Property.html" title="interface in org.gradle.api.provider">Property</a>&lt;java.lang.String&gt;</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getArchiveVersion--">getArchiveVersion</a></span>()</code></th>
<td class="colLast">
<div class="block">Returns the version part of the archive name.</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code><a href="../../file/DirectoryProperty.html" title="interface in org.gradle.api.file">DirectoryProperty</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getDestinationDirectory--">getDestinationDirectory</a></span>()</code></th>
<td class="colLast">
<div class="block">The directory where the archive will be placed.</div>
</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code><a href="AbstractArchiveTask.html" title="class in org.gradle.api.tasks.bundling">AbstractArchiveTask</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#into-java.lang.Object-">into</a></span>&#8203;(java.lang.Object&nbsp;destPath)</code></th>
<td class="colLast">
<div class="block">Specifies the destination directory *inside* the archive for the files.</div>
</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code><a href="AbstractArchiveTask.html" title="class in org.gradle.api.tasks.bundling">AbstractArchiveTask</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#into-java.lang.Object-groovy.lang.Closure-">into</a></span>&#8203;(java.lang.Object&nbsp;destPath,
    <a href="https://docs.groovy-lang.org/docs/groovy-3.0.17/html/gapi/groovy/lang/Closure.html?is-external=true" title="class or interface in groovy.lang" class="externalLink">Closure</a>&nbsp;configureClosure)</code></th>
<td class="colLast">
<div class="block">Creates and configures a child <code>CopySpec</code> with a destination directory *inside* the archive for the files.</div>
</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code><a href="../../file/CopySpec.html" title="interface in org.gradle.api.file">CopySpec</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#into-java.lang.Object-org.gradle.api.Action-">into</a></span>&#8203;(java.lang.Object&nbsp;destPath,
    <a href="../../Action.html" title="interface in org.gradle.api">Action</a>&lt;? super <a href="../../file/CopySpec.html" title="interface in org.gradle.api.file">CopySpec</a>&gt;&nbsp;copySpec)</code></th>
<td class="colLast">
<div class="block">Creates and configures a child <code>CopySpec</code> with a destination directory *inside* the archive for the files.</div>
</td>
</tr>
<tr id="i13" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#isPreserveFileTimestamps--">isPreserveFileTimestamps</a></span>()</code></th>
<td class="colLast">
<div class="block">Specifies whether file timestamps should be preserved in the archive.</div>
</td>
</tr>
<tr id="i14" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#isReproducibleFileOrder--">isReproducibleFileOrder</a></span>()</code></th>
<td class="colLast">
<div class="block">Specifies whether to enforce a reproducible file order when reading files from directories.</div>
</td>
</tr>
<tr id="i15" class="rowColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setPreserveFileTimestamps-boolean-">setPreserveFileTimestamps</a></span>&#8203;(boolean&nbsp;preserveFileTimestamps)</code></th>
<td class="colLast">
<div class="block">Specifies whether file timestamps should be preserved in the archive.</div>
</td>
</tr>
<tr id="i16" class="altColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setReproducibleFileOrder-boolean-">setReproducibleFileOrder</a></span>&#8203;(boolean&nbsp;reproducibleFileOrder)</code></th>
<td class="colLast">
<div class="block">Specifies whether to enforce a reproducible file order when reading files from directories.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.org.gradle.api.tasks.AbstractCopyTask">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;org.gradle.api.tasks.<a href="../AbstractCopyTask.html" title="class in org.gradle.api.tasks">AbstractCopyTask</a></h3>
<code><a href="../AbstractCopyTask.html#copy--">copy</a>, <a href="../AbstractCopyTask.html#createCopyAction--">createCopyAction</a>, <a href="../AbstractCopyTask.html#createRootSpec--">createRootSpec</a>, <a href="../AbstractCopyTask.html#dirPermissions-org.gradle.api.Action-">dirPermissions</a>, <a href="../AbstractCopyTask.html#eachFile-groovy.lang.Closure-">eachFile</a>, <a href="../AbstractCopyTask.html#eachFile-org.gradle.api.Action-">eachFile</a>, <a href="../AbstractCopyTask.html#exclude-groovy.lang.Closure-">exclude</a>, <a href="../AbstractCopyTask.html#exclude-java.lang.Iterable-">exclude</a>, <a href="../AbstractCopyTask.html#exclude-java.lang.String...-">exclude</a>, <a href="../AbstractCopyTask.html#exclude-org.gradle.api.specs.Spec-">exclude</a>, <a href="../AbstractCopyTask.html#expand-java.util.Map-">expand</a>, <a href="../AbstractCopyTask.html#expand-java.util.Map-org.gradle.api.Action-">expand</a>, <a href="../AbstractCopyTask.html#filePermissions-org.gradle.api.Action-">filePermissions</a>, <a href="../AbstractCopyTask.html#filesMatching-java.lang.Iterable-org.gradle.api.Action-">filesMatching</a>, <a href="../AbstractCopyTask.html#filesMatching-java.lang.String-org.gradle.api.Action-">filesMatching</a>, <a href="../AbstractCopyTask.html#filesNotMatching-java.lang.Iterable-org.gradle.api.Action-">filesNotMatching</a>, <a href="../AbstractCopyTask.html#filesNotMatching-java.lang.String-org.gradle.api.Action-">filesNotMatching</a>, <a href="../AbstractCopyTask.html#filter-groovy.lang.Closure-">filter</a>, <a href="../AbstractCopyTask.html#filter-java.lang.Class-">filter</a>, <a href="../AbstractCopyTask.html#filter-java.util.Map-java.lang.Class-">filter</a>, <a href="../AbstractCopyTask.html#filter-org.gradle.api.Transformer-">filter</a>, <a href="../AbstractCopyTask.html#from-java.lang.Object...-">from</a>, <a href="../AbstractCopyTask.html#from-java.lang.Object-groovy.lang.Closure-">from</a>, <a href="../AbstractCopyTask.html#from-java.lang.Object-org.gradle.api.Action-">from</a>, <a href="../AbstractCopyTask.html#getDirectoryFileTreeFactory--">getDirectoryFileTreeFactory</a>, <a href="../AbstractCopyTask.html#getDirMode--">getDirMode</a>, <a href="../AbstractCopyTask.html#getDirPermissions--">getDirPermissions</a>, <a href="../AbstractCopyTask.html#getDocumentationRegistry--">getDocumentationRegistry</a>, <a href="../AbstractCopyTask.html#getDuplicatesStrategy--">getDuplicatesStrategy</a>, <a href="../AbstractCopyTask.html#getExcludes--">getExcludes</a>, <a href="../AbstractCopyTask.html#getFileLookup--">getFileLookup</a>, <a href="../AbstractCopyTask.html#getFileMode--">getFileMode</a>, <a href="../AbstractCopyTask.html#getFilePermissions--">getFilePermissions</a>, <a href="../AbstractCopyTask.html#getFileResolver--">getFileResolver</a>, <a href="../AbstractCopyTask.html#getFileSystem--">getFileSystem</a>, <a href="../AbstractCopyTask.html#getFilteringCharset--">getFilteringCharset</a>, <a href="../AbstractCopyTask.html#getIncludeEmptyDirs--">getIncludeEmptyDirs</a>, <a href="../AbstractCopyTask.html#getIncludes--">getIncludes</a>, <a href="../AbstractCopyTask.html#getInstantiator--">getInstantiator</a>, <a href="../AbstractCopyTask.html#getMainSpec--">getMainSpec</a>, <a href="../AbstractCopyTask.html#getObjectFactory--">getObjectFactory</a>, <a href="../AbstractCopyTask.html#getRootSpec--">getRootSpec</a>, <a href="../AbstractCopyTask.html#getSource--">getSource</a>, <a href="../AbstractCopyTask.html#include-groovy.lang.Closure-">include</a>, <a href="../AbstractCopyTask.html#include-java.lang.Iterable-">include</a>, <a href="../AbstractCopyTask.html#include-java.lang.String...-">include</a>, <a href="../AbstractCopyTask.html#include-org.gradle.api.specs.Spec-">include</a>, <a href="../AbstractCopyTask.html#isCaseSensitive--">isCaseSensitive</a>, <a href="../AbstractCopyTask.html#rename-groovy.lang.Closure-">rename</a>, <a href="../AbstractCopyTask.html#rename-java.lang.String-java.lang.String-">rename</a>, <a href="../AbstractCopyTask.html#rename-java.util.regex.Pattern-java.lang.String-">rename</a>, <a href="../AbstractCopyTask.html#rename-org.gradle.api.Transformer-">rename</a>, <a href="../AbstractCopyTask.html#setCaseSensitive-boolean-">setCaseSensitive</a>, <a href="../AbstractCopyTask.html#setDirMode-java.lang.Integer-">setDirMode</a>, <a href="../AbstractCopyTask.html#setDuplicatesStrategy-org.gradle.api.file.DuplicatesStrategy-">setDuplicatesStrategy</a>, <a href="../AbstractCopyTask.html#setExcludes-java.lang.Iterable-">setExcludes</a>, <a href="../AbstractCopyTask.html#setFileMode-java.lang.Integer-">setFileMode</a>, <a href="../AbstractCopyTask.html#setFilteringCharset-java.lang.String-">setFilteringCharset</a>, <a href="../AbstractCopyTask.html#setIncludeEmptyDirs-boolean-">setIncludeEmptyDirs</a>, <a href="../AbstractCopyTask.html#setIncludes-java.lang.Iterable-">setIncludes</a>, <a href="../AbstractCopyTask.html#with-org.gradle.api.file.CopySpec...-">with</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.org.gradle.api.internal.ConventionTask">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;org.gradle.api.internal.ConventionTask</h3>
<code>conventionMapping, conventionMapping, getConventionMapping</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.org.gradle.api.DefaultTask">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;org.gradle.api.<a href="../../DefaultTask.html" title="class in org.gradle.api">DefaultTask</a></h3>
<code><a href="../../DefaultTask.html#compareTo-org.gradle.api.Task-">compareTo</a>, <a href="../../DefaultTask.html#configure-groovy.lang.Closure-">configure</a>, <a href="../../DefaultTask.html#dependsOn-java.lang.Object...-">dependsOn</a>, <a href="../../DefaultTask.html#doFirst-groovy.lang.Closure-">doFirst</a>, <a href="../../DefaultTask.html#doFirst-java.lang.String-org.gradle.api.Action-">doFirst</a>, <a href="../../DefaultTask.html#doFirst-org.gradle.api.Action-">doFirst</a>, <a href="../../DefaultTask.html#doLast-groovy.lang.Closure-">doLast</a>, <a href="../../DefaultTask.html#doLast-java.lang.String-org.gradle.api.Action-">doLast</a>, <a href="../../DefaultTask.html#doLast-org.gradle.api.Action-">doLast</a>, <a href="../../DefaultTask.html#finalizedBy-java.lang.Object...-">finalizedBy</a>, <a href="../../DefaultTask.html#getActions--">getActions</a>, <a href="../../DefaultTask.html#getAnt--">getAnt</a>, <a href="../../DefaultTask.html#getDependsOn--">getDependsOn</a>, <a href="../../DefaultTask.html#getDescription--">getDescription</a>, <a href="../../DefaultTask.html#getDestroyables--">getDestroyables</a>, <a href="../../DefaultTask.html#getDidWork--">getDidWork</a>, <a href="../../DefaultTask.html#getEnabled--">getEnabled</a>, <a href="../../DefaultTask.html#getExtensions--">getExtensions</a>, <a href="../../DefaultTask.html#getFinalizedBy--">getFinalizedBy</a>, <a href="../../DefaultTask.html#getGroup--">getGroup</a>, <a href="../../DefaultTask.html#getInputs--">getInputs</a>, <a href="../../DefaultTask.html#getLocalState--">getLocalState</a>, <a href="../../DefaultTask.html#getLogger--">getLogger</a>, <a href="../../DefaultTask.html#getLogging--">getLogging</a>, <a href="../../DefaultTask.html#getMustRunAfter--">getMustRunAfter</a>, <a href="../../DefaultTask.html#getName--">getName</a>, <a href="../../DefaultTask.html#getOutputs--">getOutputs</a>, <a href="../../DefaultTask.html#getPath--">getPath</a>, <a href="../../DefaultTask.html#getProject--">getProject</a>, <a href="../../DefaultTask.html#getShouldRunAfter--">getShouldRunAfter</a>, <a href="../../DefaultTask.html#getState--">getState</a>, <a href="../../DefaultTask.html#getTaskDependencies--">getTaskDependencies</a>, <a href="../../DefaultTask.html#getTemporaryDir--">getTemporaryDir</a>, <a href="../../DefaultTask.html#getTimeout--">getTimeout</a>, <a href="../../DefaultTask.html#hasProperty-java.lang.String-">hasProperty</a>, <a href="../../DefaultTask.html#mustRunAfter-java.lang.Object...-">mustRunAfter</a>, <a href="../../DefaultTask.html#onlyIf-groovy.lang.Closure-">onlyIf</a>, <a href="../../DefaultTask.html#onlyIf-java.lang.String-org.gradle.api.specs.Spec-">onlyIf</a>, <a href="../../DefaultTask.html#onlyIf-org.gradle.api.specs.Spec-">onlyIf</a>, <a href="../../DefaultTask.html#property-java.lang.String-">property</a>, <a href="../../DefaultTask.html#setActions-java.util.List-">setActions</a>, <a href="../../DefaultTask.html#setDependsOn-java.lang.Iterable-">setDependsOn</a>, <a href="../../DefaultTask.html#setDescription-java.lang.String-">setDescription</a>, <a href="../../DefaultTask.html#setDidWork-boolean-">setDidWork</a>, <a href="../../DefaultTask.html#setEnabled-boolean-">setEnabled</a>, <a href="../../DefaultTask.html#setFinalizedBy-java.lang.Iterable-">setFinalizedBy</a>, <a href="../../DefaultTask.html#setGroup-java.lang.String-">setGroup</a>, <a href="../../DefaultTask.html#setMustRunAfter-java.lang.Iterable-">setMustRunAfter</a>, <a href="../../DefaultTask.html#setOnlyIf-groovy.lang.Closure-">setOnlyIf</a>, <a href="../../DefaultTask.html#setOnlyIf-java.lang.String-org.gradle.api.specs.Spec-">setOnlyIf</a>, <a href="../../DefaultTask.html#setOnlyIf-org.gradle.api.specs.Spec-">setOnlyIf</a>, <a href="../../DefaultTask.html#setProperty-java.lang.String-java.lang.Object-">setProperty</a>, <a href="../../DefaultTask.html#setShouldRunAfter-java.lang.Iterable-">setShouldRunAfter</a>, <a href="../../DefaultTask.html#shouldRunAfter-java.lang.Object...-">shouldRunAfter</a>, <a href="../../DefaultTask.html#usesService-org.gradle.api.provider.Provider-">usesService</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.org.gradle.api.internal.AbstractTask">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;org.gradle.api.internal.AbstractTask</h3>
<code>acceptServiceReferences, appendParallelSafeAction, doNotTrackState, getAsDynamicObject, getConvention, getIdentityPath, getImpliesSubProjects, getLifecycleDependencies, getOnlyIf, getReasonNotToTrackState, getReasonTaskIsIncompatibleWithConfigurationCache, getRequiredServices, getServices, getSharedResources, getStandardOutputCapture, getTaskActions, getTaskIdentity, getTemporaryDirFactory, hasTaskActions, injectIntoNewInstance, isCompatibleWithConfigurationCache, isEnabled, isHasCustomActions, notCompatibleWithConfigurationCache, prependParallelSafeAction, setImpliesSubProjects</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>clone, equals, finalize, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.org.gradle.api.Task">
<!--   -->
</a>
<h3>Methods inherited from interface&nbsp;org.gradle.api.<a href="../../Task.html" title="interface in org.gradle.api">Task</a></h3>
<code><a href="../../Task.html#doNotTrackState-java.lang.String-">doNotTrackState</a>, <a href="../../Task.html#getConvention--">getConvention</a>, <a href="../../Task.html#notCompatibleWithConfigurationCache-java.lang.String-">notCompatibleWithConfigurationCache</a></code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a name="AbstractArchiveTask--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>AbstractArchiveTask</h4>
<pre>public&nbsp;AbstractArchiveTask()</pre>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="getArchiveFileName--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getArchiveFileName</h4>
<pre class="methodSignature"><a href="../Internal.html" title="annotation in org.gradle.api.tasks">@Internal</a>("Represented as part of archiveFile")
public&nbsp;<a href="../../provider/Property.html" title="interface in org.gradle.api.provider">Property</a>&lt;java.lang.String&gt;&nbsp;getArchiveFileName()</pre>
<div class="block">Returns the archive name. If the name has not been explicitly set, the pattern for the name is:
 <code>[archiveBaseName]-[archiveAppendix]-[archiveVersion]-[archiveClassifier].[archiveExtension]</code></div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the archive name.</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>5.1</dd>
</dl>
</li>
</ul>
<a name="getArchiveFile--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getArchiveFile</h4>
<pre class="methodSignature"><a href="../OutputFile.html" title="annotation in org.gradle.api.tasks">@OutputFile</a>
public&nbsp;<a href="../../provider/Provider.html" title="interface in org.gradle.api.provider">Provider</a>&lt;<a href="../../file/RegularFile.html" title="interface in org.gradle.api.file">RegularFile</a>&gt;&nbsp;getArchiveFile()</pre>
<div class="block">The <a href="../../file/RegularFile.html" title="interface in org.gradle.api.file"><code>RegularFile</code></a> where the archive is constructed.
 The path is simply the <code>destinationDirectory</code> plus the <code>archiveFileName</code>.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>a <a href="../../file/RegularFile.html" title="interface in org.gradle.api.file"><code>RegularFile</code></a> object with the path to the archive</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>5.1</dd>
</dl>
</li>
</ul>
<a name="getDestinationDirectory--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getDestinationDirectory</h4>
<pre class="methodSignature"><a href="../Internal.html" title="annotation in org.gradle.api.tasks">@Internal</a>("Represented by the archiveFile")
public&nbsp;<a href="../../file/DirectoryProperty.html" title="interface in org.gradle.api.file">DirectoryProperty</a>&nbsp;getDestinationDirectory()</pre>
<div class="block">The directory where the archive will be placed.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>5.1</dd>
</dl>
</li>
</ul>
<a name="getArchivePath--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getArchivePath</h4>
<pre class="methodSignature">@Deprecated
<a href="../../model/ReplacedBy.html" title="annotation in org.gradle.api.model">@ReplacedBy</a>("archiveFile")
public&nbsp;java.io.File&nbsp;getArchivePath()</pre>
<div class="deprecationBlock"><span class="deprecatedLabel">Deprecated.</span>
<div class="deprecationComment">Use <a href="#getArchiveFile--"><code>getArchiveFile()</code></a></div>
</div>
<div class="block">The path where the archive is constructed. The path is simply the <code>destinationDirectory</code> plus the <code>archiveFileName</code>.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>a File object with the path to the archive</dd>
</dl>
</li>
</ul>
<a name="getArchiveBaseName--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getArchiveBaseName</h4>
<pre class="methodSignature"><a href="../Internal.html" title="annotation in org.gradle.api.tasks">@Internal</a>("Represented as part of archiveFile")
public&nbsp;<a href="../../provider/Property.html" title="interface in org.gradle.api.provider">Property</a>&lt;java.lang.String&gt;&nbsp;getArchiveBaseName()</pre>
<div class="block">Returns the base name of the archive.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the base name. Internal property may be null.</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>5.1</dd>
</dl>
</li>
</ul>
<a name="getArchiveAppendix--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getArchiveAppendix</h4>
<pre class="methodSignature"><a href="../Internal.html" title="annotation in org.gradle.api.tasks">@Internal</a>("Represented as part of archiveFile")
public&nbsp;<a href="../../provider/Property.html" title="interface in org.gradle.api.provider">Property</a>&lt;java.lang.String&gt;&nbsp;getArchiveAppendix()</pre>
<div class="block">Returns the appendix part of the archive name, if any.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the appendix. May be null</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>5.1</dd>
</dl>
</li>
</ul>
<a name="getArchiveVersion--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getArchiveVersion</h4>
<pre class="methodSignature"><a href="../Internal.html" title="annotation in org.gradle.api.tasks">@Internal</a>("Represented as part of archiveFile")
public&nbsp;<a href="../../provider/Property.html" title="interface in org.gradle.api.provider">Property</a>&lt;java.lang.String&gt;&nbsp;getArchiveVersion()</pre>
<div class="block">Returns the version part of the archive name.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the version. Internal property may be null.</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>5.1</dd>
</dl>
</li>
</ul>
<a name="getArchiveExtension--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getArchiveExtension</h4>
<pre class="methodSignature"><a href="../Internal.html" title="annotation in org.gradle.api.tasks">@Internal</a>("Represented as part of archiveFile")
public&nbsp;<a href="../../provider/Property.html" title="interface in org.gradle.api.provider">Property</a>&lt;java.lang.String&gt;&nbsp;getArchiveExtension()</pre>
<div class="block">Returns the extension part of the archive name.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>5.1</dd>
</dl>
</li>
</ul>
<a name="getArchiveClassifier--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getArchiveClassifier</h4>
<pre class="methodSignature"><a href="../Internal.html" title="annotation in org.gradle.api.tasks">@Internal</a>("Represented as part of archiveFile")
public&nbsp;<a href="../../provider/Property.html" title="interface in org.gradle.api.provider">Property</a>&lt;java.lang.String&gt;&nbsp;getArchiveClassifier()</pre>
<div class="block">Returns the classifier part of the archive name, if any.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The classifier. Internal property may be null.</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>5.1</dd>
</dl>
</li>
</ul>
<a name="into-java.lang.Object-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>into</h4>
<pre class="methodSignature">public&nbsp;<a href="AbstractArchiveTask.html" title="class in org.gradle.api.tasks.bundling">AbstractArchiveTask</a>&nbsp;into&#8203;(java.lang.Object&nbsp;destPath)</pre>
<div class="block">Specifies the destination directory *inside* the archive for the files.
 The destination is evaluated as per <a href="../../Project.html#file-java.lang.Object-"><code>Project.file(Object)</code></a>.
 Don't mix it up with <a href="#getDestinationDirectory--"><code>getDestinationDirectory()</code></a> which specifies the output directory for the archive.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../file/CopyProcessingSpec.html#into-java.lang.Object-">into</a></code>&nbsp;in interface&nbsp;<code><a href="../../file/CopyProcessingSpec.html" title="interface in org.gradle.api.file">CopyProcessingSpec</a></code></dd>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../file/CopySpec.html#into-java.lang.Object-">into</a></code>&nbsp;in interface&nbsp;<code><a href="../../file/CopySpec.html" title="interface in org.gradle.api.file">CopySpec</a></code></dd>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code><a href="../AbstractCopyTask.html#into-java.lang.Object-">into</a></code>&nbsp;in class&nbsp;<code><a href="../AbstractCopyTask.html" title="class in org.gradle.api.tasks">AbstractCopyTask</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>destPath</code> - destination directory *inside* the archive for the files</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>this</dd>
</dl>
</li>
</ul>
<a name="into-java.lang.Object-groovy.lang.Closure-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>into</h4>
<pre class="methodSignature">public&nbsp;<a href="AbstractArchiveTask.html" title="class in org.gradle.api.tasks.bundling">AbstractArchiveTask</a>&nbsp;into&#8203;(java.lang.Object&nbsp;destPath,
                                <a href="https://docs.groovy-lang.org/docs/groovy-3.0.17/html/gapi/groovy/lang/Closure.html?is-external=true" title="class or interface in groovy.lang" class="externalLink">Closure</a>&nbsp;configureClosure)</pre>
<div class="block">Creates and configures a child <code>CopySpec</code> with a destination directory *inside* the archive for the files.
 The destination is evaluated as per <a href="../../Project.html#file-java.lang.Object-"><code>Project.file(Object)</code></a>.
 Don't mix it up with <a href="#getDestinationDirectory--"><code>getDestinationDirectory()</code></a> which specifies the output directory for the archive.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../file/CopySpec.html#into-java.lang.Object-groovy.lang.Closure-">into</a></code>&nbsp;in interface&nbsp;<code><a href="../../file/CopySpec.html" title="interface in org.gradle.api.file">CopySpec</a></code></dd>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code><a href="../AbstractCopyTask.html#into-java.lang.Object-groovy.lang.Closure-">into</a></code>&nbsp;in class&nbsp;<code><a href="../AbstractCopyTask.html" title="class in org.gradle.api.tasks">AbstractCopyTask</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>destPath</code> - destination directory *inside* the archive for the files</dd>
<dd><code>configureClosure</code> - The closure to use to configure the child <code>CopySpec</code>.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>this</dd>
</dl>
</li>
</ul>
<a name="into-java.lang.Object-org.gradle.api.Action-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>into</h4>
<pre class="methodSignature">public&nbsp;<a href="../../file/CopySpec.html" title="interface in org.gradle.api.file">CopySpec</a>&nbsp;into&#8203;(java.lang.Object&nbsp;destPath,
                     <a href="../../Action.html" title="interface in org.gradle.api">Action</a>&lt;? super <a href="../../file/CopySpec.html" title="interface in org.gradle.api.file">CopySpec</a>&gt;&nbsp;copySpec)</pre>
<div class="block">Creates and configures a child <code>CopySpec</code> with a destination directory *inside* the archive for the files.
 The destination is evaluated as per <a href="../../Project.html#file-java.lang.Object-"><code>Project.file(Object)</code></a>.
 Don't mix it up with <a href="#getDestinationDirectory--"><code>getDestinationDirectory()</code></a> which specifies the output directory for the archive.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../file/CopySpec.html#into-java.lang.Object-org.gradle.api.Action-">into</a></code>&nbsp;in interface&nbsp;<code><a href="../../file/CopySpec.html" title="interface in org.gradle.api.file">CopySpec</a></code></dd>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code><a href="../AbstractCopyTask.html#into-java.lang.Object-org.gradle.api.Action-">into</a></code>&nbsp;in class&nbsp;<code><a href="../AbstractCopyTask.html" title="class in org.gradle.api.tasks">AbstractCopyTask</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>destPath</code> - destination directory *inside* the archive for the files</dd>
<dd><code>copySpec</code> - The closure to use to configure the child <code>CopySpec</code>.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>this</dd>
</dl>
</li>
</ul>
<a name="isPreserveFileTimestamps--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isPreserveFileTimestamps</h4>
<pre class="methodSignature"><a href="../Input.html" title="annotation in org.gradle.api.tasks">@Input</a>
public&nbsp;boolean&nbsp;isPreserveFileTimestamps()</pre>
<div class="block">Specifies whether file timestamps should be preserved in the archive.
 <p>
 If <code>false</code> this ensures that archive entries have the same time for builds between different machines, Java versions and operating systems.
 </p></div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd><code>true</code> if file timestamps should be preserved for archive entries</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>3.4</dd>
</dl>
</li>
</ul>
<a name="setPreserveFileTimestamps-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setPreserveFileTimestamps</h4>
<pre class="methodSignature">public&nbsp;void&nbsp;setPreserveFileTimestamps&#8203;(boolean&nbsp;preserveFileTimestamps)</pre>
<div class="block">Specifies whether file timestamps should be preserved in the archive.
 <p>
 If <code>false</code> this ensures that archive entries have the same time for builds between different machines, Java versions and operating systems.
 </p></div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>preserveFileTimestamps</code> - <code>true</code> if file timestamps should be preserved for archive entries</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>3.4</dd>
</dl>
</li>
</ul>
<a name="isReproducibleFileOrder--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isReproducibleFileOrder</h4>
<pre class="methodSignature"><a href="../Input.html" title="annotation in org.gradle.api.tasks">@Input</a>
public&nbsp;boolean&nbsp;isReproducibleFileOrder()</pre>
<div class="block">Specifies whether to enforce a reproducible file order when reading files from directories.
 <p>
 Gradle will then walk the directories on disk which are part of this archive in a reproducible order
 independent of file systems and operating systems.
 This helps Gradle reliably produce byte-for-byte reproducible archives.
 </p></div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd><code>true</code> if the files should read from disk in a reproducible order.</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>3.4</dd>
</dl>
</li>
</ul>
<a name="setReproducibleFileOrder-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setReproducibleFileOrder</h4>
<pre class="methodSignature">public&nbsp;void&nbsp;setReproducibleFileOrder&#8203;(boolean&nbsp;reproducibleFileOrder)</pre>
<div class="block">Specifies whether to enforce a reproducible file order when reading files from directories.
 <p>
 Gradle will then walk the directories on disk which are part of this archive in a reproducible order
 independent of file systems and operating systems.
 This helps Gradle reliably produce byte-for-byte reproducible archives.
 </p></div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>reproducibleFileOrder</code> - <code>true</code> if the files should read from disk in a reproducible order.</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>3.4</dd>
</dl>
</li>
</ul>
<a name="createCopyActionExecuter--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>createCopyActionExecuter</h4>
<pre class="methodSignature">protected&nbsp;org.gradle.api.internal.file.copy.CopyActionExecuter&nbsp;createCopyActionExecuter()</pre>
<dl>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code><a href="../AbstractCopyTask.html#createCopyActionExecuter--">createCopyActionExecuter</a></code>&nbsp;in class&nbsp;<code><a href="../AbstractCopyTask.html" title="class in org.gradle.api.tasks">AbstractCopyTask</a></code></dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../../allclasses.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li><a href="#nested.class.summary">Nested</a>&nbsp;|&nbsp;</li>
<li><a href="#field.summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
