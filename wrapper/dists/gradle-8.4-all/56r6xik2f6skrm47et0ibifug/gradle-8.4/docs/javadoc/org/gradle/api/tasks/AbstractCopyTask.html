<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc -->
<title>AbstractCopyTask (Gradle API 8.4)</title>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<link rel="stylesheet" type="text/css" href="../../../../javadoc.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../jquery/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
<script type="text/javascript" src="../../../../jquery/jszip/dist/jszip.min.js"></script>
<script type="text/javascript" src="../../../../jquery/jszip-utils/dist/jszip-utils.min.js"></script>
<!--[if IE]>
<script type="text/javascript" src="../../../../jquery/jszip-utils/dist/jszip-utils-ie.min.js"></script>
<![endif]-->
<script type="text/javascript" src="../../../../jquery/jquery-3.6.0.min.js"></script>
<script type="text/javascript" src="../../../../jquery/jquery-ui.min.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="AbstractCopyTask (Gradle API 8.4)";
        }
    }
    catch(err) {
    }
//-->
var data = {"i0":10,"i1":6,"i2":10,"i3":10,"i4":10,"i5":10,"i6":10,"i7":10,"i8":10,"i9":10,"i10":10,"i11":10,"i12":10,"i13":10,"i14":10,"i15":10,"i16":10,"i17":10,"i18":10,"i19":10,"i20":10,"i21":10,"i22":10,"i23":10,"i24":10,"i25":10,"i26":10,"i27":10,"i28":10,"i29":10,"i30":10,"i31":10,"i32":10,"i33":10,"i34":10,"i35":10,"i36":10,"i37":10,"i38":10,"i39":10,"i40":10,"i41":10,"i42":10,"i43":10,"i44":10,"i45":10,"i46":10,"i47":10,"i48":10,"i49":10,"i50":10,"i51":10,"i52":10,"i53":10,"i54":10,"i55":10,"i56":10,"i57":10,"i58":10,"i59":10,"i60":10,"i61":10,"i62":10,"i63":10,"i64":10};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],4:["t3","Abstract Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
var pathtoroot = "../../../../";
var useModuleDirectories = false;
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="fixedNav">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses.html">All&nbsp;Classes</a></li>
</ul>
<ul class="navListSearch">
<li><label for="search">SEARCH:</label>
<input type="text" id="search" value="search" disabled="disabled">
<input type="reset" id="reset" value="reset" disabled="disabled">
</li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li><a href="#nested.class.summary">Nested</a>&nbsp;|&nbsp;</li>
<li><a href="#field.summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
</div>
<div class="navPadding">&nbsp;</div>
<script type="text/javascript"><!--
$('.navPadding').css('padding-top', $('.fixedNav').css("height"));
//-->
</script>
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle"><span class="packageLabelInType">Package</span>&nbsp;<a href="package-summary.html">org.gradle.api.tasks</a></div>
<h2 title="Class AbstractCopyTask" class="title">Class AbstractCopyTask</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>org.gradle.api.internal.AbstractTask</li>
<li>
<ul class="inheritance">
<li><a href="../DefaultTask.html" title="class in org.gradle.api">org.gradle.api.DefaultTask</a></li>
<li>
<ul class="inheritance">
<li>org.gradle.api.internal.ConventionTask</li>
<li>
<ul class="inheritance">
<li>org.gradle.api.tasks.AbstractCopyTask</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Implemented Interfaces:</dt>
<dd><code>java.lang.Comparable&lt;<a href="../Task.html" title="interface in org.gradle.api">Task</a>&gt;</code>, <code><a href="../file/ContentFilterable.html" title="interface in org.gradle.api.file">ContentFilterable</a></code>, <code><a href="../file/CopyProcessingSpec.html" title="interface in org.gradle.api.file">CopyProcessingSpec</a></code>, <code><a href="../file/CopySourceSpec.html" title="interface in org.gradle.api.file">CopySourceSpec</a></code>, <code><a href="../file/CopySpec.html" title="interface in org.gradle.api.file">CopySpec</a></code>, <code>org.gradle.api.internal.DynamicObjectAware</code>, <code>org.gradle.api.internal.file.copy.CopySpecSource</code>, <code>org.gradle.api.internal.IConventionAware</code>, <code>org.gradle.api.internal.TaskInternal</code>, <code><a href="../plugins/ExtensionAware.html" title="interface in org.gradle.api.plugins">ExtensionAware</a></code>, <code><a href="../Task.html" title="interface in org.gradle.api">Task</a></code>, <code><a href="util/PatternFilterable.html" title="interface in org.gradle.api.tasks.util">PatternFilterable</a></code>, <code><a href="../../util/Configurable.html" title="interface in org.gradle.util">Configurable</a>&lt;<a href="../Task.html" title="interface in org.gradle.api">Task</a>&gt;</code></dd>
</dl>
<dl>
<dt>Direct Known Subclasses:</dt>
<dd><code><a href="bundling/AbstractArchiveTask.html" title="class in org.gradle.api.tasks.bundling">AbstractArchiveTask</a></code>, <code><a href="Copy.html" title="class in org.gradle.api.tasks">Copy</a></code>, <code><a href="Sync.html" title="class in org.gradle.api.tasks">Sync</a></code></dd>
</dl>
<hr>
<pre><a href="../NonNullApi.html" title="annotation in org.gradle.api">@NonNullApi</a>
<a href="../../work/DisableCachingByDefault.html" title="annotation in org.gradle.work">@DisableCachingByDefault</a>(<a href="../../work/DisableCachingByDefault.html#because--">because</a>="Abstract super-class, not to be instantiated directly")
public abstract class <span class="typeNameLabel">AbstractCopyTask</span>
extends org.gradle.api.internal.ConventionTask
implements <a href="../file/CopySpec.html" title="interface in org.gradle.api.file">CopySpec</a>, org.gradle.api.internal.file.copy.CopySpecSource</pre>
<div class="block"><code>AbstractCopyTask</code> is the base class for all copy tasks.</div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== NESTED CLASS SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="nested.class.summary">
<!--   -->
</a>
<h3>Nested Class Summary</h3>
<ul class="blockList">
<li class="blockList"><a name="nested.classes.inherited.from.class.org.gradle.api.Task">
<!--   -->
</a>
<h3>Nested classes/interfaces inherited from interface&nbsp;org.gradle.api.<a href="../Task.html" title="interface in org.gradle.api">Task</a></h3>
<code><a href="../Task.Namer.html" title="class in org.gradle.api">Task.Namer</a></code></li>
</ul>
</li>
</ul>
<!-- =========== FIELD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.summary">
<!--   -->
</a>
<h3>Field Summary</h3>
<ul class="blockList">
<li class="blockList"><a name="fields.inherited.from.class.org.gradle.api.Task">
<!--   -->
</a>
<h3>Fields inherited from interface&nbsp;org.gradle.api.<a href="../Task.html" title="interface in org.gradle.api">Task</a></h3>
<code><a href="../Task.html#TASK_ACTION">TASK_ACTION</a>, <a href="../Task.html#TASK_CONSTRUCTOR_ARGS">TASK_CONSTRUCTOR_ARGS</a>, <a href="../Task.html#TASK_DEPENDS_ON">TASK_DEPENDS_ON</a>, <a href="../Task.html#TASK_DESCRIPTION">TASK_DESCRIPTION</a>, <a href="../Task.html#TASK_GROUP">TASK_GROUP</a>, <a href="../Task.html#TASK_NAME">TASK_NAME</a>, <a href="../Task.html#TASK_OVERWRITE">TASK_OVERWRITE</a>, <a href="../Task.html#TASK_TYPE">TASK_TYPE</a></code></li>
</ul>
</li>
</ul>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="memberSummary" summary="Constructor Summary table, listing constructors, and an explanation">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier</th>
<th class="colSecond" scope="col">Constructor</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected </code></td>
<th class="colConstructorName" scope="row"><code><span class="memberNameLink"><a href="#AbstractCopyTask--">AbstractCopyTask</a></span>()</code></th>
<td class="colLast">&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t3" class="tableTab"><span><a href="javascript:show(4);">Abstract Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colSecond" scope="col">Method</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>protected void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#copy--">copy</a></span>()</code></th>
<td class="colLast">&nbsp;</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>protected abstract org.gradle.api.internal.file.copy.CopyAction</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#createCopyAction--">createCopyAction</a></span>()</code></th>
<td class="colLast">&nbsp;</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>protected org.gradle.api.internal.file.copy.CopyActionExecuter</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#createCopyActionExecuter--">createCopyActionExecuter</a></span>()</code></th>
<td class="colLast">&nbsp;</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>protected org.gradle.api.internal.file.copy.CopySpecInternal</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#createRootSpec--">createRootSpec</a></span>()</code></th>
<td class="colLast">&nbsp;</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code><a href="../file/CopyProcessingSpec.html" title="interface in org.gradle.api.file">CopyProcessingSpec</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#dirPermissions-org.gradle.api.Action-">dirPermissions</a></span>&#8203;(<a href="../Action.html" title="interface in org.gradle.api">Action</a>&lt;? super <a href="../file/ConfigurableFilePermissions.html" title="interface in org.gradle.api.file">ConfigurableFilePermissions</a>&gt;&nbsp;configureAction)</code></th>
<td class="colLast">
<div class="block">Configuration action for specifying directory access permissions.</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code><a href="AbstractCopyTask.html" title="class in org.gradle.api.tasks">AbstractCopyTask</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#eachFile-groovy.lang.Closure-">eachFile</a></span>&#8203;(<a href="https://docs.groovy-lang.org/docs/groovy-3.0.17/html/gapi/groovy/lang/Closure.html?is-external=true" title="class or interface in groovy.lang" class="externalLink">Closure</a>&nbsp;closure)</code></th>
<td class="colLast">
<div class="block">Adds an action to be applied to each file as it about to be copied into its destination.</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code><a href="AbstractCopyTask.html" title="class in org.gradle.api.tasks">AbstractCopyTask</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#eachFile-org.gradle.api.Action-">eachFile</a></span>&#8203;(<a href="../Action.html" title="interface in org.gradle.api">Action</a>&lt;? super <a href="../file/FileCopyDetails.html" title="interface in org.gradle.api.file">FileCopyDetails</a>&gt;&nbsp;action)</code></th>
<td class="colLast">
<div class="block">Adds an action to be applied to each file as it is about to be copied into its destination.</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code><a href="AbstractCopyTask.html" title="class in org.gradle.api.tasks">AbstractCopyTask</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#exclude-groovy.lang.Closure-">exclude</a></span>&#8203;(<a href="https://docs.groovy-lang.org/docs/groovy-3.0.17/html/gapi/groovy/lang/Closure.html?is-external=true" title="class or interface in groovy.lang" class="externalLink">Closure</a>&nbsp;excludeSpec)</code></th>
<td class="colLast">
<div class="block">Adds an exclude spec.</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code><a href="AbstractCopyTask.html" title="class in org.gradle.api.tasks">AbstractCopyTask</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#exclude-java.lang.Iterable-">exclude</a></span>&#8203;(java.lang.Iterable&lt;java.lang.String&gt;&nbsp;excludes)</code></th>
<td class="colLast">
<div class="block">Adds an ANT style exclude pattern.</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code><a href="AbstractCopyTask.html" title="class in org.gradle.api.tasks">AbstractCopyTask</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#exclude-java.lang.String...-">exclude</a></span>&#8203;(java.lang.String...&nbsp;excludes)</code></th>
<td class="colLast">
<div class="block">Adds an ANT style exclude pattern.</div>
</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code><a href="AbstractCopyTask.html" title="class in org.gradle.api.tasks">AbstractCopyTask</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#exclude-org.gradle.api.specs.Spec-">exclude</a></span>&#8203;(<a href="../specs/Spec.html" title="interface in org.gradle.api.specs">Spec</a>&lt;<a href="../file/FileTreeElement.html" title="interface in org.gradle.api.file">FileTreeElement</a>&gt;&nbsp;excludeSpec)</code></th>
<td class="colLast">
<div class="block">Adds an exclude spec.</div>
</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code><a href="AbstractCopyTask.html" title="class in org.gradle.api.tasks">AbstractCopyTask</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#expand-java.util.Map-">expand</a></span>&#8203;(java.util.Map&lt;java.lang.String,&#8203;?&gt;&nbsp;properties)</code></th>
<td class="colLast">
<div class="block">Expands property references in each file as it is copied.</div>
</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code><a href="AbstractCopyTask.html" title="class in org.gradle.api.tasks">AbstractCopyTask</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#expand-java.util.Map-org.gradle.api.Action-">expand</a></span>&#8203;(java.util.Map&lt;java.lang.String,&#8203;?&gt;&nbsp;properties,
      <a href="../Action.html" title="interface in org.gradle.api">Action</a>&lt;? super <a href="../file/ExpandDetails.html" title="interface in org.gradle.api.file">ExpandDetails</a>&gt;&nbsp;action)</code></th>
<td class="colLast">
<div class="block">Expands property references in each file as it is copied.</div>
</td>
</tr>
<tr id="i13" class="rowColor">
<td class="colFirst"><code><a href="../file/CopyProcessingSpec.html" title="interface in org.gradle.api.file">CopyProcessingSpec</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#filePermissions-org.gradle.api.Action-">filePermissions</a></span>&#8203;(<a href="../Action.html" title="interface in org.gradle.api">Action</a>&lt;? super <a href="../file/ConfigurableFilePermissions.html" title="interface in org.gradle.api.file">ConfigurableFilePermissions</a>&gt;&nbsp;configureAction)</code></th>
<td class="colLast">
<div class="block">Configuration action for specifying file access permissions.</div>
</td>
</tr>
<tr id="i14" class="altColor">
<td class="colFirst"><code><a href="AbstractCopyTask.html" title="class in org.gradle.api.tasks">AbstractCopyTask</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#filesMatching-java.lang.Iterable-org.gradle.api.Action-">filesMatching</a></span>&#8203;(java.lang.Iterable&lt;java.lang.String&gt;&nbsp;patterns,
             <a href="../Action.html" title="interface in org.gradle.api">Action</a>&lt;? super <a href="../file/FileCopyDetails.html" title="interface in org.gradle.api.file">FileCopyDetails</a>&gt;&nbsp;action)</code></th>
<td class="colLast">
<div class="block">Configure the <a href="../file/FileCopyDetails.html" title="interface in org.gradle.api.file"><code>FileCopyDetails</code></a> for each file whose path matches any of the specified Ant-style patterns.</div>
</td>
</tr>
<tr id="i15" class="rowColor">
<td class="colFirst"><code><a href="AbstractCopyTask.html" title="class in org.gradle.api.tasks">AbstractCopyTask</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#filesMatching-java.lang.String-org.gradle.api.Action-">filesMatching</a></span>&#8203;(java.lang.String&nbsp;pattern,
             <a href="../Action.html" title="interface in org.gradle.api">Action</a>&lt;? super <a href="../file/FileCopyDetails.html" title="interface in org.gradle.api.file">FileCopyDetails</a>&gt;&nbsp;action)</code></th>
<td class="colLast">
<div class="block">Configure the <a href="../file/FileCopyDetails.html" title="interface in org.gradle.api.file"><code>FileCopyDetails</code></a> for each file whose path matches the specified Ant-style pattern.</div>
</td>
</tr>
<tr id="i16" class="altColor">
<td class="colFirst"><code><a href="AbstractCopyTask.html" title="class in org.gradle.api.tasks">AbstractCopyTask</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#filesNotMatching-java.lang.Iterable-org.gradle.api.Action-">filesNotMatching</a></span>&#8203;(java.lang.Iterable&lt;java.lang.String&gt;&nbsp;patterns,
                <a href="../Action.html" title="interface in org.gradle.api">Action</a>&lt;? super <a href="../file/FileCopyDetails.html" title="interface in org.gradle.api.file">FileCopyDetails</a>&gt;&nbsp;action)</code></th>
<td class="colLast">
<div class="block">Configure the <a href="../file/FileCopyDetails.html" title="interface in org.gradle.api.file"><code>FileCopyDetails</code></a> for each file whose path does not match any of the specified
 Ant-style patterns.</div>
</td>
</tr>
<tr id="i17" class="rowColor">
<td class="colFirst"><code><a href="AbstractCopyTask.html" title="class in org.gradle.api.tasks">AbstractCopyTask</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#filesNotMatching-java.lang.String-org.gradle.api.Action-">filesNotMatching</a></span>&#8203;(java.lang.String&nbsp;pattern,
                <a href="../Action.html" title="interface in org.gradle.api">Action</a>&lt;? super <a href="../file/FileCopyDetails.html" title="interface in org.gradle.api.file">FileCopyDetails</a>&gt;&nbsp;action)</code></th>
<td class="colLast">
<div class="block">Configure the <a href="../file/FileCopyDetails.html" title="interface in org.gradle.api.file"><code>FileCopyDetails</code></a> for each file whose path does not match the specified
 Ant-style pattern.</div>
</td>
</tr>
<tr id="i18" class="altColor">
<td class="colFirst"><code><a href="AbstractCopyTask.html" title="class in org.gradle.api.tasks">AbstractCopyTask</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#filter-groovy.lang.Closure-">filter</a></span>&#8203;(<a href="https://docs.groovy-lang.org/docs/groovy-3.0.17/html/gapi/groovy/lang/Closure.html?is-external=true" title="class or interface in groovy.lang" class="externalLink">Closure</a>&nbsp;closure)</code></th>
<td class="colLast">
<div class="block">Adds a content filter based on the provided closure.</div>
</td>
</tr>
<tr id="i19" class="rowColor">
<td class="colFirst"><code><a href="AbstractCopyTask.html" title="class in org.gradle.api.tasks">AbstractCopyTask</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#filter-java.lang.Class-">filter</a></span>&#8203;(java.lang.Class&lt;? extends java.io.FilterReader&gt;&nbsp;filterType)</code></th>
<td class="colLast">
<div class="block">Adds a content filter to be used during the copy.</div>
</td>
</tr>
<tr id="i20" class="altColor">
<td class="colFirst"><code><a href="AbstractCopyTask.html" title="class in org.gradle.api.tasks">AbstractCopyTask</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#filter-java.util.Map-java.lang.Class-">filter</a></span>&#8203;(java.util.Map&lt;java.lang.String,&#8203;?&gt;&nbsp;properties,
      java.lang.Class&lt;? extends java.io.FilterReader&gt;&nbsp;filterType)</code></th>
<td class="colLast">
<div class="block">Adds a content filter to be used during the copy.</div>
</td>
</tr>
<tr id="i21" class="rowColor">
<td class="colFirst"><code><a href="AbstractCopyTask.html" title="class in org.gradle.api.tasks">AbstractCopyTask</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#filter-org.gradle.api.Transformer-">filter</a></span>&#8203;(<a href="../Transformer.html" title="interface in org.gradle.api">Transformer</a>&lt;java.lang.String,&#8203;java.lang.String&gt;&nbsp;transformer)</code></th>
<td class="colLast">
<div class="block">Adds a content filter based on the provided transformer.</div>
</td>
</tr>
<tr id="i22" class="altColor">
<td class="colFirst"><code><a href="AbstractCopyTask.html" title="class in org.gradle.api.tasks">AbstractCopyTask</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#from-java.lang.Object...-">from</a></span>&#8203;(java.lang.Object...&nbsp;sourcePaths)</code></th>
<td class="colLast">
<div class="block">Specifies source files or directories for a copy.</div>
</td>
</tr>
<tr id="i23" class="rowColor">
<td class="colFirst"><code><a href="AbstractCopyTask.html" title="class in org.gradle.api.tasks">AbstractCopyTask</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#from-java.lang.Object-groovy.lang.Closure-">from</a></span>&#8203;(java.lang.Object&nbsp;sourcePath,
    <a href="https://docs.groovy-lang.org/docs/groovy-3.0.17/html/gapi/groovy/lang/Closure.html?is-external=true" title="class or interface in groovy.lang" class="externalLink">Closure</a>&nbsp;c)</code></th>
<td class="colLast">
<div class="block">Specifies the source files or directories for a copy and creates a child <code>CopySourceSpec</code>.</div>
</td>
</tr>
<tr id="i24" class="altColor">
<td class="colFirst"><code><a href="AbstractCopyTask.html" title="class in org.gradle.api.tasks">AbstractCopyTask</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#from-java.lang.Object-org.gradle.api.Action-">from</a></span>&#8203;(java.lang.Object&nbsp;sourcePath,
    <a href="../Action.html" title="interface in org.gradle.api">Action</a>&lt;? super <a href="../file/CopySpec.html" title="interface in org.gradle.api.file">CopySpec</a>&gt;&nbsp;configureAction)</code></th>
<td class="colLast">
<div class="block">Specifies the source files or directories for a copy and creates a child <code>CopySpec</code>.</div>
</td>
</tr>
<tr id="i25" class="rowColor">
<td class="colFirst"><code>protected org.gradle.api.internal.file.collections.DirectoryFileTreeFactory</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getDirectoryFileTreeFactory--">getDirectoryFileTreeFactory</a></span>()</code></th>
<td class="colLast">&nbsp;</td>
</tr>
<tr id="i26" class="altColor">
<td class="colFirst"><code>java.lang.Integer</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getDirMode--">getDirMode</a></span>()</code></th>
<td class="colLast">
<div class="block">Returns the Unix permissions to use for the target directories.</div>
</td>
</tr>
<tr id="i27" class="rowColor">
<td class="colFirst"><code><a href="../provider/Property.html" title="interface in org.gradle.api.provider">Property</a>&lt;<a href="../file/ConfigurableFilePermissions.html" title="interface in org.gradle.api.file">ConfigurableFilePermissions</a>&gt;</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getDirPermissions--">getDirPermissions</a></span>()</code></th>
<td class="colLast">
<div class="block">Property for configuring directory access permissions.</div>
</td>
</tr>
<tr id="i28" class="altColor">
<td class="colFirst"><code>protected org.gradle.api.internal.DocumentationRegistry</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getDocumentationRegistry--">getDocumentationRegistry</a></span>()</code></th>
<td class="colLast">&nbsp;</td>
</tr>
<tr id="i29" class="rowColor">
<td class="colFirst"><code><a href="../file/DuplicatesStrategy.html" title="enum in org.gradle.api.file">DuplicatesStrategy</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getDuplicatesStrategy--">getDuplicatesStrategy</a></span>()</code></th>
<td class="colLast">
<div class="block">Returns the strategy to use when trying to copy more than one file to the same destination.</div>
</td>
</tr>
<tr id="i30" class="altColor">
<td class="colFirst"><code>java.util.Set&lt;java.lang.String&gt;</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getExcludes--">getExcludes</a></span>()</code></th>
<td class="colLast">
<div class="block">Returns the set of exclude patterns.</div>
</td>
</tr>
<tr id="i31" class="rowColor">
<td class="colFirst"><code>protected org.gradle.api.internal.file.FileLookup</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getFileLookup--">getFileLookup</a></span>()</code></th>
<td class="colLast">&nbsp;</td>
</tr>
<tr id="i32" class="altColor">
<td class="colFirst"><code>java.lang.Integer</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getFileMode--">getFileMode</a></span>()</code></th>
<td class="colLast">
<div class="block">Returns the Unix permissions to use for the target files.</div>
</td>
</tr>
<tr id="i33" class="rowColor">
<td class="colFirst"><code><a href="../provider/Property.html" title="interface in org.gradle.api.provider">Property</a>&lt;<a href="../file/ConfigurableFilePermissions.html" title="interface in org.gradle.api.file">ConfigurableFilePermissions</a>&gt;</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getFilePermissions--">getFilePermissions</a></span>()</code></th>
<td class="colLast">
<div class="block">Property for configuring file access permissions.</div>
</td>
</tr>
<tr id="i34" class="altColor">
<td class="colFirst"><code>protected org.gradle.api.internal.file.FileResolver</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getFileResolver--">getFileResolver</a></span>()</code></th>
<td class="colLast">&nbsp;</td>
</tr>
<tr id="i35" class="rowColor">
<td class="colFirst"><code>protected org.gradle.internal.nativeintegration.filesystem.FileSystem</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getFileSystem--">getFileSystem</a></span>()</code></th>
<td class="colLast">&nbsp;</td>
</tr>
<tr id="i36" class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getFilteringCharset--">getFilteringCharset</a></span>()</code></th>
<td class="colLast">
<div class="block">Gets the charset used to read and write files when filtering.</div>
</td>
</tr>
<tr id="i37" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getIncludeEmptyDirs--">getIncludeEmptyDirs</a></span>()</code></th>
<td class="colLast">
<div class="block">Tells if empty target directories will be included in the copy.</div>
</td>
</tr>
<tr id="i38" class="altColor">
<td class="colFirst"><code>java.util.Set&lt;java.lang.String&gt;</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getIncludes--">getIncludes</a></span>()</code></th>
<td class="colLast">
<div class="block">Returns the set of include patterns.</div>
</td>
</tr>
<tr id="i39" class="rowColor">
<td class="colFirst"><code>protected org.gradle.internal.reflect.Instantiator</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getInstantiator--">getInstantiator</a></span>()</code></th>
<td class="colLast">&nbsp;</td>
</tr>
<tr id="i40" class="altColor">
<td class="colFirst"><code>protected org.gradle.api.internal.file.copy.CopySpecInternal</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getMainSpec--">getMainSpec</a></span>()</code></th>
<td class="colLast">&nbsp;</td>
</tr>
<tr id="i41" class="rowColor">
<td class="colFirst"><code>protected <a href="../model/ObjectFactory.html" title="interface in org.gradle.api.model">ObjectFactory</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getObjectFactory--">getObjectFactory</a></span>()</code></th>
<td class="colLast">&nbsp;</td>
</tr>
<tr id="i42" class="altColor">
<td class="colFirst"><code>org.gradle.api.internal.file.copy.CopySpecInternal</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getRootSpec--">getRootSpec</a></span>()</code></th>
<td class="colLast">&nbsp;</td>
</tr>
<tr id="i43" class="rowColor">
<td class="colFirst"><code><a href="../file/FileCollection.html" title="interface in org.gradle.api.file">FileCollection</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getSource--">getSource</a></span>()</code></th>
<td class="colLast">
<div class="block">Returns the source files for this task.</div>
</td>
</tr>
<tr id="i44" class="altColor">
<td class="colFirst"><code><a href="AbstractCopyTask.html" title="class in org.gradle.api.tasks">AbstractCopyTask</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#include-groovy.lang.Closure-">include</a></span>&#8203;(<a href="https://docs.groovy-lang.org/docs/groovy-3.0.17/html/gapi/groovy/lang/Closure.html?is-external=true" title="class or interface in groovy.lang" class="externalLink">Closure</a>&nbsp;includeSpec)</code></th>
<td class="colLast">
<div class="block">Adds an include spec.</div>
</td>
</tr>
<tr id="i45" class="rowColor">
<td class="colFirst"><code><a href="AbstractCopyTask.html" title="class in org.gradle.api.tasks">AbstractCopyTask</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#include-java.lang.Iterable-">include</a></span>&#8203;(java.lang.Iterable&lt;java.lang.String&gt;&nbsp;includes)</code></th>
<td class="colLast">
<div class="block">Adds an ANT style include pattern.</div>
</td>
</tr>
<tr id="i46" class="altColor">
<td class="colFirst"><code><a href="AbstractCopyTask.html" title="class in org.gradle.api.tasks">AbstractCopyTask</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#include-java.lang.String...-">include</a></span>&#8203;(java.lang.String...&nbsp;includes)</code></th>
<td class="colLast">
<div class="block">Adds an ANT style include pattern.</div>
</td>
</tr>
<tr id="i47" class="rowColor">
<td class="colFirst"><code><a href="AbstractCopyTask.html" title="class in org.gradle.api.tasks">AbstractCopyTask</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#include-org.gradle.api.specs.Spec-">include</a></span>&#8203;(<a href="../specs/Spec.html" title="interface in org.gradle.api.specs">Spec</a>&lt;<a href="../file/FileTreeElement.html" title="interface in org.gradle.api.file">FileTreeElement</a>&gt;&nbsp;includeSpec)</code></th>
<td class="colLast">
<div class="block">Adds an include spec.</div>
</td>
</tr>
<tr id="i48" class="altColor">
<td class="colFirst"><code><a href="AbstractCopyTask.html" title="class in org.gradle.api.tasks">AbstractCopyTask</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#into-java.lang.Object-">into</a></span>&#8203;(java.lang.Object&nbsp;destDir)</code></th>
<td class="colLast">
<div class="block">Specifies the destination directory for a copy.</div>
</td>
</tr>
<tr id="i49" class="rowColor">
<td class="colFirst"><code><a href="AbstractCopyTask.html" title="class in org.gradle.api.tasks">AbstractCopyTask</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#into-java.lang.Object-groovy.lang.Closure-">into</a></span>&#8203;(java.lang.Object&nbsp;destPath,
    <a href="https://docs.groovy-lang.org/docs/groovy-3.0.17/html/gapi/groovy/lang/Closure.html?is-external=true" title="class or interface in groovy.lang" class="externalLink">Closure</a>&nbsp;configureClosure)</code></th>
<td class="colLast">
<div class="block">Creates and configures a child <code>CopySpec</code> with the given destination path.</div>
</td>
</tr>
<tr id="i50" class="altColor">
<td class="colFirst"><code><a href="../file/CopySpec.html" title="interface in org.gradle.api.file">CopySpec</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#into-java.lang.Object-org.gradle.api.Action-">into</a></span>&#8203;(java.lang.Object&nbsp;destPath,
    <a href="../Action.html" title="interface in org.gradle.api">Action</a>&lt;? super <a href="../file/CopySpec.html" title="interface in org.gradle.api.file">CopySpec</a>&gt;&nbsp;copySpec)</code></th>
<td class="colLast">
<div class="block">Creates and configures a child <code>CopySpec</code> with the given destination path.</div>
</td>
</tr>
<tr id="i51" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#isCaseSensitive--">isCaseSensitive</a></span>()</code></th>
<td class="colLast">
<div class="block">Specifies whether case-sensitive pattern matching should be used.</div>
</td>
</tr>
<tr id="i52" class="altColor">
<td class="colFirst"><code><a href="AbstractCopyTask.html" title="class in org.gradle.api.tasks">AbstractCopyTask</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#rename-groovy.lang.Closure-">rename</a></span>&#8203;(<a href="https://docs.groovy-lang.org/docs/groovy-3.0.17/html/gapi/groovy/lang/Closure.html?is-external=true" title="class or interface in groovy.lang" class="externalLink">Closure</a>&nbsp;closure)</code></th>
<td class="colLast">
<div class="block">Renames a source file.</div>
</td>
</tr>
<tr id="i53" class="rowColor">
<td class="colFirst"><code><a href="AbstractCopyTask.html" title="class in org.gradle.api.tasks">AbstractCopyTask</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#rename-java.lang.String-java.lang.String-">rename</a></span>&#8203;(java.lang.String&nbsp;sourceRegEx,
      java.lang.String&nbsp;replaceWith)</code></th>
<td class="colLast">
<div class="block">Renames files based on a regular expression.</div>
</td>
</tr>
<tr id="i54" class="altColor">
<td class="colFirst"><code><a href="AbstractCopyTask.html" title="class in org.gradle.api.tasks">AbstractCopyTask</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#rename-java.util.regex.Pattern-java.lang.String-">rename</a></span>&#8203;(java.util.regex.Pattern&nbsp;sourceRegEx,
      java.lang.String&nbsp;replaceWith)</code></th>
<td class="colLast">
<div class="block">Renames files based on a regular expression.</div>
</td>
</tr>
<tr id="i55" class="rowColor">
<td class="colFirst"><code><a href="AbstractCopyTask.html" title="class in org.gradle.api.tasks">AbstractCopyTask</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#rename-org.gradle.api.Transformer-">rename</a></span>&#8203;(<a href="../Transformer.html" title="interface in org.gradle.api">Transformer</a>&lt;java.lang.String,&#8203;java.lang.String&gt;&nbsp;renamer)</code></th>
<td class="colLast">
<div class="block">Renames a source file.</div>
</td>
</tr>
<tr id="i56" class="altColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setCaseSensitive-boolean-">setCaseSensitive</a></span>&#8203;(boolean&nbsp;caseSensitive)</code></th>
<td class="colLast">
<div class="block">Specifies whether case-sensitive pattern matching should be used for this CopySpec.</div>
</td>
</tr>
<tr id="i57" class="rowColor">
<td class="colFirst"><code><a href="AbstractCopyTask.html" title="class in org.gradle.api.tasks">AbstractCopyTask</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setDirMode-java.lang.Integer-">setDirMode</a></span>&#8203;(java.lang.Integer&nbsp;mode)</code></th>
<td class="colLast">
<div class="block">Sets the Unix permissions to use for the target directories.</div>
</td>
</tr>
<tr id="i58" class="altColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setDuplicatesStrategy-org.gradle.api.file.DuplicatesStrategy-">setDuplicatesStrategy</a></span>&#8203;(<a href="../file/DuplicatesStrategy.html" title="enum in org.gradle.api.file">DuplicatesStrategy</a>&nbsp;strategy)</code></th>
<td class="colLast">
<div class="block">The strategy to use when trying to copy more than one file to the same destination.</div>
</td>
</tr>
<tr id="i59" class="rowColor">
<td class="colFirst"><code><a href="AbstractCopyTask.html" title="class in org.gradle.api.tasks">AbstractCopyTask</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setExcludes-java.lang.Iterable-">setExcludes</a></span>&#8203;(java.lang.Iterable&lt;java.lang.String&gt;&nbsp;excludes)</code></th>
<td class="colLast">
<div class="block">Set the allowable exclude patterns.</div>
</td>
</tr>
<tr id="i60" class="altColor">
<td class="colFirst"><code><a href="AbstractCopyTask.html" title="class in org.gradle.api.tasks">AbstractCopyTask</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setFileMode-java.lang.Integer-">setFileMode</a></span>&#8203;(java.lang.Integer&nbsp;mode)</code></th>
<td class="colLast">
<div class="block">Sets the Unix permissions to use for the target files.</div>
</td>
</tr>
<tr id="i61" class="rowColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setFilteringCharset-java.lang.String-">setFilteringCharset</a></span>&#8203;(java.lang.String&nbsp;charset)</code></th>
<td class="colLast">
<div class="block">Specifies the charset used to read and write files when filtering.</div>
</td>
</tr>
<tr id="i62" class="altColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setIncludeEmptyDirs-boolean-">setIncludeEmptyDirs</a></span>&#8203;(boolean&nbsp;includeEmptyDirs)</code></th>
<td class="colLast">
<div class="block">Controls if empty target directories should be included in the copy.</div>
</td>
</tr>
<tr id="i63" class="rowColor">
<td class="colFirst"><code><a href="AbstractCopyTask.html" title="class in org.gradle.api.tasks">AbstractCopyTask</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setIncludes-java.lang.Iterable-">setIncludes</a></span>&#8203;(java.lang.Iterable&lt;java.lang.String&gt;&nbsp;includes)</code></th>
<td class="colLast">
<div class="block">Set the allowable include patterns.</div>
</td>
</tr>
<tr id="i64" class="altColor">
<td class="colFirst"><code><a href="../file/CopySpec.html" title="interface in org.gradle.api.file">CopySpec</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#with-org.gradle.api.file.CopySpec...-">with</a></span>&#8203;(<a href="../file/CopySpec.html" title="interface in org.gradle.api.file">CopySpec</a>...&nbsp;sourceSpecs)</code></th>
<td class="colLast">
<div class="block">Adds the given specs as a child of this spec.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.org.gradle.api.internal.ConventionTask">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;org.gradle.api.internal.ConventionTask</h3>
<code>conventionMapping, conventionMapping, getConventionMapping</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.org.gradle.api.DefaultTask">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;org.gradle.api.<a href="../DefaultTask.html" title="class in org.gradle.api">DefaultTask</a></h3>
<code><a href="../DefaultTask.html#compareTo-org.gradle.api.Task-">compareTo</a>, <a href="../DefaultTask.html#configure-groovy.lang.Closure-">configure</a>, <a href="../DefaultTask.html#dependsOn-java.lang.Object...-">dependsOn</a>, <a href="../DefaultTask.html#doFirst-groovy.lang.Closure-">doFirst</a>, <a href="../DefaultTask.html#doFirst-java.lang.String-org.gradle.api.Action-">doFirst</a>, <a href="../DefaultTask.html#doFirst-org.gradle.api.Action-">doFirst</a>, <a href="../DefaultTask.html#doLast-groovy.lang.Closure-">doLast</a>, <a href="../DefaultTask.html#doLast-java.lang.String-org.gradle.api.Action-">doLast</a>, <a href="../DefaultTask.html#doLast-org.gradle.api.Action-">doLast</a>, <a href="../DefaultTask.html#finalizedBy-java.lang.Object...-">finalizedBy</a>, <a href="../DefaultTask.html#getActions--">getActions</a>, <a href="../DefaultTask.html#getAnt--">getAnt</a>, <a href="../DefaultTask.html#getDependsOn--">getDependsOn</a>, <a href="../DefaultTask.html#getDescription--">getDescription</a>, <a href="../DefaultTask.html#getDestroyables--">getDestroyables</a>, <a href="../DefaultTask.html#getDidWork--">getDidWork</a>, <a href="../DefaultTask.html#getEnabled--">getEnabled</a>, <a href="../DefaultTask.html#getExtensions--">getExtensions</a>, <a href="../DefaultTask.html#getFinalizedBy--">getFinalizedBy</a>, <a href="../DefaultTask.html#getGroup--">getGroup</a>, <a href="../DefaultTask.html#getInputs--">getInputs</a>, <a href="../DefaultTask.html#getLocalState--">getLocalState</a>, <a href="../DefaultTask.html#getLogger--">getLogger</a>, <a href="../DefaultTask.html#getLogging--">getLogging</a>, <a href="../DefaultTask.html#getMustRunAfter--">getMustRunAfter</a>, <a href="../DefaultTask.html#getName--">getName</a>, <a href="../DefaultTask.html#getOutputs--">getOutputs</a>, <a href="../DefaultTask.html#getPath--">getPath</a>, <a href="../DefaultTask.html#getProject--">getProject</a>, <a href="../DefaultTask.html#getShouldRunAfter--">getShouldRunAfter</a>, <a href="../DefaultTask.html#getState--">getState</a>, <a href="../DefaultTask.html#getTaskDependencies--">getTaskDependencies</a>, <a href="../DefaultTask.html#getTemporaryDir--">getTemporaryDir</a>, <a href="../DefaultTask.html#getTimeout--">getTimeout</a>, <a href="../DefaultTask.html#hasProperty-java.lang.String-">hasProperty</a>, <a href="../DefaultTask.html#mustRunAfter-java.lang.Object...-">mustRunAfter</a>, <a href="../DefaultTask.html#onlyIf-groovy.lang.Closure-">onlyIf</a>, <a href="../DefaultTask.html#onlyIf-java.lang.String-org.gradle.api.specs.Spec-">onlyIf</a>, <a href="../DefaultTask.html#onlyIf-org.gradle.api.specs.Spec-">onlyIf</a>, <a href="../DefaultTask.html#property-java.lang.String-">property</a>, <a href="../DefaultTask.html#setActions-java.util.List-">setActions</a>, <a href="../DefaultTask.html#setDependsOn-java.lang.Iterable-">setDependsOn</a>, <a href="../DefaultTask.html#setDescription-java.lang.String-">setDescription</a>, <a href="../DefaultTask.html#setDidWork-boolean-">setDidWork</a>, <a href="../DefaultTask.html#setEnabled-boolean-">setEnabled</a>, <a href="../DefaultTask.html#setFinalizedBy-java.lang.Iterable-">setFinalizedBy</a>, <a href="../DefaultTask.html#setGroup-java.lang.String-">setGroup</a>, <a href="../DefaultTask.html#setMustRunAfter-java.lang.Iterable-">setMustRunAfter</a>, <a href="../DefaultTask.html#setOnlyIf-groovy.lang.Closure-">setOnlyIf</a>, <a href="../DefaultTask.html#setOnlyIf-java.lang.String-org.gradle.api.specs.Spec-">setOnlyIf</a>, <a href="../DefaultTask.html#setOnlyIf-org.gradle.api.specs.Spec-">setOnlyIf</a>, <a href="../DefaultTask.html#setProperty-java.lang.String-java.lang.Object-">setProperty</a>, <a href="../DefaultTask.html#setShouldRunAfter-java.lang.Iterable-">setShouldRunAfter</a>, <a href="../DefaultTask.html#shouldRunAfter-java.lang.Object...-">shouldRunAfter</a>, <a href="../DefaultTask.html#usesService-org.gradle.api.provider.Provider-">usesService</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.org.gradle.api.internal.AbstractTask">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;org.gradle.api.internal.AbstractTask</h3>
<code>acceptServiceReferences, appendParallelSafeAction, doNotTrackState, getAsDynamicObject, getConvention, getIdentityPath, getImpliesSubProjects, getLifecycleDependencies, getOnlyIf, getReasonNotToTrackState, getReasonTaskIsIncompatibleWithConfigurationCache, getRequiredServices, getServices, getSharedResources, getStandardOutputCapture, getTaskActions, getTaskIdentity, getTemporaryDirFactory, hasTaskActions, injectIntoNewInstance, isCompatibleWithConfigurationCache, isEnabled, isHasCustomActions, notCompatibleWithConfigurationCache, prependParallelSafeAction, setImpliesSubProjects</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>clone, equals, finalize, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.org.gradle.api.Task">
<!--   -->
</a>
<h3>Methods inherited from interface&nbsp;org.gradle.api.<a href="../Task.html" title="interface in org.gradle.api">Task</a></h3>
<code><a href="../Task.html#doNotTrackState-java.lang.String-">doNotTrackState</a>, <a href="../Task.html#getConvention--">getConvention</a>, <a href="../Task.html#notCompatibleWithConfigurationCache-java.lang.String-">notCompatibleWithConfigurationCache</a></code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a name="AbstractCopyTask--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>AbstractCopyTask</h4>
<pre>protected&nbsp;AbstractCopyTask()</pre>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="createRootSpec--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createRootSpec</h4>
<pre class="methodSignature">protected&nbsp;org.gradle.api.internal.file.copy.CopySpecInternal&nbsp;createRootSpec()</pre>
</li>
</ul>
<a name="createCopyAction--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createCopyAction</h4>
<pre class="methodSignature">protected abstract&nbsp;org.gradle.api.internal.file.copy.CopyAction&nbsp;createCopyAction()</pre>
</li>
</ul>
<a name="getInstantiator--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getInstantiator</h4>
<pre class="methodSignature">@Inject
protected&nbsp;org.gradle.internal.reflect.Instantiator&nbsp;getInstantiator()</pre>
</li>
</ul>
<a name="getFileSystem--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getFileSystem</h4>
<pre class="methodSignature">@Inject
protected&nbsp;org.gradle.internal.nativeintegration.filesystem.FileSystem&nbsp;getFileSystem()</pre>
</li>
</ul>
<a name="getFileResolver--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getFileResolver</h4>
<pre class="methodSignature">@Inject
protected&nbsp;org.gradle.api.internal.file.FileResolver&nbsp;getFileResolver()</pre>
</li>
</ul>
<a name="getFileLookup--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getFileLookup</h4>
<pre class="methodSignature">@Inject
protected&nbsp;org.gradle.api.internal.file.FileLookup&nbsp;getFileLookup()</pre>
</li>
</ul>
<a name="getDirectoryFileTreeFactory--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getDirectoryFileTreeFactory</h4>
<pre class="methodSignature">@Inject
protected&nbsp;org.gradle.api.internal.file.collections.DirectoryFileTreeFactory&nbsp;getDirectoryFileTreeFactory()</pre>
</li>
</ul>
<a name="getDocumentationRegistry--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getDocumentationRegistry</h4>
<pre class="methodSignature">@Inject
protected&nbsp;org.gradle.api.internal.DocumentationRegistry&nbsp;getDocumentationRegistry()</pre>
</li>
</ul>
<a name="getObjectFactory--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getObjectFactory</h4>
<pre class="methodSignature">@Inject
protected&nbsp;<a href="../model/ObjectFactory.html" title="interface in org.gradle.api.model">ObjectFactory</a>&nbsp;getObjectFactory()</pre>
</li>
</ul>
<a name="copy--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>copy</h4>
<pre class="methodSignature">protected&nbsp;void&nbsp;copy()</pre>
</li>
</ul>
<a name="createCopyActionExecuter--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createCopyActionExecuter</h4>
<pre class="methodSignature">protected&nbsp;org.gradle.api.internal.file.copy.CopyActionExecuter&nbsp;createCopyActionExecuter()</pre>
</li>
</ul>
<a name="getSource--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getSource</h4>
<pre class="methodSignature"><a href="Internal.html" title="annotation in org.gradle.api.tasks">@Internal</a>
public&nbsp;<a href="../file/FileCollection.html" title="interface in org.gradle.api.file">FileCollection</a>&nbsp;getSource()</pre>
<div class="block">Returns the source files for this task.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The source files. Never returns null.</dd>
</dl>
</li>
</ul>
<a name="getRootSpec--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getRootSpec</h4>
<pre class="methodSignature"><a href="Internal.html" title="annotation in org.gradle.api.tasks">@Internal</a>
public&nbsp;org.gradle.api.internal.file.copy.CopySpecInternal&nbsp;getRootSpec()</pre>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code>getRootSpec</code>&nbsp;in interface&nbsp;<code>org.gradle.api.internal.file.copy.CopySpecSource</code></dd>
</dl>
</li>
</ul>
<a name="getMainSpec--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getMainSpec</h4>
<pre class="methodSignature"><a href="Internal.html" title="annotation in org.gradle.api.tasks">@Internal</a>
protected&nbsp;org.gradle.api.internal.file.copy.CopySpecInternal&nbsp;getMainSpec()</pre>
</li>
</ul>
<a name="isCaseSensitive--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isCaseSensitive</h4>
<pre class="methodSignature"><a href="Internal.html" title="annotation in org.gradle.api.tasks">@Internal</a>
public&nbsp;boolean&nbsp;isCaseSensitive()</pre>
<div class="block">Specifies whether case-sensitive pattern matching should be used.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../file/CopySpec.html#isCaseSensitive--">isCaseSensitive</a></code>&nbsp;in interface&nbsp;<code><a href="../file/CopySpec.html" title="interface in org.gradle.api.file">CopySpec</a></code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>true for case-sensitive matching.</dd>
</dl>
</li>
</ul>
<a name="setCaseSensitive-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCaseSensitive</h4>
<pre class="methodSignature">public&nbsp;void&nbsp;setCaseSensitive&#8203;(boolean&nbsp;caseSensitive)</pre>
<div class="block">Specifies whether case-sensitive pattern matching should be used for this CopySpec.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../file/CopySpec.html#setCaseSensitive-boolean-">setCaseSensitive</a></code>&nbsp;in interface&nbsp;<code><a href="../file/CopySpec.html" title="interface in org.gradle.api.file">CopySpec</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>caseSensitive</code> - true for case-sensitive matching.</dd>
</dl>
</li>
</ul>
<a name="getIncludeEmptyDirs--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getIncludeEmptyDirs</h4>
<pre class="methodSignature"><a href="Internal.html" title="annotation in org.gradle.api.tasks">@Internal</a>
public&nbsp;boolean&nbsp;getIncludeEmptyDirs()</pre>
<div class="block">Tells if empty target directories will be included in the copy.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../file/CopySpec.html#getIncludeEmptyDirs--">getIncludeEmptyDirs</a></code>&nbsp;in interface&nbsp;<code><a href="../file/CopySpec.html" title="interface in org.gradle.api.file">CopySpec</a></code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd><code>true</code> if empty target directories will be included in the copy, <code>false</code> otherwise</dd>
</dl>
</li>
</ul>
<a name="setIncludeEmptyDirs-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setIncludeEmptyDirs</h4>
<pre class="methodSignature">public&nbsp;void&nbsp;setIncludeEmptyDirs&#8203;(boolean&nbsp;includeEmptyDirs)</pre>
<div class="block">Controls if empty target directories should be included in the copy.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../file/CopySpec.html#setIncludeEmptyDirs-boolean-">setIncludeEmptyDirs</a></code>&nbsp;in interface&nbsp;<code><a href="../file/CopySpec.html" title="interface in org.gradle.api.file">CopySpec</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>includeEmptyDirs</code> - <code>true</code> if empty target directories should be included in the copy, <code>false</code> otherwise</dd>
</dl>
</li>
</ul>
<a name="setDuplicatesStrategy-org.gradle.api.file.DuplicatesStrategy-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setDuplicatesStrategy</h4>
<pre class="methodSignature">public&nbsp;void&nbsp;setDuplicatesStrategy&#8203;(<a href="../file/DuplicatesStrategy.html" title="enum in org.gradle.api.file">DuplicatesStrategy</a>&nbsp;strategy)</pre>
<div class="block">The strategy to use when trying to copy more than one file to the same destination. Set to <a href="../file/DuplicatesStrategy.html#INHERIT"><code>DuplicatesStrategy.INHERIT</code></a>, the default strategy, to use
 the strategy inherited from the parent copy spec, if any, or <a href="../file/DuplicatesStrategy.html#INCLUDE"><code>DuplicatesStrategy.INCLUDE</code></a> if this copy spec has no parent.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../file/CopySpec.html#setDuplicatesStrategy-org.gradle.api.file.DuplicatesStrategy-">setDuplicatesStrategy</a></code>&nbsp;in interface&nbsp;<code><a href="../file/CopySpec.html" title="interface in org.gradle.api.file">CopySpec</a></code></dd>
</dl>
</li>
</ul>
<a name="getDuplicatesStrategy--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getDuplicatesStrategy</h4>
<pre class="methodSignature"><a href="Internal.html" title="annotation in org.gradle.api.tasks">@Internal</a>
public&nbsp;<a href="../file/DuplicatesStrategy.html" title="enum in org.gradle.api.file">DuplicatesStrategy</a>&nbsp;getDuplicatesStrategy()</pre>
<div class="block">Returns the strategy to use when trying to copy more than one file to the same destination.
 <p>
 The value can be set with a case insensitive string of the enum value (e.g. <code>'exclude'</code> for <a href="../file/DuplicatesStrategy.html#EXCLUDE"><code>DuplicatesStrategy.EXCLUDE</code></a>).
 <p>
 This strategy can be overridden for individual files by using <a href="../file/CopySpec.html#eachFile-org.gradle.api.Action-"><code>CopySpec.eachFile(org.gradle.api.Action)</code></a> or <a href="../file/CopySpec.html#filesMatching-java.lang.String-org.gradle.api.Action-"><code>CopySpec.filesMatching(String, org.gradle.api.Action)</code></a>.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../file/CopySpec.html#getDuplicatesStrategy--">getDuplicatesStrategy</a></code>&nbsp;in interface&nbsp;<code><a href="../file/CopySpec.html" title="interface in org.gradle.api.file">CopySpec</a></code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the strategy to use for files included by this copy spec.</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../file/DuplicatesStrategy.html" title="enum in org.gradle.api.file"><code>DuplicatesStrategy</code></a></dd>
</dl>
</li>
</ul>
<a name="from-java.lang.Object...-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>from</h4>
<pre class="methodSignature">public&nbsp;<a href="AbstractCopyTask.html" title="class in org.gradle.api.tasks">AbstractCopyTask</a>&nbsp;from&#8203;(java.lang.Object...&nbsp;sourcePaths)</pre>
<div class="block">Specifies source files or directories for a copy. The given paths are evaluated as per <a href="../Project.html#files-java.lang.Object...-"><code>Project.files(Object...)</code></a>.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../file/CopySourceSpec.html#from-java.lang.Object...-">from</a></code>&nbsp;in interface&nbsp;<code><a href="../file/CopySourceSpec.html" title="interface in org.gradle.api.file">CopySourceSpec</a></code></dd>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../file/CopySpec.html#from-java.lang.Object...-">from</a></code>&nbsp;in interface&nbsp;<code><a href="../file/CopySpec.html" title="interface in org.gradle.api.file">CopySpec</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>sourcePaths</code> - Paths to source files for the copy</dd>
</dl>
</li>
</ul>
<a name="filesMatching-java.lang.String-org.gradle.api.Action-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>filesMatching</h4>
<pre class="methodSignature">public&nbsp;<a href="AbstractCopyTask.html" title="class in org.gradle.api.tasks">AbstractCopyTask</a>&nbsp;filesMatching&#8203;(java.lang.String&nbsp;pattern,
                                      <a href="../Action.html" title="interface in org.gradle.api">Action</a>&lt;? super <a href="../file/FileCopyDetails.html" title="interface in org.gradle.api.file">FileCopyDetails</a>&gt;&nbsp;action)</pre>
<div class="block">Configure the <a href="../file/FileCopyDetails.html" title="interface in org.gradle.api.file"><code>FileCopyDetails</code></a> for each file whose path matches the specified Ant-style pattern.
 This is equivalent to using eachFile() and selectively applying a configuration based on the file's path.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../file/CopySpec.html#filesMatching-java.lang.String-org.gradle.api.Action-">filesMatching</a></code>&nbsp;in interface&nbsp;<code><a href="../file/CopySpec.html" title="interface in org.gradle.api.file">CopySpec</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>pattern</code> - Ant-style pattern used to match against files' relative paths</dd>
<dd><code>action</code> - action called for the FileCopyDetails of each file matching pattern</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>this</dd>
</dl>
</li>
</ul>
<a name="filesMatching-java.lang.Iterable-org.gradle.api.Action-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>filesMatching</h4>
<pre class="methodSignature">public&nbsp;<a href="AbstractCopyTask.html" title="class in org.gradle.api.tasks">AbstractCopyTask</a>&nbsp;filesMatching&#8203;(java.lang.Iterable&lt;java.lang.String&gt;&nbsp;patterns,
                                      <a href="../Action.html" title="interface in org.gradle.api">Action</a>&lt;? super <a href="../file/FileCopyDetails.html" title="interface in org.gradle.api.file">FileCopyDetails</a>&gt;&nbsp;action)</pre>
<div class="block">Configure the <a href="../file/FileCopyDetails.html" title="interface in org.gradle.api.file"><code>FileCopyDetails</code></a> for each file whose path matches any of the specified Ant-style patterns.
 This is equivalent to using eachFile() and selectively applying a configuration based on the file's path.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../file/CopySpec.html#filesMatching-java.lang.Iterable-org.gradle.api.Action-">filesMatching</a></code>&nbsp;in interface&nbsp;<code><a href="../file/CopySpec.html" title="interface in org.gradle.api.file">CopySpec</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>patterns</code> - Ant-style patterns used to match against files' relative paths</dd>
<dd><code>action</code> - action called for the FileCopyDetails of each file matching pattern</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>this</dd>
</dl>
</li>
</ul>
<a name="filesNotMatching-java.lang.String-org.gradle.api.Action-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>filesNotMatching</h4>
<pre class="methodSignature">public&nbsp;<a href="AbstractCopyTask.html" title="class in org.gradle.api.tasks">AbstractCopyTask</a>&nbsp;filesNotMatching&#8203;(java.lang.String&nbsp;pattern,
                                         <a href="../Action.html" title="interface in org.gradle.api">Action</a>&lt;? super <a href="../file/FileCopyDetails.html" title="interface in org.gradle.api.file">FileCopyDetails</a>&gt;&nbsp;action)</pre>
<div class="block">Configure the <a href="../file/FileCopyDetails.html" title="interface in org.gradle.api.file"><code>FileCopyDetails</code></a> for each file whose path does not match the specified
 Ant-style pattern. This is equivalent to using eachFile() and selectively applying a configuration based on the
 file's path.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../file/CopySpec.html#filesNotMatching-java.lang.String-org.gradle.api.Action-">filesNotMatching</a></code>&nbsp;in interface&nbsp;<code><a href="../file/CopySpec.html" title="interface in org.gradle.api.file">CopySpec</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>pattern</code> - Ant-style pattern used to match against files' relative paths</dd>
<dd><code>action</code> - action called for the FileCopyDetails of each file that does not match pattern</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>this</dd>
</dl>
</li>
</ul>
<a name="filesNotMatching-java.lang.Iterable-org.gradle.api.Action-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>filesNotMatching</h4>
<pre class="methodSignature">public&nbsp;<a href="AbstractCopyTask.html" title="class in org.gradle.api.tasks">AbstractCopyTask</a>&nbsp;filesNotMatching&#8203;(java.lang.Iterable&lt;java.lang.String&gt;&nbsp;patterns,
                                         <a href="../Action.html" title="interface in org.gradle.api">Action</a>&lt;? super <a href="../file/FileCopyDetails.html" title="interface in org.gradle.api.file">FileCopyDetails</a>&gt;&nbsp;action)</pre>
<div class="block">Configure the <a href="../file/FileCopyDetails.html" title="interface in org.gradle.api.file"><code>FileCopyDetails</code></a> for each file whose path does not match any of the specified
 Ant-style patterns. This is equivalent to using eachFile() and selectively applying a configuration based on the
 file's path.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../file/CopySpec.html#filesNotMatching-java.lang.Iterable-org.gradle.api.Action-">filesNotMatching</a></code>&nbsp;in interface&nbsp;<code><a href="../file/CopySpec.html" title="interface in org.gradle.api.file">CopySpec</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>patterns</code> - Ant-style patterns used to match against files' relative paths</dd>
<dd><code>action</code> - action called for the FileCopyDetails of each file that does not match any pattern</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>this</dd>
</dl>
</li>
</ul>
<a name="from-java.lang.Object-groovy.lang.Closure-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>from</h4>
<pre class="methodSignature">public&nbsp;<a href="AbstractCopyTask.html" title="class in org.gradle.api.tasks">AbstractCopyTask</a>&nbsp;from&#8203;(java.lang.Object&nbsp;sourcePath,
                             <a href="https://docs.groovy-lang.org/docs/groovy-3.0.17/html/gapi/groovy/lang/Closure.html?is-external=true" title="class or interface in groovy.lang" class="externalLink">Closure</a>&nbsp;c)</pre>
<div class="block">Specifies the source files or directories for a copy and creates a child <code>CopySourceSpec</code>. The given source
 path is evaluated as per <a href="../Project.html#files-java.lang.Object...-"><code>Project.files(Object...)</code></a> .</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../file/CopySourceSpec.html#from-java.lang.Object-groovy.lang.Closure-">from</a></code>&nbsp;in interface&nbsp;<code><a href="../file/CopySourceSpec.html" title="interface in org.gradle.api.file">CopySourceSpec</a></code></dd>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../file/CopySpec.html#from-java.lang.Object-groovy.lang.Closure-">from</a></code>&nbsp;in interface&nbsp;<code><a href="../file/CopySpec.html" title="interface in org.gradle.api.file">CopySpec</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>sourcePath</code> - Path to source for the copy</dd>
<dd><code>c</code> - closure for configuring the child CopySourceSpec</dd>
</dl>
</li>
</ul>
<a name="from-java.lang.Object-org.gradle.api.Action-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>from</h4>
<pre class="methodSignature">public&nbsp;<a href="AbstractCopyTask.html" title="class in org.gradle.api.tasks">AbstractCopyTask</a>&nbsp;from&#8203;(java.lang.Object&nbsp;sourcePath,
                             <a href="../Action.html" title="interface in org.gradle.api">Action</a>&lt;? super <a href="../file/CopySpec.html" title="interface in org.gradle.api.file">CopySpec</a>&gt;&nbsp;configureAction)</pre>
<div class="block">Specifies the source files or directories for a copy and creates a child <code>CopySpec</code>. The given source
 path is evaluated as per <a href="../Project.html#files-java.lang.Object...-"><code>Project.files(Object...)</code></a> .</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../file/CopySourceSpec.html#from-java.lang.Object-org.gradle.api.Action-">from</a></code>&nbsp;in interface&nbsp;<code><a href="../file/CopySourceSpec.html" title="interface in org.gradle.api.file">CopySourceSpec</a></code></dd>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../file/CopySpec.html#from-java.lang.Object-org.gradle.api.Action-">from</a></code>&nbsp;in interface&nbsp;<code><a href="../file/CopySpec.html" title="interface in org.gradle.api.file">CopySpec</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>sourcePath</code> - Path to source for the copy</dd>
<dd><code>configureAction</code> - action for configuring the child CopySpec</dd>
</dl>
</li>
</ul>
<a name="with-org.gradle.api.file.CopySpec...-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>with</h4>
<pre class="methodSignature">public&nbsp;<a href="../file/CopySpec.html" title="interface in org.gradle.api.file">CopySpec</a>&nbsp;with&#8203;(<a href="../file/CopySpec.html" title="interface in org.gradle.api.file">CopySpec</a>...&nbsp;sourceSpecs)</pre>
<div class="block">Adds the given specs as a child of this spec.

 <pre class='autoTested'>
 def contentSpec = copySpec {
   from("content") {
     include "**&#47;*.txt"
   }
 }

 task copy(type: Copy) {
   into "$buildDir/copy"
   with contentSpec
 }
 </pre></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../file/CopySpec.html#with-org.gradle.api.file.CopySpec...-">with</a></code>&nbsp;in interface&nbsp;<code><a href="../file/CopySpec.html" title="interface in org.gradle.api.file">CopySpec</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>sourceSpecs</code> - The specs to add</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>this</dd>
</dl>
</li>
</ul>
<a name="into-java.lang.Object-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>into</h4>
<pre class="methodSignature">public&nbsp;<a href="AbstractCopyTask.html" title="class in org.gradle.api.tasks">AbstractCopyTask</a>&nbsp;into&#8203;(java.lang.Object&nbsp;destDir)</pre>
<div class="block">Specifies the destination directory for a copy. The destination is evaluated as per <a href="../Project.html#file-java.lang.Object-"><code>Project.file(Object)</code></a>.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../file/CopyProcessingSpec.html#into-java.lang.Object-">into</a></code>&nbsp;in interface&nbsp;<code><a href="../file/CopyProcessingSpec.html" title="interface in org.gradle.api.file">CopyProcessingSpec</a></code></dd>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../file/CopySpec.html#into-java.lang.Object-">into</a></code>&nbsp;in interface&nbsp;<code><a href="../file/CopySpec.html" title="interface in org.gradle.api.file">CopySpec</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>destDir</code> - Path to the destination directory for a Copy</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>this</dd>
</dl>
</li>
</ul>
<a name="into-java.lang.Object-groovy.lang.Closure-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>into</h4>
<pre class="methodSignature">public&nbsp;<a href="AbstractCopyTask.html" title="class in org.gradle.api.tasks">AbstractCopyTask</a>&nbsp;into&#8203;(java.lang.Object&nbsp;destPath,
                             <a href="https://docs.groovy-lang.org/docs/groovy-3.0.17/html/gapi/groovy/lang/Closure.html?is-external=true" title="class or interface in groovy.lang" class="externalLink">Closure</a>&nbsp;configureClosure)</pre>
<div class="block">Creates and configures a child <code>CopySpec</code> with the given destination path.
 The destination is evaluated as per <a href="../Project.html#file-java.lang.Object-"><code>Project.file(Object)</code></a>.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../file/CopySpec.html#into-java.lang.Object-groovy.lang.Closure-">into</a></code>&nbsp;in interface&nbsp;<code><a href="../file/CopySpec.html" title="interface in org.gradle.api.file">CopySpec</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>destPath</code> - Path to the destination directory for a Copy</dd>
<dd><code>configureClosure</code> - The closure to use to configure the child <code>CopySpec</code>.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>this</dd>
</dl>
</li>
</ul>
<a name="into-java.lang.Object-org.gradle.api.Action-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>into</h4>
<pre class="methodSignature">public&nbsp;<a href="../file/CopySpec.html" title="interface in org.gradle.api.file">CopySpec</a>&nbsp;into&#8203;(java.lang.Object&nbsp;destPath,
                     <a href="../Action.html" title="interface in org.gradle.api">Action</a>&lt;? super <a href="../file/CopySpec.html" title="interface in org.gradle.api.file">CopySpec</a>&gt;&nbsp;copySpec)</pre>
<div class="block">Creates and configures a child <code>CopySpec</code> with the given destination path.
 The destination is evaluated as per <a href="../Project.html#file-java.lang.Object-"><code>Project.file(Object)</code></a>.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../file/CopySpec.html#into-java.lang.Object-org.gradle.api.Action-">into</a></code>&nbsp;in interface&nbsp;<code><a href="../file/CopySpec.html" title="interface in org.gradle.api.file">CopySpec</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>destPath</code> - Path to the destination directory for a Copy</dd>
<dd><code>copySpec</code> - The action to use to configure the child <code>CopySpec</code>.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>this</dd>
</dl>
</li>
</ul>
<a name="include-java.lang.String...-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>include</h4>
<pre class="methodSignature">public&nbsp;<a href="AbstractCopyTask.html" title="class in org.gradle.api.tasks">AbstractCopyTask</a>&nbsp;include&#8203;(java.lang.String...&nbsp;includes)</pre>
<div class="block">Adds an ANT style include pattern. This method may be called multiple times to append new patterns and multiple
 patterns may be specified in a single call.

 If includes are not provided, then all files in this container will be included. If includes are provided, then a
 file must match at least one of the include patterns to be processed.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../file/CopySpec.html#include-java.lang.String...-">include</a></code>&nbsp;in interface&nbsp;<code><a href="../file/CopySpec.html" title="interface in org.gradle.api.file">CopySpec</a></code></dd>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="util/PatternFilterable.html#include-java.lang.String...-">include</a></code>&nbsp;in interface&nbsp;<code><a href="util/PatternFilterable.html" title="interface in org.gradle.api.tasks.util">PatternFilterable</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>includes</code> - a vararg list of include patterns</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>this</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="util/PatternFilterable.html" title="interface in org.gradle.api.tasks.util"><code>Pattern Format</code></a></dd>
</dl>
</li>
</ul>
<a name="include-java.lang.Iterable-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>include</h4>
<pre class="methodSignature">public&nbsp;<a href="AbstractCopyTask.html" title="class in org.gradle.api.tasks">AbstractCopyTask</a>&nbsp;include&#8203;(java.lang.Iterable&lt;java.lang.String&gt;&nbsp;includes)</pre>
<div class="block">Adds an ANT style include pattern. This method may be called multiple times to append new patterns and multiple
 patterns may be specified in a single call.

 If includes are not provided, then all files in this container will be included. If includes are provided, then a
 file must match at least one of the include patterns to be processed.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../file/CopySpec.html#include-java.lang.Iterable-">include</a></code>&nbsp;in interface&nbsp;<code><a href="../file/CopySpec.html" title="interface in org.gradle.api.file">CopySpec</a></code></dd>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="util/PatternFilterable.html#include-java.lang.Iterable-">include</a></code>&nbsp;in interface&nbsp;<code><a href="util/PatternFilterable.html" title="interface in org.gradle.api.tasks.util">PatternFilterable</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>includes</code> - a Iterable providing more include patterns</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>this</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="util/PatternFilterable.html" title="interface in org.gradle.api.tasks.util"><code>Pattern Format</code></a></dd>
</dl>
</li>
</ul>
<a name="include-org.gradle.api.specs.Spec-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>include</h4>
<pre class="methodSignature">public&nbsp;<a href="AbstractCopyTask.html" title="class in org.gradle.api.tasks">AbstractCopyTask</a>&nbsp;include&#8203;(<a href="../specs/Spec.html" title="interface in org.gradle.api.specs">Spec</a>&lt;<a href="../file/FileTreeElement.html" title="interface in org.gradle.api.file">FileTreeElement</a>&gt;&nbsp;includeSpec)</pre>
<div class="block">Adds an include spec. This method may be called multiple times to append new specs.

 If includes are not provided, then all files in this container will be included. If includes are provided, then a
 file must match at least one of the include patterns or specs to be included.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../file/CopySpec.html#include-org.gradle.api.specs.Spec-">include</a></code>&nbsp;in interface&nbsp;<code><a href="../file/CopySpec.html" title="interface in org.gradle.api.file">CopySpec</a></code></dd>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="util/PatternFilterable.html#include-org.gradle.api.specs.Spec-">include</a></code>&nbsp;in interface&nbsp;<code><a href="util/PatternFilterable.html" title="interface in org.gradle.api.tasks.util">PatternFilterable</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>includeSpec</code> - the spec to add</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>this</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="util/PatternFilterable.html" title="interface in org.gradle.api.tasks.util"><code>Pattern Format</code></a></dd>
</dl>
</li>
</ul>
<a name="include-groovy.lang.Closure-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>include</h4>
<pre class="methodSignature">public&nbsp;<a href="AbstractCopyTask.html" title="class in org.gradle.api.tasks">AbstractCopyTask</a>&nbsp;include&#8203;(<a href="https://docs.groovy-lang.org/docs/groovy-3.0.17/html/gapi/groovy/lang/Closure.html?is-external=true" title="class or interface in groovy.lang" class="externalLink">Closure</a>&nbsp;includeSpec)</pre>
<div class="block">Adds an include spec. This method may be called multiple times to append new specs. The given closure is passed a
 <a href="../file/FileTreeElement.html" title="interface in org.gradle.api.file"><code>FileTreeElement</code></a> as its parameter.

 If includes are not provided, then all files in this container will be included. If includes are provided, then a
 file must match at least one of the include patterns or specs to be included.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../file/CopySpec.html#include-groovy.lang.Closure-">include</a></code>&nbsp;in interface&nbsp;<code><a href="../file/CopySpec.html" title="interface in org.gradle.api.file">CopySpec</a></code></dd>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="util/PatternFilterable.html#include-groovy.lang.Closure-">include</a></code>&nbsp;in interface&nbsp;<code><a href="util/PatternFilterable.html" title="interface in org.gradle.api.tasks.util">PatternFilterable</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>includeSpec</code> - the spec to add</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>this</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="util/PatternFilterable.html" title="interface in org.gradle.api.tasks.util"><code>Pattern Format</code></a></dd>
</dl>
</li>
</ul>
<a name="exclude-java.lang.String...-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>exclude</h4>
<pre class="methodSignature">public&nbsp;<a href="AbstractCopyTask.html" title="class in org.gradle.api.tasks">AbstractCopyTask</a>&nbsp;exclude&#8203;(java.lang.String...&nbsp;excludes)</pre>
<div class="block">Adds an ANT style exclude pattern. This method may be called multiple times to append new patterns and multiple
 patterns may be specified in a single call.

 If excludes are not provided, then no files will be excluded. If excludes are provided, then files must not match
 any exclude pattern to be processed.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../file/CopySpec.html#exclude-java.lang.String...-">exclude</a></code>&nbsp;in interface&nbsp;<code><a href="../file/CopySpec.html" title="interface in org.gradle.api.file">CopySpec</a></code></dd>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="util/PatternFilterable.html#exclude-java.lang.String...-">exclude</a></code>&nbsp;in interface&nbsp;<code><a href="util/PatternFilterable.html" title="interface in org.gradle.api.tasks.util">PatternFilterable</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>excludes</code> - a vararg list of exclude patterns</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>this</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="util/PatternFilterable.html" title="interface in org.gradle.api.tasks.util"><code>Pattern Format</code></a></dd>
</dl>
</li>
</ul>
<a name="exclude-java.lang.Iterable-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>exclude</h4>
<pre class="methodSignature">public&nbsp;<a href="AbstractCopyTask.html" title="class in org.gradle.api.tasks">AbstractCopyTask</a>&nbsp;exclude&#8203;(java.lang.Iterable&lt;java.lang.String&gt;&nbsp;excludes)</pre>
<div class="block">Adds an ANT style exclude pattern. This method may be called multiple times to append new patterns and multiple
 patterns may be specified in a single call.

 If excludes are not provided, then no files will be excluded. If excludes are provided, then files must not match
 any exclude pattern to be processed.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../file/CopySpec.html#exclude-java.lang.Iterable-">exclude</a></code>&nbsp;in interface&nbsp;<code><a href="../file/CopySpec.html" title="interface in org.gradle.api.file">CopySpec</a></code></dd>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="util/PatternFilterable.html#exclude-java.lang.Iterable-">exclude</a></code>&nbsp;in interface&nbsp;<code><a href="util/PatternFilterable.html" title="interface in org.gradle.api.tasks.util">PatternFilterable</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>excludes</code> - a Iterable providing new exclude patterns</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>this</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="util/PatternFilterable.html" title="interface in org.gradle.api.tasks.util"><code>Pattern Format</code></a></dd>
</dl>
</li>
</ul>
<a name="exclude-org.gradle.api.specs.Spec-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>exclude</h4>
<pre class="methodSignature">public&nbsp;<a href="AbstractCopyTask.html" title="class in org.gradle.api.tasks">AbstractCopyTask</a>&nbsp;exclude&#8203;(<a href="../specs/Spec.html" title="interface in org.gradle.api.specs">Spec</a>&lt;<a href="../file/FileTreeElement.html" title="interface in org.gradle.api.file">FileTreeElement</a>&gt;&nbsp;excludeSpec)</pre>
<div class="block">Adds an exclude spec. This method may be called multiple times to append new specs.

 If excludes are not provided, then no files will be excluded. If excludes are provided, then files must not match
 any exclude pattern to be processed.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../file/CopySpec.html#exclude-org.gradle.api.specs.Spec-">exclude</a></code>&nbsp;in interface&nbsp;<code><a href="../file/CopySpec.html" title="interface in org.gradle.api.file">CopySpec</a></code></dd>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="util/PatternFilterable.html#exclude-org.gradle.api.specs.Spec-">exclude</a></code>&nbsp;in interface&nbsp;<code><a href="util/PatternFilterable.html" title="interface in org.gradle.api.tasks.util">PatternFilterable</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>excludeSpec</code> - the spec to add</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>this</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="util/PatternFilterable.html" title="interface in org.gradle.api.tasks.util"><code>Pattern Format</code></a></dd>
</dl>
</li>
</ul>
<a name="exclude-groovy.lang.Closure-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>exclude</h4>
<pre class="methodSignature">public&nbsp;<a href="AbstractCopyTask.html" title="class in org.gradle.api.tasks">AbstractCopyTask</a>&nbsp;exclude&#8203;(<a href="https://docs.groovy-lang.org/docs/groovy-3.0.17/html/gapi/groovy/lang/Closure.html?is-external=true" title="class or interface in groovy.lang" class="externalLink">Closure</a>&nbsp;excludeSpec)</pre>
<div class="block">Adds an exclude spec. This method may be called multiple times to append new specs.The given closure is passed a
 <a href="../file/FileTreeElement.html" title="interface in org.gradle.api.file"><code>FileTreeElement</code></a> as its parameter. The closure should return true or false. Example:

 <pre class='autoTested'>
 copySpec {
   from 'source'
   into 'destination'
   //an example of excluding files from certain configuration:
   exclude { it.file in configurations.someConf.files }
 }
 </pre>

 If excludes are not provided, then no files will be excluded. If excludes are provided, then files must not match
 any exclude pattern to be processed.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../file/CopySpec.html#exclude-groovy.lang.Closure-">exclude</a></code>&nbsp;in interface&nbsp;<code><a href="../file/CopySpec.html" title="interface in org.gradle.api.file">CopySpec</a></code></dd>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="util/PatternFilterable.html#exclude-groovy.lang.Closure-">exclude</a></code>&nbsp;in interface&nbsp;<code><a href="util/PatternFilterable.html" title="interface in org.gradle.api.tasks.util">PatternFilterable</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>excludeSpec</code> - the spec to add</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>this</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="util/PatternFilterable.html" title="interface in org.gradle.api.tasks.util"><code>Pattern Format</code></a></dd>
</dl>
</li>
</ul>
<a name="setIncludes-java.lang.Iterable-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setIncludes</h4>
<pre class="methodSignature">public&nbsp;<a href="AbstractCopyTask.html" title="class in org.gradle.api.tasks">AbstractCopyTask</a>&nbsp;setIncludes&#8203;(java.lang.Iterable&lt;java.lang.String&gt;&nbsp;includes)</pre>
<div class="block">Set the allowable include patterns.  Note that unlike <a href="util/PatternFilterable.html#include-java.lang.Iterable-"><code>PatternFilterable.include(Iterable)</code></a> this replaces any previously
 defined includes.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../file/CopySpec.html#setIncludes-java.lang.Iterable-">setIncludes</a></code>&nbsp;in interface&nbsp;<code><a href="../file/CopySpec.html" title="interface in org.gradle.api.file">CopySpec</a></code></dd>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="util/PatternFilterable.html#setIncludes-java.lang.Iterable-">setIncludes</a></code>&nbsp;in interface&nbsp;<code><a href="util/PatternFilterable.html" title="interface in org.gradle.api.tasks.util">PatternFilterable</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>includes</code> - an Iterable providing new include patterns</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>this</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="util/PatternFilterable.html" title="interface in org.gradle.api.tasks.util"><code>Pattern Format</code></a></dd>
</dl>
</li>
</ul>
<a name="getIncludes--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getIncludes</h4>
<pre class="methodSignature"><a href="Internal.html" title="annotation in org.gradle.api.tasks">@Internal</a>
public&nbsp;java.util.Set&lt;java.lang.String&gt;&nbsp;getIncludes()</pre>
<div class="block">Returns the set of include patterns.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="util/PatternFilterable.html#getIncludes--">getIncludes</a></code>&nbsp;in interface&nbsp;<code><a href="util/PatternFilterable.html" title="interface in org.gradle.api.tasks.util">PatternFilterable</a></code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The include patterns. Returns an empty set when there are no include patterns.</dd>
</dl>
</li>
</ul>
<a name="setExcludes-java.lang.Iterable-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setExcludes</h4>
<pre class="methodSignature">public&nbsp;<a href="AbstractCopyTask.html" title="class in org.gradle.api.tasks">AbstractCopyTask</a>&nbsp;setExcludes&#8203;(java.lang.Iterable&lt;java.lang.String&gt;&nbsp;excludes)</pre>
<div class="block">Set the allowable exclude patterns.  Note that unlike <a href="util/PatternFilterable.html#exclude-java.lang.Iterable-"><code>PatternFilterable.exclude(Iterable)</code></a> this replaces any previously
 defined excludes.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../file/CopySpec.html#setExcludes-java.lang.Iterable-">setExcludes</a></code>&nbsp;in interface&nbsp;<code><a href="../file/CopySpec.html" title="interface in org.gradle.api.file">CopySpec</a></code></dd>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="util/PatternFilterable.html#setExcludes-java.lang.Iterable-">setExcludes</a></code>&nbsp;in interface&nbsp;<code><a href="util/PatternFilterable.html" title="interface in org.gradle.api.tasks.util">PatternFilterable</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>excludes</code> - an Iterable providing new exclude patterns</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>this</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="util/PatternFilterable.html" title="interface in org.gradle.api.tasks.util"><code>Pattern Format</code></a></dd>
</dl>
</li>
</ul>
<a name="getExcludes--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getExcludes</h4>
<pre class="methodSignature"><a href="Internal.html" title="annotation in org.gradle.api.tasks">@Internal</a>
public&nbsp;java.util.Set&lt;java.lang.String&gt;&nbsp;getExcludes()</pre>
<div class="block">Returns the set of exclude patterns.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="util/PatternFilterable.html#getExcludes--">getExcludes</a></code>&nbsp;in interface&nbsp;<code><a href="util/PatternFilterable.html" title="interface in org.gradle.api.tasks.util">PatternFilterable</a></code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The exclude patterns. Returns an empty set when there are no exclude patterns.</dd>
</dl>
</li>
</ul>
<a name="rename-groovy.lang.Closure-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>rename</h4>
<pre class="methodSignature">public&nbsp;<a href="AbstractCopyTask.html" title="class in org.gradle.api.tasks">AbstractCopyTask</a>&nbsp;rename&#8203;(<a href="https://docs.groovy-lang.org/docs/groovy-3.0.17/html/gapi/groovy/lang/Closure.html?is-external=true" title="class or interface in groovy.lang" class="externalLink">Closure</a>&nbsp;closure)</pre>
<div class="block">Renames a source file. The closure will be called with a single parameter, the name of the file.
 The closure should return a String object with a new target name. The closure may return null,
 in which case the original name will be used.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../file/CopyProcessingSpec.html#rename-groovy.lang.Closure-">rename</a></code>&nbsp;in interface&nbsp;<code><a href="../file/CopyProcessingSpec.html" title="interface in org.gradle.api.file">CopyProcessingSpec</a></code></dd>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../file/CopySpec.html#rename-groovy.lang.Closure-">rename</a></code>&nbsp;in interface&nbsp;<code><a href="../file/CopySpec.html" title="interface in org.gradle.api.file">CopySpec</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>closure</code> - rename closure</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>this</dd>
</dl>
</li>
</ul>
<a name="rename-org.gradle.api.Transformer-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>rename</h4>
<pre class="methodSignature">public&nbsp;<a href="AbstractCopyTask.html" title="class in org.gradle.api.tasks">AbstractCopyTask</a>&nbsp;rename&#8203;(<a href="../Transformer.html" title="interface in org.gradle.api">Transformer</a>&lt;java.lang.String,&#8203;java.lang.String&gt;&nbsp;renamer)</pre>
<div class="block">Renames a source file. The function will be called with a single parameter, the name of the file.
 The function should return a new target name. The function may return null,
 in which case the original name will be used.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../file/CopyProcessingSpec.html#rename-org.gradle.api.Transformer-">rename</a></code>&nbsp;in interface&nbsp;<code><a href="../file/CopyProcessingSpec.html" title="interface in org.gradle.api.file">CopyProcessingSpec</a></code></dd>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../file/CopySpec.html#rename-org.gradle.api.Transformer-">rename</a></code>&nbsp;in interface&nbsp;<code><a href="../file/CopySpec.html" title="interface in org.gradle.api.file">CopySpec</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>renamer</code> - rename function</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>this</dd>
</dl>
</li>
</ul>
<a name="rename-java.lang.String-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>rename</h4>
<pre class="methodSignature">public&nbsp;<a href="AbstractCopyTask.html" title="class in org.gradle.api.tasks">AbstractCopyTask</a>&nbsp;rename&#8203;(java.lang.String&nbsp;sourceRegEx,
                               java.lang.String&nbsp;replaceWith)</pre>
<div class="block">Renames files based on a regular expression.  Uses java.util.regex type of regular expressions.  Note that the
 replace string should use the '$1' syntax to refer to capture groups in the source regular expression.  Files
 that do not match the source regular expression will be copied with the original name.

 <p> Example:
 <pre>
 rename '(.*)_OEM_BLUE_(.*)', '$1$2'
 </pre>
 would map the file 'style_OEM_BLUE_.css' to 'style.css'</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../file/CopyProcessingSpec.html#rename-java.lang.String-java.lang.String-">rename</a></code>&nbsp;in interface&nbsp;<code><a href="../file/CopyProcessingSpec.html" title="interface in org.gradle.api.file">CopyProcessingSpec</a></code></dd>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../file/CopySpec.html#rename-java.lang.String-java.lang.String-">rename</a></code>&nbsp;in interface&nbsp;<code><a href="../file/CopySpec.html" title="interface in org.gradle.api.file">CopySpec</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>sourceRegEx</code> - Source regular expression</dd>
<dd><code>replaceWith</code> - Replacement string (use $ syntax for capture groups)</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>this</dd>
</dl>
</li>
</ul>
<a name="rename-java.util.regex.Pattern-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>rename</h4>
<pre class="methodSignature">public&nbsp;<a href="AbstractCopyTask.html" title="class in org.gradle.api.tasks">AbstractCopyTask</a>&nbsp;rename&#8203;(java.util.regex.Pattern&nbsp;sourceRegEx,
                               java.lang.String&nbsp;replaceWith)</pre>
<div class="block">Renames files based on a regular expression. See <a href="../file/CopyProcessingSpec.html#rename-java.lang.String-java.lang.String-"><code>CopyProcessingSpec.rename(String, String)</code></a>.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../file/CopyProcessingSpec.html#rename-java.util.regex.Pattern-java.lang.String-">rename</a></code>&nbsp;in interface&nbsp;<code><a href="../file/CopyProcessingSpec.html" title="interface in org.gradle.api.file">CopyProcessingSpec</a></code></dd>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../file/CopySpec.html#rename-java.util.regex.Pattern-java.lang.String-">rename</a></code>&nbsp;in interface&nbsp;<code><a href="../file/CopySpec.html" title="interface in org.gradle.api.file">CopySpec</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>sourceRegEx</code> - Source regular expression</dd>
<dd><code>replaceWith</code> - Replacement string (use $ syntax for capture groups)</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>this</dd>
</dl>
</li>
</ul>
<a name="filter-java.util.Map-java.lang.Class-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>filter</h4>
<pre class="methodSignature">public&nbsp;<a href="AbstractCopyTask.html" title="class in org.gradle.api.tasks">AbstractCopyTask</a>&nbsp;filter&#8203;(java.util.Map&lt;java.lang.String,&#8203;?&gt;&nbsp;properties,
                               java.lang.Class&lt;? extends java.io.FilterReader&gt;&nbsp;filterType)</pre>
<div class="block"><p>Adds a content filter to be used during the copy.  Multiple calls to filter, add additional filters to the
 filter chain.  Each filter should implement <code>java.io.FilterReader</code>. Include <code>
 org.apache.tools.ant.filters.*</code> for access to all the standard Ant filters.</p>

 <p>Filter properties may be specified using groovy map syntax.</p>

 <p> Examples:
 <pre>
    filter(HeadFilter, lines:25, skip:2)
    filter(ReplaceTokens, tokens:[copyright:'2009', version:'2.3.1'])
 </pre></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../file/ContentFilterable.html#filter-java.util.Map-java.lang.Class-">filter</a></code>&nbsp;in interface&nbsp;<code><a href="../file/ContentFilterable.html" title="interface in org.gradle.api.file">ContentFilterable</a></code></dd>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../file/CopySpec.html#filter-java.util.Map-java.lang.Class-">filter</a></code>&nbsp;in interface&nbsp;<code><a href="../file/CopySpec.html" title="interface in org.gradle.api.file">CopySpec</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>properties</code> - map of filter properties</dd>
<dd><code>filterType</code> - Class of filter to add</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>this</dd>
</dl>
</li>
</ul>
<a name="filter-java.lang.Class-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>filter</h4>
<pre class="methodSignature">public&nbsp;<a href="AbstractCopyTask.html" title="class in org.gradle.api.tasks">AbstractCopyTask</a>&nbsp;filter&#8203;(java.lang.Class&lt;? extends java.io.FilterReader&gt;&nbsp;filterType)</pre>
<div class="block"><p>Adds a content filter to be used during the copy.  Multiple calls to filter, add additional filters to the
 filter chain.  Each filter should implement <code>java.io.FilterReader</code>. Include <code>
 org.apache.tools.ant.filters.*</code> for access to all the standard Ant filters.</p>

 <p> Examples:
 <pre>
    filter(StripJavaComments)
    filter(com.mycompany.project.CustomFilter)
 </pre></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../file/ContentFilterable.html#filter-java.lang.Class-">filter</a></code>&nbsp;in interface&nbsp;<code><a href="../file/ContentFilterable.html" title="interface in org.gradle.api.file">ContentFilterable</a></code></dd>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../file/CopySpec.html#filter-java.lang.Class-">filter</a></code>&nbsp;in interface&nbsp;<code><a href="../file/CopySpec.html" title="interface in org.gradle.api.file">CopySpec</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>filterType</code> - Class of filter to add</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>this</dd>
</dl>
</li>
</ul>
<a name="filter-groovy.lang.Closure-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>filter</h4>
<pre class="methodSignature">public&nbsp;<a href="AbstractCopyTask.html" title="class in org.gradle.api.tasks">AbstractCopyTask</a>&nbsp;filter&#8203;(<a href="https://docs.groovy-lang.org/docs/groovy-3.0.17/html/gapi/groovy/lang/Closure.html?is-external=true" title="class or interface in groovy.lang" class="externalLink">Closure</a>&nbsp;closure)</pre>
<div class="block">Adds a content filter based on the provided closure.  The Closure will be called with each line (stripped of line
 endings) and should return a String to replace the line or <code>null</code> to remove the line.  If every line is
 removed, the result will be an empty file, not an absent one.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../file/ContentFilterable.html#filter-groovy.lang.Closure-">filter</a></code>&nbsp;in interface&nbsp;<code><a href="../file/ContentFilterable.html" title="interface in org.gradle.api.file">ContentFilterable</a></code></dd>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../file/CopySpec.html#filter-groovy.lang.Closure-">filter</a></code>&nbsp;in interface&nbsp;<code><a href="../file/CopySpec.html" title="interface in org.gradle.api.file">CopySpec</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>closure</code> - to implement line based filtering</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>this</dd>
</dl>
</li>
</ul>
<a name="filter-org.gradle.api.Transformer-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>filter</h4>
<pre class="methodSignature">public&nbsp;<a href="AbstractCopyTask.html" title="class in org.gradle.api.tasks">AbstractCopyTask</a>&nbsp;filter&#8203;(<a href="../Transformer.html" title="interface in org.gradle.api">Transformer</a>&lt;java.lang.String,&#8203;java.lang.String&gt;&nbsp;transformer)</pre>
<div class="block">Adds a content filter based on the provided transformer.  The Closure will be called with each line (stripped of line
 endings) and should return a String to replace the line or <code>null</code> to remove the line.  If every line is
 removed, the result will be an empty file, not an absent one.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../file/ContentFilterable.html#filter-org.gradle.api.Transformer-">filter</a></code>&nbsp;in interface&nbsp;<code><a href="../file/ContentFilterable.html" title="interface in org.gradle.api.file">ContentFilterable</a></code></dd>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../file/CopySpec.html#filter-org.gradle.api.Transformer-">filter</a></code>&nbsp;in interface&nbsp;<code><a href="../file/CopySpec.html" title="interface in org.gradle.api.file">CopySpec</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>transformer</code> - to implement line based filtering</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>this</dd>
</dl>
</li>
</ul>
<a name="expand-java.util.Map-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>expand</h4>
<pre class="methodSignature">public&nbsp;<a href="AbstractCopyTask.html" title="class in org.gradle.api.tasks">AbstractCopyTask</a>&nbsp;expand&#8203;(java.util.Map&lt;java.lang.String,&#8203;?&gt;&nbsp;properties)</pre>
<div class="block"><p>Expands property references in each file as it is copied. More specifically, each file is transformed using
 Groovy's <a href="https://docs.groovy-lang.org/docs/groovy-3.0.17/html/gapi/groovy/text/SimpleTemplateEngine.html?is-external=true" title="class or interface in groovy.text" class="externalLink"><code>SimpleTemplateEngine</code></a>. This means you can use simple property references, such as
 <code>$property</code> or <code>${property}</code> in the file. You can also include arbitrary Groovy code in the
 file, such as <code>${version ?: 'unknown'}</code> or <code>${classpath*.name.join(' ')}</code>
 <p>
 Note that all escape sequences (<code>\n</code>, <code>\t</code>, <code>\\</code>, etc) are converted to the symbols
 they represent, so, for example, <code>\n</code> becomes newline. If this is undesirable then <a href="../file/ContentFilterable.html#expand-java.util.Map-org.gradle.api.Action-"><code>ContentFilterable.expand(Map, Action)</code></a>
 should be used to disable this behavior.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../file/ContentFilterable.html#expand-java.util.Map-">expand</a></code>&nbsp;in interface&nbsp;<code><a href="../file/ContentFilterable.html" title="interface in org.gradle.api.file">ContentFilterable</a></code></dd>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../file/CopySpec.html#expand-java.util.Map-">expand</a></code>&nbsp;in interface&nbsp;<code><a href="../file/CopySpec.html" title="interface in org.gradle.api.file">CopySpec</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>properties</code> - reference-to-value map for substitution</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>this</dd>
</dl>
</li>
</ul>
<a name="expand-java.util.Map-org.gradle.api.Action-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>expand</h4>
<pre class="methodSignature">public&nbsp;<a href="AbstractCopyTask.html" title="class in org.gradle.api.tasks">AbstractCopyTask</a>&nbsp;expand&#8203;(java.util.Map&lt;java.lang.String,&#8203;?&gt;&nbsp;properties,
                               <a href="../Action.html" title="interface in org.gradle.api">Action</a>&lt;? super <a href="../file/ExpandDetails.html" title="interface in org.gradle.api.file">ExpandDetails</a>&gt;&nbsp;action)</pre>
<div class="block"><p>Expands property references in each file as it is copied. More specifically, each file is transformed using
 Groovy's <a href="https://docs.groovy-lang.org/docs/groovy-3.0.17/html/gapi/groovy/text/SimpleTemplateEngine.html?is-external=true" title="class or interface in groovy.text" class="externalLink"><code>SimpleTemplateEngine</code></a>. This means you can use simple property references, such as
 <code>$property</code> or <code>${property}</code> in the file. You can also include arbitrary Groovy code in the
 file, such as <code>${version ?: 'unknown'}</code> or <code>${classpath*.name.join(' ')}</code>. The template
 engine can be configured with the provided action.
 <p>
 Note that by default all escape sequences (<code>\n</code>, <code>\t</code>, <code>\\</code>, etc) are converted to the symbols
 they represent, so, for example, <code>\n</code> becomes newline. This behavior is controlled by
 <a href="../file/ExpandDetails.html#getEscapeBackslash--"><code>ExpandDetails.getEscapeBackslash()</code></a> property. It should be set to <code>true</code> to disable escape sequences
 conversion:
 <pre>
  expand(one: '1', two: 2) {
      escapeBackslash = true
  }
 </pre></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../file/ContentFilterable.html#expand-java.util.Map-org.gradle.api.Action-">expand</a></code>&nbsp;in interface&nbsp;<code><a href="../file/ContentFilterable.html" title="interface in org.gradle.api.file">ContentFilterable</a></code></dd>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../file/CopySpec.html#expand-java.util.Map-org.gradle.api.Action-">expand</a></code>&nbsp;in interface&nbsp;<code><a href="../file/CopySpec.html" title="interface in org.gradle.api.file">CopySpec</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>properties</code> - reference-to-value map for substitution</dd>
<dd><code>action</code> - action to perform additional configuration of the underlying template engine</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>this</dd>
</dl>
</li>
</ul>
<a name="getDirMode--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getDirMode</h4>
<pre class="methodSignature"><a href="Internal.html" title="annotation in org.gradle.api.tasks">@Internal</a>
public&nbsp;java.lang.Integer&nbsp;getDirMode()</pre>
<div class="block">Returns the Unix permissions to use for the target directories. <code>null</code> means that existing
 permissions are preserved. It is dependent on the copy action implementation whether these permissions
 will actually be applied.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../file/CopyProcessingSpec.html#getDirMode--">getDirMode</a></code>&nbsp;in interface&nbsp;<code><a href="../file/CopyProcessingSpec.html" title="interface in org.gradle.api.file">CopyProcessingSpec</a></code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The directory permissions, or <code>null</code> if existing permissions should be preserved.</dd>
</dl>
</li>
</ul>
<a name="getFileMode--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getFileMode</h4>
<pre class="methodSignature"><a href="Internal.html" title="annotation in org.gradle.api.tasks">@Internal</a>
public&nbsp;java.lang.Integer&nbsp;getFileMode()</pre>
<div class="block">Returns the Unix permissions to use for the target files. <code>null</code> means that existing
 permissions are preserved. It is dependent on the copy action implementation whether these permissions
 will actually be applied.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../file/CopyProcessingSpec.html#getFileMode--">getFileMode</a></code>&nbsp;in interface&nbsp;<code><a href="../file/CopyProcessingSpec.html" title="interface in org.gradle.api.file">CopyProcessingSpec</a></code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The file permissions, or <code>null</code> if existing permissions should be preserved.</dd>
</dl>
</li>
</ul>
<a name="setDirMode-java.lang.Integer-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setDirMode</h4>
<pre class="methodSignature">public&nbsp;<a href="AbstractCopyTask.html" title="class in org.gradle.api.tasks">AbstractCopyTask</a>&nbsp;setDirMode&#8203;(@Nullable
                                   java.lang.Integer&nbsp;mode)</pre>
<div class="block">Sets the Unix permissions to use for the target directories. <code>null</code> means that existing
 permissions are preserved. It is dependent on the copy action implementation whether these permissions
 will actually be applied.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../file/CopyProcessingSpec.html#setDirMode-java.lang.Integer-">setDirMode</a></code>&nbsp;in interface&nbsp;<code><a href="../file/CopyProcessingSpec.html" title="interface in org.gradle.api.file">CopyProcessingSpec</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>mode</code> - The directory permissions.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>this</dd>
</dl>
</li>
</ul>
<a name="setFileMode-java.lang.Integer-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setFileMode</h4>
<pre class="methodSignature">public&nbsp;<a href="AbstractCopyTask.html" title="class in org.gradle.api.tasks">AbstractCopyTask</a>&nbsp;setFileMode&#8203;(@Nullable
                                    java.lang.Integer&nbsp;mode)</pre>
<div class="block">Sets the Unix permissions to use for the target files. <code>null</code> means that existing
 permissions are preserved. It is dependent on the copy action implementation whether these permissions
 will actually be applied.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../file/CopyProcessingSpec.html#setFileMode-java.lang.Integer-">setFileMode</a></code>&nbsp;in interface&nbsp;<code><a href="../file/CopyProcessingSpec.html" title="interface in org.gradle.api.file">CopyProcessingSpec</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>mode</code> - The file permissions.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>this</dd>
</dl>
</li>
</ul>
<a name="getFilePermissions--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getFilePermissions</h4>
<pre class="methodSignature"><a href="Internal.html" title="annotation in org.gradle.api.tasks">@Internal</a>
public&nbsp;<a href="../provider/Property.html" title="interface in org.gradle.api.provider">Property</a>&lt;<a href="../file/ConfigurableFilePermissions.html" title="interface in org.gradle.api.file">ConfigurableFilePermissions</a>&gt;&nbsp;getFilePermissions()</pre>
<div class="block">Property for configuring file access permissions.
 For details see <a href="../file/ConfigurableFilePermissions.html" title="interface in org.gradle.api.file"><code>ConfigurableFilePermissions</code></a>.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../file/CopyProcessingSpec.html#getFilePermissions--">getFilePermissions</a></code>&nbsp;in interface&nbsp;<code><a href="../file/CopyProcessingSpec.html" title="interface in org.gradle.api.file">CopyProcessingSpec</a></code></dd>
</dl>
</li>
</ul>
<a name="filePermissions-org.gradle.api.Action-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>filePermissions</h4>
<pre class="methodSignature">public&nbsp;<a href="../file/CopyProcessingSpec.html" title="interface in org.gradle.api.file">CopyProcessingSpec</a>&nbsp;filePermissions&#8203;(<a href="../Action.html" title="interface in org.gradle.api">Action</a>&lt;? super <a href="../file/ConfigurableFilePermissions.html" title="interface in org.gradle.api.file">ConfigurableFilePermissions</a>&gt;&nbsp;configureAction)</pre>
<div class="block">Configuration action for specifying file access permissions.
 For details see <a href="../file/ConfigurableFilePermissions.html" title="interface in org.gradle.api.file"><code>ConfigurableFilePermissions</code></a>.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../file/CopyProcessingSpec.html#filePermissions-org.gradle.api.Action-">filePermissions</a></code>&nbsp;in interface&nbsp;<code><a href="../file/CopyProcessingSpec.html" title="interface in org.gradle.api.file">CopyProcessingSpec</a></code></dd>
</dl>
</li>
</ul>
<a name="getDirPermissions--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getDirPermissions</h4>
<pre class="methodSignature"><a href="Internal.html" title="annotation in org.gradle.api.tasks">@Internal</a>
public&nbsp;<a href="../provider/Property.html" title="interface in org.gradle.api.provider">Property</a>&lt;<a href="../file/ConfigurableFilePermissions.html" title="interface in org.gradle.api.file">ConfigurableFilePermissions</a>&gt;&nbsp;getDirPermissions()</pre>
<div class="block">Property for configuring directory access permissions.
 For details see <a href="../file/ConfigurableFilePermissions.html" title="interface in org.gradle.api.file"><code>ConfigurableFilePermissions</code></a>.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../file/CopyProcessingSpec.html#getDirPermissions--">getDirPermissions</a></code>&nbsp;in interface&nbsp;<code><a href="../file/CopyProcessingSpec.html" title="interface in org.gradle.api.file">CopyProcessingSpec</a></code></dd>
</dl>
</li>
</ul>
<a name="dirPermissions-org.gradle.api.Action-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>dirPermissions</h4>
<pre class="methodSignature">public&nbsp;<a href="../file/CopyProcessingSpec.html" title="interface in org.gradle.api.file">CopyProcessingSpec</a>&nbsp;dirPermissions&#8203;(<a href="../Action.html" title="interface in org.gradle.api">Action</a>&lt;? super <a href="../file/ConfigurableFilePermissions.html" title="interface in org.gradle.api.file">ConfigurableFilePermissions</a>&gt;&nbsp;configureAction)</pre>
<div class="block">Configuration action for specifying directory access permissions.
 For details see <a href="../file/ConfigurableFilePermissions.html" title="interface in org.gradle.api.file"><code>ConfigurableFilePermissions</code></a>.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../file/CopyProcessingSpec.html#dirPermissions-org.gradle.api.Action-">dirPermissions</a></code>&nbsp;in interface&nbsp;<code><a href="../file/CopyProcessingSpec.html" title="interface in org.gradle.api.file">CopyProcessingSpec</a></code></dd>
</dl>
</li>
</ul>
<a name="eachFile-org.gradle.api.Action-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>eachFile</h4>
<pre class="methodSignature">public&nbsp;<a href="AbstractCopyTask.html" title="class in org.gradle.api.tasks">AbstractCopyTask</a>&nbsp;eachFile&#8203;(<a href="../Action.html" title="interface in org.gradle.api">Action</a>&lt;? super <a href="../file/FileCopyDetails.html" title="interface in org.gradle.api.file">FileCopyDetails</a>&gt;&nbsp;action)</pre>
<div class="block">Adds an action to be applied to each file as it is about to be copied into its destination. The action can change
 the destination path of the file, filter the contents of the file, or exclude the file from the result entirely.
 Actions are executed in the order added, and are inherited from the parent spec.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../file/CopyProcessingSpec.html#eachFile-org.gradle.api.Action-">eachFile</a></code>&nbsp;in interface&nbsp;<code><a href="../file/CopyProcessingSpec.html" title="interface in org.gradle.api.file">CopyProcessingSpec</a></code></dd>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../file/CopySpec.html#eachFile-org.gradle.api.Action-">eachFile</a></code>&nbsp;in interface&nbsp;<code><a href="../file/CopySpec.html" title="interface in org.gradle.api.file">CopySpec</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>action</code> - The action to execute.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>this</dd>
</dl>
</li>
</ul>
<a name="eachFile-groovy.lang.Closure-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>eachFile</h4>
<pre class="methodSignature">public&nbsp;<a href="AbstractCopyTask.html" title="class in org.gradle.api.tasks">AbstractCopyTask</a>&nbsp;eachFile&#8203;(<a href="https://docs.groovy-lang.org/docs/groovy-3.0.17/html/gapi/groovy/lang/Closure.html?is-external=true" title="class or interface in groovy.lang" class="externalLink">Closure</a>&nbsp;closure)</pre>
<div class="block">Adds an action to be applied to each file as it about to be copied into its destination. The given closure is
 called with a <a href="../file/FileCopyDetails.html" title="interface in org.gradle.api.file"><code>FileCopyDetails</code></a> as its parameter. Actions are executed in the order
 added, and are inherited from the parent spec.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../file/CopyProcessingSpec.html#eachFile-groovy.lang.Closure-">eachFile</a></code>&nbsp;in interface&nbsp;<code><a href="../file/CopyProcessingSpec.html" title="interface in org.gradle.api.file">CopyProcessingSpec</a></code></dd>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../file/CopySpec.html#eachFile-groovy.lang.Closure-">eachFile</a></code>&nbsp;in interface&nbsp;<code><a href="../file/CopySpec.html" title="interface in org.gradle.api.file">CopySpec</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>closure</code> - The action to execute.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>this</dd>
</dl>
</li>
</ul>
<a name="getFilteringCharset--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getFilteringCharset</h4>
<pre class="methodSignature"><a href="Internal.html" title="annotation in org.gradle.api.tasks">@Internal</a>
public&nbsp;java.lang.String&nbsp;getFilteringCharset()</pre>
<div class="block">Gets the charset used to read and write files when filtering.
 By default, the JVM default charset is used.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../file/CopySpec.html#getFilteringCharset--">getFilteringCharset</a></code>&nbsp;in interface&nbsp;<code><a href="../file/CopySpec.html" title="interface in org.gradle.api.file">CopySpec</a></code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the charset used to read and write files when filtering</dd>
</dl>
</li>
</ul>
<a name="setFilteringCharset-java.lang.String-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>setFilteringCharset</h4>
<pre class="methodSignature">public&nbsp;void&nbsp;setFilteringCharset&#8203;(java.lang.String&nbsp;charset)</pre>
<div class="block">Specifies the charset used to read and write files when filtering.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../file/CopySpec.html#setFilteringCharset-java.lang.String-">setFilteringCharset</a></code>&nbsp;in interface&nbsp;<code><a href="../file/CopySpec.html" title="interface in org.gradle.api.file">CopySpec</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>charset</code> - the name of the charset to use when filtering files</dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li><a href="#nested.class.summary">Nested</a>&nbsp;|&nbsp;</li>
<li><a href="#field.summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
