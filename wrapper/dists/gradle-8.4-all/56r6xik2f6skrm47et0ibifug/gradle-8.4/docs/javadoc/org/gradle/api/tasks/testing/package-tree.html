<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc -->
<title>org.gradle.api.tasks.testing Class Hierarchy (Gradle API 8.4)</title>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<link rel="stylesheet" type="text/css" href="../../../../../javadoc.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../jquery/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
<script type="text/javascript" src="../../../../../jquery/jszip/dist/jszip.min.js"></script>
<script type="text/javascript" src="../../../../../jquery/jszip-utils/dist/jszip-utils.min.js"></script>
<!--[if IE]>
<script type="text/javascript" src="../../../../../jquery/jszip-utils/dist/jszip-utils-ie.min.js"></script>
<![endif]-->
<script type="text/javascript" src="../../../../../jquery/jquery-3.6.0.min.js"></script>
<script type="text/javascript" src="../../../../../jquery/jquery-ui.min.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="org.gradle.api.tasks.testing Class Hierarchy (Gradle API 8.4)";
        }
    }
    catch(err) {
    }
//-->
var pathtoroot = "../../../../../";
var useModuleDirectories = false;
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="fixedNav">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li>Class</li>
<li class="navBarCell1Rev">Tree</li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../../allclasses.html">All&nbsp;Classes</a></li>
</ul>
<ul class="navListSearch">
<li><label for="search">SEARCH:</label>
<input type="text" id="search" value="search" disabled="disabled">
<input type="reset" id="reset" value="reset" disabled="disabled">
</li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
</div>
<div class="navPadding">&nbsp;</div>
<script type="text/javascript"><!--
$('.navPadding').css('padding-top', $('.fixedNav').css("height"));
//-->
</script>
<div class="header">
<h1 class="title">Hierarchy For Package org.gradle.api.tasks.testing</h1>
<span class="packageHierarchyLabel">Package Hierarchies:</span>
<ul class="horizontal">
<li><a href="../../../../../overview-tree.html">All Packages</a></li>
</ul>
</div>
<div class="contentContainer">
<h2 title="Class Hierarchy">Class Hierarchy</h2>
<ul>
<li class="circle">java.lang.Object
<ul>
<li class="circle">org.gradle.api.internal.AbstractTask (implements org.gradle.api.internal.DynamicObjectAware, org.gradle.api.internal.TaskInternal)
<ul>
<li class="circle">org.gradle.api.<a href="../../DefaultTask.html" title="class in org.gradle.api"><span class="typeNameLink">DefaultTask</span></a> (implements org.gradle.api.<a href="../../Task.html" title="interface in org.gradle.api">Task</a>)
<ul>
<li class="circle">org.gradle.api.internal.ConventionTask (implements org.gradle.api.internal.IConventionAware)
<ul>
<li class="circle">org.gradle.api.tasks.testing.<a href="AbstractTestTask.html" title="class in org.gradle.api.tasks.testing"><span class="typeNameLink">AbstractTestTask</span></a> (implements org.gradle.api.reporting.<a href="../../reporting/Reporting.html" title="interface in org.gradle.api.reporting">Reporting</a>&lt;T&gt;, org.gradle.api.tasks.<a href="../VerificationTask.html" title="interface in org.gradle.api.tasks">VerificationTask</a>)
<ul>
<li class="circle">org.gradle.api.tasks.testing.<a href="Test.html" title="class in org.gradle.api.tasks.testing"><span class="typeNameLink">Test</span></a> (implements org.gradle.process.<a href="../../../process/JavaForkOptions.html" title="interface in org.gradle.process">JavaForkOptions</a>, org.gradle.api.tasks.util.<a href="../util/PatternFilterable.html" title="interface in org.gradle.api.tasks.util">PatternFilterable</a>)</li>
</ul>
</li>
</ul>
</li>
<li class="circle">org.gradle.api.tasks.testing.<a href="TestReport.html" title="class in org.gradle.api.tasks.testing"><span class="typeNameLink">TestReport</span></a></li>
</ul>
</li>
</ul>
</li>
<li class="circle">org.gradle.api.tasks.testing.<a href="TestFailure.html" title="class in org.gradle.api.tasks.testing"><span class="typeNameLink">TestFailure</span></a></li>
<li class="circle">org.gradle.api.tasks.testing.<a href="TestFrameworkOptions.html" title="class in org.gradle.api.tasks.testing"><span class="typeNameLink">TestFrameworkOptions</span></a></li>
<li class="circle">java.lang.Throwable (implements java.io.Serializable)
<ul>
<li class="circle">java.lang.Exception
<ul>
<li class="circle">java.lang.RuntimeException
<ul>
<li class="circle">org.gradle.api.<a href="../../GradleException.html" title="class in org.gradle.api"><span class="typeNameLink">GradleException</span></a>
<ul>
<li class="circle">org.gradle.api.tasks.testing.<a href="TestExecutionException.html" title="class in org.gradle.api.tasks.testing"><span class="typeNameLink">TestExecutionException</span></a></li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
<h2 title="Interface Hierarchy">Interface Hierarchy</h2>
<ul>
<li class="circle">org.gradle.util.<a href="../../../util/Configurable.html" title="interface in org.gradle.util"><span class="typeNameLink">Configurable</span></a>&lt;T&gt;
<ul>
<li class="circle">org.gradle.api.reporting.<a href="../../reporting/Report.html" title="interface in org.gradle.api.reporting"><span class="typeNameLink">Report</span></a>
<ul>
<li class="circle">org.gradle.api.reporting.<a href="../../reporting/ConfigurableReport.html" title="interface in org.gradle.api.reporting"><span class="typeNameLink">ConfigurableReport</span></a>
<ul>
<li class="circle">org.gradle.api.reporting.<a href="../../reporting/DirectoryReport.html" title="interface in org.gradle.api.reporting"><span class="typeNameLink">DirectoryReport</span></a>
<ul>
<li class="circle">org.gradle.api.tasks.testing.<a href="JUnitXmlReport.html" title="interface in org.gradle.api.tasks.testing"><span class="typeNameLink">JUnitXmlReport</span></a></li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
<li class="circle">org.gradle.api.reporting.<a href="../../reporting/ReportContainer.html" title="interface in org.gradle.api.reporting"><span class="typeNameLink">ReportContainer</span></a>&lt;T&gt; (also extends org.gradle.api.<a href="../../NamedDomainObjectSet.html" title="interface in org.gradle.api">NamedDomainObjectSet</a>&lt;T&gt;)
<ul>
<li class="circle">org.gradle.api.tasks.testing.<a href="TestTaskReports.html" title="interface in org.gradle.api.tasks.testing"><span class="typeNameLink">TestTaskReports</span></a></li>
</ul>
</li>
</ul>
</li>
<li class="circle">java.lang.Iterable&lt;T&gt;
<ul>
<li class="circle">java.util.Collection&lt;E&gt;
<ul>
<li class="circle">org.gradle.api.<a href="../../DomainObjectCollection.html" title="interface in org.gradle.api"><span class="typeNameLink">DomainObjectCollection</span></a>&lt;T&gt;
<ul>
<li class="circle">org.gradle.api.<a href="../../DomainObjectSet.html" title="interface in org.gradle.api"><span class="typeNameLink">DomainObjectSet</span></a>&lt;T&gt; (also extends java.util.Set&lt;E&gt;)
<ul>
<li class="circle">org.gradle.api.<a href="../../NamedDomainObjectSet.html" title="interface in org.gradle.api"><span class="typeNameLink">NamedDomainObjectSet</span></a>&lt;T&gt; (also extends org.gradle.api.<a href="../../NamedDomainObjectCollection.html" title="interface in org.gradle.api">NamedDomainObjectCollection</a>&lt;T&gt;)
<ul>
<li class="circle">org.gradle.api.reporting.<a href="../../reporting/ReportContainer.html" title="interface in org.gradle.api.reporting"><span class="typeNameLink">ReportContainer</span></a>&lt;T&gt; (also extends org.gradle.util.<a href="../../../util/Configurable.html" title="interface in org.gradle.util">Configurable</a>&lt;T&gt;)
<ul>
<li class="circle">org.gradle.api.tasks.testing.<a href="TestTaskReports.html" title="interface in org.gradle.api.tasks.testing"><span class="typeNameLink">TestTaskReports</span></a></li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
<li class="circle">org.gradle.api.<a href="../../NamedDomainObjectCollection.html" title="interface in org.gradle.api"><span class="typeNameLink">NamedDomainObjectCollection</span></a>&lt;T&gt;
<ul>
<li class="circle">org.gradle.api.<a href="../../NamedDomainObjectSet.html" title="interface in org.gradle.api"><span class="typeNameLink">NamedDomainObjectSet</span></a>&lt;T&gt; (also extends org.gradle.api.<a href="../../DomainObjectSet.html" title="interface in org.gradle.api">DomainObjectSet</a>&lt;T&gt;)
<ul>
<li class="circle">org.gradle.api.reporting.<a href="../../reporting/ReportContainer.html" title="interface in org.gradle.api.reporting"><span class="typeNameLink">ReportContainer</span></a>&lt;T&gt; (also extends org.gradle.util.<a href="../../../util/Configurable.html" title="interface in org.gradle.util">Configurable</a>&lt;T&gt;)
<ul>
<li class="circle">org.gradle.api.tasks.testing.<a href="TestTaskReports.html" title="interface in org.gradle.api.tasks.testing"><span class="typeNameLink">TestTaskReports</span></a></li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
<li class="circle">java.util.Set&lt;E&gt;
<ul>
<li class="circle">org.gradle.api.<a href="../../DomainObjectSet.html" title="interface in org.gradle.api"><span class="typeNameLink">DomainObjectSet</span></a>&lt;T&gt; (also extends org.gradle.api.<a href="../../DomainObjectCollection.html" title="interface in org.gradle.api">DomainObjectCollection</a>&lt;T&gt;)
<ul>
<li class="circle">org.gradle.api.<a href="../../NamedDomainObjectSet.html" title="interface in org.gradle.api"><span class="typeNameLink">NamedDomainObjectSet</span></a>&lt;T&gt; (also extends org.gradle.api.<a href="../../NamedDomainObjectCollection.html" title="interface in org.gradle.api">NamedDomainObjectCollection</a>&lt;T&gt;)
<ul>
<li class="circle">org.gradle.api.reporting.<a href="../../reporting/ReportContainer.html" title="interface in org.gradle.api.reporting"><span class="typeNameLink">ReportContainer</span></a>&lt;T&gt; (also extends org.gradle.util.<a href="../../../util/Configurable.html" title="interface in org.gradle.util">Configurable</a>&lt;T&gt;)
<ul>
<li class="circle">org.gradle.api.tasks.testing.<a href="TestTaskReports.html" title="interface in org.gradle.api.tasks.testing"><span class="typeNameLink">TestTaskReports</span></a></li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
<li class="circle">org.gradle.api.<a href="../../Named.html" title="interface in org.gradle.api"><span class="typeNameLink">Named</span></a>
<ul>
<li class="circle">org.gradle.api.reporting.<a href="../../reporting/ReportSpec.html" title="interface in org.gradle.api.reporting"><span class="typeNameLink">ReportSpec</span></a>
<ul>
<li class="circle">org.gradle.api.tasks.testing.<a href="AggregateTestReport.html" title="interface in org.gradle.api.tasks.testing"><span class="typeNameLink">AggregateTestReport</span></a></li>
</ul>
</li>
</ul>
</li>
<li class="circle">org.gradle.api.tasks.testing.<a href="TestDescriptor.html" title="interface in org.gradle.api.tasks.testing"><span class="typeNameLink">TestDescriptor</span></a></li>
<li class="circle">org.gradle.api.tasks.testing.<a href="TestFailureDetails.html" title="interface in org.gradle.api.tasks.testing"><span class="typeNameLink">TestFailureDetails</span></a></li>
<li class="circle">org.gradle.api.tasks.testing.<a href="TestFilter.html" title="interface in org.gradle.api.tasks.testing"><span class="typeNameLink">TestFilter</span></a></li>
<li class="circle">org.gradle.api.tasks.testing.<a href="TestListener.html" title="interface in org.gradle.api.tasks.testing"><span class="typeNameLink">TestListener</span></a></li>
<li class="circle">org.gradle.api.tasks.testing.<a href="TestOutputEvent.html" title="interface in org.gradle.api.tasks.testing"><span class="typeNameLink">TestOutputEvent</span></a></li>
<li class="circle">org.gradle.api.tasks.testing.<a href="TestOutputListener.html" title="interface in org.gradle.api.tasks.testing"><span class="typeNameLink">TestOutputListener</span></a></li>
<li class="circle">org.gradle.api.tasks.testing.<a href="TestResult.html" title="interface in org.gradle.api.tasks.testing"><span class="typeNameLink">TestResult</span></a></li>
</ul>
<h2 title="Enum Hierarchy">Enum Hierarchy</h2>
<ul>
<li class="circle">java.lang.Object
<ul>
<li class="circle">java.lang.Enum&lt;E&gt; (implements java.lang.Comparable&lt;T&gt;, java.io.Serializable)
<ul>
<li class="circle">org.gradle.api.tasks.testing.<a href="TestOutputEvent.Destination.html" title="enum in org.gradle.api.tasks.testing"><span class="typeNameLink">TestOutputEvent.Destination</span></a></li>
<li class="circle">org.gradle.api.tasks.testing.<a href="TestResult.ResultType.html" title="enum in org.gradle.api.tasks.testing"><span class="typeNameLink">TestResult.ResultType</span></a></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li>Class</li>
<li class="navBarCell1Rev">Tree</li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../../allclasses.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
